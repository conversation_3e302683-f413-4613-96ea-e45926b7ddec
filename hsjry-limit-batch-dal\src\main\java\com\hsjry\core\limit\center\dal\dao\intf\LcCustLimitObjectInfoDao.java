package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitBaseInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcQueryAmtLimitRuleDataDto;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度实例所属对象信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 03:09:49
 */
public interface LcCustLimitObjectInfoDao extends IBaseDao<LcCustLimitObjectInfoDo> {
    /**
     * 分页查询额度实例所属对象信息
     *
     * @param lcCustLimitObjectInfoQuery 条件
     * @return PageInfo<LcCustLimitObjectInfoDo>
     */
    PageInfo<LcCustLimitObjectInfoDo> selectPage(LcCustLimitObjectInfoQuery lcCustLimitObjectInfoQuery,
        PageParam pageParam);

    /**
     * 根据key查询额度实例所属对象信息
     *
     * @param custLimitId
     * @return
     */
    LcCustLimitObjectInfoDo selectByKey(String custLimitId);

    /**
     * 根据key删除额度实例所属对象信息
     *
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custLimitId);

    /**
     * 查询额度实例所属对象信息信息
     *
     * @param lcCustLimitObjectInfoQuery 条件
     * @return List<LcCustLimitObjectInfoDo>
     */
    List<LcCustLimitObjectInfoDo> selectByExample(LcCustLimitObjectInfoQuery lcCustLimitObjectInfoQuery);

    /**
     * 新增额度实例所属对象信息信息
     *
     * @param lcCustLimitObjectInfo 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitObjectInfoDo lcCustLimitObjectInfo);

    /**
     * 修改额度实例所属对象信息信息
     *
     * @param lcCustLimitObjectInfo
     * @return
     */
    int updateBySelective(LcCustLimitObjectInfoDo lcCustLimitObjectInfo);

    /**
     * 修改额度实例所属对象信息信息
     *
     * @param lcCustLimitObjectInfo
     * @param lcCustLimitObjectInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitObjectInfoDo lcCustLimitObjectInfo,
        LcCustLimitObjectInfoQuery lcCustLimitObjectInfoQuery);

    /**
     *  分页查询额度实例信息，连表查询
     *
     * @param lcCustLimitObjectInfoQuery
     * @param pageParam
     * @return
     */
    PageInfo<LcCustLimitObjectInfoDo> selectPageUserDistinct(LcCustLimitObjectInfoQuery lcCustLimitObjectInfoQuery, PageParam pageParam);

    /**
     *  分页查询额度实例信息，连表查询
     *
     * @param lcCustLimitObjectInfoQuery
     * @param pageParam
     * @return
     */
    PageInfo<LcCustLimitObjectInfoDo> selectPageProductDistinct(LcCustLimitObjectInfoQuery lcCustLimitObjectInfoQuery, PageParam pageParam);

    /**
     *  分页查询同业客户额度实例表基础信息
     *
     * @param lcCustLimitObjectInfoQuery
     * @param pageParam
     * @return
     */
    PageInfo<LcCustLimitBaseInfoDo> selectInterBankPage(LcCustLimitObjectInfoQuery lcCustLimitObjectInfoQuery, PageParam pageParam);

    /**
     * 查询集团客户
     * @param userId
     * @return
     */
    List<LcCustLimitBaseInfoDo> selectParentUserName(String userId,List<String> userIdList);

    /**
     * 分页查询额度实例所属对象信息
     *
     * @param queryAmtLimitRuleDataDto 条件
     * @return PageInfo<LcCustLimitObjectInfoDo>
     */
    PageInfo<LcCustLimitObjectInfoDo> selectObjectPage(LcQueryAmtLimitRuleDataDto queryAmtLimitRuleDataDto,
        PageParam pageParam);
}
