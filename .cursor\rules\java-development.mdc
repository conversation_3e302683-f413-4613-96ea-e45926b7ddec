---
description: Java开发规范，包含代码风格、命名规范、注释标准等指导原则
globs: *.java
alwaysApply: false
---

# Java开发规范

## 命名规范

### 类命名
- **实现类**: 以 `Impl` 结尾，如 `AmtLimitBizImpl`
- **批处理作业**: 以 `Job` 结尾，如 `AdjustAmtLimitPlanJob`
- **DAO接口**: 以 `Dao` 结尾，如 `CustLimitInfoBatchDao`
- **Mapper接口**: 以 `Mapper` 结尾，如 `CustLimitInfoBatchMapper`
- **业务对象**: 以 `Bo` 结尾，如 `SharedBo`
- **数据对象**: 以 `Do` 结尾，如 `LcCustLimitInfoDo`
- **查询对象**: 以 `Query` 结尾，如 `CustLimitInfoQuery`

### 方法命名
- **查询方法**: 以 `query`、`find`、`get` 开头
- **处理方法**: 以 `handle`、`process` 开头
- **验证方法**: 以 `valid` 开头，如 `amtLimitValid`
- **转换方法**: 以 `convert`、`transform` 开头

### 常量命名
- 使用 `UPPER_SNAKE_CASE`，如 `AMT_LIMIT_DETAIL_SHARD_NUM`
- 分组定义在 [LimitBatchConstants.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/constants/LimitBatchConstants.java)

## 注释规范

### 类注释模板
```java
/**
 * 批处理作业：调整金额限额计划
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-01
 */
public class AdjustAmtLimitPlanJob implements JobCoreBusiness {
}
```

### 方法注释模板
```java
/**
 * 处理金额限额调整计划
 * 
 * @param sharedBo 共享业务对象
 * @return 分片处理结果
 */
public ShardingResult handleBusiness(SharedBo sharedBo) {
}
```

## 代码结构

### 批处理作业结构
- 实现 `JobCoreBusiness` 接口
- 在 `handleBusiness` 方法中实现主要逻辑
- 使用分片处理大数据量
- 返回 `ShardingResult` 对象

### 业务层结构
- 接口定义在 `biz` 模块
- 实现类在 `biz-impl` 模块
- 使用 `@Service` 注解标注
- 注入DAO进行数据操作

### DAO层结构
- 接口继承基础DAO
- 实现类使用MyBatis
- 查询对象用于复杂查询
- 使用分页查询处理大数据量

## 异常处理

### 异常捕获
```java
try {
    // 业务逻辑
} catch (Exception e) {
    log.error("处理失败，参数：{}", param, e);
    throw new BusinessException("处理失败", e);
}
```

### 错误码使用
参考 [EnumLimitBatchErrorCode.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/enums/EnumLimitBatchErrorCode.java) 中定义的错误码

## 日志规范

### 日志级别
- `ERROR`: 系统错误和异常
- `WARN`: 业务警告
- `INFO`: 重要业务流程
- `DEBUG`: 详细调试信息

### 日志格式
```java
@Slf4j
public class SampleService {
    public void process(String param) {
        log.info("开始处理业务，参数：{}", param);
        log.debug("详细处理步骤：{}", detail);
        log.error("处理失败，错误信息：{}", error, exception);
    }
}
```

## 分片处理规范

### 分片大小配置
使用 [LimitBatchConstants.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/constants/LimitBatchConstants.java) 中的常量：
- `AMT_LIMIT_DETAIL_SHARD_NUM = 10` - 限额明细分片数量

### 分页查询模式
```java
PageInfo<DataObject> pageInfo = new PageInfo<>();
pageInfo.setPageSize(BATCH_SIZE);
pageInfo.setPageNum(pageNum);
List<DataObject> dataList = dao.queryByPage(query, pageInfo);
```
