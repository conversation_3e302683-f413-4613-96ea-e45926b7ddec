package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcIbnkLmtViewDo;
import com.hsjry.core.limit.center.dal.dao.query.LcIbnkLmtViewQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 同业客户额度视图数据库操作接口
 * 
 * <AUTHOR>
 * @date 2025-07-07 12:58:26
 */
public interface LcIbnkLmtViewDao extends IBaseDao<LcIbnkLmtViewDo> {
    /**
     * 分页查询同业客户额度视图
     *
     * @param lcIbnkLmtViewQuery 条件
     * @return PageInfo<LcIbnkLmtViewDo>
     */
    PageInfo<LcIbnkLmtViewDo> selectPage(LcIbnkLmtViewQuery lcIbnkLmtViewQuery, PageParam pageParam);

    /**
     * 根据key查询同业客户额度视图
     *
     * @param ibnkLmtViewId
     * @return
     */
    LcIbnkLmtViewDo selectByKey(String ibnkLmtViewId);
    /**
     * 根据key删除同业客户额度视图
     *
     * @param ibnkLmtViewId
     * @return
     */
    int deleteByKey(String ibnkLmtViewId);

    /**
     * 查询同业客户额度视图信息
     *
     * @param lcIbnkLmtViewQuery 条件
     * @return List<LcIbnkLmtViewDo>
     */
    List<LcIbnkLmtViewDo> selectByExample(LcIbnkLmtViewQuery lcIbnkLmtViewQuery);

    /**
     * 新增同业客户额度视图信息
     *
     * @param lcIbnkLmtView 条件
     * @return int>
     */
    int insertBySelective(LcIbnkLmtViewDo lcIbnkLmtView);

    /**
     * 修改同业客户额度视图信息
     *
     * @param lcIbnkLmtView
     * @return
     */
    int updateBySelective(LcIbnkLmtViewDo lcIbnkLmtView);
    /**
     * 修改同业客户额度视图信息
     *
     * @param lcIbnkLmtView
     * @param lcIbnkLmtViewQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcIbnkLmtViewDo lcIbnkLmtView, LcIbnkLmtViewQuery lcIbnkLmtViewQuery);

    /**
     * 1.处理客户信息,使用MERGE语句实现存在则更新,不存在则插入
     */
    int mergeCustomerInfo();

    /**
     * 2.更新客户总额度/可用金额/业务币种等额度信息
     */
    int mergeCustomerLimitInfo();
}
