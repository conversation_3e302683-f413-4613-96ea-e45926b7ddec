package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import com.hsjry.base.common.job.dto.ISliceBatchSerialDo;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度分片流水Do
 *
 * <AUTHOR>
 * @date 2023-03-13 12:30:49
 */
@Table(name = "lc_slice_batch_serial")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcSliceBatchSerialDo extends LcSliceBatchSerialKeyDo implements Serializable, ISliceBatchSerialDo {
    private static final long serialVersionUID = 1635257101087604736L;
    /** 交易码;EnumJobTrade */
    @Column(name = "trade_code")
    private String tradeCode;
    /** 数据分片处理失败次数 */
    @Column(name = "failure_times")
    private Integer failureTimes;
    /** 结束标记 EnumBool(N-否, Y-是) */
    @Column(name = "finish_flag")
    private String finishFlag;
    /** 数据分片备注，记录失败原因或其他 */
    @Column(name = "shared_note")
    private String sharedNote;
    /** 数据分片统计内容 */
    @Column(name = "shared_statistics")
    private String sharedStatistics;
    /** 数据分片实际成功处理的条数 */
    @Column(name = "shared_pass_count")
    private Integer sharedPassCount;
    /** 数据分片详情 */
    @Column(name = "shared_detail")
    private String sharedDetail;
    /** 执行ip */
    @Column(name = "exec_ip")
    private String execIp;
    /** 任务执行日期,8位日期标识 */
    @Column(name = "exec_date")
    private Integer execDate;
    /** 调度平台分片信息id，外部流水 */
    @Column(name = "ext_serial_id")
    private String extSerialId;
    /** 文件处理状态 */
    @Column(name = "file_status")
    private String fileStatus;
    /** 分片数据处理状态 */
    @Column(name = "shared_status")
    private String sharedStatus;
    /** 全局流水号 */
    @Column(name = "global_serial_no")
    private String globalSerialNo;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 前置业务时间 */
    @Column(name = "inbound_serial_datetime")
    private java.util.Date inboundSerialDatetime;
    /** 前置业务流水 */
    @Column(name = "inbound_serial_no")
    private String inboundSerialNo;
    /** 业务时间 */
    @Column(name = "biz_datetime")
    private java.util.Date bizDatetime;
    /** 渠道号 */
    @Column(name = "channel_no")
    private String channelNo;
    /** 业务流水号 */
    @Column(name = "serial_no")
    private String serialNo;

    @Override
    public boolean sharedStatusSuccess() {
        return EnumLimitHandlerStatus.SUCCESS.getCode()
            .equals(sharedStatus);
    }
}
