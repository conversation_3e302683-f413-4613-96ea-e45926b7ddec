package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRecordDailyDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitRecordDailyQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度记录日报数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcCustLimitRecordDailyDao extends IBaseDao<LcCustLimitRecordDailyDo> {
    /**
     * 分页查询额度记录日报
     *
     * @param lcCustLimitRecordDailyQuery 条件
     * @return PageInfo<LcCustLimitRecordDailyDo>
     */
    PageInfo<LcCustLimitRecordDailyDo> selectPage(LcCustLimitRecordDailyQuery lcCustLimitRecordDailyQuery,
        PageParam pageParam);

    /**
     * 根据key查询额度记录日报
     *
     * @param recordDateStr
     * @param recordId
     * @return
     */
    LcCustLimitRecordDailyDo selectByKey(String recordDateStr, String recordId);

    /**
     * 根据key删除额度记录日报
     *
     * @param recordDateStr
     * @param recordId
     * @return
     */
    int deleteByKey(String recordDateStr, String recordId);

    /**
     * 查询额度记录日报信息
     *
     * @param lcCustLimitRecordDailyQuery 条件
     * @return List<LcCustLimitRecordDailyDo>
     */
    List<LcCustLimitRecordDailyDo> selectByExample(LcCustLimitRecordDailyQuery lcCustLimitRecordDailyQuery);

    /**
     * 新增额度记录日报信息
     *
     * @param lcCustLimitRecordDaily 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitRecordDailyDo lcCustLimitRecordDaily);

    /**
     * 修改额度记录日报信息
     *
     * @param lcCustLimitRecordDaily
     * @return
     */
    int updateBySelective(LcCustLimitRecordDailyDo lcCustLimitRecordDaily);

    /**
     * 修改额度记录日报信息
     *
     * @param lcCustLimitRecordDaily
     * @param lcCustLimitRecordDailyQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitRecordDailyDo lcCustLimitRecordDaily,
        LcCustLimitRecordDailyQuery lcCustLimitRecordDailyQuery);
}
