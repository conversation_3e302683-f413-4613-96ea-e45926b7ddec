package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 产品信息Do
 *
 * <AUTHOR>
 * @date 2023-11-23 02:55:08
 */
@Table(name = "lc_product_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcProductInfoDo extends LcProductInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1727521130916544512L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 产品名称 */
    @Column(name = "product_name")
    private String productName;
    /** 删除标记;EnumBool */
    @Column(name = "delete_flag")
    private String deleteFlag;
    /** 父级产品编号 */
    @Column(name = "parent_product_id")
    private String parentProductId;
    /** 渠道编号 */
    @Column(name = "channel_no")
    private String channelNo;
    /** 一级目录 */
    @Column(name = "product_catalog_one")
    private String productCatalogOne;
    /** 二级目录 */
    @Column(name = "product_catalog_two")
    private String productCatalogTwo;
}
