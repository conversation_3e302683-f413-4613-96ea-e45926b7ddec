/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.intf.CustLimitAmtInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.CustLimitAmtInfoBatchMapper;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitAmtInfoQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitAmtInfoDo;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/17 9:57
 */
@Repository
public class CustLimitAmtInfoBatchDaoImpl extends AbstractBaseDaoImpl<LcCustLimitAmtInfoDo, CustLimitAmtInfoBatchMapper>
    implements CustLimitAmtInfoBatchDao {
    @Override
    public List<LcCustLimitAmtInfoDo> selectShardList(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectShardList(query);
    }

    @Override
    public LcCustLimitAmtInfoDo selectFirstOne(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectFirstOne(query);
    }

    @Override
    public Integer selectCountByCurrentGroup(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitAmtInfoDo> selectObjectShardList(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectObjectShardList(query);
    }

    @Override
    public List<LcCustLimitAmtInfoDo> selectExpireShardList(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectExpireShardList(query);
    }

    @Override
    public LcCustLimitAmtInfoDo selectObjectFirstOne(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectObjectFirstOne(query);
    }

    @Override
    public LcCustLimitAmtInfoDo selectExpireFirstOne(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectExpireFirstOne(query);
    }

    @Override
    public Integer selectObjectCountByCurrentGroup(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectObjectCountByCurrentGroup(query);
    }

    @Override
    public Integer selectExpireCountByCurrentGroup(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectExpireCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitAmtInfoDo> selectNotUsedShardList(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNotUsedShardList(query);
    }

    @Override
    public LcCustLimitAmtInfoDo selectNotUsedFirstOne(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNotUsedFirstOne(query);
    }

    @Override
    public Integer selectNotUsedCountByCurrentGroup(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectNotUsedCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitAmtInfoDo> selectNodeShardList(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNodeShardList(query);
    }

    @Override
    public LcCustLimitAmtInfoDo selectNodeFirstOne(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNodeFirstOne(query);
    }

    @Override
    public Integer selectNodeCountByCurrentGroup(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectNodeCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitAmtInfoDo> selectExpireLimitInfoByObjectId(CustLimitAmtInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectExpireLimitInfoByObjectId(query);
    }
}
