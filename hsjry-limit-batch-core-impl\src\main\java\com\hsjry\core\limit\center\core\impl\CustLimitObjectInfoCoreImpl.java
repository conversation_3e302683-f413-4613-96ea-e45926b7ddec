package com.hsjry.core.limit.center.core.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.hsjry.core.limit.center.core.ICustLimitObjectInfoCore;
import com.hsjry.core.limit.center.core.bo.CustLimitObjectInfoBo;
import com.hsjry.core.limit.center.core.convert.LcCustLimitObjectInfoDoCnvs;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitObjectInfoDao;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustLimitObjectInfoCoreImpl implements ICustLimitObjectInfoCore {
    private final LcCustLimitObjectInfoDao custLimitObjectInfoDao;

    @Override
    public List<CustLimitObjectInfoBo> enqrByExample(LcCustLimitObjectInfoQuery query) {
        return LcCustLimitObjectInfoDoCnvs.INSTANCE.cnvsDoListToBoList(custLimitObjectInfoDao.selectByExample(query));
    }

}