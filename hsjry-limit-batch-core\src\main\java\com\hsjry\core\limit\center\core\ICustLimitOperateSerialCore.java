package com.hsjry.core.limit.center.core;

import java.util.List;

import com.hsjry.core.limit.center.core.bo.CustLimitOperateSerialBo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitOperateSerialQuery;

/**
 * 额度操作流水Core
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2022/12/29 17:01
 */
public interface ICustLimitOperateSerialCore {
    /**
     * 查询额度操作流水信息
     *
     * @param query 条件
     * @return List<CustLimitOperateSerialBo>
     */
    List<CustLimitOperateSerialBo> enqrByExample(LcCustLimitOperateSerialQuery query);

    /** 查询[额度操作类型=预占额度]且[状态=成功]的额度操作流水列表 **/
    List<CustLimitOperateSerialBo> enqrPreOccpySerialList();
}
