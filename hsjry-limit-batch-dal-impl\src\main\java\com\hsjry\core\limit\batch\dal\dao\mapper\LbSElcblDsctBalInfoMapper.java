
package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblDsctBalInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblDsctBalInfoExample;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 电票系统-落地表-贴现余额信息mapper
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface LbSElcblDsctBalInfoMapper extends CommonMapper<LbSElcblDsctBalInfoDo> {

    /**
     * 批量插入贴现余额信息
     *
     * @param lbSElcblDsctBalInfoList 批量数据
     * @return int
     */
    @Override
    int insertList(@Param("list") List<LbSElcblDsctBalInfoDo> lbSElcblDsctBalInfoList);

    /**
     * 清空贴现余额信息表所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 根据条件查询数据量
     * 
     * @param example 条件
     * @return long
     */
    long countByExample(LbSElcblDsctBalInfoExample example);
}