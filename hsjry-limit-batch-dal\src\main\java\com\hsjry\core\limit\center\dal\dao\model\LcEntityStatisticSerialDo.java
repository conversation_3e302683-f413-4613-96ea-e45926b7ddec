package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 实体统计流水Do
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
@Table(name = "lc_entity_statistic_serial")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcEntityStatisticSerialDo extends LcEntityStatisticSerialKeyDo implements Serializable {
    private static final long serialVersionUID = 1602582710713516052L;
    /** 业务时间 */
    @Column(name = "biz_datetime")
    private java.util.Date bizDatetime;
    /** 渠道号 */
    @Column(name = "channel_no")
    private String channelNo;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 实体编号 */
    @Column(name = "entity_id")
    private String entityId;
    /** 全局流水号 */
    @Column(name = "global_serial_no")
    private String globalSerialNo;
    /** 前置业务时间 */
    @Column(name = "inbound_serial_datetime")
    private java.util.Date inboundSerialDatetime;
    /** 前置业务流水 */
    @Column(name = "inbound_serial_no")
    private String inboundSerialNo;
    /** 业务流水号 */
    @Column(name = "serial_no")
    private String serialNo;
    /** 统计金额 */
    @Column(name = "statistics_amount")
    private java.math.BigDecimal statisticsAmount;
    /** 统计日期 */
    @Column(name = "statistics_date")
    private Integer statisticsDate;
    /** 统计编号 */
    @Column(name = "statistics_id")
    private String statisticsId;
    /** 统计预发放金额 */
    @Column(name = "statistics_wait_amount")
    private java.math.BigDecimal statisticsWaitAmount;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
}
