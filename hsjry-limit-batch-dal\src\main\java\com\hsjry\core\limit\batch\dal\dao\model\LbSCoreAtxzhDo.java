package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 核心系统-落地表-贴现账户主文件Do
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Table(name = "lb_s_core_atxzh")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbSCoreAtxzhDo extends LbSCoreAtxzhKeyDo implements Serializable {
    private static final long serialVersionUID = 1945747816769060869L;
    /** 贴现风险标志 */
    @Column(name = "txfxbz")
    private String txfxbz;
    /** 对手行行号 */
    @Column(name = "duifhh")
    private String duifhh;
    /** 对手行类别 */
    @Column(name = "jigulb")
    private String jigulb;
    /** 转垫款金额 */
    @Column(name = "zhdkje")
    private java.math.BigDecimal zhdkje;
    /** 垫款借据编号 */
    @Column(name = "jiejuh")
    private String jiejuh;
    /** 垫款标志 */
    @Column(name = "dnknbz")
    private String dnknbz;
    /** 逆回购转入时原组号 */
    @Column(name = "xinx01")
    private String xinx01;
    /** 本次买卖利息金额 */
    @Column(name = "fxcdje")
    private java.math.BigDecimal fxcdje;
    /** 抵质押物编号 */
    @Column(name = "dzywbh")
    private String dzywbh;
    /** 对手行行名 */
    @Column(name = "duifhm")
    private String duifhm;
    /** 买方付息帐号 */
    @Column(name = "mffxzh")
    private String mffxzh;
    /** 买方付息比例 */
    @Column(name = "mffxbl")
    private java.math.BigDecimal mffxbl;
    /** 付息方式 */
    @Column(name = "txfxfs")
    private String txfxfs;
    /** 查询查复编号 */
    @Column(name = "cxcfbh")
    private String cxcfbh;
    /** 是否先贴后查 */
    @Column(name = "sfxthc")
    private String sfxthc;
    /** 客户结算帐号 */
    @Column(name = "jieszh")
    private String jieszh;
    /** 下次摊销支出日 */
    @Column(name = "xctzrq")
    private String xctzrq;
    /** 上次摊销支出日 */
    @Column(name = "sctzrq")
    private String sctzrq;
    /** 内部转贴现本次卖断利率 */
    @Column(name = "bcmdll")
    private java.math.BigDecimal bcmdll;
    /** 内部转贴现本次卖断利息 */
    @Column(name = "bcmdlx")
    private java.math.BigDecimal bcmdlx;
    /** 贴现状态 */
    @Column(name = "zhungt")
    private String zhungt;
    /** 明细序号 */
    @Column(name = "mxxhao")
    private java.math.BigDecimal mxxhao;
    /** 录入日期 */
    @Column(name = "lururq")
    private String lururq;
    /** 录入柜员 */
    @Column(name = "lurugy")
    private String lurugy;
    /** 复核日期 */
    @Column(name = "fuherq")
    private String fuherq;
    /** 复核柜员 */
    @Column(name = "fuhegy")
    private String fuhegy;
    /** 维护日期 */
    @Column(name = "weihrq")
    private String weihrq;
    /** 维护柜员 */
    @Column(name = "weihgy")
    private String weihgy;
    /** 交易日期 */
    @Column(name = "jioyrq")
    private String jioyrq;
    /** 维护机构 */
    @Column(name = "weihjg")
    private String weihjg;
    /** 维护时间 */
    @Column(name = "weihsj")
    private java.math.BigDecimal weihsj;
    /** 时间戳 */
    @Column(name = "shinch")
    private java.math.BigDecimal shinch;
    /** 记录状态 */
    @Column(name = "jiluzt")
    private String jiluzt;
    /** 宽限期 */
    @Column(name = "kuanxq")
    private java.math.BigDecimal kuanxq;
    /** 贴现帐号 */
    @Column(name = "tiexzh")
    private String tiexzh;
    /** 贴现处理种类 */
    @Column(name = "txclzl")
    private String txclzl;
    /** 贴现业务种类 */
    @Column(name = "txywzl")
    private String txywzl;
    /** 票据包号 */
    @Column(name = "piojzh")
    private String piojzh;
    /** 客户号 */
    @Column(name = "kehhao")
    private String kehhao;
    /** 客户名 */
    @Column(name = "kehzwm")
    private String kehzwm;
    /** 营业机构 */
    @Column(name = "yngyjg")
    private String yngyjg;
    /** 帐务机构 */
    @Column(name = "zhngjg")
    private String zhngjg;
    /** 入帐机构 */
    @Column(name = "ruzhjg")
    private String ruzhjg;
    /** 损益支出机构 */
    @Column(name = "syzcjg")
    private String syzcjg;
    /** 损益入帐机构 */
    @Column(name = "syrzjg")
    private String syrzjg;
    /** 货币代号 */
    @Column(name = "huobdh")
    private String huobdh;
    /** 贴现起息日 */
    @Column(name = "txqxrq")
    private String txqxrq;
    /** 贴现到期日 */
    @Column(name = "txdqrq")
    private String txdqrq;
    /** 利率编号 */
    @Column(name = "lilvbh")
    private String lilvbh;
    /** 年月利率 */
    @Column(name = "nyuell")
    private String nyuell;
    /** 贴现利率 */
    @Column(name = "tiexll")
    private java.math.BigDecimal tiexll;
    /** 贴现余额 */
    @Column(name = "txztye")
    private java.math.BigDecimal txztye;
    /** 实付金额 */
    @Column(name = "shfuje")
    private java.math.BigDecimal shfuje;
    /** 实收贴现利息 */
    @Column(name = "sxtxlx")
    private java.math.BigDecimal sxtxlx;
    /** 累计利息收入 */
    @Column(name = "ljlxsr")
    private java.math.BigDecimal ljlxsr;
    /** 利息摊销周期 */
    @Column(name = "txrzzq")
    private String txrzzq;
    /** 待摊销收入余额 */
    @Column(name = "dtsrye")
    private java.math.BigDecimal dtsrye;
    /** 上次摊销收入日 */
    @Column(name = "sctsrq")
    private String sctsrq;
    /** 下次摊销收入日 */
    @Column(name = "xctsro")
    private String xctsro;
    /** 实收金额 */
    @Column(name = "shshje")
    private java.math.BigDecimal shshje;
    /** 实付贴现利息 */
    @Column(name = "sftxlx")
    private java.math.BigDecimal sftxlx;
    /** 累计利息支出 */
    @Column(name = "ljlxzc")
    private java.math.BigDecimal ljlxzc;
    /** 待摊销支出余额 */
    @Column(name = "dtzcye")
    private java.math.BigDecimal dtzcye;
}
