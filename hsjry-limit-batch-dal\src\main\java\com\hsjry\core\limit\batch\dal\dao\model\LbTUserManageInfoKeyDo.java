package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度中心-中间表-客户经营主键
 *
 * <AUTHOR>
 * @date 2025-07-15 03:50:12
 */
@Table(name = "lb_t_user_manage_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbTUserManageInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1944967709649469441L;
    /** 资源项id */
    @Id
    @Column(name = "resource_id")
    private String resourceId;
    /** 租户 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}