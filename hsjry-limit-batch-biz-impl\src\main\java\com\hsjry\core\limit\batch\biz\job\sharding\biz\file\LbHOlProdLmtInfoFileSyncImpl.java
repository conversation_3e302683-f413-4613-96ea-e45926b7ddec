/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.io.File;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.core.limit.batch.biz.convert.file.LbHOlProdLmtInfoConverter;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.biz.entity.LbHOlProdLmtInfoData;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.AbstractFileBaseShardingPrepareBizImpl;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.constants.CharsetConstants;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.dal.dao.intf.LbHOlProdLmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbHOlProdLmtInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.sequence.SequenceTool;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
/**
 * 网贷系统历史表产品额度信息文件同步实现类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 16:30
 */
@Slf4j
@Service("lbHOlProdLmtInfoFileSyncImpl")
@RequiredArgsConstructor
public class LbHOlProdLmtInfoFileSyncImpl extends AbstractFileBaseShardingPrepareBizImpl<LbHOlProdLmtInfoData> {

    /** 额度编号列数 */
    private static final int CREDIT_LIMIT_ID_NUM = 1;
    /** 客户编号列数 */
    private static final int USER_ID_NUM = 2;
    /** 客户姓名列数 */
    private static final int USER_NAME_NUM = 3;
    /** 客户类型列数 */
    private static final int USER_TYPE_NUM = 4;
    /** 证件号码列数 */
    private static final int CERTIFICATE_NO_NUM = 5;
    /** 证件类型列数 */
    private static final int CERTIFICATE_TYPE_NUM = 6;
    /** 手机号码列数 */
    private static final int USER_MOBILE_NUM = 7;
    /** 产品编号列数 */
    private static final int PRODUCT_ID_NUM = 8;
    /** 产品名称列数 */
    private static final int PRODUCT_NAME_NUM = 9;
    /** 额度类型列数 */
    private static final int CREDIT_TYPE_NUM = 10;
    /** 状态列数 */
    private static final int STATUS_NUM = 11;
    /** 总额度列数 */
    private static final int TOTAL_AMOUNT_NUM = 12;
    /** 冻结额度列数 */
    private static final int FROZEN_AMOUNT_NUM = 13;
    /** 使用中额度列数 */
    private static final int USING_AMOUNT_NUM = 14;
    /** 已使用额度列数 */
    private static final int USED_AMOUNT_NUM = 15;
    /** 用款次数列数 */
    private static final int USE_LOAN_TIMES_NUM = 16;
    /** 用款次数限制列数 */
    private static final int LOAN_TIMES_LIMIT_NUM = 17;
    /** 生效开始时间列数 */
    private static final int EFFECTIVE_START_TIME_NUM = 18;
    /** 生效结束时间列数 */
    private static final int EFFECTIVE_END_TIME_NUM = 19;
    /** 操作员编号列数 */
    private static final int OPERATOR_ID_NUM = 20;
    /** 归属机构编号列数 */
    private static final int OWN_ORGAN_ID_NUM = 21;
    /** 创建时间列数 */
    private static final int CREATE_TIME_NUM = 22;
    /** 更新时间列数 */
    private static final int UPDATE_TIME_NUM = 23;
    /** 租户编号列数 */
    private static final int TENANT_ID_NUM = 24;

    /** 最小字段数量 */
    private static final int MIN_FIELD_COUNT = 24;
    /** 字段分隔符 */
    private static final String FIELD_SEPARATOR = "\u0003";
    /** 批量处理大小 */
    private static final int BATCH_SIZE = 1000;
    /** 序列号参数名 */
    private final String SEQUENCE_NO = "SEQUENCE_NO";

    /** 日期格式化器 */
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /** 历史表产品额度信息DAO */
    private final LbHOlProdLmtInfoDao lbHOlProdLmtInfoDao;

    /** 文件名配置 */
    @Value("${project.ol.prod.lmt.info.filename:ICM_CREDIT_PERSONAL_LIMIT_DETAIL_[DATE].txt}")
    private String fileName;

    /** 远程文件路径配置 */
    @Value("${limit.batch.remoteFilePath:/app/bftfiles/cfs/recv/ebbs/}")
    private String remoteFilePathDefine;

    @Override
    public ShardingResult<LbHOlProdLmtInfoData> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始历史表产品额度信息文件数据分片查询[{}]", GsonUtil.objToStrForLog(jobShared));

        ShardingResult<LbHOlProdLmtInfoData> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        try {
            // 读取文件分片数据
            FileLineData fileLineData = GsonUtil.json2Obj(jobShared.getExtParam(), FileLineData.class);
            log.info(prefixLog + "开始读取文件数据分片[{}]", GsonUtil.objToStrForLog(fileLineData));
            List<String> originData = FileShardingUtils.readFileSharedData(jobShared, skipFirst(), CharsetConstants.GBK);

            // 使用并行流处理数据，提升性能
            List<LbHOlProdLmtInfoData> fileDataList = processOriginDataParallel(originData, prefixLog);

            log.info(prefixLog + "读取历史表产品额度信息文件数据分片总量[{}]", fileDataList.size());
            shardingResult.setShardingResultList(fileDataList);
        } catch (Exception e) {
            log.error(prefixLog + "历史表产品额度信息文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        jobShared.setBatchSerialNo(inParam.getString(SEQUENCE_NO));
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbHOlProdLmtInfoData> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
        List<LbHOlProdLmtInfoData> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "历史表产品额度信息数据文件处理:文件分片数据为空,执行中断。");
            return;
        }

        log.info(prefixLog + "历史表产品额度信息数据文件处理:开始执行分片数据操作,数据量:[{}]", dataList.size());
        // 设置数据日期
        String dataDateStr = String.valueOf(businessDate);
        dataList.forEach(data -> data.setDataDate(dataDateStr));
        // 检查是否是第一个分片，如果是则删除当前业务日期的数据
        if (sliceBatchSerialDo.getBatchNum() == 1) {
            log.info(prefixLog + "第一个分片,删除目标表 LB_H_OL_PROD_LMT_INFO 中 DATA_DATE 为 " + businessDate + " 的数据");
            lbHOlProdLmtInfoDao.deleteByDataDate(dataDateStr);
        }

        // 确保分片流水对象的状态字段不为null
        if (sliceBatchSerialDo.getSharedStatus() == null) {
            log.debug(prefixLog + "分片流水状态为null,设置为处理中状态");
            sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
        }



        // 使用并行流进行数据转换和验证
        List<LbHOlProdLmtInfoDo> insertList = dataList.parallelStream()
                .map(LbHOlProdLmtInfoConverter::data2Do)
                .filter(this::validateData)
                .collect(Collectors.toCollection(ArrayList::new));

        if (CollectionUtil.isNotEmpty(insertList)) {
            // 批量插入数据
            processBatchInsert(insertList, prefixLog);
            log.info(prefixLog + "插入[{}]条历史表产品额度信息数据", insertList.size());
        }

        // 更新分片流水成功
        normalUpdateSliceSerial(dataList.size(), sliceBatchSerialDo);
        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataList.size());
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.H_OL_PROD_LMT_INFO;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        // 确保fixNum被正确设置
        if (jobInitDto.getFixNum() == null) {
            log.warn(prefixLog + "fixNum为null,设置默认值1000");
            jobInitDto.setFixNum(1000);
        }

        JSONObject param = JSON.parseObject(jobInitDto.getInPara());
        //设置流水ID,解决多次从redis获取唯一值的性能问题
        param.put(SEQUENCE_NO, SequenceTool.nextId());
        //更新jobInitDto的inpara参数
        jobInitDto.setInPara(param.toJSONString());

        List<JobShared> sharedList = Lists.newArrayList();
        log.info(prefixLog + "[{}]文件处理", jobTradeDesc);
        String localFilePath = FileShardingUtils.getLocalFilePath(jobInitDto.getBusinessDate(),
            remoteFilePathDefine + FileShardingUtils.ACCT_DATE_CODE_MARK + File.separator);
        Integer acctDate = jobInitDto.getBusinessDate();
        String localFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK, String.valueOf(acctDate));
        try {
            String fileAttr = FIELD_SEPARATOR;
            String filePath = localFilePath + localFileName;
            log.info("实际查找的文件路径: [{}]", filePath);

            // 检查目录是否存在
            File directory = new File(localFilePath);
            if (!directory.exists()) {
                log.info(prefixLog + "目录[{}]不存在,尝试创建", localFilePath);
                directory.mkdirs();
            } else {
                log.info(prefixLog + "目录[{}]已存在", localFilePath);
                // 列出目录中的文件
                File[] files = directory.listFiles();
                if (files != null && files.length > 0) {
                    log.info(prefixLog + "目录[{}]中的文件列表:", localFilePath);
                    for (File f : files) {
                        log.info(prefixLog + " - {}", f.getName());
                    }
                } else {
                    log.info(prefixLog + "目录[{}]为空", localFilePath);
                }
            }

            File localFile = new File(filePath);
            log.info(prefixLog + "检查文件[{}]是否存在: {}", filePath, localFile.exists());

            //判断本地文件是否存在，不存在则直接报错
            if(!localFile.exists()){
                log.error(prefixLog + "本地文件[{}]不存在,请确认文件路径是否正确", filePath);
                String errorCode = EnumBatchJobError.FILE_PATH_NOT_EXIST.getCode();
                String errorMsg = EnumBatchJobError.FILE_PATH_NOT_EXIST.getDescription();
                throw new HsjryBizException(errorCode, errorMsg);
            }

            // 文件存在，直接进行分片处理
            log.info(prefixLog + "[{}]开始[{}]文件数据分片处理", jobTradeDesc, filePath);
            log.info(prefixLog + "分片参数: fixNum=[{}], fileAttr=[{}], skipFirst=[{}]",
                jobInitDto.getFixNum(), fileAttr, skipFirst());

            List<JobShared> jobShareds = FileShardingUtils.getFileSharedData(jobInitDto, localFile, fileAttr,
                skipFirst());
            log.info(prefixLog + "[{}]结束[{}]文件数据分片当前分片数[{}]", jobTradeDesc, filePath,
                jobShareds.size());
            sharedList.addAll(jobShareds);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }
        return sharedList;
    }

    /**
     * 是否跳过第一行（表头）
     *
     * @return true-跳过第一行
     */
    private boolean skipFirst() {
        return true;
    }

    /**
     * 并行处理原始数据
     * 使用并行流提升大数据量处理性能
     *
     * @param originData 原始数据列表
     * @param prefixLog 日志前缀
     * @return 处理后的数据列表
     */
    private List<LbHOlProdLmtInfoData> processOriginDataParallel(List<String> originData, String prefixLog) {
        AtomicInteger invalidCount = new AtomicInteger(0);
        AtomicInteger parseErrorCount = new AtomicInteger(0);

        List<LbHOlProdLmtInfoData> dataList = originData.parallelStream()//
            .map(item -> parseLineData(item, prefixLog, invalidCount, parseErrorCount))//
            .filter(Objects::nonNull)//
            .collect(Collectors.toCollection(ArrayList::new));

        if (invalidCount.get() > 0) {
            log.warn(prefixLog + "发现[{}]条无效数据行", invalidCount.get());
        }
        if (parseErrorCount.get() > 0) {
            log.warn(prefixLog + "发现[{}]个字段解析错误", parseErrorCount.get());
        }

        return dataList;
    }

    /**
     * 解析单行CSV数据
     * 解析25个字段的产品额度信息数据
     *
     * @param item 单行数据
     * @param prefixLog 日志前缀
     * @param invalidCount 无效数据计数器
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的数据对象
     */
    private LbHOlProdLmtInfoData parseLineData(String item, String prefixLog, AtomicInteger invalidCount,
        AtomicInteger parseErrorCount) {
        String[] split = item.split(FIELD_SEPARATOR);
        if (split.length < MIN_FIELD_COUNT) {
            invalidCount.incrementAndGet();
            return null;
        }

        LbHOlProdLmtInfoData fileData = new LbHOlProdLmtInfoData();

        // 设置字符串字段
        fileData.setCreditLimitId(split[CREDIT_LIMIT_ID_NUM - 1]);
        fileData.setUserId(split[USER_ID_NUM - 1]);
        fileData.setUserName(split[USER_NAME_NUM - 1]);
        fileData.setUserType(split[USER_TYPE_NUM - 1]);
        fileData.setCertificateNo(split[CERTIFICATE_NO_NUM - 1]);
        fileData.setCertificateType(split[CERTIFICATE_TYPE_NUM - 1]);
        fileData.setUserMobile(split[USER_MOBILE_NUM - 1]);
        fileData.setProductId(split[PRODUCT_ID_NUM - 1]);
        fileData.setProductName(split[PRODUCT_NAME_NUM - 1]);
        fileData.setCreditType(split[CREDIT_TYPE_NUM - 1]);
        fileData.setStatus(split[STATUS_NUM - 1]);
        fileData.setOperatorId(split[OPERATOR_ID_NUM - 1]);
        fileData.setOwnOrganId(split[OWN_ORGAN_ID_NUM - 1]);
        fileData.setTenantId(split[TENANT_ID_NUM - 1]);
        // dataDate将在execJobCoreBusiness中使用业务日期设置，不从文件读取

        // 安全解析BigDecimal字段
        fileData.setTotalAmount(parseBigDecimalSafely(split[TOTAL_AMOUNT_NUM - 1], parseErrorCount));
        fileData.setFrozenAmount(parseBigDecimalSafely(split[FROZEN_AMOUNT_NUM - 1], parseErrorCount));
        fileData.setUsingAmount(parseBigDecimalSafely(split[USING_AMOUNT_NUM - 1], parseErrorCount));
        fileData.setUsedAmount(parseBigDecimalSafely(split[USED_AMOUNT_NUM - 1], parseErrorCount));

        // 安全解析Integer字段
        fileData.setUseLoanTimes(parseIntegerSafely(split[USE_LOAN_TIMES_NUM - 1], parseErrorCount));
        fileData.setLoanTimesLimit(parseIntegerSafely(split[LOAN_TIMES_LIMIT_NUM - 1], parseErrorCount));

        // 安全解析Date字段
        fileData.setEffectiveStartTime(parseDateSafely(split[EFFECTIVE_START_TIME_NUM - 1], parseErrorCount));
        fileData.setEffectiveEndTime(parseDateSafely(split[EFFECTIVE_END_TIME_NUM - 1], parseErrorCount));
        fileData.setCreateTime(parseDateSafely(split[CREATE_TIME_NUM - 1], parseErrorCount));
        fileData.setUpdateTime(parseDateSafely(split[UPDATE_TIME_NUM - 1], parseErrorCount));

        return fileData;
    }

    /**
     * 安全解析BigDecimal
     * 统一的数字字段解析逻辑，避免代码重复
     *
     * @param value 待解析的字符串值
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的BigDecimal值
     */
    private BigDecimal parseBigDecimalSafely(String value, AtomicInteger parseErrorCount) {
        try {
            return StringUtil.isBlank(value) ? BigDecimal.ZERO : new BigDecimal(value);
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return BigDecimal.ZERO;
        }
    }

    /**
     * 安全解析Integer
     * 统一的整数字段解析逻辑
     *
     * @param value 待解析的字符串值
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的Integer值
     */
    private Integer parseIntegerSafely(String value, AtomicInteger parseErrorCount) {
        try {
            return StringUtil.isBlank(value) ? 0 : Integer.valueOf(value);
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return 0;
        }
    }

    /**
     * 安全解析Date
     * 统一的日期字段解析逻辑
     *
     * @param value 待解析的字符串值
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的Date值
     */
    private Date parseDateSafely(String value, AtomicInteger parseErrorCount) {
        try {
            return StringUtil.isBlank(value) ? null : DATE_FORMAT.parse(value);
        } catch (ParseException e) {
            parseErrorCount.incrementAndGet();
            return null;
        }
    }

    /**
     * 增强的数据验证方法
     * 使用Objects工具类和优化的验证逻辑
     *
     * @param data 待验证的数据
     * @return 是否有效
     */
    private boolean validateData(LbHOlProdLmtInfoDo data) {
        if (Objects.isNull(data)) {
            return false;
        }

        if (StringUtil.isBlank(data.getCreditLimitId())) {
            log.warn("额度编号为空,数据无效");
            return false;
        }

        if (StringUtil.isBlank(data.getUserId())) {
            log.warn("客户编号为空,额度编号:[{}]", data.getCreditLimitId());
            return false;
        }

        if (StringUtil.isBlank(data.getTenantId())) {
            log.warn("租户编号为空,额度编号:[{}]", data.getCreditLimitId());
            return false;
        }

        if (StringUtil.isBlank(data.getDataDate())) {
            log.warn("数据日期为空,额度编号:[{}]", data.getCreditLimitId());
            return false;
        }

        return true;
    }

    /**
     * 批量插入数据处理
     * 支持大批量数据的分批处理，提升插入性能
     *
     * @param insertList 待插入的数据列表
     * @param prefixLog 日志前缀
     */
    private void processBatchInsert(List<LbHOlProdLmtInfoDo> insertList, String prefixLog) {
        if (CollectionUtil.isEmpty(insertList)) {
            return;
        }

        int totalSize = insertList.size();
        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, totalSize);
            List<LbHOlProdLmtInfoDo> batchList = insertList.subList(i, endIndex);

            try {
                int insertCount = lbHOlProdLmtInfoDao.insertList(batchList);
                log.info(prefixLog + "批量插入第[{}]批,数量:[{}],实际插入:[{}]",
                    (i / BATCH_SIZE + 1), batchList.size(), insertCount);
            } catch (Exception e) {
                log.error(prefixLog + "批量插入第[{}]批失败,数量:[{}]", (i / BATCH_SIZE + 1), batchList.size(), e);
                throw e;
            }
        }
    }
}
