package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.mapper.LcRecalContractOccupyPlanMapper;
import com.hsjry.core.limit.center.dal.dao.model.LcRecalContractOccupyPlanDo;
import com.hsjry.core.limit.center.dal.dao.model.LcRecalContractOccupyPlanExample;
import com.hsjry.core.limit.center.dal.dao.model.LcRecalContractOccupyPlanKeyDo;
import com.hsjry.core.limit.center.dal.dao.query.LcRecalContractOccupyPlanQuery;
import com.hsjry.core.limit.center.dal.dao.intf.LcRecalContractOccupyPlanDao;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 合同占用重算计划(准备)数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2023-11-08 02:51:47
 */
@Repository
public class LcRecalContractOccupyPlanDaoImpl
    extends AbstractBaseDaoImpl<LcRecalContractOccupyPlanDo, LcRecalContractOccupyPlanMapper>
    implements LcRecalContractOccupyPlanDao {
    /**
     * 分页查询
     *
     * @param lcRecalContractOccupyPlan 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LcRecalContractOccupyPlanDo> selectPage(LcRecalContractOccupyPlanQuery lcRecalContractOccupyPlan,
        PageParam pageParam) {
        LcRecalContractOccupyPlanExample example = buildExample(lcRecalContractOccupyPlan);
        return PageHelper.<LcRecalContractOccupyPlanDo>startPage(pageParam.getPageNum(),
            pageParam.getPageSize()).doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询合同占用重算计划(准备)
     *
     * @param relationId
     * @param custLimitId
     * @return
     */
    @Override
    public LcRecalContractOccupyPlanDo selectByKey(String relationId, String custLimitId) {
        LcRecalContractOccupyPlanKeyDo lcRecalContractOccupyPlanKeyDo = new LcRecalContractOccupyPlanKeyDo();
        lcRecalContractOccupyPlanKeyDo.setRelationId(relationId);
        lcRecalContractOccupyPlanKeyDo.setCustLimitId(custLimitId);
        lcRecalContractOccupyPlanKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lcRecalContractOccupyPlanKeyDo);
    }

    /**
     * 根据key删除合同占用重算计划(准备)
     *
     * @param relationId
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String relationId, String custLimitId) {
        LcRecalContractOccupyPlanKeyDo lcRecalContractOccupyPlanKeyDo = new LcRecalContractOccupyPlanKeyDo();
        lcRecalContractOccupyPlanKeyDo.setRelationId(relationId);
        lcRecalContractOccupyPlanKeyDo.setCustLimitId(custLimitId);
        lcRecalContractOccupyPlanKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lcRecalContractOccupyPlanKeyDo);
    }

    /**
     * 查询合同占用重算计划(准备)信息
     *
     * @param lcRecalContractOccupyPlan 条件
     * @return List<LcRecalContractOccupyPlanDo>
     */
    @Override
    public List<LcRecalContractOccupyPlanDo> selectByExample(LcRecalContractOccupyPlanQuery lcRecalContractOccupyPlan) {
        return getMapper().selectByExample(buildExample(lcRecalContractOccupyPlan));
    }

    /**
     * 新增合同占用重算计划(准备)信息
     *
     * @param lcRecalContractOccupyPlan 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LcRecalContractOccupyPlanDo lcRecalContractOccupyPlan) {
        if (lcRecalContractOccupyPlan == null) {
            return -1;
        }

        lcRecalContractOccupyPlan.setCreateTime(BusinessDateUtil.getDate());
        lcRecalContractOccupyPlan.setUpdateTime(BusinessDateUtil.getDate());
        lcRecalContractOccupyPlan.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lcRecalContractOccupyPlan);
    }

    /**
     * 修改合同占用重算计划(准备)信息
     *
     * @param lcRecalContractOccupyPlan
     * @return
     */
    @Override
    public int updateBySelective(LcRecalContractOccupyPlanDo lcRecalContractOccupyPlan) {
        if (lcRecalContractOccupyPlan == null) {
            return -1;
        }
        lcRecalContractOccupyPlan.setUpdateTime(BusinessDateUtil.getDate());
        lcRecalContractOccupyPlan.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lcRecalContractOccupyPlan);
    }

    @Override
    public int updateBySelectiveByExample(LcRecalContractOccupyPlanDo lcRecalContractOccupyPlan,
        LcRecalContractOccupyPlanQuery lcRecalContractOccupyPlanQuery) {
        lcRecalContractOccupyPlan.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lcRecalContractOccupyPlan,
            buildExample(lcRecalContractOccupyPlanQuery));
    }

    /**
     * 构建合同占用重算计划(准备)Example信息
     *
     * @param lcRecalContractOccupyPlan
     * @return
     */
    public LcRecalContractOccupyPlanExample buildExample(LcRecalContractOccupyPlanQuery lcRecalContractOccupyPlan) {
        LcRecalContractOccupyPlanExample example = new LcRecalContractOccupyPlanExample();
        LcRecalContractOccupyPlanExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lcRecalContractOccupyPlan != null) {
            //添加查询条件
            if (null != lcRecalContractOccupyPlan.getNewExchangeRateVersion()) {
                criteria.andNewExchangeRateVersionEqualTo(lcRecalContractOccupyPlan.getNewExchangeRateVersion());
            }
            if (StringUtil.isNotEmpty(lcRecalContractOccupyPlan.getCalObject())) {
                criteria.andCalObjectEqualTo(lcRecalContractOccupyPlan.getCalObject());
            }
            if (null != lcRecalContractOccupyPlan.getCaPreAmountShare()) {
                criteria.andCaPreAmountShareEqualTo(lcRecalContractOccupyPlan.getCaPreAmountShare());
            }
            if (null != lcRecalContractOccupyPlan.getPreAmountShare()) {
                criteria.andPreAmountShareEqualTo(lcRecalContractOccupyPlan.getPreAmountShare());
            }
            if (null != lcRecalContractOccupyPlan.getCaPreLowRiskAmtBalance()) {
                criteria.andCaPreLowRiskAmtBalanceEqualTo(lcRecalContractOccupyPlan.getCaPreLowRiskAmtBalance());
            }
            if (null != lcRecalContractOccupyPlan.getCaPreAmountBalance()) {
                criteria.andCaPreAmountBalanceEqualTo(lcRecalContractOccupyPlan.getCaPreAmountBalance());
            }
            if (null != lcRecalContractOccupyPlan.getPreLowRiskAmtBalance()) {
                criteria.andPreLowRiskAmtBalanceEqualTo(lcRecalContractOccupyPlan.getPreLowRiskAmtBalance());
            }
            if (null != lcRecalContractOccupyPlan.getPreAmountBalance()) {
                criteria.andPreAmountBalanceEqualTo(lcRecalContractOccupyPlan.getPreAmountBalance());
            }
            if (null != lcRecalContractOccupyPlan.getCaAmountShare()) {
                criteria.andCaAmountShareEqualTo(lcRecalContractOccupyPlan.getCaAmountShare());
            }
            if (null != lcRecalContractOccupyPlan.getAmountShare()) {
                criteria.andAmountShareEqualTo(lcRecalContractOccupyPlan.getAmountShare());
            }
            if (null != lcRecalContractOccupyPlan.getExchangeRateVersion()) {
                criteria.andExchangeRateVersionEqualTo(lcRecalContractOccupyPlan.getExchangeRateVersion());
            }
            if (null != lcRecalContractOccupyPlan.getCaLowRiskAmtBalance()) {
                criteria.andCaLowRiskAmtBalanceEqualTo(lcRecalContractOccupyPlan.getCaLowRiskAmtBalance());
            }
            if (null != lcRecalContractOccupyPlan.getCaAmountBalance()) {
                criteria.andCaAmountBalanceEqualTo(lcRecalContractOccupyPlan.getCaAmountBalance());
            }
            if (null != lcRecalContractOccupyPlan.getLowRiskAmtBalance()) {
                criteria.andLowRiskAmtBalanceEqualTo(lcRecalContractOccupyPlan.getLowRiskAmtBalance());
            }
            if (null != lcRecalContractOccupyPlan.getAmountBalance()) {
                criteria.andAmountBalanceEqualTo(lcRecalContractOccupyPlan.getAmountBalance());
            }
            if (StringUtil.isNotEmpty(lcRecalContractOccupyPlan.getLimitObjectId())) {
                criteria.andLimitObjectIdEqualTo(lcRecalContractOccupyPlan.getLimitObjectId());
            }
            if (StringUtil.isNotEmpty(lcRecalContractOccupyPlan.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lcRecalContractOccupyPlan.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lcRecalContractOccupyPlan.getRelationId())) {
                criteria.andRelationIdEqualTo(lcRecalContractOccupyPlan.getRelationId());
            }
        }
        buildExampleExt(lcRecalContractOccupyPlan, criteria);
        return example;
    }

    /**
     * 构建合同占用重算计划(准备)ExampleExt方法
     *
     * @param lcRecalContractOccupyPlan
     * @return
     */
    public void buildExampleExt(LcRecalContractOccupyPlanQuery lcRecalContractOccupyPlan,
        LcRecalContractOccupyPlanExample.Criteria criteria) {
        if (CollectionUtil.isNotEmpty(lcRecalContractOccupyPlan.getCustLimitIdList())) {
            criteria.andCustLimitIdIn(lcRecalContractOccupyPlan.getCustLimitIdList());
        }
        //自定义实现
    }

    @Override
    public String selectFirstOne(LcRecalContractOccupyPlanQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectFirstOne(query);
    }

    @Override
    public Integer selectCountByCurrentGroup(LcRecalContractOccupyPlanQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectCountByCurrentGroup(query);
    }

    @Override
    public List<LcRecalContractOccupyPlanDo> selectShardList(LcRecalContractOccupyPlanQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectShardList(query);
    }

    @Override
    public int deleteAll() {
        return getMapper().deleteAll(AppParamUtil.getTenantId());
    }
}
