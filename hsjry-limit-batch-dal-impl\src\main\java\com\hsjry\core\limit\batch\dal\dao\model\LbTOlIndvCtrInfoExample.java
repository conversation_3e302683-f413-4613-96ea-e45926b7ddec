package com.hsjry.core.limit.batch.dal.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 网贷系统-中间表-个人合同信息Example
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
public class LbTOlIndvCtrInfoExample {
    /** 排序字段 */
    protected String orderByClause;

    /** 是否只查询记录不同的数据 */
    protected boolean distinct;

    /** or条件查询集合 */
    protected List<Criteria> oredCriteria;

    /**
     * 初始化or条件查询集合
     */
    public LbTOlIndvCtrInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * 排序字段set方法
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * 排序字段get方法
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * 不同记录set方法.
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * 不同记录get方法.
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * 排序集合get方法
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * or
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * or
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * 清除数据
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andLowRiskAmountIsNull() {
            addCriterion("low_risk_amount is null");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountIsNotNull() {
            addCriterion("low_risk_amount is not null");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk_amount =", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountNotEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk_amount <>", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountGreaterThan(java.math.BigDecimal value) {
            addCriterion("low_risk_amount >", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk_amount >=", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountLessThan(java.math.BigDecimal value) {
            addCriterion("low_risk_amount <", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk_amount <=", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountLike(java.math.BigDecimal value) {
            addCriterion("low_risk_amount like", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountNotLike(java.math.BigDecimal value) {
            addCriterion("low_risk_amount not like", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountIn(List<java.math.BigDecimal> values) {
            addCriterion("low_risk_amount in", values, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountNotIn(List<java.math.BigDecimal> values) {
            addCriterion("low_risk_amount not in", values, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("low_risk_amount between", value1, value2, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("low_risk_amount not between", value1, value2, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLike(Date value) {
            addCriterion("update_time like", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotLike(Date value) {
            addCriterion("update_time not like", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLike(Date value) {
            addCriterion("create_time like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotLike(Date value) {
            addCriterion("create_time not like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdIsNull() {
            addCriterion("own_organ_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdIsNotNull() {
            addCriterion("own_organ_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdEqualTo(String value) {
            addCriterion("own_organ_id =", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdNotEqualTo(String value) {
            addCriterion("own_organ_id <>", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdGreaterThan(String value) {
            addCriterion("own_organ_id >", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdGreaterThanOrEqualTo(String value) {
            addCriterion("own_organ_id >=", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdLessThan(String value) {
            addCriterion("own_organ_id <", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdLessThanOrEqualTo(String value) {
            addCriterion("own_organ_id <=", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdLike(String value) {
            addCriterion("own_organ_id like", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdNotLike(String value) {
            addCriterion("own_organ_id not like", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdIn(List<String> values) {
            addCriterion("own_organ_id in", values, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdNotIn(List<String> values) {
            addCriterion("own_organ_id not in", values, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdBetween(String value1, String value2) {
            addCriterion("own_organ_id between", value1, value2, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdNotBetween(String value1, String value2) {
            addCriterion("own_organ_id not between", value1, value2, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIsNull() {
            addCriterion("operator_id is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIsNotNull() {
            addCriterion("operator_id is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorIdEqualTo(String value) {
            addCriterion("operator_id =", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotEqualTo(String value) {
            addCriterion("operator_id <>", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdGreaterThan(String value) {
            addCriterion("operator_id >", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdGreaterThanOrEqualTo(String value) {
            addCriterion("operator_id >=", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLessThan(String value) {
            addCriterion("operator_id <", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLessThanOrEqualTo(String value) {
            addCriterion("operator_id <=", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLike(String value) {
            addCriterion("operator_id like", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotLike(String value) {
            addCriterion("operator_id not like", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIn(List<String> values) {
            addCriterion("operator_id in", values, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotIn(List<String> values) {
            addCriterion("operator_id not in", values, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdBetween(String value1, String value2) {
            addCriterion("operator_id between", value1, value2, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotBetween(String value1, String value2) {
            addCriterion("operator_id not between", value1, value2, "operatorId");
            return (Criteria) this;
        }

        public Criteria andEntityIdIsNull() {
            addCriterion("entity_id is null");
            return (Criteria) this;
        }

        public Criteria andEntityIdIsNotNull() {
            addCriterion("entity_id is not null");
            return (Criteria) this;
        }

        public Criteria andEntityIdEqualTo(String value) {
            addCriterion("entity_id =", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdNotEqualTo(String value) {
            addCriterion("entity_id <>", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdGreaterThan(String value) {
            addCriterion("entity_id >", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdGreaterThanOrEqualTo(String value) {
            addCriterion("entity_id >=", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdLessThan(String value) {
            addCriterion("entity_id <", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdLessThanOrEqualTo(String value) {
            addCriterion("entity_id <=", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdLike(String value) {
            addCriterion("entity_id like", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdNotLike(String value) {
            addCriterion("entity_id not like", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdIn(List<String> values) {
            addCriterion("entity_id in", values, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdNotIn(List<String> values) {
            addCriterion("entity_id not in", values, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdBetween(String value1, String value2) {
            addCriterion("entity_id between", value1, value2, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdNotBetween(String value1, String value2) {
            addCriterion("entity_id not between", value1, value2, "entityId");
            return (Criteria) this;
        }

        public Criteria andVirtualContractFlagIsNull() {
            addCriterion("virtual_contract_flag is null");
            return (Criteria) this;
        }

        public Criteria andVirtualContractFlagIsNotNull() {
            addCriterion("virtual_contract_flag is not null");
            return (Criteria) this;
        }

        public Criteria andVirtualContractFlagEqualTo(String value) {
            addCriterion("virtual_contract_flag =", value, "virtualContractFlag");
            return (Criteria) this;
        }

        public Criteria andVirtualContractFlagNotEqualTo(String value) {
            addCriterion("virtual_contract_flag <>", value, "virtualContractFlag");
            return (Criteria) this;
        }

        public Criteria andVirtualContractFlagGreaterThan(String value) {
            addCriterion("virtual_contract_flag >", value, "virtualContractFlag");
            return (Criteria) this;
        }

        public Criteria andVirtualContractFlagGreaterThanOrEqualTo(String value) {
            addCriterion("virtual_contract_flag >=", value, "virtualContractFlag");
            return (Criteria) this;
        }

        public Criteria andVirtualContractFlagLessThan(String value) {
            addCriterion("virtual_contract_flag <", value, "virtualContractFlag");
            return (Criteria) this;
        }

        public Criteria andVirtualContractFlagLessThanOrEqualTo(String value) {
            addCriterion("virtual_contract_flag <=", value, "virtualContractFlag");
            return (Criteria) this;
        }

        public Criteria andVirtualContractFlagLike(String value) {
            addCriterion("virtual_contract_flag like", value, "virtualContractFlag");
            return (Criteria) this;
        }

        public Criteria andVirtualContractFlagNotLike(String value) {
            addCriterion("virtual_contract_flag not like", value, "virtualContractFlag");
            return (Criteria) this;
        }

        public Criteria andVirtualContractFlagIn(List<String> values) {
            addCriterion("virtual_contract_flag in", values, "virtualContractFlag");
            return (Criteria) this;
        }

        public Criteria andVirtualContractFlagNotIn(List<String> values) {
            addCriterion("virtual_contract_flag not in", values, "virtualContractFlag");
            return (Criteria) this;
        }

        public Criteria andVirtualContractFlagBetween(String value1, String value2) {
            addCriterion("virtual_contract_flag between", value1, value2, "virtualContractFlag");
            return (Criteria) this;
        }

        public Criteria andVirtualContractFlagNotBetween(String value1, String value2) {
            addCriterion("virtual_contract_flag not between", value1, value2, "virtualContractFlag");
            return (Criteria) this;
        }

        public Criteria andRelationIdIsNull() {
            addCriterion("relation_id is null");
            return (Criteria) this;
        }

        public Criteria andRelationIdIsNotNull() {
            addCriterion("relation_id is not null");
            return (Criteria) this;
        }

        public Criteria andRelationIdEqualTo(String value) {
            addCriterion("relation_id =", value, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdNotEqualTo(String value) {
            addCriterion("relation_id <>", value, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdGreaterThan(String value) {
            addCriterion("relation_id >", value, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdGreaterThanOrEqualTo(String value) {
            addCriterion("relation_id >=", value, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdLessThan(String value) {
            addCriterion("relation_id <", value, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdLessThanOrEqualTo(String value) {
            addCriterion("relation_id <=", value, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdLike(String value) {
            addCriterion("relation_id like", value, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdNotLike(String value) {
            addCriterion("relation_id not like", value, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdIn(List<String> values) {
            addCriterion("relation_id in", values, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdNotIn(List<String> values) {
            addCriterion("relation_id not in", values, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdBetween(String value1, String value2) {
            addCriterion("relation_id between", value1, value2, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdNotBetween(String value1, String value2) {
            addCriterion("relation_id not between", value1, value2, "relationId");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtIsNull() {
            addCriterion("real_occupy_low_risk_amt is null");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtIsNotNull() {
            addCriterion("real_occupy_low_risk_amt is not null");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt =", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtNotEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt <>", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtGreaterThan(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt >", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt >=", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtLessThan(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt <", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt <=", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtLike(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt like", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtNotLike(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt not like", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtIn(List<java.math.BigDecimal> values) {
            addCriterion("real_occupy_low_risk_amt in", values, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtNotIn(List<java.math.BigDecimal> values) {
            addCriterion("real_occupy_low_risk_amt not in", values, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("real_occupy_low_risk_amt between", value1, value2, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("real_occupy_low_risk_amt not between", value1, value2, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andCustNoIsNull() {
            addCriterion("cust_no is null");
            return (Criteria) this;
        }

        public Criteria andCustNoIsNotNull() {
            addCriterion("cust_no is not null");
            return (Criteria) this;
        }

        public Criteria andCustNoEqualTo(String value) {
            addCriterion("cust_no =", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoNotEqualTo(String value) {
            addCriterion("cust_no <>", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoGreaterThan(String value) {
            addCriterion("cust_no >", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoGreaterThanOrEqualTo(String value) {
            addCriterion("cust_no >=", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoLessThan(String value) {
            addCriterion("cust_no <", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoLessThanOrEqualTo(String value) {
            addCriterion("cust_no <=", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoLike(String value) {
            addCriterion("cust_no like", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoNotLike(String value) {
            addCriterion("cust_no not like", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoIn(List<String> values) {
            addCriterion("cust_no in", values, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoNotIn(List<String> values) {
            addCriterion("cust_no not in", values, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoBetween(String value1, String value2) {
            addCriterion("cust_no between", value1, value2, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoNotBetween(String value1, String value2) {
            addCriterion("cust_no not between", value1, value2, "custNo");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountIsNull() {
            addCriterion("real_occupy_amount is null");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountIsNotNull() {
            addCriterion("real_occupy_amount is not null");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount =", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountNotEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount <>", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountGreaterThan(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount >", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount >=", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountLessThan(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount <", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount <=", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountLike(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount like", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountNotLike(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount not like", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountIn(List<java.math.BigDecimal> values) {
            addCriterion("real_occupy_amount in", values, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountNotIn(List<java.math.BigDecimal> values) {
            addCriterion("real_occupy_amount not in", values, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("real_occupy_amount between", value1, value2, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("real_occupy_amount not between", value1, value2, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIsNull() {
            addCriterion("total_amount is null");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIsNotNull() {
            addCriterion("total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTotalAmountEqualTo(java.math.BigDecimal value) {
            addCriterion("total_amount =", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotEqualTo(java.math.BigDecimal value) {
            addCriterion("total_amount <>", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountGreaterThan(java.math.BigDecimal value) {
            addCriterion("total_amount >", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("total_amount >=", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLessThan(java.math.BigDecimal value) {
            addCriterion("total_amount <", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("total_amount <=", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLike(java.math.BigDecimal value) {
            addCriterion("total_amount like", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotLike(java.math.BigDecimal value) {
            addCriterion("total_amount not like", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIn(List<java.math.BigDecimal> values) {
            addCriterion("total_amount in", values, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotIn(List<java.math.BigDecimal> values) {
            addCriterion("total_amount not in", values, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("total_amount between", value1, value2, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("total_amount not between", value1, value2, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andLimitStatusIsNull() {
            addCriterion("limit_status is null");
            return (Criteria) this;
        }

        public Criteria andLimitStatusIsNotNull() {
            addCriterion("limit_status is not null");
            return (Criteria) this;
        }

        public Criteria andLimitStatusEqualTo(String value) {
            addCriterion("limit_status =", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusNotEqualTo(String value) {
            addCriterion("limit_status <>", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusGreaterThan(String value) {
            addCriterion("limit_status >", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusGreaterThanOrEqualTo(String value) {
            addCriterion("limit_status >=", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusLessThan(String value) {
            addCriterion("limit_status <", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusLessThanOrEqualTo(String value) {
            addCriterion("limit_status <=", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusLike(String value) {
            addCriterion("limit_status like", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusNotLike(String value) {
            addCriterion("limit_status not like", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusIn(List<String> values) {
            addCriterion("limit_status in", values, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusNotIn(List<String> values) {
            addCriterion("limit_status not in", values, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusBetween(String value1, String value2) {
            addCriterion("limit_status between", value1, value2, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusNotBetween(String value1, String value2) {
            addCriterion("limit_status not between", value1, value2, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdIsNull() {
            addCriterion("cust_limit_id is null");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdIsNotNull() {
            addCriterion("cust_limit_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdEqualTo(String value) {
            addCriterion("cust_limit_id =", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotEqualTo(String value) {
            addCriterion("cust_limit_id <>", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdGreaterThan(String value) {
            addCriterion("cust_limit_id >", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdGreaterThanOrEqualTo(String value) {
            addCriterion("cust_limit_id >=", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdLessThan(String value) {
            addCriterion("cust_limit_id <", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdLessThanOrEqualTo(String value) {
            addCriterion("cust_limit_id <=", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdLike(String value) {
            addCriterion("cust_limit_id like", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotLike(String value) {
            addCriterion("cust_limit_id not like", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdIn(List<String> values) {
            addCriterion("cust_limit_id in", values, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotIn(List<String> values) {
            addCriterion("cust_limit_id not in", values, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdBetween(String value1, String value2) {
            addCriterion("cust_limit_id between", value1, value2, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotBetween(String value1, String value2) {
            addCriterion("cust_limit_id not between", value1, value2, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCertNoIsNull() {
            addCriterion("cert_no is null");
            return (Criteria) this;
        }

        public Criteria andCertNoIsNotNull() {
            addCriterion("cert_no is not null");
            return (Criteria) this;
        }

        public Criteria andCertNoEqualTo(String value) {
            addCriterion("cert_no =", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoNotEqualTo(String value) {
            addCriterion("cert_no <>", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoGreaterThan(String value) {
            addCriterion("cert_no >", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoGreaterThanOrEqualTo(String value) {
            addCriterion("cert_no >=", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoLessThan(String value) {
            addCriterion("cert_no <", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoLessThanOrEqualTo(String value) {
            addCriterion("cert_no <=", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoLike(String value) {
            addCriterion("cert_no like", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoNotLike(String value) {
            addCriterion("cert_no not like", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoIn(List<String> values) {
            addCriterion("cert_no in", values, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoNotIn(List<String> values) {
            addCriterion("cert_no not in", values, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoBetween(String value1, String value2) {
            addCriterion("cert_no between", value1, value2, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoNotBetween(String value1, String value2) {
            addCriterion("cert_no not between", value1, value2, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertTypIsNull() {
            addCriterion("cert_typ is null");
            return (Criteria) this;
        }

        public Criteria andCertTypIsNotNull() {
            addCriterion("cert_typ is not null");
            return (Criteria) this;
        }

        public Criteria andCertTypEqualTo(String value) {
            addCriterion("cert_typ =", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypNotEqualTo(String value) {
            addCriterion("cert_typ <>", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypGreaterThan(String value) {
            addCriterion("cert_typ >", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypGreaterThanOrEqualTo(String value) {
            addCriterion("cert_typ >=", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypLessThan(String value) {
            addCriterion("cert_typ <", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypLessThanOrEqualTo(String value) {
            addCriterion("cert_typ <=", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypLike(String value) {
            addCriterion("cert_typ like", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypNotLike(String value) {
            addCriterion("cert_typ not like", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypIn(List<String> values) {
            addCriterion("cert_typ in", values, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypNotIn(List<String> values) {
            addCriterion("cert_typ not in", values, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypBetween(String value1, String value2) {
            addCriterion("cert_typ between", value1, value2, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypNotBetween(String value1, String value2) {
            addCriterion("cert_typ not between", value1, value2, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCustNmIsNull() {
            addCriterion("cust_nm is null");
            return (Criteria) this;
        }

        public Criteria andCustNmIsNotNull() {
            addCriterion("cust_nm is not null");
            return (Criteria) this;
        }

        public Criteria andCustNmEqualTo(String value) {
            addCriterion("cust_nm =", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmNotEqualTo(String value) {
            addCriterion("cust_nm <>", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmGreaterThan(String value) {
            addCriterion("cust_nm >", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmGreaterThanOrEqualTo(String value) {
            addCriterion("cust_nm >=", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmLessThan(String value) {
            addCriterion("cust_nm <", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmLessThanOrEqualTo(String value) {
            addCriterion("cust_nm <=", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmLike(String value) {
            addCriterion("cust_nm like", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmNotLike(String value) {
            addCriterion("cust_nm not like", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmIn(List<String> values) {
            addCriterion("cust_nm in", values, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmNotIn(List<String> values) {
            addCriterion("cust_nm not in", values, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmBetween(String value1, String value2) {
            addCriterion("cust_nm between", value1, value2, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmNotBetween(String value1, String value2) {
            addCriterion("cust_nm not between", value1, value2, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustTypIsNull() {
            addCriterion("cust_typ is null");
            return (Criteria) this;
        }

        public Criteria andCustTypIsNotNull() {
            addCriterion("cust_typ is not null");
            return (Criteria) this;
        }

        public Criteria andCustTypEqualTo(String value) {
            addCriterion("cust_typ =", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypNotEqualTo(String value) {
            addCriterion("cust_typ <>", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypGreaterThan(String value) {
            addCriterion("cust_typ >", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypGreaterThanOrEqualTo(String value) {
            addCriterion("cust_typ >=", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypLessThan(String value) {
            addCriterion("cust_typ <", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypLessThanOrEqualTo(String value) {
            addCriterion("cust_typ <=", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypLike(String value) {
            addCriterion("cust_typ like", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypNotLike(String value) {
            addCriterion("cust_typ not like", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypIn(List<String> values) {
            addCriterion("cust_typ in", values, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypNotIn(List<String> values) {
            addCriterion("cust_typ not in", values, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypBetween(String value1, String value2) {
            addCriterion("cust_typ between", value1, value2, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypNotBetween(String value1, String value2) {
            addCriterion("cust_typ not between", value1, value2, "custTyp");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}