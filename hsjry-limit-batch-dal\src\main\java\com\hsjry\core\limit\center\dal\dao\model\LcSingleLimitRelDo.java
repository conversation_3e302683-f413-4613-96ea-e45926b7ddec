package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 单一限额关联Do
 *
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
@Table(name = "lc_single_limit_rel")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcSingleLimitRelDo extends LcSingleLimitRelKeyDo implements Serializable {
    private static final long serialVersionUID = 1673314101088157700L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 额度编号 */
    @Column(name = "cust_limit_id")
    private String custLimitId;
    /** 实体编号 */
    @Column(name = "entity_id")
    private String entityId;
    /** 主体编号 */
    @Column(name = "limit_object_id")
    private String limitObjectId;
    /** 关联类型 */
    @Column(name = "relation_type")
    private String relationType;
    /** 单一限额规则编号 */
    @Column(name = "single_limit_rule_id")
    private String singleLimitRuleId;
    /** 单一限额模板编号 */
    @Column(name = "single_limit_template_id")
    private String singleLimitTemplateId;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 限额使用阶段;EnumSingleLimitUseStage（001-授信阶段、002-用信阶段） */
    @Column(name = "use_stage")
    private String useStage;
}
