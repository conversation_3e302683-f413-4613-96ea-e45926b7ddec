<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.center.dal.dao.mapper.InboundSerialBatchMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.center.dal.dao.model.LcInboundSerialDo">
        <result property="bizDatetime" column="biz_datetime" jdbcType="TIMESTAMP"/> <!-- 业务时间 -->
        <result property="channelNo" column="channel_no" jdbcType="CHAR"/> <!-- 渠道号 -->
        <result property="cisSerialNo" column="cis_serial_no" jdbcType="VARCHAR"/> <!-- 请求流水编号 -->
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
        <result property="errCode" column="err_code" jdbcType="VARCHAR"/> <!-- 错误码 -->
        <result property="errMsg" column="err_msg" jdbcType="VARCHAR"/> <!-- 错误信息 -->
        <result property="globalSerialNo" column="global_serial_no" jdbcType="VARCHAR"/> <!-- 全局流水号 -->
        <result property="handlerStatus" column="handler_status"
                jdbcType="CHAR"/> <!-- 处理状态;EnumLimitHandlerStatus(010-待处理, 020-处理中, 030-成功, 040-失败, 050-无此交易, 060-已冲正) -->
        <result property="inboundSerialDatetime" column="inbound_serial_datetime" jdbcType="TIMESTAMP"/> <!-- 前置业务时间 -->
        <result property="inboundSerialNo" column="inbound_serial_no" jdbcType="VARCHAR"/> <!-- 前置业务流水 -->
        <result property="requestMethod" column="request_method" jdbcType="VARCHAR"/> <!-- 请求方法（交易） -->
        <result property="serialNo" column="serial_no" jdbcType="VARCHAR"/> <!-- 业务流水号 -->
        <result property="tenantId" column="tenant_id" jdbcType="CHAR"/> <!-- 租户号 -->
        <result property="tradeCode" column="trade_code" jdbcType="VARCHAR"/> <!-- 请求交易编码;EnumInterfaceTrade -->
        <result property="tradeStatus" column="trade_status"
                jdbcType="CHAR"/> <!-- 交易状态;EnumTradeStatus(010-初始化, 020-处理中, 030-已处理, 040-处理失败, 050-交易取消, 060-交易取消中) -->
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
    </resultMap>
    <sql id="Base_Column_List">
        biz_datetime
        , channel_no
                , cis_serial_no
                , create_time
                , err_code
                , err_msg
                , global_serial_no
                , handler_status
                , inbound_serial_datetime
                , inbound_serial_no
                , request_method
                , serial_no
                , tenant_id
                , trade_code
                , trade_status
                , update_time
    </sql>

    <select id="selectCountByCurrentGroup"
            parameterType="com.hsjry.core.limit.center.dal.dao.query.InboundSerialBatchQuery"
            resultType="java.lang.Integer">
        SELECT count(*)
        FROM lc_inbound_serial WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectFirstOne" parameterType="com.hsjry.core.limit.center.dal.dao.query.InboundSerialBatchQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_inbound_serial
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectShardList" parameterType="com.hsjry.core.limit.center.dal.dao.query.InboundSerialBatchQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_inbound_serial
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <sql id="fixQuerySql">
        <if test="query.tradeCode !=null and query.tradeCode!=''">
            AND trade_code = #{query.tradeCode}
        </if>
        <if test="query.bizDatetimeBegin !=null ">
            AND biz_datetime <![CDATA[ >= ]]> #{query.bizDatetimeBegin}
        </if>
        <if test="query.bizDatetimeEnd !=null ">
            AND biz_datetime <![CDATA[ <= ]]>#{query.bizDatetimeEnd}
        </if>
        <if test="query.cisSerialNo != null and query.cisSerialNo!=''">
            AND cis_serial_no > #{query.cisSerialNo}
        </if>
        ORDER BY cis_serial_no
    </sql>
</mapper>