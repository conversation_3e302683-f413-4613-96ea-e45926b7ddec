package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcEntityStatisticSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcEntityStatisticSerialQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 实体统计流水数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcEntityStatisticSerialDao extends IBaseDao<LcEntityStatisticSerialDo> {
    /**
     * 分页查询实体统计流水
     *
     * @param lcEntityStatisticSerialQuery 条件
     * @return PageInfo<LcEntityStatisticSerialDo>
     */
    PageInfo<LcEntityStatisticSerialDo> selectPage(LcEntityStatisticSerialQuery lcEntityStatisticSerialQuery,
        PageParam pageParam);

    /**
     * 根据key查询实体统计流水
     *
     * @param entityStatisticId
     * @return
     */
    LcEntityStatisticSerialDo selectByKey(String entityStatisticId);

    /**
     * 根据key删除实体统计流水
     *
     * @param entityStatisticId
     * @return
     */
    int deleteByKey(String entityStatisticId);

    /**
     * 查询实体统计流水信息
     *
     * @param lcEntityStatisticSerialQuery 条件
     * @return List<LcEntityStatisticSerialDo>
     */
    List<LcEntityStatisticSerialDo> selectByExample(LcEntityStatisticSerialQuery lcEntityStatisticSerialQuery);

    /**
     * 新增实体统计流水信息
     *
     * @param lcEntityStatisticSerial 条件
     * @return int>
     */
    int insertBySelective(LcEntityStatisticSerialDo lcEntityStatisticSerial);

    /**
     * 修改实体统计流水信息
     *
     * @param lcEntityStatisticSerial
     * @return
     */
    int updateBySelective(LcEntityStatisticSerialDo lcEntityStatisticSerial);

    /**
     * 修改实体统计流水信息
     *
     * @param lcEntityStatisticSerial
     * @param lcEntityStatisticSerialQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcEntityStatisticSerialDo lcEntityStatisticSerial,
        LcEntityStatisticSerialQuery lcEntityStatisticSerialQuery);

    /**
     * 删除实体统计流水信息
     *
     * @param lcEntityStatisticSerialQuery 条件
     * @return List<LcEntityStatisticSerialDo>
     */
    int deleteByExample(LcEntityStatisticSerialQuery lcEntityStatisticSerialQuery);
}
