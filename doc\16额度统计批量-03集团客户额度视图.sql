--1.查询[额度中心-中间表-集团客户信息]
select ltgci.cust_no, ltgci.*
from lb_t_grp_cust_info ltgci
where 1 = 1;
select *
from lb_t_grp_cust_info;
select *
from lc_grp_lmt_view;
truncate table lc_grp_lmt_view;

--1.处理客户信息,使用MERGE语句实现存在则更新,不存在则插入
merge into lc_grp_lmt_view tgt
using (select ltgci.tenant_id,
              ltgci.cust_no,
              ltgci.cust_nm,
              ltgci.cert_typ,
              ltgci.cert_no
       from lb_t_grp_cust_info ltgci
       where 1 = 1) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.cust_nm = src.cust_nm
    when not matched then
insert (grp_lmt_view_id, cust_nm, cust_no,
    tenant_id)
    values
(generate_primary_key('LGLV'),
    src.cust_nm,
    src.cust_no,
    src.tenant_id);

--2.更新集团综合授信额度
select lcli.tenant_id       as tenant_id,
       lcli.limit_object_id as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.total_amount   as grp_cprsv_crdt_lmt,
       lcli.operator_id     as operator_id,
       lcli.own_organ_id    as own_organ_id,
       lcli.create_time     as create_time
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai on lcli.cust_limit_id = lclai.cust_limit_id
    and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSJTKHEDTX')
  and lcli.template_node_id in ('JTZHSXED');

merge into lc_grp_lmt_view tgt
using (select lcli.tenant_id       as tenant_id,
              lcli.limit_object_id as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.total_amount   as grp_cprsv_crdt_lmt,
              lcli.operator_id     as operator_id,
              lcli.own_organ_id    as own_organ_id,
              lcli.create_time     as create_time
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai on lcli.cust_limit_id = lclai.cust_limit_id
           and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSJTKHEDTX')
         and lcli.template_node_id in ('JTZHSXED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.grp_cprsv_crdt_lmt = src.grp_cprsv_crdt_lmt,
    tgt.operator_id = src.operator_id,
    tgt.own_organ_id = src.own_organ_id,
    tgt.create_time = src.create_time,
    tgt.update_time = sysdate;

--3.更新集团一般授信额度/集团一般授信可用额度
select lcli.tenant_id                                                          as tenant_id,
       lcli.limit_object_id                                                    as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.total_amount                                                      as grp_com_crdt_lmt,
       lclai.total_amount - lclai.pre_occupy_amount - lclai.real_occupy_amount as grp_com_crdt_avl_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai on lcli.cust_limit_id = lclai.cust_limit_id
    and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSJTKHEDTX')
  and lcli.template_node_id in ('JTYBSXED');

merge into lc_grp_lmt_view tgt
using (select lcli.tenant_id                                                          as tenant_id,
              lcli.limit_object_id                                                    as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.total_amount                                                      as grp_com_crdt_lmt,
              lclai.total_amount - lclai.pre_occupy_amount - lclai.real_occupy_amount as grp_com_crdt_avl_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai on lcli.cust_limit_id = lclai.cust_limit_id
           and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSJTKHEDTX')
         and lcli.template_node_id in ('JTYBSXED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.grp_com_crdt_lmt = src.grp_com_crdt_lmt,
    tgt.grp_com_crdt_avl_lmt = src.grp_com_crdt_avl_lmt,
    tgt.update_time = sysdate;

--4.更新集团纯低风险额度/集团纯低风险可用额度
select lcli.tenant_id                                                          as tenant_id,
       lcli.limit_object_id                                                    as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.total_amount                                                      as grp_whl_low_risk_lmt,
       lclai.total_amount - lclai.pre_occupy_amount - lclai.real_occupy_amount as grp_whl_low_risk_avl_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai on lcli.cust_limit_id = lclai.cust_limit_id
    and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSJTKHEDTX')
  and lcli.template_node_id in ('JTCDFXED');

merge into lc_grp_lmt_view tgt
using (select lcli.tenant_id                                                          as tenant_id,
              lcli.limit_object_id                                                    as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.total_amount                                                      as grp_whl_low_risk_lmt,
              lclai.total_amount - lclai.pre_occupy_amount - lclai.real_occupy_amount as grp_whl_low_risk_avl_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai on lcli.cust_limit_id = lclai.cust_limit_id
           and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSJTKHEDTX')
         and lcli.template_node_id in ('JTCDFXED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.grp_whl_low_risk_lmt = src.grp_whl_low_risk_lmt,
    tgt.grp_whl_low_risk_avl_lmt = src.grp_whl_low_risk_avl_lmt,
    tgt.update_time = sysdate;

--5.更新集团非授信额度
select lcli.tenant_id       as tenant_id,
       lcli.limit_object_id as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.total_amount   as grp_no_crdt_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai on lcli.cust_limit_id = lclai.cust_limit_id
    and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSJTKHEDTX')
  and lcli.template_node_id in ('JTFSXED');

merge into lc_grp_lmt_view tgt
using (select lcli.tenant_id       as tenant_id,
              lcli.limit_object_id as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.total_amount   as grp_no_crdt_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai on lcli.cust_limit_id = lclai.cust_limit_id
           and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSJTKHEDTX')
         and lcli.template_node_id in ('JTFSXED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.grp_no_crdt_lmt = src.grp_no_crdt_lmt,
    tgt.update_time = sysdate;
--6.更新集团总担保额度
select lcli.tenant_id       as tenant_id,
       lcli.limit_object_id as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.total_amount   as grp_guar_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai on lcli.cust_limit_id = lclai.cust_limit_id
    and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSJTKHEDTX')
  and lcli.template_node_id in ('JTPTDBED');

merge into lc_grp_lmt_view tgt
using (select lcli.tenant_id       as tenant_id,
              lcli.limit_object_id as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.total_amount   as grp_guar_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai on lcli.cust_limit_id = lclai.cust_limit_id
           and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSJTKHEDTX')
         and lcli.template_node_id in ('JTPTDBED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.grp_guar_lmt = src.grp_guar_lmt,
    tgt.update_time = sysdate;

--7.更新集团总合作方额度/合作方可用额度
select lcli.tenant_id                                                          as tenant_id,
       lcli.limit_object_id                                                    as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.total_amount                                                      as grp_co_prtn_lmt,
       lclai.total_amount - lclai.pre_occupy_amount - lclai.real_occupy_amount as grp_co_prtn_avl_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai on lcli.cust_limit_id = lclai.cust_limit_id
    and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSJTKHEDTX')
  and lcli.template_node_id in ('JTFSXED');

merge into lc_grp_lmt_view tgt
using (select lcli.tenant_id                                                          as tenant_id,
              lcli.limit_object_id                                                    as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.total_amount                                                      as grp_co_prtn_lmt,
              lclai.total_amount - lclai.pre_occupy_amount - lclai.real_occupy_amount as grp_co_prtn_avl_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai on lcli.cust_limit_id = lclai.cust_limit_id
           and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSJTKHEDTX')
         and lcli.template_node_id in ('JTFSXED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.grp_co_prtn_lmt = src.grp_co_prtn_lmt,
    tgt.grp_co_prtn_avl_lmt = src.grp_co_prtn_avl_lmt,
    tgt.update_time = sysdate;