package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 单一限额规则Do
 *
 * <AUTHOR>
 * @date 2023-06-28 08:56:52
 */
@Table(name = "lc_single_limit_rule")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcSingleLimitRuleDo extends LcSingleLimitRuleKeyDo implements Serializable {
    private static final long serialVersionUID = 1673978759944011776L;
    /** 单一限额规则模板编号 */
    @Column(name = "limit_template_id")
    private String limitTemplateId;
    /** 已使用金额 */
    @Column(name = "used_amount")
    private java.math.BigDecimal usedAmount;
    /** 限额使用阶段;EnumSingleLimitUseStage（001-授信阶段、002-用信阶段） */
    @Column(name = "use_stage")
    private String useStage;
    /** 使用方式;EnumUsageType(001-非循环授信, 002-循环授信） */
    @Column(name = "usage_type")
    private String usageType;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 限额金额 */
    @Column(name = "total_amount")
    private java.math.BigDecimal totalAmount;
    /** 规则名称 */
    @Column(name = "rule_name")
    private String ruleName;
    /** 实占金额 */
    @Column(name = "real_occupy_amount")
    private java.math.BigDecimal realOccupyAmount;
    /** 预占金额 */
    @Column(name = "pre_occupy_amount")
    private java.math.BigDecimal preOccupyAmount;
    /** 校验策略 */
    @Column(name = "check_strategy")
    private String checkStrategy;
    /** 限额状态;EnumSingleLimitStatus */
    @Column(name = "limit_status")
    private String limitStatus;
    /** 所属对象类型 */
    @Column(name = "limit_object_type")
    private String limitObjectType;
    /** 所属对象编号 */
    @Column(name = "limit_object_id")
    private String limitObjectId;
    /** 生效开始时间 */
    @Column(name = "effective_start_time")
    private java.util.Date effectiveStartTime;
    /** 生效结束时间int */
    @Column(name = "effective_end_time_int")
    private Integer effectiveEndTimeInt;
    /** 生效结束时间 */
    @Column(name = "effective_end_time")
    private java.util.Date effectiveEndTime;
    /** 额度编号 */
    @Column(name = "cust_limit_id")
    private String custLimitId;
    /** 币种 */
    @Column(name = "currency")
    private String currency;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
}
