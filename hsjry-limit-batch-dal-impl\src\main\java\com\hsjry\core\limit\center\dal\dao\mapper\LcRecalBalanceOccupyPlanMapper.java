package com.hsjry.core.limit.center.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.center.dal.dao.model.LcRecalBalanceOccupyPlanDo;
import com.hsjry.core.limit.center.dal.dao.query.LcRecalBalanceOccupyPlanQuery;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 余额占用重算计划(准备)mapper
 *
 * <AUTHOR>
 * @date 2023-03-15 02:59:47
 */
public interface LcRecalBalanceOccupyPlanMapper extends CommonMapper<LcRecalBalanceOccupyPlanDo> {
    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    String selectFirstOne(@Param("query") LcRecalBalanceOccupyPlanQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(@Param("query") LcRecalBalanceOccupyPlanQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcRecalBalanceOccupyPlanDo> selectShardList(@Param("query") LcRecalBalanceOccupyPlanQuery query);

    /**
     * 清理所有准备
     *
     * @param tenantId
     * @return
     */
    int deleteAll(@Param("tenantId") String tenantId);
}