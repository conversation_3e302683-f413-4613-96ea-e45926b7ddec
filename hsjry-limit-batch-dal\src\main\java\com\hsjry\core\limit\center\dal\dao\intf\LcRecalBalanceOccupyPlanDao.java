package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcRecalBalanceOccupyPlanDo;
import com.hsjry.core.limit.center.dal.dao.query.LcRecalBalanceOccupyPlanQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 余额占用重算计划(准备)数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-03-15 02:59:47
 */
public interface LcRecalBalanceOccupyPlanDao extends IBaseDao<LcRecalBalanceOccupyPlanDo> {
    /**
     * 分页查询余额占用重算计划(准备)
     *
     * @param lcRecalBalanceOccupyPlanQuery 条件
     * @return PageInfo<LcRecalBalanceOccupyPlanDo>
     */
    PageInfo<LcRecalBalanceOccupyPlanDo> selectPage(LcRecalBalanceOccupyPlanQuery lcRecalBalanceOccupyPlanQuery,
        PageParam pageParam);

    /**
     * 根据key查询余额占用重算计划(准备)
     *
     * @param custLimitId
     * @param entityId
     * @return
     */
    LcRecalBalanceOccupyPlanDo selectByKey(String custLimitId, String entityId);

    /**
     * 根据key删除余额占用重算计划(准备)
     *
     * @param custLimitId
     * @param entityId
     * @return
     */
    int deleteByKey(String custLimitId, String entityId);

    /**
     * 查询余额占用重算计划(准备)信息
     *
     * @param lcRecalBalanceOccupyPlanQuery 条件
     * @return List<LcRecalBalanceOccupyPlanDo>
     */
    List<LcRecalBalanceOccupyPlanDo> selectByExample(LcRecalBalanceOccupyPlanQuery lcRecalBalanceOccupyPlanQuery);

    /**
     * 新增余额占用重算计划(准备)信息
     *
     * @param lcRecalBalanceOccupyPlan 条件
     * @return int>
     */
    int insertBySelective(LcRecalBalanceOccupyPlanDo lcRecalBalanceOccupyPlan);

    /**
     * 修改余额占用重算计划(准备)信息
     *
     * @param lcRecalBalanceOccupyPlan
     * @return
     */
    int updateBySelective(LcRecalBalanceOccupyPlanDo lcRecalBalanceOccupyPlan);

    /**
     * 修改余额占用重算计划(准备)信息
     *
     * @param lcRecalBalanceOccupyPlan
     * @param lcRecalBalanceOccupyPlanQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcRecalBalanceOccupyPlanDo lcRecalBalanceOccupyPlan,
        LcRecalBalanceOccupyPlanQuery lcRecalBalanceOccupyPlanQuery);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    String selectFirstOne(LcRecalBalanceOccupyPlanQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(LcRecalBalanceOccupyPlanQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcRecalBalanceOccupyPlanDo> selectShardList(LcRecalBalanceOccupyPlanQuery query);
    /**
     * 清理租户下所有
     *
     * @return
     */
    int deleteAll();
}
