package com.hsjry.core.limit.batch.biz.job.job.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 电票系统-贴现余额信息文件的同步处理任务
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/7/17
 */
@Slf4j
@Service("lbSElcblDsctBalInfoFileSyncJob")
public class LbSElcblDsctBalInfoFileSyncJob extends AbstractBaseBatchJob {

    public LbSElcblDsctBalInfoFileSyncJob() {
        log.info("LbSElcblDsctBalInfoFileSyncJob Bean 正在创建...");
    }

    @Autowired
    @Qualifier("lbSElcblDsctBalInfoFileSyncBizImpl")
    private BaseOrdinaryBiz baseOrdinaryBiz;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return baseOrdinaryBiz;
    }

    /**
     * 设置基础业务逻辑对象
     *
     * @param baseOrdinaryBiz 基础业务逻辑对象
     */
    public void setBaseOrdinaryBiz(BaseOrdinaryBiz baseOrdinaryBiz) {
        this.baseOrdinaryBiz = baseOrdinaryBiz;
    }
}