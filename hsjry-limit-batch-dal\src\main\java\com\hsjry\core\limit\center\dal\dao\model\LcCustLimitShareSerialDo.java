package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度串用流水Do
 *
 * <AUTHOR>
 * @date 2023-06-01 03:00:26
 */
@Table(name = "lc_cust_limit_share_serial")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitShareSerialDo extends LcCustLimitShareSerialKeyDo implements Serializable {
    private static final long serialVersionUID = 1664104590133624832L;
    /** 前置业务流水 */
    @Column(name = "inbound_serial_no")
    private String inboundSerialNo;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 状态 */
    @Column(name = "status")
    private String status;
    /** 业务流水号 */
    @Column(name = "serial_no")
    private String serialNo;
    /** 备注 */
    @Column(name = "remark")
    private String remark;
    /** 关联编号 */
    @Column(name = "relation_id")
    private String relationId;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 操作类型;EnumCustLimitOperateType */
    @Column(name = "operate_type")
    private String operateType;
    /** 操作金额 */
    @Column(name = "operate_amount")
    private java.math.BigDecimal operateAmount;
    /** 前置业务关联流水 */
    @Column(name = "last_inbound_serial_no")
    private String lastInboundSerialNo;
    /** 金额编号 */
    @Column(name = "amount_id")
    private String amountId;
    /** 前置业务时间 */
    @Column(name = "inbound_serial_datetime")
    private java.util.Date inboundSerialDatetime;
    /** 全局流水号 */
    @Column(name = "global_serial_no")
    private String globalSerialNo;
    /** 失败原因 */
    @Column(name = "fail_reason")
    private String failReason;
    /** 实体编号 */
    @Column(name = "entity_id")
    private String entityId;
    /** 额度串用编号 */
    @Column(name = "cust_limit_share_id")
    private String custLimitShareId;
    /** 币种 */
    @Column(name = "currency")
    private String currency;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 渠道号 */
    @Column(name = "channel_no")
    private String channelNo;
    /** 业务时间 */
    @Column(name = "biz_datetime")
    private java.util.Date bizDatetime;
}
