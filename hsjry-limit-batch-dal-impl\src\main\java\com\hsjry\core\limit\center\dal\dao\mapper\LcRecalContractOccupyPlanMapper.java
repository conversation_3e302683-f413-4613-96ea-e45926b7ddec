package com.hsjry.core.limit.center.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.center.dal.dao.model.LcRecalContractOccupyPlanDo;
import com.hsjry.core.limit.center.dal.dao.query.LcRecalContractOccupyPlanQuery;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 合同占用重算计划(准备)mapper
 *
 * <AUTHOR>
 * @date 2023-03-15 02:59:47
 */
public interface LcRecalContractOccupyPlanMapper extends CommonMapper<LcRecalContractOccupyPlanDo> {
    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    String selectFirstOne(@Param("query") LcRecalContractOccupyPlanQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(@Param("query") LcRecalContractOccupyPlanQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcRecalContractOccupyPlanDo> selectShardList(@Param("query") LcRecalContractOccupyPlanQuery query);

    /**
     * 清理所有准备
     * @param tenantId
     * @return
     */
    int deleteAll(@Param("tenantId") String tenantId);
}