package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 实体限额关系Do
 *
 * <AUTHOR>
 * @date 2023-02-24 08:41:16
 */
@Table(name = "lc_entity_amt_limit_rel")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcEntityAmtLimitRelDo extends LcEntityAmtLimitRelKeyDo implements Serializable {
    private static final long serialVersionUID = 1629038738606850048L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 记录编号 */
    @Column(name = "record_id")
    private String recordId;
    /** 实体编号 */
    @Column(name = "entity_id")
    private String entityId;
    /** 分片序号 */
    @Column(name = "shard_seq")
    private Integer shardSeq;
    /** 规则编号 */
    @Column(name = "rule_id")
    private String ruleId;
}
