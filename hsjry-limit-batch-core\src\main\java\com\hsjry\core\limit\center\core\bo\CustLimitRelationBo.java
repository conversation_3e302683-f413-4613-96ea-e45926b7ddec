/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2022 All Rights Reserved.
 */
package com.hsjry.core.limit.center.core.bo;

import javax.validation.constraints.NotBlank;

import org.apache.commons.lang.ObjectUtils;

import com.hsjry.base.common.model.constant.SystemCharConstant;
import com.hsjry.base.common.model.enums.limit.EnumLimitRelationType;

import lombok.Builder;
import lombok.Data;

/**
 * 额度实例关联Bo
 *
 * <AUTHOR>
 * @version V4.0
 * @see com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationDo
 * @since 4.0.1 2022/12/13 11:05
 */
@Data
@Builder
public class CustLimitRelationBo {
    /**
     * 关系编号
     */
    private String limitRelationId;
    /**
     * 关系操作类型
     * 001- 新增关系
     * 002- 解除关系
     */
    private String relationOperateType;
    /**
     * 开始实例编号
     * 只会将com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationDo#parentNodeLimitId设置进来,
     * 即CustLimitRelationBo.setParentInstId(LcCustLimitRelationDo#parentNodeLimitId),
     * 不会将[parentInstId]设置到LcCustLimitRelationDo#parentNodeLimitId,即不存在setParentNodeLimitId(CustLimitRelationBo#parentInstId)
     */
    @NotBlank(message = "开始实例编号不能为空")
    private String parentInstId;
    /**
     * 结束实例编号
     * 只会将com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationDo#currentNodeLimitId设置进来,
     * 即CustLimitRelationBo.setCurrentInstId(LcCustLimitRelationDo#currentNodeLimitId),
     * 不会将[currentInstId]设置到LcCustLimitRelationDo#currentNodeLimitId,即不存在setCurrentNodeLimitId(CustLimitRelationBo#currentInstId)
     */
    @NotBlank(message = "结束实例编号不能为空")
    private String currentInstId;
    /**
     * 关系类型 ，EnumLimitRelationType:001-默认、002-关联 、003-计算关系（不会落库）
     */
    @Builder.Default
    private String limitRelationType = EnumLimitRelationType.DEFAULT.getCode();

    /** 入参标记-用于比对 */
    private boolean requestFlag;

    /**
     * 关系唯一标识
     *
     * @return 开始实例编号_结束实例编号
     */
    public String relationSoleSign() {
        return String.join(SystemCharConstant.UNDERLINE, currentInstId, parentInstId);
    }

    public String printCoreFields() {
        return String.format(
            "关系编号为[%s],关系操作类型(001-新增关系,002-解除关系)为[%s],开始实例编号为[%s],结束实例编号为[%s],关系类型(001-默认,002-关联,003-计算关系(不会落库))为[%s],关系唯一标识为[%s]",
            ObjectUtils.defaultIfNull(this.limitRelationId, ""),//
            ObjectUtils.defaultIfNull(this.relationOperateType, ""),//
            ObjectUtils.defaultIfNull(this.parentInstId, ""), //
            ObjectUtils.defaultIfNull(this.currentInstId, ""), //
            ObjectUtils.defaultIfNull(this.limitRelationType, ""),//
            ObjectUtils.defaultIfNull(this.relationSoleSign(), ""));
    }
}
