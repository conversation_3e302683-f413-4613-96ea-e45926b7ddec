package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcInboundSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcInboundSerialQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度前置流水数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcInboundSerialDao extends IBaseDao<LcInboundSerialDo> {
    /**
     * 分页查询额度前置流水
     *
     * @param lcInboundSerialQuery 条件
     * @return PageInfo<LcInboundSerialDo>
     */
    PageInfo<LcInboundSerialDo> selectPage(LcInboundSerialQuery lcInboundSerialQuery, PageParam pageParam);

    /**
     * 根据key查询额度前置流水
     *
     * @param cisSerialNo
     * @return
     */
    LcInboundSerialDo selectByKey(String cisSerialNo);

    /**
     * 根据key删除额度前置流水
     *
     * @param cisSerialNo
     * @return
     */
    int deleteByKey(String cisSerialNo);

    /**
     * 查询额度前置流水信息
     *
     * @param lcInboundSerialQuery 条件
     * @return List<LcInboundSerialDo>
     */
    List<LcInboundSerialDo> selectByExample(LcInboundSerialQuery lcInboundSerialQuery);

    /**
     * 新增额度前置流水信息
     *
     * @param lcInboundSerial 条件
     * @return int>
     */
    int insertBySelective(LcInboundSerialDo lcInboundSerial);

    /**
     * 修改额度前置流水信息
     *
     * @param lcInboundSerial
     * @return
     */
    int updateBySelective(LcInboundSerialDo lcInboundSerial);

    /**
     * 修改额度前置流水信息
     *
     * @param lcInboundSerial
     * @param lcInboundSerialQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcInboundSerialDo lcInboundSerial, LcInboundSerialQuery lcInboundSerialQuery);
}
