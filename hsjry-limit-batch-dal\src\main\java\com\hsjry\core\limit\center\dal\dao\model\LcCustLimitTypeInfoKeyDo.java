package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度类型信息主键
 *
 * <AUTHOR>
 * @date 2022-12-02 03:03:37
 */
@Table(name = "lc_cust_limit_type_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcCustLimitTypeInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1598513186523512832L;
    /** 额度类型编号 */
    @Id
    @Column(name = "cust_limit_type_id")
    private String custLimitTypeId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}