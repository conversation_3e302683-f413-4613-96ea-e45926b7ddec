/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.entity;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 核心系统-历史表-贴现账户主文件数据实体
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/7/17
 */
@Data
public class LbHCoreAtxzhData implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 法人代码 */
    private String faredm;
    /** 贴现借据号 */
    private String txnjjh;
    /** 贴现帐号 */
    private String tiexzh;
    /** 贴现处理种类 */
    private String txclzl;
    /** 贴现业务种类 */
    private String txywzl;
    /** 票据包号 */
    private String piojzh;
    /** 客户号 */
    private String kehhao;
    /** 客户名 */
    private String kehzwm;
    /** 营业机构 */
    private String yngyjg;
    /** 帐务机构 */
    private String zhngjg;
    /** 入帐机构 */
    private String ruzhjg;
    /** 损益支出机构 */
    private String syzcjg;
    /** 损益入帐机构 */
    private String syrzjg;
    /** 货币代号 */
    private String huobdh;
    /** 贴现起息日 */
    private String txqxrq;
    /** 贴现到期日 */
    private String txdqrq;
    /** 宽限期 */
    private BigDecimal kuanxq;
    /** 利率编号 */
    private String lilvbh;
    /** 年月利率 */
    private String nyuell;
    /** 贴现利率 */
    private BigDecimal tiexll;
    /** 贴现余额 */
    private BigDecimal txztye;
    /** 实付金额 */
    private BigDecimal shfuje;
    /** 实收贴现利息 */
    private BigDecimal sxtxlx;
    /** 累计利息收入 */
    private BigDecimal ljlxsr;
    /** 利息摊销周期 */
    private String txrzzq;
    /** 待摊销收入余额 */
    private BigDecimal dtsrye;
    /** 上次摊销收入日 */
    private String sctsrq;
    /** 下次摊销收入日 */
    private String xctsro;
    /** 实收金额 */
    private BigDecimal shshje;
    /** 实付贴现利息 */
    private BigDecimal sftxlx;
    /** 累计利息支出 */
    private BigDecimal ljlxzc;
    /** 待摊销支出余额 */
    private BigDecimal dtzcye;
    /** 上次摊销支出日 */
    private String sctzrq;
    /** 下次摊销支出日 */
    private String xctzrq;
    /** 客户结算帐号 */
    private String jieszh;
    /** 是否先贴后查 */
    private String sfxthc;
    /** 查询查复编号 */
    private String cxcfbh;
    /** 付息方式 */
    private String txfxfs;
    /** 买方付息比例 */
    private BigDecimal mffxbl;
    /** 买方付息帐号 */
    private String mffxzh;
    /** 贴现风险标志 */
    private String txfxbz;
    /** 抵质押物编号 */
    private String dzywbh;
    /** 本次买卖利息金额 */
    private BigDecimal fxcdje;
    /** 逆回购转入时原组号 */
    private String xinx01;
    /** 垫款标志 */
    private String dnknbz;
    /** 垫款借据编号 */
    private String jiejuh;
    /** 转垫款金额 */
    private BigDecimal zhdkje;
    /** 对手行类别 */
    private String jigulb;
    /** 对手行行号 */
    private String duifhh;
    /** 对手行行名 */
    private String duifhm;
    /** 内部转贴现本次卖断利率 */
    private BigDecimal bcmdll;
    /** 内部转贴现本次卖断利息 */
    private BigDecimal bcmdlx;
    /** 贴现状态 */
    private String zhungt;
    /** 明细序号 */
    private BigDecimal mxxhao;
    /** 录入日期 */
    private String lururq;
    /** 录入柜员 */
    private String lurugy;
    /** 复核日期 */
    private String fuherq;
    /** 复核柜员 */
    private String fuhegy;
    /** 维护日期 */
    private String weihrq;
    /** 维护柜员 */
    private String weihgy;
    /** 交易日期 */
    private String jioyrq;
    /** 维护机构 */
    private String weihjg;
    /** 维护时间 */
    private BigDecimal weihsj;
    /** 时间戳 */
    private BigDecimal shinch;
    /** 记录状态 */
    private String jiluzt;
    /** 数据日期 */
    private String dataDate;
}