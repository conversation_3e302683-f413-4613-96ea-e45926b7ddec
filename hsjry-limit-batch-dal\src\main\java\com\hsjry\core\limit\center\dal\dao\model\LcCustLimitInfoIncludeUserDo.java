package com.hsjry.core.limit.center.dal.dao.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 额度信息及用户信息
 *
 * <AUTHOR>
 * @date 2023-11-07 08:52:41
 */
@Data
public class LcCustLimitInfoIncludeUserDo implements Serializable {
    private static final long serialVersionUID = -5450398080239397152L;

    // lc_cust_limit_info
    /** 额度编号 */
    private String custLimitId;
    /** 模板节点编号 */
    private String templateNodeId;
    /** 所属对象编号 */
    private String limitObjectId;

    // lc_cust_limit_object_info
    /** 客户名称 */
    private String userName;
    /** 客户类型 */
    private String userType;
}
