package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 实体限额关系主键
 *
 * <AUTHOR>
 * @date 2023-02-24 08:41:16
 */
@Table(name = "lc_entity_amt_limit_rel")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcEntityAmtLimitRelKeyDo implements Serializable {

    private static final long serialVersionUID = 1629038738606850048L;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    /** 实体限额关系编号 */
    @Id
    @Column(name = "entity_amt_limit_id")
    private String entityAmtLimitId;
}