package com.hsjry.core.limit.batch.dal.dao.query;

import java.io.Serializable;

import lombok.Data;
import lombok.Builder;

/**
 * 电票系统-中间表-贴现对公借据信息查询条件
 *
 * <AUTHOR>
 * @date 2025-07-29 02:46:35
 */
@Data
@Builder
public class LbTElcblCorpLoanInfoQuery implements Serializable {

    /** serialVersionUID */
    private static final long serialVersionUID = 1950025131455873024L;

    /** 实体业务编号 */
    private String entityRelationId;
    /** 所属组织编号 */
    private String ownOrganId;
    /** 操作人编号 */
    private String operatorId;
    /** 低风险余额 */
    private java.math.BigDecimal leftLowRisk;
    /** 发放低风险 */
    private java.math.BigDecimal lowRisk;
    /** 当前余额 */
    private java.math.BigDecimal leftAmount;
    /** 发放金额 */
    private java.math.BigDecimal amount;
    /** 客户编号 */
    private String custNo;
    /** 实体申请编号 */
    private String entityApplyId;
    /** 状态;EnumEntityStatus */
    private String status;
    /** 实体类型;EnumLimitCenterEntityType */
    private String entityType;
    /** 实体编号 */
    private String entityId;
    /** 额度编号 */
    private String custLimitId;
    /** 证件号码 */
    private String certNo;
    /** 证件类型 */
    private String certTyp;
    /** 客户名称 */
    private String custNm;
    /** 客户类型 */
    private String custTyp;
}
