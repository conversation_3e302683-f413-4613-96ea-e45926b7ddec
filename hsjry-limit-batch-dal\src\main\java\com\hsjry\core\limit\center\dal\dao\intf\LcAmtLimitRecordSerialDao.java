package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitRecordSerialQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 限额记录操作流水数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-01-30 01:44:45
 */
public interface LcAmtLimitRecordSerialDao extends IBaseDao<LcAmtLimitRecordSerialDo> {
    /**
     * 分页查询限额记录操作流水
     *
     * @param lcAmtLimitRecordSerialQuery 条件
     * @return PageInfo<LcAmtLimitRecordSerialDo>
     */
    PageInfo<LcAmtLimitRecordSerialDo> selectPage(LcAmtLimitRecordSerialQuery lcAmtLimitRecordSerialQuery,
        PageParam pageParam);

    /**
     * 根据key查询限额记录操作流水
     *
     * @param lalrsSerialNo
     * @return
     */
    LcAmtLimitRecordSerialDo selectByKey(String lalrsSerialNo);

    /**
     * 根据key删除限额记录操作流水
     *
     * @param lalrsSerialNo
     * @return
     */
    int deleteByKey(String lalrsSerialNo);

    /**
     * 查询限额记录操作流水信息
     *
     * @param lcAmtLimitRecordSerialQuery 条件
     * @return List<LcAmtLimitRecordSerialDo>
     */
    List<LcAmtLimitRecordSerialDo> selectByExample(LcAmtLimitRecordSerialQuery lcAmtLimitRecordSerialQuery);

    /**
     * 新增限额记录操作流水信息
     *
     * @param lcAmtLimitRecordSerial 条件
     * @return int>
     */
    int insertBySelective(LcAmtLimitRecordSerialDo lcAmtLimitRecordSerial);

    /**
     * 修改限额记录操作流水信息
     *
     * @param lcAmtLimitRecordSerial
     * @return
     */
    int updateBySelective(LcAmtLimitRecordSerialDo lcAmtLimitRecordSerial);

    /**
     * 修改限额记录操作流水信息
     *
     * @param lcAmtLimitRecordSerial
     * @param lcAmtLimitRecordSerialQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcAmtLimitRecordSerialDo lcAmtLimitRecordSerial,
        LcAmtLimitRecordSerialQuery lcAmtLimitRecordSerialQuery);

    /**
     * 删除限额记录操作流水信息
     *
     * @param lcAmtLimitRecordSerialQuery 条件
     * @return List<LcAmtLimitRecordSerialDo>
     */
    int deleteByExample(LcAmtLimitRecordSerialQuery lcAmtLimitRecordSerialQuery);
}
