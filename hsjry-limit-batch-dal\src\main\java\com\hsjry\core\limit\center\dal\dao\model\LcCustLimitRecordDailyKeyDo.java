package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度记录日报主键
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
@Table(name = "lc_cust_limit_record_daily")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcCustLimitRecordDailyKeyDo implements Serializable {

    private static final long serialVersionUID = 1602582710713516053L;
    /** 记录日期 */
    @Id
    @Column(name = "record_date_str")
    private String recordDateStr;
    /** 记录编号 */
    @Id
    @Column(name = "record_id")
    private String recordId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}