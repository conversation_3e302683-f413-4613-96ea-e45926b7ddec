package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 导入流水主键
 *
 * <AUTHOR>
 * @date 2023-04-18 08:29:40
 */
@Table(name = "lc_import_serial")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcImportSerialKeyDo implements Serializable {

    private static final long serialVersionUID = 1648242377338585088L;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    /** 导入流水编号 */
    @Id
    @Column(name = "import_serial_no")
    private String importSerialNo;
}