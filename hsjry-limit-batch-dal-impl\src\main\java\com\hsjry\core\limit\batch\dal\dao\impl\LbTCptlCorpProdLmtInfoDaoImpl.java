package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTCptlCorpProdLmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTCptlCorpProdLmtInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTCptlCorpProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTCptlCorpProdLmtInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTCptlCorpProdLmtInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTCptlCorpProdLmtInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 资金系统-中间表-对公客户产品层额度信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-30 02:03:23
 */
@Repository
public class LbTCptlCorpProdLmtInfoDaoImpl
    extends AbstractBaseDaoImpl<LbTCptlCorpProdLmtInfoDo, LbTCptlCorpProdLmtInfoMapper>
    implements LbTCptlCorpProdLmtInfoDao {
    /**
     * 分页查询
     *
     * @param lbTCptlCorpProdLmtInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTCptlCorpProdLmtInfoDo> selectPage(LbTCptlCorpProdLmtInfoQuery lbTCptlCorpProdLmtInfo,
        PageParam pageParam) {
        LbTCptlCorpProdLmtInfoExample example = buildExample(lbTCptlCorpProdLmtInfo);
        return PageHelper.<LbTCptlCorpProdLmtInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询资金系统-中间表-对公客户产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTCptlCorpProdLmtInfoDo selectByKey(String custNo, String custLimitId) {
        LbTCptlCorpProdLmtInfoKeyDo lbTCptlCorpProdLmtInfoKeyDo = new LbTCptlCorpProdLmtInfoKeyDo();
        lbTCptlCorpProdLmtInfoKeyDo.setCustNo(custNo);
        lbTCptlCorpProdLmtInfoKeyDo.setCustLimitId(custLimitId);
        lbTCptlCorpProdLmtInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTCptlCorpProdLmtInfoKeyDo);
    }

    /**
     * 根据key删除资金系统-中间表-对公客户产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTCptlCorpProdLmtInfoKeyDo lbTCptlCorpProdLmtInfoKeyDo = new LbTCptlCorpProdLmtInfoKeyDo();
        lbTCptlCorpProdLmtInfoKeyDo.setCustNo(custNo);
        lbTCptlCorpProdLmtInfoKeyDo.setCustLimitId(custLimitId);
        lbTCptlCorpProdLmtInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTCptlCorpProdLmtInfoKeyDo);
    }

    /**
     * 查询资金系统-中间表-对公客户产品层额度信息信息
     *
     * @param lbTCptlCorpProdLmtInfo 条件
     * @return List<LbTCptlCorpProdLmtInfoDo>
     */
    @Override
    public List<LbTCptlCorpProdLmtInfoDo> selectByExample(LbTCptlCorpProdLmtInfoQuery lbTCptlCorpProdLmtInfo) {
        return getMapper().selectByExample(buildExample(lbTCptlCorpProdLmtInfo));
    }

    /**
     * 新增资金系统-中间表-对公客户产品层额度信息信息
     *
     * @param lbTCptlCorpProdLmtInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTCptlCorpProdLmtInfoDo lbTCptlCorpProdLmtInfo) {
        if (lbTCptlCorpProdLmtInfo == null) {
            return -1;
        }

        lbTCptlCorpProdLmtInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTCptlCorpProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTCptlCorpProdLmtInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTCptlCorpProdLmtInfo);
    }

    /**
     * 修改资金系统-中间表-对公客户产品层额度信息信息
     *
     * @param lbTCptlCorpProdLmtInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTCptlCorpProdLmtInfoDo lbTCptlCorpProdLmtInfo) {
        if (lbTCptlCorpProdLmtInfo == null) {
            return -1;
        }
        lbTCptlCorpProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTCptlCorpProdLmtInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTCptlCorpProdLmtInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTCptlCorpProdLmtInfoDo lbTCptlCorpProdLmtInfo,
        LbTCptlCorpProdLmtInfoQuery lbTCptlCorpProdLmtInfoQuery) {
        lbTCptlCorpProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTCptlCorpProdLmtInfo, buildExample(lbTCptlCorpProdLmtInfoQuery));
    }

    /**
     * 构建资金系统-中间表-对公客户产品层额度信息Example信息
     *
     * @param lbTCptlCorpProdLmtInfo
     * @return
     */
    public LbTCptlCorpProdLmtInfoExample buildExample(LbTCptlCorpProdLmtInfoQuery lbTCptlCorpProdLmtInfo) {
        LbTCptlCorpProdLmtInfoExample example = new LbTCptlCorpProdLmtInfoExample();
        LbTCptlCorpProdLmtInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTCptlCorpProdLmtInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTCptlCorpProdLmtInfo.getProductId())) {
                criteria.andProductIdEqualTo(lbTCptlCorpProdLmtInfo.getProductId());
            }
            if (StringUtil.isNotEmpty(lbTCptlCorpProdLmtInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTCptlCorpProdLmtInfo.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lbTCptlCorpProdLmtInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTCptlCorpProdLmtInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbTCptlCorpProdLmtInfo.getCoreInstNo())) {
                criteria.andCoreInstNoEqualTo(lbTCptlCorpProdLmtInfo.getCoreInstNo());
            }
            if (null != lbTCptlCorpProdLmtInfo.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTCptlCorpProdLmtInfo.getRealOccupyAmount());
            }
            if (StringUtil.isNotEmpty(lbTCptlCorpProdLmtInfo.getProductName())) {
                criteria.andProductNameEqualTo(lbTCptlCorpProdLmtInfo.getProductName());
            }
            if (StringUtil.isNotEmpty(lbTCptlCorpProdLmtInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTCptlCorpProdLmtInfo.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTCptlCorpProdLmtInfo.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTCptlCorpProdLmtInfo.getLimitStatus());
            }
            if (StringUtil.isNotEmpty(lbTCptlCorpProdLmtInfo.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTCptlCorpProdLmtInfo.getCustLimitId());
            }
            if (null != lbTCptlCorpProdLmtInfo.getUsedAmount()) {
                criteria.andUsedAmountEqualTo(lbTCptlCorpProdLmtInfo.getUsedAmount());
            }
            if (StringUtil.isNotEmpty(lbTCptlCorpProdLmtInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTCptlCorpProdLmtInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTCptlCorpProdLmtInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTCptlCorpProdLmtInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTCptlCorpProdLmtInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTCptlCorpProdLmtInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTCptlCorpProdLmtInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTCptlCorpProdLmtInfo.getCustTyp());
            }
        }
        buildExampleExt(lbTCptlCorpProdLmtInfo, criteria);
        return example;
    }

    /**
     * 构建资金系统-中间表-对公客户产品层额度信息ExampleExt方法
     *
     * @param lbTCptlCorpProdLmtInfo
     * @return
     */
    public void buildExampleExt(LbTCptlCorpProdLmtInfoQuery lbTCptlCorpProdLmtInfo,
        LbTCptlCorpProdLmtInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 往[资金系统-中间表-对公客户产品层额度信息]插入数据
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param limitIdList 额度ID列表
     * @return 插入条数
     */
    @Override
    public int insertCptlCorpProdLmtInfo(List<String> templateNodeIdList, List<String> limitIdList) {
        return getMapper().insertCptlCorpProdLmtInfo(templateNodeIdList, limitIdList);
    }

    /**
     * 更新[额度实例金额信息]中[实占额度]
     *
     * @param limitIdList 额度ID列表
     * @return 更新条数
     */
    @Override
    public int updateRealOccupyAmount(List<String> limitIdList) {
        return getMapper().updateRealOccupyAmount(limitIdList);
    }

}
