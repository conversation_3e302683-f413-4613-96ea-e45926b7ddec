package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 资金系统-中间表-同业客户产品层额度信息Do
 *
 * <AUTHOR>
 * @date 2025-07-30 02:03:23
 */
@Table(name = "lb_t_cptl_ibnk_prod_lmt_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbTCptlIbnkProdLmtInfoDo extends LbTCptlIbnkProdLmtInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1950376645714182145L;
    /** 客户类型 */
    @Column(name = "cust_typ")
    private String custTyp;
    /** 客户名称 */
    @Column(name = "cust_nm")
    private String custNm;
    /** 证件类型 */
    @Column(name = "cert_typ")
    private String certTyp;
    /** 证件号码 */
    @Column(name = "cert_no")
    private String certNo;
    /** 已用额度 */
    @Column(name = "used_amount")
    private java.math.BigDecimal usedAmount;
    /** 额度状态 */
    @Column(name = "limit_status")
    private String limitStatus;
    /** 额度实例金额信息中实占额度 */
    @Column(name = "real_occupy_amount")
    private java.math.BigDecimal realOccupyAmount;
    /** 核心机构号 */
    @Column(name = "core_inst_no")
    private String coreInstNo;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
}
