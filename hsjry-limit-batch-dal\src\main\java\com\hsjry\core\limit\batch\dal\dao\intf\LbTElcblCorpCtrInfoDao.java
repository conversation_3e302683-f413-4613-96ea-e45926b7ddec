package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpCtrInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTElcblCorpCtrInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-中间表-贴现对公合同信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-29 02:46:35
 */
public interface LbTElcblCorpCtrInfoDao extends IBaseDao<LbTElcblCorpCtrInfoDo> {
    /**
     * 分页查询电票系统-中间表-贴现对公合同信息
     *
     * @param lbTElcblCorpCtrInfoQuery 条件
     * @return PageInfo<LbTElcblCorpCtrInfoDo>
     */
    PageInfo<LbTElcblCorpCtrInfoDo> selectPage(LbTElcblCorpCtrInfoQuery lbTElcblCorpCtrInfoQuery, PageParam pageParam);

    /**
     * 根据key查询电票系统-中间表-贴现对公合同信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTElcblCorpCtrInfoDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除电票系统-中间表-贴现对公合同信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询电票系统-中间表-贴现对公合同信息信息
     *
     * @param lbTElcblCorpCtrInfoQuery 条件
     * @return List<LbTElcblCorpCtrInfoDo>
     */
    List<LbTElcblCorpCtrInfoDo> selectByExample(LbTElcblCorpCtrInfoQuery lbTElcblCorpCtrInfoQuery);

    /**
     * 新增电票系统-中间表-贴现对公合同信息信息
     *
     * @param lbTElcblCorpCtrInfo 条件
     * @return int>
     */
    int insertBySelective(LbTElcblCorpCtrInfoDo lbTElcblCorpCtrInfo);

    /**
     * 修改电票系统-中间表-贴现对公合同信息信息
     *
     * @param lbTElcblCorpCtrInfo
     * @return
     */
    int updateBySelective(LbTElcblCorpCtrInfoDo lbTElcblCorpCtrInfo);

    /**
     * 修改电票系统-中间表-贴现对公合同信息信息
     *
     * @param lbTElcblCorpCtrInfo
     * @param lbTElcblCorpCtrInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTElcblCorpCtrInfoDo lbTElcblCorpCtrInfo,
        LbTElcblCorpCtrInfoQuery lbTElcblCorpCtrInfoQuery);

    /**
     * 将电票对公客户额度同步数据插入到合同信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_elcbl_corp_ctr_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    int insertElcblCorpCtrInfo(List<String> templateNodeIdList, List<String> custLimitIdList);

    /**
     * 更新额度实例金额信息表
     * 根据电票系统中间表数据更新LC_CUST_LIMIT_AMT_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    int updateCustLimitAmtInfo(List<String> custLimitIdList);
}
