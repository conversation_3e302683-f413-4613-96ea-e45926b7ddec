package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 核心系统-落地表-贴现账户主文件主键
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Table(name = "lb_s_core_atxzh")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbSCoreAtxzhKeyDo implements Serializable {

    private static final long serialVersionUID = 1945747816769060869L;
    /** 法人代码 */
    @Id
    @Column(name = "faredm")
    private String faredm;
    /** 贴现借据号 */
    @Id
    @Column(name = "txnjjh")
    private String txnjjh;
}