package com.hsjry.core.limit.center.core;

import java.util.List;

import com.hsjry.core.limit.center.core.bo.CustLimitRelationBo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitRelationQuery;

public interface ICustLimitRelationCore {
    /**
     * 查询额度实例关联
     *
     * @param query 条件
     * @return List<CustLimitRelationBo>
     */
    List<CustLimitRelationBo> enqrByExample(LcCustLimitRelationQuery query);
}
