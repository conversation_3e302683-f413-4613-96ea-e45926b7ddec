package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度实例历史主键
 *
 * <AUTHOR>
 * @date 2023-11-13 01:05:13
 */
@Table(name = "lc_cust_limit_info_his")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcCustLimitInfoHisKeyDo implements Serializable {

    private static final long serialVersionUID = 1723869594676035584L;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    /** 历史编号 */
    @Id
    @Column(name = "his_id")
    private String hisId;
    /** 额度编号 */
    @Id
    @Column(name = "cust_limit_id")
    private String custLimitId;
}