package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度实例关联Do
 *
 * <AUTHOR>
 * @date 2022-12-13 03:09:49
 */
@Table(name = "lc_cust_limit_relation")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitRelationDo extends LcCustLimitRelationKeyDo implements Serializable {
    private static final long serialVersionUID = 1602501015033282563L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 当前节点额度编号 */
    @Column(name = "current_node_limit_id")
    private String currentNodeLimitId;
    /** 关系类型;关联关系不校验体系，EnumLimitRelationType:001-默认、002-关联 */
    @Column(name = "limit_relation_type")
    private String limitRelationType;
    /** 父节点额度编号 */
    @Column(name = "parent_node_limit_id")
    private String parentNodeLimitId;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 客户编号 */
    @Column(name = "cust_no")
    private String custNo;
}
