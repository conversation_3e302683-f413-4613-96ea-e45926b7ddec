package com.hsjry.core.limit.batch.dal.dao.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbHElcblIbnkLmtSynzDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbHElcblIbnkLmtSynzMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblIbnkLmtSynzDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblIbnkLmtSynzExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblIbnkLmtSynzKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHElcblIbnkLmtSynzQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-历史表-同业客户额度同步数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Repository
public class LbHElcblIbnkLmtSynzDaoImpl extends AbstractBaseDaoImpl<LbHElcblIbnkLmtSynzDo, LbHElcblIbnkLmtSynzMapper>
    implements LbHElcblIbnkLmtSynzDao {
    /**
     * 分页查询
     *
     * @param lbHElcblIbnkLmtSynz 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbHElcblIbnkLmtSynzDo> selectPage(LbHElcblIbnkLmtSynzQuery lbHElcblIbnkLmtSynz,
        PageParam pageParam) {
        LbHElcblIbnkLmtSynzExample example = buildExample(lbHElcblIbnkLmtSynz);
        return PageHelper.<LbHElcblIbnkLmtSynzDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询电票系统-历史表-同业客户额度同步
     *
     * @param id 主键ID
     * @return
     */
    @Override
    public LbHElcblIbnkLmtSynzDo selectByKey(String id) {
        LbHElcblIbnkLmtSynzKeyDo lbHElcblIbnkLmtSynzKeyDo = new LbHElcblIbnkLmtSynzKeyDo();
        lbHElcblIbnkLmtSynzKeyDo.setId(id);
        return getMapper().selectByPrimaryKey(lbHElcblIbnkLmtSynzKeyDo);
    }

    /**
     * 根据key删除电票系统-历史表-同业客户额度同步
     *
     * @param id 主键ID
     * @return
     */
    @Override
    public int deleteByKey(String id) {
        LbHElcblIbnkLmtSynzKeyDo lbHElcblIbnkLmtSynzKeyDo = new LbHElcblIbnkLmtSynzKeyDo();
        lbHElcblIbnkLmtSynzKeyDo.setId(id);
        return getMapper().deleteByPrimaryKey(lbHElcblIbnkLmtSynzKeyDo);
    }

    /**
     * 查询电票系统-历史表-同业客户额度同步信息
     *
     * @param lbHElcblIbnkLmtSynz 条件
     * @return List<LbHElcblIbnkLmtSynzDo>
     */
    @Override
    public List<LbHElcblIbnkLmtSynzDo> selectByExample(LbHElcblIbnkLmtSynzQuery lbHElcblIbnkLmtSynz) {
        return getMapper().selectByExample(buildExample(lbHElcblIbnkLmtSynz));
    }

    /**
     * 新增电票系统-历史表-同业客户额度同步信息
     *
     * @param lbHElcblIbnkLmtSynz 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbHElcblIbnkLmtSynzDo lbHElcblIbnkLmtSynz) {
        if (lbHElcblIbnkLmtSynz == null) {
            return -1;
        }

        // 生成UUID作为主键ID
        if (lbHElcblIbnkLmtSynz.getId() == null) {
            lbHElcblIbnkLmtSynz.setId(UUID.randomUUID().toString());
        }

        // 获取日期时间对象
        LocalDateTime localDateTime = LocalDateTime.now();
        // 转换为字符串
        String dateTimeString = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        lbHElcblIbnkLmtSynz.setCreateTime(dateTimeString);
        lbHElcblIbnkLmtSynz.setUpdateTime(dateTimeString);
        return getMapper().insertSelective(lbHElcblIbnkLmtSynz);
    }

    /**
     * 修改电票系统-历史表-同业客户额度同步信息
     *
     * @param lbHElcblIbnkLmtSynz
     * @return
     */
    @Override
    public int updateBySelective(LbHElcblIbnkLmtSynzDo lbHElcblIbnkLmtSynz) {
        if (lbHElcblIbnkLmtSynz == null) {
            return -1;
        }
        // 获取日期时间对象
        LocalDateTime localDateTime = LocalDateTime.now();
        // 转换为字符串
        String dateTimeString = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        lbHElcblIbnkLmtSynz.setUpdateTime(dateTimeString);
        return getMapper().updateByPrimaryKeySelective(lbHElcblIbnkLmtSynz);
    }

    @Override
    public int updateBySelectiveByExample(LbHElcblIbnkLmtSynzDo lbHElcblIbnkLmtSynz,
        LbHElcblIbnkLmtSynzQuery lbHElcblIbnkLmtSynzQuery) {
        // 获取日期时间对象
        LocalDateTime localDateTime = LocalDateTime.now();
        // 转换为字符串
        String dateTimeString = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        lbHElcblIbnkLmtSynz.setUpdateTime(dateTimeString);
        return getMapper().updateByExampleSelective(lbHElcblIbnkLmtSynz, buildExample(lbHElcblIbnkLmtSynzQuery));
    }

    /**
     * 构建电票系统-历史表-同业客户额度同步Example信息
     *
     * @param lbHElcblIbnkLmtSynz
     * @return
     */
    public LbHElcblIbnkLmtSynzExample buildExample(LbHElcblIbnkLmtSynzQuery lbHElcblIbnkLmtSynz) {
        LbHElcblIbnkLmtSynzExample example = new LbHElcblIbnkLmtSynzExample();
        LbHElcblIbnkLmtSynzExample.Criteria criteria = example.createCriteria();
        if (lbHElcblIbnkLmtSynz != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbHElcblIbnkLmtSynz.getIbnkUserId())) {
                criteria.andIbnkUserIdEqualTo(lbHElcblIbnkLmtSynz.getIbnkUserId());
            }
            if (StringUtil.isNotEmpty(lbHElcblIbnkLmtSynz.getIbnkUserCertificateKind())) {
                criteria.andIbnkUserCertificateKindEqualTo(lbHElcblIbnkLmtSynz.getIbnkUserCertificateKind());
            }
            if (StringUtil.isNotEmpty(lbHElcblIbnkLmtSynz.getIbnkUserCertificateNo())) {
                criteria.andIbnkUserCertificateNoEqualTo(lbHElcblIbnkLmtSynz.getIbnkUserCertificateNo());
            }
            if (null != lbHElcblIbnkLmtSynz.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbHElcblIbnkLmtSynz.getTotalAmount());
            }
            if (null != lbHElcblIbnkLmtSynz.getAvailableAmount()) {
                criteria.andAvailableAmountEqualTo(lbHElcblIbnkLmtSynz.getAvailableAmount());
            }
            if (null != lbHElcblIbnkLmtSynz.getUseOccupyAmount()) {
                criteria.andUseOccupyAmountEqualTo(lbHElcblIbnkLmtSynz.getUseOccupyAmount());
            }
            if (StringUtil.isNotEmpty(lbHElcblIbnkLmtSynz.getCoreInstNo())) {
                criteria.andCoreInstNoEqualTo(lbHElcblIbnkLmtSynz.getCoreInstNo());
            }
            if (StringUtil.isNotEmpty(lbHElcblIbnkLmtSynz.getDataDate())) {
                criteria.andDataDateEqualTo(lbHElcblIbnkLmtSynz.getDataDate());
            }
        }
        buildExampleExt(lbHElcblIbnkLmtSynz, criteria);
        return example;
    }

    /**
     * 构建电票系统-历史表-同业客户额度同步ExampleExt方法
     *
     * @param lbHElcblIbnkLmtSynz
     * @return
     */
    public void buildExampleExt(LbHElcblIbnkLmtSynzQuery lbHElcblIbnkLmtSynz,
        LbHElcblIbnkLmtSynzExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 批量插入电票系统-历史表-同业客户额度同步信息
     *
     * @param lbHElcblIbnkLmtSynzList 批量数据
     * @return int
     */
    @Override
    public int insertList(List<LbHElcblIbnkLmtSynzDo> lbHElcblIbnkLmtSynzList) {
        if (lbHElcblIbnkLmtSynzList.isEmpty()) {
            return 0;
        }
        
        // 为每条记录生成UUID作为主键ID
        for (LbHElcblIbnkLmtSynzDo item : lbHElcblIbnkLmtSynzList) {
            if (item.getId() == null) {
                item.setId(UUID.randomUUID().toString());
            }
        }
        
        return getMapper().insertList(lbHElcblIbnkLmtSynzList);
    }

    /**
     * 清空电票系统-历史表-同业客户额度同步所有数据
     *
     * @return int
     */
    @Override
    public int deleteAll() {
        return getMapper().deleteAll();
    }

    @Override
    public int deleteByDataDate(String dataDate) {
        return getMapper().deleteByDataDate(dataDate);
    }
}
