/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.core.limit.batch.biz.convert.file.LbSOlProdLmtInfoConverter;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.biz.entity.LbSOlProdLmtInfoData;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.AbstractFileBaseShardingPrepareBizImpl;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.constants.CharsetConstants;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.dal.dao.intf.LbSOlProdLmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlProdLmtInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.sequence.SequenceTool;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
/**
 * 网贷系统产品额度信息文件同步实现类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 15:30
 */
@Slf4j
@Service("lbSOlProdLmtInfoFileSyncImpl")
@RequiredArgsConstructor
public class LbSOlProdLmtInfoFileSyncImpl extends AbstractFileBaseShardingPrepareBizImpl<LbSOlProdLmtInfoData> {

    /** 额度编号列数 */
    private static final int CREDIT_LIMIT_ID_NUM = 1;
    /** 客户编号列数 */
    private static final int USER_ID_NUM = 2;
    /** 客户姓名列数 */
    private static final int USER_NAME_NUM = 3;
    /** 客户类型列数 */
    private static final int USER_TYPE_NUM = 4;
    /** 证件号码列数 */
    private static final int CERTIFICATE_NO_NUM = 5;
    /** 证件类型列数 */
    private static final int CERTIFICATE_TYPE_NUM = 6;
    /** 手机号码列数 */
    private static final int USER_MOBILE_NUM = 7;
    /** 产品编号列数 */
    private static final int PRODUCT_ID_NUM = 8;
    /** 产品名称列数 */
    private static final int PRODUCT_NAME_NUM = 9;
    /** 额度类型列数 */
    private static final int CREDIT_TYPE_NUM = 10;
    /** 状态列数 */
    private static final int STATUS_NUM = 11;
    /** 总额度列数 */
    private static final int TOTAL_AMOUNT_NUM = 12;
    /** 冻结额度列数 */
    private static final int FROZEN_AMOUNT_NUM = 13;
    /** 使用中额度列数 */
    private static final int USING_AMOUNT_NUM = 14;
    /** 已使用额度列数 */
    private static final int USED_AMOUNT_NUM = 15;
    /** 已使用支用次数列数 */
    private static final int USE_LOAN_TIMES_NUM = 16;
    /** 支用次数限制列数 */
    private static final int LOAN_TIMES_LIMIT_NUM = 17;
    /** 生效起始时间列数 */
    private static final int EFFECTIVE_START_TIME_NUM = 18;
    /** 生效结束时间列数 */
    private static final int EFFECTIVE_END_TIME_NUM = 19;
    /** 操作人ID列数 */
    private static final int OPERATOR_ID_NUM = 20;
    /** 所属组织ID列数 */
    private static final int OWN_ORGAN_ID_NUM = 21;
    /** 创建时间列数 */
    private static final int CREATE_TIME_NUM = 22;
    /** 更新时间列数 */
    private static final int UPDATE_TIME_NUM = 23;
    /** 租户号列数 */
    private static final int TENANT_ID_NUM = 24;

    /** 最小字段数量 */
    private static final int MIN_FIELD_COUNT = 24;
    /** 分隔符 */
    private static final String FIELD_SEPARATOR = "\u0003";
    /** 批处理大小 */
    private static final int BATCH_SIZE = 1000;
    private final String SEQUENCE_NO = "SEQUENCE_NO";

    private final LbSOlProdLmtInfoDao lbSOlProdLmtInfoDao;
    @Value("${project.ol.prod.lmt.info.filename:ICM_CREDIT_PERSONAL_LIMIT_DETAIL_[DATE].txt}")
    private String fileName;
    @Value("${limit.batch.remoteFilePath:/app/bftfiles/cfs/recv/ebbs/}")
    private String remoteFilePathDefine;

    @Override
    public ShardingResult<LbSOlProdLmtInfoData> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始文件数据分片查询[{}]", GsonUtil.objToStrForLog(jobShared));

        ShardingResult<LbSOlProdLmtInfoData> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        try {
            // 读取文件分片数据
            FileLineData fileLineData = GsonUtil.json2Obj(jobShared.getExtParam(), FileLineData.class);
            log.info(prefixLog + "开始读取文件数据分片[{}]", GsonUtil.objToStrForLog(fileLineData));
            List<String> originData = FileShardingUtils.readFileSharedData(jobShared, skipFirst(), CharsetConstants.GBK);

            // 使用并行流处理数据，提升性能
            List<LbSOlProdLmtInfoData> fileDataList = processOriginDataParallel(originData, prefixLog);

            log.info(prefixLog + "读取文件数据分片总量[{}]", fileDataList.size());
            shardingResult.setShardingResultList(fileDataList);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        jobShared.setBatchSerialNo(inParam.getString(SEQUENCE_NO));
        return shardingResult;
    }

    private boolean skipFirst() {
        return false;
    }

    private List<LbSOlProdLmtInfoData> processOriginDataParallel(List<String> originData, String prefixLog) {
        if (CollectionUtil.isEmpty(originData)) {
            return Lists.newArrayList();
        }

        AtomicInteger invalidCount = new AtomicInteger(0);
        AtomicInteger parseErrorCount = new AtomicInteger(0);

        List<LbSOlProdLmtInfoData> result = originData.parallelStream()
            .map(item -> parseLineData(item, prefixLog, invalidCount, parseErrorCount))
            .filter(Objects::nonNull)
            .collect(Collectors.toCollection(ArrayList::new));

        log.info(prefixLog + "数据处理完成,总量:[{}],无效数据:[{}],解析错误:[{}]",
            originData.size(), invalidCount.get(), parseErrorCount.get());

        return result;
    }

    private LbSOlProdLmtInfoData parseLineData(String item, String prefixLog, AtomicInteger invalidCount,
        AtomicInteger parseErrorCount) {
        if (StringUtil.isBlank(item)) {
            invalidCount.incrementAndGet();
            return null;
        }

        try {
            String[] fields = item.split(FIELD_SEPARATOR, -1);
            if (fields.length < MIN_FIELD_COUNT) {
                log.warn(prefixLog + "字段数量不足,期望{}个字段,实际{}个字段,数据:[{}]", MIN_FIELD_COUNT, fields.length,
                    item);
                invalidCount.incrementAndGet();
                return null;
            }

            return LbSOlProdLmtInfoData.builder()
                .creditLimitId(StringUtil.isNotEmpty(fields[CREDIT_LIMIT_ID_NUM - 1]) ? fields[CREDIT_LIMIT_ID_NUM - 1] : null)
                .userId(StringUtil.isNotEmpty(fields[USER_ID_NUM - 1]) ? fields[USER_ID_NUM - 1] : null)
                .userName(StringUtil.isNotEmpty(fields[USER_NAME_NUM - 1]) ? fields[USER_NAME_NUM - 1] : null)
                .userType(StringUtil.isNotEmpty(fields[USER_TYPE_NUM - 1]) ? fields[USER_TYPE_NUM - 1] : null)
                .certificateNo(StringUtil.isNotEmpty(fields[CERTIFICATE_NO_NUM - 1]) ? fields[CERTIFICATE_NO_NUM - 1] : null)
                .certificateType(StringUtil.isNotEmpty(fields[CERTIFICATE_TYPE_NUM - 1]) ? fields[CERTIFICATE_TYPE_NUM - 1] : null)
                .userMobile(StringUtil.isNotEmpty(fields[USER_MOBILE_NUM - 1]) ? fields[USER_MOBILE_NUM - 1] : null)
                .productId(StringUtil.isNotEmpty(fields[PRODUCT_ID_NUM - 1]) ? fields[PRODUCT_ID_NUM - 1] : null)
                .productName(StringUtil.isNotEmpty(fields[PRODUCT_NAME_NUM - 1]) ? fields[PRODUCT_NAME_NUM - 1] : null)
                .creditType(StringUtil.isNotEmpty(fields[CREDIT_TYPE_NUM - 1]) ? fields[CREDIT_TYPE_NUM - 1] : null)
                .status(StringUtil.isNotEmpty(fields[STATUS_NUM - 1]) ? fields[STATUS_NUM - 1] : null)
                .totalAmount(parseBigDecimalSafely(fields[TOTAL_AMOUNT_NUM - 1], parseErrorCount))
                .frozenAmount(parseBigDecimalSafely(fields[FROZEN_AMOUNT_NUM - 1], parseErrorCount))
                .usingAmount(parseBigDecimalSafely(fields[USING_AMOUNT_NUM - 1], parseErrorCount))
                .usedAmount(parseBigDecimalSafely(fields[USED_AMOUNT_NUM - 1], parseErrorCount))
                .useLoanTimes(parseIntegerSafely(fields[USE_LOAN_TIMES_NUM - 1], parseErrorCount))
                .loanTimesLimit(parseIntegerSafely(fields[LOAN_TIMES_LIMIT_NUM - 1], parseErrorCount))
                .effectiveStartTime(parseDateTime(fields[EFFECTIVE_START_TIME_NUM - 1]))
                .effectiveEndTime(parseDateTime(fields[EFFECTIVE_END_TIME_NUM - 1]))
                .operatorId(StringUtil.isNotEmpty(fields[OPERATOR_ID_NUM - 1]) ? fields[OPERATOR_ID_NUM - 1] : null)
                .ownOrganId(StringUtil.isNotEmpty(fields[OWN_ORGAN_ID_NUM - 1]) ? fields[OWN_ORGAN_ID_NUM - 1] : null)
                .createTime(parseDateTime(fields[CREATE_TIME_NUM - 1]))
                .updateTime(parseDateTime(fields[UPDATE_TIME_NUM - 1]))
                .tenantId(StringUtil.isNotEmpty(fields[TENANT_ID_NUM - 1]) ? fields[TENANT_ID_NUM - 1] : null)
                .build();
        } catch (Exception e) {
            log.warn(prefixLog + "解析数据异常,数据:[{}]", item, e);
            parseErrorCount.incrementAndGet();
            return null;
        }
    }

    private BigDecimal parseBigDecimalSafely(String value, AtomicInteger parseErrorCount) {
        if (StringUtil.isEmpty(value)) {
            return null;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return null;
        }
    }

    private Integer parseIntegerSafely(String value, AtomicInteger parseErrorCount) {
        if (StringUtil.isEmpty(value)) {
            return null;
        }
        try {
            return Integer.valueOf(value);
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return null;
        }
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbSOlProdLmtInfoData> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
        List<LbSOlProdLmtInfoData> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "网贷系统产品额度信息文件处理:文件分片数据为空,执行中断。");
            return;
        }

        log.info(prefixLog + "网贷系统产品额度信息文件处理:开始执行分片数据操作,数据量:[{}]", dataList.size());

        // 检查是否是第一个分片，如果是则清空表
        if (sliceBatchSerialDo.getBatchNum() == 1) {
            log.info(prefixLog + "第一个分片,清空目标表 lb_s_ol_prod_lmt_info");
            lbSOlProdLmtInfoDao.deleteAll();
        }

        // 确保分片流水对象的状态字段不为null
        if (sliceBatchSerialDo.getSharedStatus() == null) {
            log.debug(prefixLog + "分片流水状态为null,设置为处理中状态");
            sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
        }

        // 使用并行流进行数据转换和验证
        List<LbSOlProdLmtInfoDo> insertList = dataList.parallelStream().map(LbSOlProdLmtInfoConverter::data2Do)//
            .filter(this::validateData).collect(Collectors.toCollection(ArrayList::new));

        if (CollectionUtil.isNotEmpty(insertList)) {
            // 批量插入数据
            processBatchInsert(insertList, prefixLog);
            log.info(prefixLog + "插入[{}]条网贷系统产品额度信息数据", insertList.size());
        }

        // 更新分片流水成功
        normalUpdateSliceSerial(dataList.size(), sliceBatchSerialDo);
        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataList.size());
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.S_OL_PROD_LMT_INFO;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        // 确保fixNum被正确设置
        if (jobInitDto.getFixNum() == null) {
            log.warn(prefixLog + "fixNum为null,设置默认值1000");
            jobInitDto.setFixNum(1000);
        }

        Map<String, Object> jobParameters = jobInitDto.getJobParameters();
        JSONObject param = JSON.parseObject(jobInitDto.getInPara());
        //设置流水ID,解决多次从redis获取唯一值的性能问题
        param.put(SEQUENCE_NO, SequenceTool.nextId());
        //更新jobInitDto的inpara参数
        jobInitDto.setInPara(param.toJSONString());

        List<JobShared> sharedList = Lists.newArrayList();
        log.info(prefixLog + "[{}]文件处理", jobTradeDesc);
        String localFilePath = FileShardingUtils.getLocalFilePath(jobInitDto.getBusinessDate(),
            remoteFilePathDefine + FileShardingUtils.ACCT_DATE_CODE_MARK + File.separator);
        Integer acctDate = jobInitDto.getBusinessDate();
        String localFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK, String.valueOf(acctDate));

        try {
            String fileAttr = FIELD_SEPARATOR;
            String filePath = localFilePath + localFileName;
            log.info("实际查找的文件路径: [{}]", filePath);

            // 检查目录是否存在
            File directory = new File(localFilePath);
            if (!directory.exists()) {
                log.info(prefixLog + "目录[{}]不存在,尝试创建", localFilePath);
                directory.mkdirs();
            } else {
                log.info(prefixLog + "目录[{}]已存在", localFilePath);
                // 列出目录中的文件
                File[] files = directory.listFiles();
                if (files != null && files.length > 0) {
                    log.info(prefixLog + "目录[{}]中的文件列表:", localFilePath);
                    for (File f : files) {
                        log.info(prefixLog + " - {}", f.getName());
                    }
                } else {
                    log.info(prefixLog + "目录[{}]为空", localFilePath);
                }
            }

            File localFile = new File(filePath);
            log.info(prefixLog + "检查文件[{}]是否存在: {}", filePath, localFile.exists());

            //判断本地文件是否存在，不存在则直接报错
            if(!localFile.exists()){
                log.error(prefixLog + "本地文件[{}]不存在,请确认文件路径是否正确", filePath);
                String errorCode = EnumBatchJobError.FILE_PATH_NOT_EXIST.getCode();
                String errorMsg = EnumBatchJobError.FILE_PATH_NOT_EXIST.getDescription();
                throw new HsjryBizException(errorCode, errorMsg);
            }

            // 文件存在，直接进行分片处理
            log.info(prefixLog + "[{}]开始[{}]文件数据分片处理", jobTradeDesc, filePath);
            log.info(prefixLog + "分片参数: fixNum=[{}], fileAttr=[{}], skipFirst=[{}]",
                jobInitDto.getFixNum(), fileAttr, skipFirst());

            List<JobShared> jobShareds = FileShardingUtils.getFileSharedData(jobInitDto, localFile, fileAttr,
                skipFirst());
            log.info(prefixLog + "[{}]结束[{}]文件数据分片当前分片数[{}]", jobTradeDesc, filePath,
                jobShareds.size());
            sharedList.addAll(jobShareds);
        } catch (Exception e) {
            log.error(prefixLog + "[{}]文件分片异常,错误信息:{}", jobTradeDesc, e.getMessage(), e);
            throw new HsjryBizException(EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode(),
                EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription() + ":" + e.getMessage());
        }

        log.info(prefixLog + "文件分片完成,共生成[{}]个分片", sharedList.size());
        return sharedList;
    }

    /**
     * 数据验证
     *
     * @param data 数据对象
     * @return 是否有效
     */
    private boolean validateData(LbSOlProdLmtInfoDo data) {
        if (data == null) {
            log.warn("数据对象为空");
            return false;
        }

        if (StringUtil.isEmpty(data.getCreditLimitId())) {
            log.warn("额度编号为空,跳过该记录");
            return false;
        }

        if (StringUtil.isEmpty(data.getUserId())) {
            log.warn("客户编号为空,跳过该记录,额度编号:[{}]", data.getCreditLimitId());
            return false;
        }

        return true;
    }

    /**
     * 批量插入数据
     *
     * @param insertList 待插入数据列表
     * @param prefixLog 日志前缀
     */
    private void processBatchInsert(List<LbSOlProdLmtInfoDo> insertList, String prefixLog) {
        if (CollectionUtil.isEmpty(insertList)) {
            return;
        }

        // 分批处理
        List<List<LbSOlProdLmtInfoDo>> batchList = Lists.partition(insertList, BATCH_SIZE);
        for (List<LbSOlProdLmtInfoDo> batch : batchList) {
            try {
                int result = lbSOlProdLmtInfoDao.insertList(batch);
                log.info(prefixLog + "批量插入{}条记录,实际插入{}条", batch.size(), result);
            } catch (Exception e) {
                log.error(prefixLog + "批量插入失败", e);
                throw new HsjryBizException(EnumLimitBatchErrorCode.FILE_DATA_ERROR.getCode(),
                    "批量插入网贷系统产品额度信息失败: " + e.getMessage());
            }
        }
    }

    /**
     * 解析日期时间字符串
     *
     * @param dateTimeStr 日期时间字符串
     * @return Date对象
     */
    private java.util.Date parseDateTime(String dateTimeStr) {
        if (StringUtil.isEmpty(dateTimeStr)) {
            return null;
        }
        try {
            // 这里可以根据实际的日期格式进行解析
            return java.sql.Timestamp.valueOf(dateTimeStr);
        } catch (Exception e) {
            log.warn("日期时间解析失败: {}", dateTimeStr, e);
            return null;
        }
    }
}
