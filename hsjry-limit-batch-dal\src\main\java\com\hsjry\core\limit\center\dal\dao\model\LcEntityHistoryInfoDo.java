package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 实体历史信息Do
 *
 * <AUTHOR>
 * @date 2023-04-17 11:39:12
 */
@Table(name = "lc_entity_history_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcEntityHistoryInfoDo extends LcEntityHistoryInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1647927686536036352L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 实体信息 */
    @Column(name = "entity_info")
    private String entityInfo;
    /** 实体限额关系 */
    @Column(name = "entity_amt_limit_rel")
    private String entityAmtLimitRel;
    /** 实体维度信息 */
    @Column(name = "entity_dimension")
    private String entityDimension;
    /** 实体操作流水 */
    @Column(name = "entity_operate_serial")
    private String entityOperateSerial;
    /** 实体统计流水 */
    @Column(name = "entity_statistic_serial")
    private String entityStatisticSerial;
    /** 限额记录操作流水 */
    @Column(name = "amt_limit_record_serial")
    private String amtLimitRecordSerial;
    /** 额度操作流水 */
    @Column(name = "cust_limit_operate_serial")
    private String custLimitOperateSerial;
}
