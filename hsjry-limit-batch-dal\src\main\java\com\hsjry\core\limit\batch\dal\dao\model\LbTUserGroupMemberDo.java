package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度中心-中间表-集团客户成员Do
 *
 * <AUTHOR>
 * @date 2025-07-15 03:50:12
 */
@Table(name = "lb_t_user_group_member")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbTUserGroupMemberDo extends LbTUserGroupMemberKeyDo implements Serializable {
    private static final long serialVersionUID = 1944967709649469440L;
    /** 成员类型 */
    @Column(name = "member_type")
    private String memberType;
    /** 主办客户经理名称 */
    @Column(name = "account_manager_name")
    private String accountManagerName;
    /** 主办客户经理号 */
    @Column(name = "account_manager_no")
    private String accountManagerNo;
    /** 经办行 */
    @Column(name = "handling_agent")
    private String handlingAgent;
    /** 备注 */
    @Column(name = "node")
    private String node;
    /** 认定结果 */
    @Column(name = "member_determination_result")
    private String memberDeterminationResult;
    /** 建议结果 */
    @Column(name = "member_suggested_result")
    private String memberSuggestedResult;
    /** 命中规则 */
    @Column(name = "member_rule")
    private String memberRule;
    /** 客户类型 */
    @Column(name = "user_type")
    private String userType;
    /** 企业性质 */
    @Column(name = "member_enterprise_nature")
    private String memberEnterpriseNature;
    /** 成员证件类别 */
    @Column(name = "member_certificate_no_type")
    private String memberCertificateNoType;
    /** 成员名称 */
    @Column(name = "member_name")
    private String memberName;
    /** 成员证件号码 */
    @Column(name = "member_certificate_no")
    private String memberCertificateNo;
    /** 成员id */
    @Column(name = "user_id")
    private String userId;
    /** 集团客户id */
    @Column(name = "group_user_id")
    private String groupUserId;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 更新人 */
    @Column(name = "modifier")
    private String modifier;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 创建人 */
    @Column(name = "creator")
    private String creator;
}
