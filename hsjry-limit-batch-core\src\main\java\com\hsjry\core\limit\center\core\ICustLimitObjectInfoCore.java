package com.hsjry.core.limit.center.core;

import java.util.List;

import com.hsjry.core.limit.center.core.bo.CustLimitObjectInfoBo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery;

public interface ICustLimitObjectInfoCore {
    /**
     * 查询额度实例所属对象信息
     *
     * @param query 条件
     * @return List<CustLimitObjectInfoBo>
     */
    List<CustLimitObjectInfoBo> enqrByExample(LcCustLimitObjectInfoQuery query);
}
