package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTCptlCorpProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTCptlCorpProdLmtInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 资金系统-中间表-对公客户产品层额度信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-30 02:03:23
 */
public interface LbTCptlCorpProdLmtInfoDao extends IBaseDao<LbTCptlCorpProdLmtInfoDo> {
    /**
     * 分页查询资金系统-中间表-对公客户产品层额度信息
     *
     * @param lbTCptlCorpProdLmtInfoQuery 条件
     * @return PageInfo<LbTCptlCorpProdLmtInfoDo>
     */
    PageInfo<LbTCptlCorpProdLmtInfoDo> selectPage(LbTCptlCorpProdLmtInfoQuery lbTCptlCorpProdLmtInfoQuery,
        PageParam pageParam);

    /**
     * 根据key查询资金系统-中间表-对公客户产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTCptlCorpProdLmtInfoDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除资金系统-中间表-对公客户产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询资金系统-中间表-对公客户产品层额度信息信息
     *
     * @param lbTCptlCorpProdLmtInfoQuery 条件
     * @return List<LbTCptlCorpProdLmtInfoDo>
     */
    List<LbTCptlCorpProdLmtInfoDo> selectByExample(LbTCptlCorpProdLmtInfoQuery lbTCptlCorpProdLmtInfoQuery);

    /**
     * 新增资金系统-中间表-对公客户产品层额度信息信息
     *
     * @param lbTCptlCorpProdLmtInfo 条件
     * @return int>
     */
    int insertBySelective(LbTCptlCorpProdLmtInfoDo lbTCptlCorpProdLmtInfo);

    /**
     * 修改资金系统-中间表-对公客户产品层额度信息信息
     *
     * @param lbTCptlCorpProdLmtInfo
     * @return
     */
    int updateBySelective(LbTCptlCorpProdLmtInfoDo lbTCptlCorpProdLmtInfo);

    /**
     * 修改资金系统-中间表-对公客户产品层额度信息信息
     *
     * @param lbTCptlCorpProdLmtInfo
     * @param lbTCptlCorpProdLmtInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTCptlCorpProdLmtInfoDo lbTCptlCorpProdLmtInfo,
        LbTCptlCorpProdLmtInfoQuery lbTCptlCorpProdLmtInfoQuery);

    /**
     * 往[资金系统-中间表-对公客户产品层额度信息]插入数据
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param limitIdList 额度ID列表
     * @return 插入条数
     */
    int insertCptlCorpProdLmtInfo(List<String> templateNodeIdList, List<String> limitIdList);

    /**
     * 更新[额度实例金额信息]中[实占额度]
     *
     * @param limitIdList 额度ID列表
     * @return 更新条数
     */
    int updateRealOccupyAmount(List<String> limitIdList);
}
