package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 实体统计流水主键
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
@Table(name = "lc_entity_statistic_serial")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcEntityStatisticSerialKeyDo implements Serializable {

    private static final long serialVersionUID = 1602582710713516052L;
    /** 实体统计流水 */
    @Id
    @Column(name = "entity_statistic_id")
    private String entityStatisticId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}