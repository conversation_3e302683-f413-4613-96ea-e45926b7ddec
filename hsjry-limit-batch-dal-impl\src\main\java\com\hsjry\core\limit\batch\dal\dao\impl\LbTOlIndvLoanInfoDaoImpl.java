package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTOlIndvLoanInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTOlIndvLoanInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlIndvLoanInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlIndvLoanInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlIndvLoanInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTOlIndvLoanInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-中间表-个人借据信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
@Repository
public class LbTOlIndvLoanInfoDaoImpl extends AbstractBaseDaoImpl<LbTOlIndvLoanInfoDo, LbTOlIndvLoanInfoMapper>
    implements LbTOlIndvLoanInfoDao {
    /**
     * 分页查询
     *
     * @param lbTOlIndvLoanInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTOlIndvLoanInfoDo> selectPage(LbTOlIndvLoanInfoQuery lbTOlIndvLoanInfo, PageParam pageParam) {
        LbTOlIndvLoanInfoExample example = buildExample(lbTOlIndvLoanInfo);
        return PageHelper.<LbTOlIndvLoanInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询网贷系统-中间表-个人借据信息
     *
     * @param custNo
     * @param custLimitId
     * @param entityId
     * @return
     */
    @Override
    public LbTOlIndvLoanInfoDo selectByKey(String custNo, String custLimitId, String entityId) {
        LbTOlIndvLoanInfoKeyDo lbTOlIndvLoanInfoKeyDo = new LbTOlIndvLoanInfoKeyDo();
        lbTOlIndvLoanInfoKeyDo.setCustNo(custNo);
        lbTOlIndvLoanInfoKeyDo.setCustLimitId(custLimitId);
        lbTOlIndvLoanInfoKeyDo.setEntityId(entityId);
        lbTOlIndvLoanInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTOlIndvLoanInfoKeyDo);
    }

    /**
     * 根据key删除网贷系统-中间表-个人借据信息
     *
     * @param custNo
     * @param custLimitId
     * @param entityId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId, String entityId) {
        LbTOlIndvLoanInfoKeyDo lbTOlIndvLoanInfoKeyDo = new LbTOlIndvLoanInfoKeyDo();
        lbTOlIndvLoanInfoKeyDo.setCustNo(custNo);
        lbTOlIndvLoanInfoKeyDo.setCustLimitId(custLimitId);
        lbTOlIndvLoanInfoKeyDo.setEntityId(entityId);
        lbTOlIndvLoanInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTOlIndvLoanInfoKeyDo);
    }

    /**
     * 查询网贷系统-中间表-个人借据信息信息
     *
     * @param lbTOlIndvLoanInfo 条件
     * @return List<LbTOlIndvLoanInfoDo>
     */
    @Override
    public List<LbTOlIndvLoanInfoDo> selectByExample(LbTOlIndvLoanInfoQuery lbTOlIndvLoanInfo) {
        return getMapper().selectByExample(buildExample(lbTOlIndvLoanInfo));
    }

    /**
     * 新增网贷系统-中间表-个人借据信息信息
     *
     * @param lbTOlIndvLoanInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTOlIndvLoanInfoDo lbTOlIndvLoanInfo) {
        if (lbTOlIndvLoanInfo == null) {
            return -1;
        }

        lbTOlIndvLoanInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTOlIndvLoanInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTOlIndvLoanInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTOlIndvLoanInfo);
    }

    /**
     * 修改网贷系统-中间表-个人借据信息信息
     *
     * @param lbTOlIndvLoanInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTOlIndvLoanInfoDo lbTOlIndvLoanInfo) {
        if (lbTOlIndvLoanInfo == null) {
            return -1;
        }
        lbTOlIndvLoanInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTOlIndvLoanInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTOlIndvLoanInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTOlIndvLoanInfoDo lbTOlIndvLoanInfo,
        LbTOlIndvLoanInfoQuery lbTOlIndvLoanInfoQuery) {
        lbTOlIndvLoanInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTOlIndvLoanInfo, buildExample(lbTOlIndvLoanInfoQuery));
    }

    /**
     * 构建网贷系统-中间表-个人借据信息Example信息
     *
     * @param lbTOlIndvLoanInfo
     * @return
     */
    public LbTOlIndvLoanInfoExample buildExample(LbTOlIndvLoanInfoQuery lbTOlIndvLoanInfo) {
        LbTOlIndvLoanInfoExample example = new LbTOlIndvLoanInfoExample();
        LbTOlIndvLoanInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTOlIndvLoanInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTOlIndvLoanInfo.getEntityRelationId())) {
                criteria.andEntityRelationIdEqualTo(lbTOlIndvLoanInfo.getEntityRelationId());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvLoanInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTOlIndvLoanInfo.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvLoanInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTOlIndvLoanInfo.getOperatorId());
            }
            if (null != lbTOlIndvLoanInfo.getLeftLowRisk()) {
                criteria.andLeftLowRiskEqualTo(lbTOlIndvLoanInfo.getLeftLowRisk());
            }
            if (null != lbTOlIndvLoanInfo.getLowRisk()) {
                criteria.andLowRiskEqualTo(lbTOlIndvLoanInfo.getLowRisk());
            }
            if (null != lbTOlIndvLoanInfo.getLeftAmount()) {
                criteria.andLeftAmountEqualTo(lbTOlIndvLoanInfo.getLeftAmount());
            }
            if (null != lbTOlIndvLoanInfo.getAmount()) {
                criteria.andAmountEqualTo(lbTOlIndvLoanInfo.getAmount());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvLoanInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTOlIndvLoanInfo.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvLoanInfo.getEntityApplyId())) {
                criteria.andEntityApplyIdEqualTo(lbTOlIndvLoanInfo.getEntityApplyId());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvLoanInfo.getStatus())) {
                criteria.andStatusEqualTo(lbTOlIndvLoanInfo.getStatus());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvLoanInfo.getEntityType())) {
                criteria.andEntityTypeEqualTo(lbTOlIndvLoanInfo.getEntityType());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvLoanInfo.getEntityId())) {
                criteria.andEntityIdEqualTo(lbTOlIndvLoanInfo.getEntityId());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvLoanInfo.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTOlIndvLoanInfo.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvLoanInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTOlIndvLoanInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvLoanInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTOlIndvLoanInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvLoanInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTOlIndvLoanInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvLoanInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTOlIndvLoanInfo.getCustTyp());
            }
        }
        buildExampleExt(lbTOlIndvLoanInfo, criteria);
        return example;
    }

    /**
     * 构建网贷系统-中间表-个人借据信息ExampleExt方法
     *
     * @param lbTOlIndvLoanInfo
     * @return
     */
    public void buildExampleExt(LbTOlIndvLoanInfoQuery lbTOlIndvLoanInfo, LbTOlIndvLoanInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 将网贷个人客户额度同步数据插入到借据信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_ol_indv_loan_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    @Override
    public int insertOlIndvLoanInfo(List<String> templateNodeIdList, List<String> custLimitIdList) {
        return getMapper().insertOlIndvLoanInfo(templateNodeIdList, custLimitIdList);
    }

    /**
     * 更新实体信息表
     * 根据网贷系统中间表数据更新LC_ENTITY_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    @Override
    public int updateEntityInfo(List<String> custLimitIdList) {
        return getMapper().updateEntityInfo(custLimitIdList);
    }

    /**
     * 更新实体操作流水表
     * 根据网贷系统中间表数据更新LC_ENTITY_OPERATE_SERIAL表
     *
     * @return int 更新的记录数
     */
    @Override
    public int updateEntityOperateSerial() {
        return getMapper().updateEntityOperateSerial();
    }

}
