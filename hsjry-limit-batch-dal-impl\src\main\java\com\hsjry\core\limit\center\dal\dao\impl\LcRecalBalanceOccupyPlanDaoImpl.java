package com.hsjry.core.limit.center.dal.dao.impl;

import com.hsjry.core.limit.center.dal.dao.model.LcRecalBalanceOccupyPlanDo;
import com.hsjry.core.limit.center.dal.dao.model.LcRecalBalanceOccupyPlanKeyDo;
import com.hsjry.core.limit.center.dal.dao.query.LcRecalBalanceOccupyPlanQuery;
import com.hsjry.core.limit.center.dal.dao.intf.LcRecalBalanceOccupyPlanDao;
import com.hsjry.core.limit.center.dal.dao.mapper.LcRecalBalanceOccupyPlanMapper;
import com.hsjry.core.limit.center.dal.dao.model.LcRecalBalanceOccupyPlanExample;

import org.springframework.stereotype.Repository;

import java.util.List;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.business.date.BusinessDateUtil;

/**
 * 余额占用重算计划(准备)数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2023-06-12 05:41:04
 */
@Repository
public class LcRecalBalanceOccupyPlanDaoImpl
    extends AbstractBaseDaoImpl<LcRecalBalanceOccupyPlanDo, LcRecalBalanceOccupyPlanMapper>
    implements LcRecalBalanceOccupyPlanDao {
    /**
     * 分页查询
     *
     * @param lcRecalBalanceOccupyPlan 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LcRecalBalanceOccupyPlanDo> selectPage(LcRecalBalanceOccupyPlanQuery lcRecalBalanceOccupyPlan,
        PageParam pageParam) {
        LcRecalBalanceOccupyPlanExample example = buildExample(lcRecalBalanceOccupyPlan);
        return PageHelper.<LcRecalBalanceOccupyPlanDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询余额占用重算计划(准备)
     *
     * @param custLimitId
     * @param entityId
     * @return
     */
    @Override
    public LcRecalBalanceOccupyPlanDo selectByKey(String custLimitId, String entityId) {
        LcRecalBalanceOccupyPlanKeyDo lcRecalBalanceOccupyPlanKeyDo = new LcRecalBalanceOccupyPlanKeyDo();
        lcRecalBalanceOccupyPlanKeyDo.setCustLimitId(custLimitId);
        lcRecalBalanceOccupyPlanKeyDo.setEntityId(entityId);
        lcRecalBalanceOccupyPlanKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lcRecalBalanceOccupyPlanKeyDo);
    }

    /**
     * 根据key删除余额占用重算计划(准备)
     *
     * @param custLimitId
     * @param entityId
     * @return
     */
    @Override
    public int deleteByKey(String custLimitId, String entityId) {
        LcRecalBalanceOccupyPlanKeyDo lcRecalBalanceOccupyPlanKeyDo = new LcRecalBalanceOccupyPlanKeyDo();
        lcRecalBalanceOccupyPlanKeyDo.setCustLimitId(custLimitId);
        lcRecalBalanceOccupyPlanKeyDo.setEntityId(entityId);
        lcRecalBalanceOccupyPlanKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lcRecalBalanceOccupyPlanKeyDo);
    }

    /**
     * 查询余额占用重算计划(准备)信息
     *
     * @param lcRecalBalanceOccupyPlan 条件
     * @return List<LcRecalBalanceOccupyPlanDo>
     */
    @Override
    public List<LcRecalBalanceOccupyPlanDo> selectByExample(LcRecalBalanceOccupyPlanQuery lcRecalBalanceOccupyPlan) {
        return getMapper().selectByExample(buildExample(lcRecalBalanceOccupyPlan));
    }

    /**
     * 新增余额占用重算计划(准备)信息
     *
     * @param lcRecalBalanceOccupyPlan 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LcRecalBalanceOccupyPlanDo lcRecalBalanceOccupyPlan) {
        if (lcRecalBalanceOccupyPlan == null) {
            return -1;
        }

        lcRecalBalanceOccupyPlan.setCreateTime(BusinessDateUtil.getDate());
        lcRecalBalanceOccupyPlan.setUpdateTime(BusinessDateUtil.getDate());
        lcRecalBalanceOccupyPlan.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lcRecalBalanceOccupyPlan);
    }

    /**
     * 修改余额占用重算计划(准备)信息
     *
     * @param lcRecalBalanceOccupyPlan
     * @return
     */
    @Override
    public int updateBySelective(LcRecalBalanceOccupyPlanDo lcRecalBalanceOccupyPlan) {
        if (lcRecalBalanceOccupyPlan == null) {
            return -1;
        }
        lcRecalBalanceOccupyPlan.setUpdateTime(BusinessDateUtil.getDate());
        lcRecalBalanceOccupyPlan.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lcRecalBalanceOccupyPlan);
    }

    @Override
    public int updateBySelectiveByExample(LcRecalBalanceOccupyPlanDo lcRecalBalanceOccupyPlan,
        LcRecalBalanceOccupyPlanQuery lcRecalBalanceOccupyPlanQuery) {
        lcRecalBalanceOccupyPlan.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lcRecalBalanceOccupyPlan,
            buildExample(lcRecalBalanceOccupyPlanQuery));
    }

    /**
     * 构建余额占用重算计划(准备)Example信息
     *
     * @param lcRecalBalanceOccupyPlan
     * @return
     */
    public LcRecalBalanceOccupyPlanExample buildExample(LcRecalBalanceOccupyPlanQuery lcRecalBalanceOccupyPlan) {
        LcRecalBalanceOccupyPlanExample example = new LcRecalBalanceOccupyPlanExample();
        LcRecalBalanceOccupyPlanExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lcRecalBalanceOccupyPlan != null) {
            //添加查询条件
            if (null != lcRecalBalanceOccupyPlan.getLowRiskAmtBalance()) {
                criteria.andLowRiskAmtBalanceEqualTo(lcRecalBalanceOccupyPlan.getLowRiskAmtBalance());
            }
            if (null != lcRecalBalanceOccupyPlan.getCaPreAmountShare()) {
                criteria.andCaPreAmountShareEqualTo(lcRecalBalanceOccupyPlan.getCaPreAmountShare());
            }
            if (null != lcRecalBalanceOccupyPlan.getCaAmountShare()) {
                criteria.andCaAmountShareEqualTo(lcRecalBalanceOccupyPlan.getCaAmountShare());
            }
            if (null != lcRecalBalanceOccupyPlan.getPreAmountShare()) {
                criteria.andPreAmountShareEqualTo(lcRecalBalanceOccupyPlan.getPreAmountShare());
            }
            if (null != lcRecalBalanceOccupyPlan.getAmountShare()) {
                criteria.andAmountShareEqualTo(lcRecalBalanceOccupyPlan.getAmountShare());
            }
            if (null != lcRecalBalanceOccupyPlan.getExchangeRateVersion()) {
                criteria.andExchangeRateVersionEqualTo(lcRecalBalanceOccupyPlan.getExchangeRateVersion());
            }
            if (null != lcRecalBalanceOccupyPlan.getNewExchangeRateVersion()) {
                criteria.andNewExchangeRateVersionEqualTo(lcRecalBalanceOccupyPlan.getNewExchangeRateVersion());
            }
            if (null != lcRecalBalanceOccupyPlan.getCaPreLowRiskAmtBalance()) {
                criteria.andCaPreLowRiskAmtBalanceEqualTo(lcRecalBalanceOccupyPlan.getCaPreLowRiskAmtBalance());
            }
            if (null != lcRecalBalanceOccupyPlan.getCaLowRiskAmtBalance()) {
                criteria.andCaLowRiskAmtBalanceEqualTo(lcRecalBalanceOccupyPlan.getCaLowRiskAmtBalance());
            }
            if (null != lcRecalBalanceOccupyPlan.getPreLowRiskAmtBalance()) {
                criteria.andPreLowRiskAmtBalanceEqualTo(lcRecalBalanceOccupyPlan.getPreLowRiskAmtBalance());
            }
            if (null != lcRecalBalanceOccupyPlan.getCaAmountBalance()) {
                criteria.andCaAmountBalanceEqualTo(lcRecalBalanceOccupyPlan.getCaAmountBalance());
            }
            if (null != lcRecalBalanceOccupyPlan.getCaPreAmountBalance()) {
                criteria.andCaPreAmountBalanceEqualTo(lcRecalBalanceOccupyPlan.getCaPreAmountBalance());
            }
            if (null != lcRecalBalanceOccupyPlan.getPreAmountBalance()) {
                criteria.andPreAmountBalanceEqualTo(lcRecalBalanceOccupyPlan.getPreAmountBalance());
            }
            if (null != lcRecalBalanceOccupyPlan.getAmountBalance()) {
                criteria.andAmountBalanceEqualTo(lcRecalBalanceOccupyPlan.getAmountBalance());
            }
            if (StringUtil.isNotEmpty(lcRecalBalanceOccupyPlan.getLimitObjectId())) {
                criteria.andLimitObjectIdEqualTo(lcRecalBalanceOccupyPlan.getLimitObjectId());
            }
            if (StringUtil.isNotEmpty(lcRecalBalanceOccupyPlan.getEntityId())) {
                criteria.andEntityIdEqualTo(lcRecalBalanceOccupyPlan.getEntityId());
            }
            if (StringUtil.isNotEmpty(lcRecalBalanceOccupyPlan.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lcRecalBalanceOccupyPlan.getCustLimitId());
            }
        }
        buildExampleExt(lcRecalBalanceOccupyPlan, criteria);
        return example;
    }

    /**
     * 构建余额占用重算计划(准备)ExampleExt方法
     *
     * @param lcRecalBalanceOccupyPlan
     * @return
     */
    public void buildExampleExt(LcRecalBalanceOccupyPlanQuery lcRecalBalanceOccupyPlan,
        LcRecalBalanceOccupyPlanExample.Criteria criteria) {
        if (CollectionUtil.isNotEmpty(lcRecalBalanceOccupyPlan.getCustLimitIdList())) {
            criteria.andCustLimitIdIn(lcRecalBalanceOccupyPlan.getCustLimitIdList());
        }
        //自定义实现
    }

    @Override
    public String selectFirstOne(LcRecalBalanceOccupyPlanQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectFirstOne(query);
    }

    @Override
    public Integer selectCountByCurrentGroup(LcRecalBalanceOccupyPlanQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectCountByCurrentGroup(query);
    }

    @Override
    public List<LcRecalBalanceOccupyPlanDo> selectShardList(LcRecalBalanceOccupyPlanQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectShardList(query);
    }

    @Override
    public int deleteAll() {
        return getMapper().deleteAll(AppParamUtil.getTenantId());
    }

}
