package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitTemplateDo;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitTemplateQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 限额模板数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcAmtLimitTemplateDao extends IBaseDao<LcAmtLimitTemplateDo> {
    /**
     * 分页查询限额模板
     *
     * @param lcAmtLimitTemplateQuery 条件
     * @return PageInfo<LcAmtLimitTemplateDo>
     */
    PageInfo<LcAmtLimitTemplateDo> selectPage(LcAmtLimitTemplateQuery lcAmtLimitTemplateQuery, PageParam pageParam);

    /**
     * 根据key查询限额模板
     *
     * @param templateId
     * @return
     */
    LcAmtLimitTemplateDo selectByKey(String templateId);

    /**
     * 根据key删除限额模板
     *
     * @param templateId
     * @return
     */
    int deleteByKey(String templateId);

    /**
     * 查询限额模板信息
     *
     * @param lcAmtLimitTemplateQuery 条件
     * @return List<LcAmtLimitTemplateDo>
     */
    List<LcAmtLimitTemplateDo> selectByExample(LcAmtLimitTemplateQuery lcAmtLimitTemplateQuery);

    /**
     * 新增限额模板信息
     *
     * @param lcAmtLimitTemplate 条件
     * @return int>
     */
    int insertBySelective(LcAmtLimitTemplateDo lcAmtLimitTemplate);

    /**
     * 修改限额模板信息
     *
     * @param lcAmtLimitTemplate
     * @return
     */
    int updateBySelective(LcAmtLimitTemplateDo lcAmtLimitTemplate);

    /**
     * 修改限额模板信息
     *
     * @param lcAmtLimitTemplate
     * @param lcAmtLimitTemplateQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcAmtLimitTemplateDo lcAmtLimitTemplate,
        LcAmtLimitTemplateQuery lcAmtLimitTemplateQuery);
}
