package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTElcblCorpLoanInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTElcblCorpLoanInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpLoanInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpLoanInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpLoanInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTElcblCorpLoanInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

import lombok.extern.slf4j.Slf4j;

/**
 * 电票系统-中间表-贴现对公借据信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-29 02:46:35
 */
@Slf4j
@Repository
public class LbTElcblCorpLoanInfoDaoImpl extends AbstractBaseDaoImpl<LbTElcblCorpLoanInfoDo, LbTElcblCorpLoanInfoMapper>
    implements LbTElcblCorpLoanInfoDao {
    /**
     * 分页查询
     *
     * @param lbTElcblCorpLoanInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTElcblCorpLoanInfoDo> selectPage(LbTElcblCorpLoanInfoQuery lbTElcblCorpLoanInfo,
        PageParam pageParam) {
        LbTElcblCorpLoanInfoExample example = buildExample(lbTElcblCorpLoanInfo);
        return PageHelper.<LbTElcblCorpLoanInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询电票系统-中间表-贴现对公借据信息
     *
     * @param custNo
     * @param custLimitId
     * @param entityId
     * @return
     */
    @Override
    public LbTElcblCorpLoanInfoDo selectByKey(String custNo, String custLimitId, String entityId) {
        LbTElcblCorpLoanInfoKeyDo lbTElcblCorpLoanInfoKeyDo = new LbTElcblCorpLoanInfoKeyDo();
        lbTElcblCorpLoanInfoKeyDo.setCustNo(custNo);
        lbTElcblCorpLoanInfoKeyDo.setCustLimitId(custLimitId);
        lbTElcblCorpLoanInfoKeyDo.setEntityId(entityId);
        lbTElcblCorpLoanInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTElcblCorpLoanInfoKeyDo);
    }

    /**
     * 根据key删除电票系统-中间表-贴现对公借据信息
     *
     * @param custNo
     * @param custLimitId
     * @param entityId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId, String entityId) {
        LbTElcblCorpLoanInfoKeyDo lbTElcblCorpLoanInfoKeyDo = new LbTElcblCorpLoanInfoKeyDo();
        lbTElcblCorpLoanInfoKeyDo.setCustNo(custNo);
        lbTElcblCorpLoanInfoKeyDo.setCustLimitId(custLimitId);
        lbTElcblCorpLoanInfoKeyDo.setEntityId(entityId);
        lbTElcblCorpLoanInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTElcblCorpLoanInfoKeyDo);
    }

    /**
     * 查询电票系统-中间表-贴现对公借据信息信息
     *
     * @param lbTElcblCorpLoanInfo 条件
     * @return List<LbTElcblCorpLoanInfoDo>
     */
    @Override
    public List<LbTElcblCorpLoanInfoDo> selectByExample(LbTElcblCorpLoanInfoQuery lbTElcblCorpLoanInfo) {
        return getMapper().selectByExample(buildExample(lbTElcblCorpLoanInfo));
    }

    /**
     * 新增电票系统-中间表-贴现对公借据信息信息
     *
     * @param lbTElcblCorpLoanInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTElcblCorpLoanInfoDo lbTElcblCorpLoanInfo) {
        if (lbTElcblCorpLoanInfo == null) {
            return -1;
        }

        lbTElcblCorpLoanInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTElcblCorpLoanInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTElcblCorpLoanInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTElcblCorpLoanInfo);
    }

    /**
     * 修改电票系统-中间表-贴现对公借据信息信息
     *
     * @param lbTElcblCorpLoanInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTElcblCorpLoanInfoDo lbTElcblCorpLoanInfo) {
        if (lbTElcblCorpLoanInfo == null) {
            return -1;
        }
        lbTElcblCorpLoanInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTElcblCorpLoanInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTElcblCorpLoanInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTElcblCorpLoanInfoDo lbTElcblCorpLoanInfo,
        LbTElcblCorpLoanInfoQuery lbTElcblCorpLoanInfoQuery) {
        lbTElcblCorpLoanInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTElcblCorpLoanInfo, buildExample(lbTElcblCorpLoanInfoQuery));
    }

    /**
     * 构建电票系统-中间表-贴现对公借据信息Example信息
     *
     * @param lbTElcblCorpLoanInfo
     * @return
     */
    public LbTElcblCorpLoanInfoExample buildExample(LbTElcblCorpLoanInfoQuery lbTElcblCorpLoanInfo) {
        LbTElcblCorpLoanInfoExample example = new LbTElcblCorpLoanInfoExample();
        LbTElcblCorpLoanInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTElcblCorpLoanInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTElcblCorpLoanInfo.getEntityRelationId())) {
                criteria.andEntityRelationIdEqualTo(lbTElcblCorpLoanInfo.getEntityRelationId());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpLoanInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTElcblCorpLoanInfo.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpLoanInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTElcblCorpLoanInfo.getOperatorId());
            }
            if (null != lbTElcblCorpLoanInfo.getLeftLowRisk()) {
                criteria.andLeftLowRiskEqualTo(lbTElcblCorpLoanInfo.getLeftLowRisk());
            }
            if (null != lbTElcblCorpLoanInfo.getLowRisk()) {
                criteria.andLowRiskEqualTo(lbTElcblCorpLoanInfo.getLowRisk());
            }
            if (null != lbTElcblCorpLoanInfo.getLeftAmount()) {
                criteria.andLeftAmountEqualTo(lbTElcblCorpLoanInfo.getLeftAmount());
            }
            if (null != lbTElcblCorpLoanInfo.getAmount()) {
                criteria.andAmountEqualTo(lbTElcblCorpLoanInfo.getAmount());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpLoanInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTElcblCorpLoanInfo.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpLoanInfo.getEntityApplyId())) {
                criteria.andEntityApplyIdEqualTo(lbTElcblCorpLoanInfo.getEntityApplyId());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpLoanInfo.getStatus())) {
                criteria.andStatusEqualTo(lbTElcblCorpLoanInfo.getStatus());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpLoanInfo.getEntityType())) {
                criteria.andEntityTypeEqualTo(lbTElcblCorpLoanInfo.getEntityType());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpLoanInfo.getEntityId())) {
                criteria.andEntityIdEqualTo(lbTElcblCorpLoanInfo.getEntityId());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpLoanInfo.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTElcblCorpLoanInfo.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpLoanInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTElcblCorpLoanInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpLoanInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTElcblCorpLoanInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpLoanInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTElcblCorpLoanInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpLoanInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTElcblCorpLoanInfo.getCustTyp());
            }
        }
        buildExampleExt(lbTElcblCorpLoanInfo, criteria);
        return example;
    }

    /**
     * 构建电票系统-中间表-贴现对公借据信息ExampleExt方法
     *
     * @param lbTElcblCorpLoanInfo
     * @return
     */
    public void buildExampleExt(LbTElcblCorpLoanInfoQuery lbTElcblCorpLoanInfo,
        LbTElcblCorpLoanInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 将电票对公客户额度同步数据插入到借据信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_elcbl_corp_loan_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    @Override
    public int insertElcblCorpLoanInfo(List<String> templateNodeIdList, List<String> custLimitIdList) {
        if (CollectionUtils.isEmpty(templateNodeIdList) || CollectionUtils.isEmpty(custLimitIdList)) {
            log.warn("模板节点ID列表或客户额度ID列表为空，跳过插入操作");
            return 0;
        }
        return getMapper().insertElcblCorpLoanInfo(templateNodeIdList, custLimitIdList);
    }

    /**
     * 更新实体信息表
     * 根据电票系统中间表数据更新LC_ENTITY_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    @Override
    public int updateEntityInfo(List<String> custLimitIdList) {
        if (CollectionUtils.isEmpty(custLimitIdList)) {
            log.warn("客户额度ID列表为空，跳过更新实体信息操作");
            return 0;
        }
        return getMapper().updateEntityInfo(custLimitIdList);
    }

    /**
     * 更新实体操作流水表
     * 根据电票系统中间表数据更新LC_ENTITY_OPERATE_SERIAL表
     *
     * @return int 更新的记录数
     */
    @Override
    public int updateEntityOperateSerial() {
        return getMapper().updateEntityOperateSerial();
    }

}
