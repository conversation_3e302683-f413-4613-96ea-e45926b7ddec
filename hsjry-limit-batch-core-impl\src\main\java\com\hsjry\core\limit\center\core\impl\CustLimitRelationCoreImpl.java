package com.hsjry.core.limit.center.core.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.hsjry.core.limit.center.core.ICustLimitRelationCore;
import com.hsjry.core.limit.center.core.bo.CustLimitRelationBo;
import com.hsjry.core.limit.center.core.convert.LcCustLimitRelationDoCnvs;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitRelationDao;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitRelationQuery;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustLimitRelationCoreImpl implements ICustLimitRelationCore {
    private final LcCustLimitRelationDao custLimitRelationDao;

    @Override
    public List<CustLimitRelationBo> enqrByExample(LcCustLimitRelationQuery query) {
        return LcCustLimitRelationDoCnvs.INSTANCE.cnvsDoListToBoList(custLimitRelationDao.selectByExample(query));
    }
}