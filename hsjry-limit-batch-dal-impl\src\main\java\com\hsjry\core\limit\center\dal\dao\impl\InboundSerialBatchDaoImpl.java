/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.intf.InboundSerialBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.InboundSerialBatchMapper;
import com.hsjry.core.limit.center.dal.dao.query.InboundSerialBatchQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcInboundSerialDo;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/4/12 14:44
 */
@Repository
public class InboundSerialBatchDaoImpl extends AbstractBaseDaoImpl<LcInboundSerialDo, InboundSerialBatchMapper>
    implements InboundSerialBatchDao {
    @Override
    public List<LcInboundSerialDo> selectShardList(InboundSerialBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectShardList(query);
    }

    @Override
    public LcInboundSerialDo selectFirstOne(InboundSerialBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectFirstOne(query);
    }

    @Override
    public Integer selectCountByCurrentGroup(InboundSerialBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectCountByCurrentGroup(query);
    }
}
