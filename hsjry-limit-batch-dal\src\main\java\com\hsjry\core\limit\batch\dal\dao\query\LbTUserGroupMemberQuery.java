package com.hsjry.core.limit.batch.dal.dao.query;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * 额度中心-中间表-集团客户成员查询条件
 *
 * <AUTHOR>
 * @date 2025-07-15 03:50:12
 */
@Data
@Builder
public class LbTUserGroupMemberQuery implements Serializable {

    /** serialVersionUID */
    private static final long serialVersionUID = 1944967709649469440L;

    /** 成员类型 */
    private String memberType;
    /** 主办客户经理名称 */
    private String accountManagerName;
    /** 主办客户经理号 */
    private String accountManagerNo;
    /** 经办行 */
    private String handlingAgent;
    /** 备注 */
    private String node;
    /** 认定结果 */
    private String memberDeterminationResult;
    /** 建议结果 */
    private String memberSuggestedResult;
    /** 命中规则 */
    private String memberRule;
    /** 客户类型 */
    private String userType;
    /** 企业性质 */
    private String memberEnterpriseNature;
    /** 成员证件类别 */
    private String memberCertificateNoType;
    /** 成员名称 */
    private String memberName;
    /** 成员证件号码 */
    private String memberCertificateNo;
    /** 成员id */
    private String userId;
    /** 集团客户id */
    private String groupUserId;
    /** 资源项id */
    private String resourceId;
    /** 更新人 */
    private String modifier;
    /** 创建人 */
    private String creator;
}
