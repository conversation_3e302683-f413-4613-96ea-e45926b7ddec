package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度体系模板Do
 *
 * <AUTHOR>
 * @date 2022-12-07 06:17:16
 */
@Table(name = "lc_cust_limit_template")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitTemplateDo extends LcCustLimitTemplateKeyDo implements Serializable {
    private static final long serialVersionUID = 1600373860908335104L;
    /** 适用客户类型;EnumUserType:100-机构客户、200-个人客户、110-同业客户、120-集团客户 */
    @Column(name = "applicable_user_type")
    private String applicableUserType;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 体系模板名称 */
    @Column(name = "limit_template_name")
    private String limitTemplateName;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 体系描述 */
    @Column(name = "remark")
    private String remark;
    /** 状态;EnumLimitTemplateStatus 001-草稿,002-上架，003-下架 */
    @Column(name = "template_status")
    private String templateStatus;
    /** 体系模板类型;EnumLimitTemplateType:001-单一体系、002-复合体系 */
    @Column(name = "template_type")
    private String templateType;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
}
