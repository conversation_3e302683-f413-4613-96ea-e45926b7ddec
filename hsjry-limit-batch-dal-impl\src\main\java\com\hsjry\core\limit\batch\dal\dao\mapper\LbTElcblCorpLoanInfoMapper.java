package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpLoanInfoDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 电票系统-中间表-贴现对公借据信息mapper
 *
 * <AUTHOR>
 * @date 2025-07-29 02:46:35
 */
public interface LbTElcblCorpLoanInfoMapper extends CommonMapper<LbTElcblCorpLoanInfoDo> {

    /**
     * 将电票对公客户额度同步数据插入到借据信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_elcbl_corp_loan_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    int insertElcblCorpLoanInfo(@Param("templateNodeIdList") List<String> templateNodeIdList,
        @Param("custLimitIdList") List<String> custLimitIdList);

    /**
     * 更新实体信息表
     * 根据电票系统中间表数据更新LC_ENTITY_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    int updateEntityInfo(@Param("custLimitIdList") List<String> custLimitIdList);

    /**
     * 更新实体操作流水表
     * 根据电票系统中间表数据更新LC_ENTITY_OPERATE_SERIAL表
     *
     * @return int 更新的记录数
     */
    int updateEntityOperateSerial();
}