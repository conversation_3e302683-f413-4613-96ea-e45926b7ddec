package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 限额维度定义主键
 *
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
@Table(name = "lc_limit_dimension_define")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcLimitDimensionDefineKeyDo implements Serializable {

    private static final long serialVersionUID = 1673314101088157701L;
        /** 维度编码 */
    @Id
    @Column(name = "dimension_code")
    private String dimensionCode;
        /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    }