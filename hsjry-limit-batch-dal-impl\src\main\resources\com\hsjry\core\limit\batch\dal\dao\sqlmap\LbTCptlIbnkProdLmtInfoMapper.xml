<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbTCptlIbnkProdLmtInfoMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbTCptlIbnkProdLmtInfoDo">
        <result property="custNo" column="cust_no" jdbcType="VARCHAR"/> <!-- 客户编号 -->
        <result property="custTyp" column="cust_typ" jdbcType="VARCHAR"/> <!-- 客户类型 -->
        <result property="custNm" column="cust_nm" jdbcType="VARCHAR"/> <!-- 客户名称 -->
        <result property="certTyp" column="cert_typ" jdbcType="VARCHAR"/> <!-- 证件类型 -->
        <result property="certNo" column="cert_no" jdbcType="VARCHAR"/> <!-- 证件号码 -->
        <result property="usedAmount" column="used_amount" jdbcType="DECIMAL"/> <!-- 已用额度 -->
        <result property="custLimitId" column="cust_limit_id" jdbcType="VARCHAR"/> <!-- 额度编号 -->
        <result property="limitStatus" column="limit_status" jdbcType="VARCHAR"/> <!-- 额度状态 -->
        <result property="realOccupyAmount" column="real_occupy_amount" jdbcType="DECIMAL"/> <!-- 额度实例金额信息中实占额度 -->
        <result property="coreInstNo" column="core_inst_no" jdbcType="VARCHAR"/> <!-- 核心机构号 -->
        <result property="operatorId" column="operator_id" jdbcType="VARCHAR"/> <!-- 操作人编号 -->
        <result property="ownOrganId" column="own_organ_id" jdbcType="VARCHAR"/> <!-- 所属组织编号 -->
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/> <!-- 租户号 -->
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
    </resultMap>
    <sql id="Base_Column_List">
        cust_no
        , cust_typ
                , cust_nm
                , cert_typ
                , cert_no
                , used_amount
                , cust_limit_id
                , limit_status
                , real_occupy_amount
                , core_inst_no
                , operator_id
                , own_organ_id
                , tenant_id
                , create_time
                , update_time
    </sql>

    <!-- 往[资金系统-中间表-同业客户产品层额度信息]插入数据 -->
    <insert id="insertCptlIbnkProdLmtInfo" parameterType="java.util.Map">
        insert into LB_T_CPTL_IBNK_PROD_LMT_INFO(cust_no, cust_typ, cust_nm, cert_typ, cert_no, used_amount,
        cust_limit_id,
        limit_status, real_occupy_amount, core_inst_no, operator_id, own_organ_id,
        tenant_id, create_time, update_time)
        select ltici.CUST_NO,
        ltici.CUST_TYP,
        ltici.CUST_NM,
        ltici.cert_typ,
        ltici.cert_no,
        lscblus.USED_AMOUNT,
        lcli.CUST_LIMIT_ID,
        lcli.limit_status,
        lscblus.USED_AMOUNT as real_occupy_amount,
        lscblus.CORE_INST_NO,
        lcli.operator_id,
        lcli.own_organ_id,
        lcli.TENANT_ID,
        sysdate as create_time,
        sysdate as update_time
        from lb_s_cptl_biz_lmt_use_sttn lscblus
        inner join LB_T_IBNK_CUST_INFO ltici on lscblus.USER_ID = ltici.CUST_NO
        inner join LC_CUST_LIMIT_INFO lcli on lcli.LIMIT_OBJECT_ID = ltici.CUST_NO
        where lcli.TEMPLATE_NODE_ID in
        <if test="templateNodeIdList != null and templateNodeIdList.size() > 0">
            <foreach collection="templateNodeIdList" item="templateNodeId" open="(" separator="," close=")">
                #{templateNodeId}
            </foreach>
        </if>
        <if test="templateNodeIdList == null or templateNodeIdList.size() == 0">
            (null)
        </if>
        and lcli.CUST_LIMIT_ID in
        <if test="limitIdList != null and limitIdList.size() > 0">
            <foreach collection="limitIdList" item="limitId" open="(" separator="," close=")">
                #{limitId}
            </foreach>
        </if>
        <if test="limitIdList == null or limitIdList.size() == 0">
            (null)
        </if>
    </insert>

    <!-- 更新[额度实例金额信息]中[实占额度] -->
    <update id="updateRealOccupyAmount" parameterType="java.util.Map">
        MERGE INTO LC_CUST_LIMIT_AMT_INFO lclai
        USING LB_T_CPTL_IBNK_PROD_LMT_INFO ltcipli
        ON (lclai.CUST_LIMIT_ID = ltcipli.CUST_LIMIT_ID AND lclai.CUST_NO = ltcipli.CUST_NO)
        WHEN MATCHED THEN
        UPDATE
        SET lclai.REAL_OCCUPY_AMOUNT = ltcipli.REAL_OCCUPY_AMOUNT
        WHERE lclai.CUST_LIMIT_ID IN
        <if test="limitIdList != null and limitIdList.size() > 0">
            <foreach collection="limitIdList" item="limitId" open="(" separator="," close=")">
                #{limitId}
            </foreach>
        </if>
        <if test="limitIdList == null or limitIdList.size() == 0">
            (null)
        </if>
    </update>

</mapper>