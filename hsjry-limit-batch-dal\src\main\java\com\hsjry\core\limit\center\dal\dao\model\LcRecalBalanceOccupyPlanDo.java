package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 余额占用重算计划(准备)Do
 *
 * <AUTHOR>
 * @date 2023-06-12 05:41:04
 */
@Table(name = "lc_recal_balance_occupy_plan")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcRecalBalanceOccupyPlanDo extends LcRecalBalanceOccupyPlanKeyDo implements Serializable {
    private static final long serialVersionUID = 1668131282032459776L;
    /** 实体低风险额度余额 */
    @Column(name = "low_risk_amt_balance")
    private java.math.BigDecimal lowRiskAmtBalance;
    /** 撤销实体串用预发放余额 */
    @Column(name = "ca_pre_amount_share")
    private java.math.BigDecimal caPreAmountShare;
    /** 撤销实体串用余额 */
    @Column(name = "ca_amount_share")
    private java.math.BigDecimal caAmountShare;
    /** 实体串用预发放余额 */
    @Column(name = "pre_amount_share")
    private java.math.BigDecimal preAmountShare;
    /** 实体串用余额 */
    @Column(name = "amount_share")
    private java.math.BigDecimal amountShare;
    /** 汇率版本 */
    @Column(name = "exchange_rate_version")
    private Integer exchangeRateVersion;
    /** 新汇率版本 */
    @Column(name = "new_exchange_rate_version")
    private Integer newExchangeRateVersion;
    /** 撤销预发实体低风险额度余额 */
    @Column(name = "ca_pre_low_risk_amt_balance")
    private java.math.BigDecimal caPreLowRiskAmtBalance;
    /** 撤销实体低风险额度余额 */
    @Column(name = "ca_low_risk_amt_balance")
    private java.math.BigDecimal caLowRiskAmtBalance;
    /** 预发实体低风险额度余额 */
    @Column(name = "pre_low_risk_amt_balance")
    private java.math.BigDecimal preLowRiskAmtBalance;
    /** 撤销实体额度余额 */
    @Column(name = "ca_amount_balance")
    private java.math.BigDecimal caAmountBalance;
    /** 撤销预发实体额度余额 */
    @Column(name = "ca_pre_amount_balance")
    private java.math.BigDecimal caPreAmountBalance;
    /** 预发实体额度余额 */
    @Column(name = "pre_amount_balance")
    private java.math.BigDecimal preAmountBalance;
    /** 实体额度余额 */
    @Column(name = "amount_balance")
    private java.math.BigDecimal amountBalance;
    /** 所属对象编号 */
    @Column(name = "limit_object_id")
    private String limitObjectId;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
}
