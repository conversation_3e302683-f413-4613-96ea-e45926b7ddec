package com.hsjry.core.limit.batch.biz.job.sharding.biz.mid;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.customer.EnumUserType;
import com.hsjry.base.common.model.enums.limit.EnumCustLimitStatus;
import com.hsjry.base.common.model.enums.limit.EnumLmtTplNode;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.dal.dao.intf.LbSOlProdLmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTOlCorpProdLmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSOlProdLmtInfoQuery;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitInfoDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitObjectInfoDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 网贷对公额度处理
 *
 * <AUTHOR>
 * @date 2025-07-10 12:31:05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LbTOlCorpProdLmtInfoProcessImpl extends AbstractShardingPrepareBiz<LbSOlProdLmtInfoQuery>
    implements JobCoreBusiness<LbSOlProdLmtInfoDo> {

    private final LbSOlProdLmtInfoDao lbSOlProdLmtInfoDao;
    private final LbTOlCorpProdLmtInfoDao lbTOlCorpProdLmtInfoDao;
    private final LcCustLimitInfoDao custLimitInfoDao;
    private final LcCustLimitObjectInfoDao custLimitObjectInfoDao;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.LB_T_OL_CORP_PROD_LMT_INFO_PROCESS;
    }

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据creditLimitId、tenantId复合主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含主键范围
     * @return 当前分片的数据量
     */
    @Override
    public Integer selectCountByCurrentGroupFromDb(LbSOlProdLmtInfoQuery query) {
        Integer count = lbSOlProdLmtInfoDao.selectCountByCurrentGroup(query);
        return count != null ? count : 0;
    }

    /**
     * 生成分片任务列表
     * 采用复合主键分页方式，循环查找最大主键对象，动态生成分片任务
     *
     * @param jobInitDto 任务初始化参数
     * @return 分片任务列表
     */
    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "====================== 分片逻辑 start ================================================");

        List<JobShared> jobSharedList = new ArrayList<>();
        // SQL批处理数量,暂定为分片数量
        Integer batchFixNum = jobInitDto.getFixNum();
        // 当前分组的最大值,为下次批处理的最小值
        LbSOlProdLmtInfoDo maxDo = null;

        // 构造查询条件,查询当前分批处理的排序最大对象
        LbSOlProdLmtInfoQuery query = LbSOlProdLmtInfoQuery.builder().build();

        // 分片流水
        int batchNum = 0;
        while (true) {
            if (maxDo != null) {
                query.setCreditLimitId(maxDo.getCreditLimitId());
            }
            maxDo = lbSOlProdLmtInfoDao.selectFirstOne(query);
            if (maxDo == null) {
                break;
            }
            // 统计分片数量
            batchNum = countBatchNum(batchFixNum, query, maxDo, batchNum, jobInitDto, jobSharedList,
                query.getCreditLimitId(), false);
        }

        log.info(prefixLog + "====================== 分片逻辑 end ================================================");
        log.info(prefixLog + "网贷对公额度处理分片任务生成完成,共[{}]个分片", jobSharedList.size());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LbSOlProdLmtInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {

        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始查询分片数据,分片号:{}", jobShared.getBatchNum());

        ShardingResult<LbSOlProdLmtInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);

        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        // 创建查询条件
        LbSOlProdLmtInfoQuery query = LbSOlProdLmtInfoQuery.builder().build();

        log.info(prefixLog + "=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");

        List<LbSOlProdLmtInfoDo> dataList = lbSOlProdLmtInfoDao.selectByExample(query);
        shardingResult.setShardingResultList(dataList);

        log.info("分片数据查询完成,分片号:{},数据量:{}", jobShared.getBatchNum(),
            CollectionUtils.isEmpty(dataList) ? 0 : dataList.size());

        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbSOlProdLmtInfoDo> shardingResult) {
        // 获取分片数据列表
        List<LbSOlProdLmtInfoDo> shardingDataList = shardingResult.getShardingResultList();
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        Integer dataSize = shardingResult.getShardingResultList().size();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "=========分片执行开始:[{}]数量为[{}]===========", batchNum, dataSize);

        // 判空处理，若分片数据为空直接返回
        if (CollectionUtils.isEmpty(shardingDataList)) {
            log.info(prefixLog + "=========分片执行结束:分片号[{}]数量为空===========", batchNum);
            return;
        }
        try {
            log.info(prefixLog + "开始处理[{}]条[网贷系统-历史表-合同信息]数据", shardingDataList.size());
            LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
            //查询[网贷系统-落地表-合同信息]中所有的[USER_ID]
            List<String> userIdList = shardingDataList.stream().filter(Objects::nonNull)//
                .map(LbSOlProdLmtInfoDo::getUserId)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            //查询[额度实例所属对象信息]
            List<LcCustLimitObjectInfoDo> objectInfoDoList = custLimitObjectInfoDao.selectByExample(
                LcCustLimitObjectInfoQuery.builder()//
                    .userType(EnumUserType.INSTITUTIONAL_CUSTOMER.getCode())//
                    .userIdList(userIdList)//
                    .build());
            List<String> custLimitIdList = objectInfoDoList.stream().filter(Objects::nonNull)//
                .map(LcCustLimitObjectInfoDo::getCustLimitId)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            //查询[额度实例信息]中TEMPLATE_NODE_ID=['DGDBDPDYCPED']列表
            List<String> nodeIdList = Lists.newArrayList(EnumLmtTplNode.DGDBDPDYCPED.getTemplateNodeId());
            List<LcCustLimitInfoDo> limitInfoDoList = custLimitInfoDao.selectByExample(LcCustLimitInfoQuery.builder()
                .custLimitIdList(custLimitIdList)
                .limitObjectIdList(userIdList)
                .templateNodeIdList(nodeIdList)
                .statusList(EnumCustLimitStatus.getOperableCodeList())
                .build());
            List<String> limitIdList = limitInfoDoList.stream().filter(Objects::nonNull)//
                .map(LcCustLimitInfoDo::getCustLimitId)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

            //插入数据到[网贷系统-中间表-对公产品层额度信息]中
            int insertProdCount = lbTOlCorpProdLmtInfoDao.insertOlCorpProdLmtInfo(nodeIdList, limitIdList);
            log.info(prefixLog + "插入网贷系统-中间表-对公产品层额度信息完成,插入记录数:[{}]", insertProdCount);

            //更新[额度实例金额信息]信息
            int updateCustLimitAmtInfoCount = lbTOlCorpProdLmtInfoDao.updateCustLimitAmtInfo(limitIdList);
            log.info(prefixLog + "更新额度实例金额信息完成,更新记录数:[{}]", updateCustLimitAmtInfoCount);
            // 更新分片流水成功
            normalUpdateSliceSerial(shardingDataList.size(), sliceBatchSerialDo);
        } catch (Exception e) {
            // 异常处理，记录详细日志并抛出运行时异常
            log.error(prefixLog + "分片执行异常:[{}]", batchNum, e);
            throw new RuntimeException(prefixLog + "处理异常", e);
        }
        log.info(prefixLog + "=========分片执行结束:[{}]===========", batchNum);
    }
}