package com.hsjry.core.limit.center.core.bo;

import lombok.Builder;
import lombok.Data;

/**
 * 额度实例所属对象信息Bo
 */
@Data
@Builder
public class CustLimitObjectInfoBo {
    /** 额度编号 */
    private String custLimitId;
    /** 租户号 */
    private String tenantId;
    /** 创建时间 */
    private java.util.Date createTime;
    /** 同业金融编号 */
    private String ibFinancialId;
    /** 核心同业金融产品编号 */
    private String ibFinancialProdCoreId;
    /** 同业金融产品编号 */
    private String ibFinancialProdId;
    /** 同业金融产品名称 */
    private String ibFinancialProdName;
    /** 同业金融产品类型 */
    private String ibFinancialProdType;
    /** 同业金融类型 */
    private String ibFinancialType;
    /** 核心客户编号 */
    private String outUserId;
    /** 更新时间 */
    private java.util.Date updateTime;
    /** 客户证件类型 */
    private String userCertificateKind;
    /** 客户证件编号 */
    private String userCertificateNo;
    /** 客户编号 */
    private String userId;
    /** 客户名称 */
    private String userName;
    /** 客户类型 */
    private String userType;
}
