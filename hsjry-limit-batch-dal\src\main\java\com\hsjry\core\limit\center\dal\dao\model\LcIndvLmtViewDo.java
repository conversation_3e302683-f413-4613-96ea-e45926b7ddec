package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 个人客户额度视图Do
 *
 * <AUTHOR>
 * @date 2025-07-07 12:58:26
 */
@Table(name = "lc_indv_lmt_view")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcIndvLmtViewDo extends LcIndvLmtViewKeyDo implements Serializable {
    private static final long serialVersionUID = 1942206575120941059L;
    /** 消费贷额度(元) */
    @Column(name = "consm_loan_lmt")
    private java.math.BigDecimal consmLoanLmt;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 信用卡可用额度(元) */
    @Column(name = "crd_cd_avl_lmt")
    private java.math.BigDecimal crdCdAvlLmt;
    /** 信用卡额度(元) */
    @Column(name = "crd_cd_lmt")
    private java.math.BigDecimal crdCdLmt;
    /** 消费按揭产品可用额度(元) */
    @Column(name = "consm_mrtg_prod_avl_avl_lmt")
    private java.math.BigDecimal consmMrtgProdAvlAvlLmt;
    /** 消费按揭产品总额度(元) */
    @Column(name = "consm_mrtg_prod_tot_avl_lmt")
    private java.math.BigDecimal consmMrtgProdTotAvlLmt;
    /** 消费贷产品可用额度(元) */
    @Column(name = "consm_loan_prod_avl_lmt")
    private java.math.BigDecimal consmLoanProdAvlLmt;
    /** 消费贷产品总额度(元) */
    @Column(name = "consm_loan_prod_tot_lmt")
    private java.math.BigDecimal consmLoanProdTotLmt;
    /** 消费贷可用额度(元) */
    @Column(name = "consm_loan_avl_lmt")
    private java.math.BigDecimal consmLoanAvlLmt;
    /** 经营按揭产品可用额度(元) */
    @Column(name = "op_mrtg_prod_tot_avl_lmt")
    private java.math.BigDecimal opMrtgProdTotAvlLmt;
    /** 经营按揭产品总额度(元) */
    @Column(name = "op_mrtg_prod_tot_lmt")
    private java.math.BigDecimal opMrtgProdTotLmt;
    /** 经营贷产品可用额度(元) */
    @Column(name = "op_loan_prod_avl_lmt")
    private java.math.BigDecimal opLoanProdAvlLmt;
    /** 经营贷产品总额度(元) */
    @Column(name = "op_loan_prod_tot_lmt")
    private java.math.BigDecimal opLoanProdTotLmt;
    /** 经营贷可用额度(元) */
    @Column(name = "op_loan_avl_lmt")
    private java.math.BigDecimal opLoanAvlLmt;
    /** 经营贷额度(元) */
    @Column(name = "op_loan_lmt")
    private java.math.BigDecimal opLoanLmt;
    /** 所属行业类型 */
    @Column(name = "belg_inds_typ")
    private String belgIndsTyp;
    /** 证件号码 */
    @Column(name = "cert_no")
    private String certNo;
    /** 证件类型 */
    @Column(name = "cert_typ")
    private String certTyp;
    /** 客户编号 */
    @Column(name = "cust_no")
    private String custNo;
    /** 客户名称 */
    @Column(name = "cust_nm")
    private String custNm;
    /** 客户风险标签 */
    @Column(name = "cust_risk_tag")
    private String custRiskTag;
}
