/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationDo;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitRelationQuery;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/17 9:54
 */
public interface CustLimitRelationBatchDao {
    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitRelationDo> selectShardList(CustLimitRelationQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitRelationDo selectFirstOne(CustLimitRelationQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(CustLimitRelationQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitRelationDo> selectObjectShardList(CustLimitRelationQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitRelationDo selectObjectFirstOne(CustLimitRelationQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitRelationDo> selectExpireShardList(CustLimitRelationQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitRelationDo selectExpireFirstOne(CustLimitRelationQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectObjectCountByCurrentGroup(CustLimitRelationQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectExpireCountByCurrentGroup(CustLimitRelationQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitRelationDo> selectNotUsedShardList(CustLimitRelationQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitRelationDo selectNotUsedFirstOne(CustLimitRelationQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNotUsedCountByCurrentGroup(CustLimitRelationQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitRelationDo> selectNodeShardList(CustLimitRelationQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitRelationDo selectNodeFirstOne(CustLimitRelationQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNodeCountByCurrentGroup(CustLimitRelationQuery query);

    List<LcCustLimitRelationDo> selectExpireLimitInfoByObjectId(CustLimitRelationQuery query);
}
