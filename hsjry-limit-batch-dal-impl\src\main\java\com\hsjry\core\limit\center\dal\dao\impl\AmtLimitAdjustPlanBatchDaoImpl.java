/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.Date;

import org.springframework.stereotype.Repository;

import com.google.common.collect.Lists;
import com.hsjry.base.common.model.enums.limit.EnumPlanStatus;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.intf.AmtLimitAdjustPlanBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.LcAmtRuleAdjustPlanMapper;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtRuleAdjustPlanDo;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtRuleAdjustPlanExample;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/8 10:33
 */
@Repository
@Slf4j
public class AmtLimitAdjustPlanBatchDaoImpl
    extends AbstractBaseDaoImpl<LcAmtRuleAdjustPlanDo, LcAmtRuleAdjustPlanMapper>
    implements AmtLimitAdjustPlanBatchDao {
    @Override
    public PageInfo<LcAmtRuleAdjustPlanDo> queryNeedAdjustPlan(Date date, PageParam pageParam) {
        LcAmtRuleAdjustPlanExample example = new LcAmtRuleAdjustPlanExample();

        LcAmtRuleAdjustPlanExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        criteria.andPlanStatusIn(Lists.newArrayList(EnumPlanStatus.UN_EXECUTE.getCode(), EnumPlanStatus.FAIL.getCode()));
        criteria.andPlanDateStrEqualTo(DateUtil.getDate(date, DateUtil.DATE_FORMAT_2));
        return PageHelper.<LcAmtRuleAdjustPlanDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }
}
