/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.Date;
import java.util.List;

import com.hsjry.core.limit.center.dal.dao.query.EntityInfoBatchQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityInfoDo;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/14 10:20
 */
public interface EntityInfoBatchDao {

    /**
     * 查询分片数据
     *
     * @param batchQuery
     * @return
     */
    List<LcEntityInfoDo> selectShardList(EntityInfoBatchQuery batchQuery);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcEntityInfoDo selectFirstOne(EntityInfoBatchQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(EntityInfoBatchQuery query);

    /**
     * 更新利率版本
     *
     * @param entityIdList
     * @param updateTime
     * @return
     */
    int updateExchangeRateVersion(List<String> entityIdList, Date updateTime, String targetCustFlag,
        String targetAmtFlag, String sourceCustFlag, String sourceAmtFlag);

}
