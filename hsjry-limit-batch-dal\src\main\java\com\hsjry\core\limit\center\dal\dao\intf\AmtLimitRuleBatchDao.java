package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.Date;
import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRuleDo;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/4/23 16:18
 */
public interface AmtLimitRuleBatchDao {
    /**
     * 查询X天后停用的限额规则
     *
     * @param date
     * @param disableDay
     * @return
     */
    List<LcAmtLimitRuleDo> queryDisableAmtLimitRuleList(Date date, int disableDay);
}
