/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.center.dal.dao.query.EntityInfoBatchQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityInfoDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/14 10:29
 */
public interface EntityInfoBatchMapper extends CommonMapper<LcEntityInfoDo> {
    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(@Param("query") EntityInfoBatchQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcEntityInfoDo selectFirstOne(@Param("query") EntityInfoBatchQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcEntityInfoDo> selectShardList(@Param("query") EntityInfoBatchQuery query);

    /**
     * 批量更新利率版本
     *
     * @param tenantId
     * @param entityIdList
     * @param updateTime
     * @return
     */
    int updateExchangeRateVersion(@Param("tenantId") String tenantId, @Param("entityIdList") List<String> entityIdList,
        @Param("updateTime") Date updateTime,
        @Param("targetCustFlag") String targetCustFlag,
        @Param("targetAmtFlag") String targetAmtFlag,
        @Param("sourceCustFlag") String sourceCustFlag,
        @Param("sourceAmtFlag") String sourceAmtFlag);
}
