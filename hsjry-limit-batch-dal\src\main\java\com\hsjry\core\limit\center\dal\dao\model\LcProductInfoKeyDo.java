package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 产品信息主键
 *
 * <AUTHOR>
 * @date 2023-11-23 02:55:08
 */
@Table(name = "lc_product_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcProductInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1727521130916544512L;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    /** 产品编号 */
    @Id
    @Column(name = "product_id")
    private String productId;
}