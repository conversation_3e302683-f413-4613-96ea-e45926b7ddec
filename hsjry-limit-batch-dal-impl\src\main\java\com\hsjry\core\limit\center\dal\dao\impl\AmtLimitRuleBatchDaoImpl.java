package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.intf.AmtLimitRuleBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.LcAmtLimitRuleMapper;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRuleDo;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRuleExample;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/4/23 16:18
 */
@Repository
@Slf4j
public class AmtLimitRuleBatchDaoImpl extends AbstractBaseDaoImpl<LcAmtLimitRuleDo, LcAmtLimitRuleMapper>
    implements AmtLimitRuleBatchDao {

    @Override
    public List<LcAmtLimitRuleDo> queryDisableAmtLimitRuleList(Date date, int disableDay) {
        {
            LcAmtLimitRuleExample example = new LcAmtLimitRuleExample();
            LcAmtLimitRuleExample.Criteria criteria = example.createCriteria();
            criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
            Date addDate = DateUtil.addDate(date, disableDay);
            criteria.andAmtLastTimeGreaterThanOrEqualTo(DateUtil.getDayBegin(addDate));
            criteria.andAmtLastTimeLessThanOrEqualTo(DateUtil.getDayEnd(addDate));
            example.setOrderByClause("rule_id asc");
            return getMapper().selectByExample(example);
        }
    }
}
