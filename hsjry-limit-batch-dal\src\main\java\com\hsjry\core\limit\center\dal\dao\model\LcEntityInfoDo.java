package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 实体信息Do
 *
 * <AUTHOR>
 * @date 2023-10-30 09:23:55
 */
@Table(name = "lc_entity_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcEntityInfoDo extends LcEntityInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1718921664835813376L;
    /** 额度编号 */
    @Column(name = "cust_limit_id")
    private String custLimitId;
    /** 预发放低风险金额编号 */
    @Column(name = "pre_grant_low_risk_amt_id")
    private String preGrantLowRiskAmtId;
    /** 发放低风险 */
    @Column(name = "low_risk")
    private java.math.BigDecimal lowRisk;
    /** 发放低风险金额编号 */
    @Column(name = "low_risk_amount_id")
    private String lowRiskAmountId;
    /** 低风险余额 */
    @Column(name = "left_low_risk")
    private java.math.BigDecimal leftLowRisk;
    /** 低风险余额金额编号 */
    @Column(name = "left_low_risk_amount_id")
    private String leftLowRiskAmountId;
    /** 低风险币种 */
    @Column(name = "low_risk_currency")
    private String lowRiskCurrency;
    /** 是否限额管控;EnumBool   Y-是、N-否 */
    @Column(name = "amt_limit_flag")
    private String amtLimitFlag;
    /** 产品编号 */
    @Column(name = "product_id")
    private String productId;
    /** 预发放低风险 */
    @Column(name = "pre_grant_low_risk")
    private java.math.BigDecimal preGrantLowRisk;
    /** 所属对象编号 */
    @Column(name = "limit_object_id")
    private String limitObjectId;
    /** 实体唯一标识 */
    @Column(name = "unique_identifier")
    private String uniqueIdentifier;
    /** 更新汇率版本 */
    @Column(name = "next_exchange_rate_version")
    private Integer nextExchangeRateVersion;
    /** 关联同业金融产品编号 */
    @Column(name = "ib_financial_prod_rel_id")
    private String ibFinancialProdRelId;
    /** 所属对象类型 EnumLimitObjectType:001-客户、002-金融产品 */
    @Column(name = "limit_object_type")
    private String limitObjectType;
    /** 卖空标识;EnumBool   Y-是、N-否 */
    @Column(name = "short_selling_flag")
    private String shortSellingFlag;
    /** 金额分配模式 EnumAmountAssignMode:001-主动分配、002-自动分配-按比例分配 */
    @Column(name = "amount_assign_mode")
    private String amountAssignMode;
    /** 汇率版本 */
    @Column(name = "exchange_rate_version")
    private Integer exchangeRateVersion;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 实体类型;EnumLimitEntityType:001-借据、002-信用证、003-保函、004-垫款、005-贴现、006-贷款承诺 */
    @Column(name = "entity_type")
    private String entityType;
    /** 状态;EnumLimitEntityStatus(010-未结清,020-结清,030-已冲账,040-已撤销) */
    @Column(name = "status")
    private String status;
    /** 实体申请编号 */
    @Column(name = "entity_apply_id")
    private String entityApplyId;
    /** 实体业务编号 */
    @Column(name = "entity_relation_id")
    private String entityRelationId;
    /** 实体所属系统 */
    @Column(name = "system_sign")
    private String systemSign;
    /** 额度汇率重选标记;EnumBool   Y-是、N-否 */
    @Column(name = "cust_limit_ex_rate_recal_flag")
    private String custLimitExRateRecalFlag;
    /** 限额汇率重算标记;EnumBool   Y-是、N-否 */
    @Column(name = "amt_limit_ex_rate_recal_flag")
    private String amtLimitExRateRecalFlag;
    /** 发放时间 */
    @Column(name = "grant_date_time")
    private java.util.Date grantDateTime;
    /** 预发放金额 */
    @Column(name = "pre_grant_amount")
    private java.math.BigDecimal preGrantAmount;
    /** 发放金额 */
    @Column(name = "amount")
    private java.math.BigDecimal amount;
    /** 当前余额 */
    @Column(name = "left_amount")
    private java.math.BigDecimal leftAmount;
    /** 金额币种 */
    @Column(name = "amount_currency")
    private String amountCurrency;
}
