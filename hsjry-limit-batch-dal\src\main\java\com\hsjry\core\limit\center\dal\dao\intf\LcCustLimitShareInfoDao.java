package com.hsjry.core.limit.center.dal.dao.intf;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitShareInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitShareInfoQuery;


import java.util.List;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度串用信息数据库操作接口
 * 
 * <AUTHOR>
 * @date 2023-06-01 02:19:57
 */
public interface LcCustLimitShareInfoDao extends IBaseDao<LcCustLimitShareInfoDo> {
    /**
     * 分页查询额度串用信息
     *
     * @param lcCustLimitShareInfoQuery 条件
     * @return PageInfo<LcCustLimitShareInfoDo>
     */
    PageInfo<LcCustLimitShareInfoDo> selectPage(LcCustLimitShareInfoQuery lcCustLimitShareInfoQuery,PageParam pageParam);
	  /**
     * 根据key查询额度串用信息
     *
     	 	 * @param custLimitShareId
	 	 	 	      * @return
     */
	LcCustLimitShareInfoDo selectByKey(String custLimitShareId);
    /**
     * 根据key删除额度串用信息
     *
               * @param custLimitShareId
                      * @return
     */
    int deleteByKey(String custLimitShareId);

    /**
     * 查询额度串用信息信息
     *
     * @param lcCustLimitShareInfoQuery 条件
     * @return List<LcCustLimitShareInfoDo>
     */
    List<LcCustLimitShareInfoDo> selectByExample(LcCustLimitShareInfoQuery lcCustLimitShareInfoQuery);

    /**
     * 新增额度串用信息信息
     *
     * @param lcCustLimitShareInfo 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitShareInfoDo lcCustLimitShareInfo);

    /**
     * 修改额度串用信息信息
     *
     * @param lcCustLimitShareInfo
     * @return
     */
    int updateBySelective(LcCustLimitShareInfoDo lcCustLimitShareInfo);
    /**
     * 修改额度串用信息信息
     *
     * @param lcCustLimitShareInfo
     * @param lcCustLimitShareInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitShareInfoDo lcCustLimitShareInfo,
    LcCustLimitShareInfoQuery lcCustLimitShareInfoQuery);
}
