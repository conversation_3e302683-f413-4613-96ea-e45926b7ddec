package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 集团成员关联关系Do
 *
 * <AUTHOR>
 * @date 2024-05-29 02:31:51
 */
@Table(name = "lc_group_member_relation")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcGroupMemberRelationDo extends LcGroupMemberRelationKeyDo implements Serializable {
    private static final long serialVersionUID = 1795644192496746496L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 集团客户编号 */
    @Column(name = "group_user_id")
    private String groupUserId;
    /** 集团成员编号 */
    @Column(name = "user_id")
    private String userId;
    /** 成员证件号码 */
    @Column(name = "member_certificate_no")
    private String memberCertificateNo;
    /** 成员证件类型 */
    @Column(name = "member_certificate_kind")
    private String memberCertificateKind;
    /** 成员名称 */
    @Column(name = "member_name")
    private String memberName;
    /** 成员客户类型 */
    @Column(name = "member_type")
    private String memberType;
}
