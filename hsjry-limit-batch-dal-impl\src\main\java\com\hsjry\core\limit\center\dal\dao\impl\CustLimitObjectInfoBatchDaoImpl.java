/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.intf.CustLimitObjectInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.CustLimitObjectInfoBatchMapper;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitObjectInfoQuery;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/17 9:57
 */
@Repository
public class CustLimitObjectInfoBatchDaoImpl
    extends AbstractBaseDaoImpl<LcCustLimitObjectInfoDo, CustLimitObjectInfoBatchMapper>
    implements CustLimitObjectInfoBatchDao {
    @Override
    public List<LcCustLimitObjectInfoDo> selectShardList(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectShardList(query);
    }

    @Override
    public LcCustLimitObjectInfoDo selectFirstOne(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectFirstOne(query);
    }

    @Override
    public Integer selectCountByCurrentGroup(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitObjectInfoDo> selectObjectShardList(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectObjectShardList(query);
    }

    @Override
    public List<LcCustLimitObjectInfoDo> selectExpireShardList(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectExpireShardList(query);
    }

    @Override
    public LcCustLimitObjectInfoDo selectObjectFirstOne(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectObjectFirstOne(query);
    }

    @Override
    public LcCustLimitObjectInfoDo selectExpireFirstOne(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectExpireFirstOne(query);
    }

    @Override
    public Integer selectObjectCountByCurrentGroup(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectObjectCountByCurrentGroup(query);
    }

    @Override
    public Integer selectExpireCountByCurrentGroup(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectExpireCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitObjectInfoDo> selectNotUsedShardList(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNotUsedShardList(query);
    }

    @Override
    public LcCustLimitObjectInfoDo selectNotUsedFirstOne(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNotUsedFirstOne(query);
    }

    @Override
    public Integer selectNotUsedCountByCurrentGroup(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectNotUsedCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitObjectInfoDo> selectNodeShardList(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNodeShardList(query);
    }

    @Override
    public LcCustLimitObjectInfoDo selectNodeFirstOne(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNodeFirstOne(query);
    }

    @Override
    public Integer selectNodeCountByCurrentGroup(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectNodeCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitObjectInfoDo> selectExpireLimitInfoByObjectId(CustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectExpireLimitInfoByObjectId(query);
    }
}
