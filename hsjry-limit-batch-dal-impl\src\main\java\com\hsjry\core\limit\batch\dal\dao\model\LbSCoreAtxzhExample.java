package com.hsjry.core.limit.batch.dal.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 核心系统-落地表-贴现账户主文件Example
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
public class LbSCoreAtxzhExample {
    /** 排序字段 */
    protected String orderByClause;

    /** 是否只查询记录不同的数据 */
    protected boolean distinct;

    /** or条件查询集合 */
    protected List<Criteria> oredCriteria;

    /**
     * 初始化or条件查询集合
     */
    public LbSCoreAtxzhExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * 排序字段set方法
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * 排序字段get方法
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * 不同记录set方法.
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * 不同记录get方法.
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * 排序集合get方法
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * or
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * or
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * 清除数据
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andTxfxbzIsNull() {
            addCriterion("txfxbz is null");
            return (Criteria) this;
        }

        public Criteria andTxfxbzIsNotNull() {
            addCriterion("txfxbz is not null");
            return (Criteria) this;
        }

        public Criteria andTxfxbzEqualTo(String value) {
            addCriterion("txfxbz =", value, "txfxbz");
            return (Criteria) this;
        }

        public Criteria andTxfxbzNotEqualTo(String value) {
            addCriterion("txfxbz <>", value, "txfxbz");
            return (Criteria) this;
        }

        public Criteria andTxfxbzGreaterThan(String value) {
            addCriterion("txfxbz >", value, "txfxbz");
            return (Criteria) this;
        }

        public Criteria andTxfxbzGreaterThanOrEqualTo(String value) {
            addCriterion("txfxbz >=", value, "txfxbz");
            return (Criteria) this;
        }

        public Criteria andTxfxbzLessThan(String value) {
            addCriterion("txfxbz <", value, "txfxbz");
            return (Criteria) this;
        }

        public Criteria andTxfxbzLessThanOrEqualTo(String value) {
            addCriterion("txfxbz <=", value, "txfxbz");
            return (Criteria) this;
        }

        public Criteria andTxfxbzLike(String value) {
            addCriterion("txfxbz like", value, "txfxbz");
            return (Criteria) this;
        }

        public Criteria andTxfxbzNotLike(String value) {
            addCriterion("txfxbz not like", value, "txfxbz");
            return (Criteria) this;
        }

        public Criteria andTxfxbzIn(List<String> values) {
            addCriterion("txfxbz in", values, "txfxbz");
            return (Criteria) this;
        }

        public Criteria andTxfxbzNotIn(List<String> values) {
            addCriterion("txfxbz not in", values, "txfxbz");
            return (Criteria) this;
        }

        public Criteria andTxfxbzBetween(String value1, String value2) {
            addCriterion("txfxbz between", value1, value2, "txfxbz");
            return (Criteria) this;
        }

        public Criteria andTxfxbzNotBetween(String value1, String value2) {
            addCriterion("txfxbz not between", value1, value2, "txfxbz");
            return (Criteria) this;
        }

        public Criteria andDuifhhIsNull() {
            addCriterion("duifhh is null");
            return (Criteria) this;
        }

        public Criteria andDuifhhIsNotNull() {
            addCriterion("duifhh is not null");
            return (Criteria) this;
        }

        public Criteria andDuifhhEqualTo(String value) {
            addCriterion("duifhh =", value, "duifhh");
            return (Criteria) this;
        }

        public Criteria andDuifhhNotEqualTo(String value) {
            addCriterion("duifhh <>", value, "duifhh");
            return (Criteria) this;
        }

        public Criteria andDuifhhGreaterThan(String value) {
            addCriterion("duifhh >", value, "duifhh");
            return (Criteria) this;
        }

        public Criteria andDuifhhGreaterThanOrEqualTo(String value) {
            addCriterion("duifhh >=", value, "duifhh");
            return (Criteria) this;
        }

        public Criteria andDuifhhLessThan(String value) {
            addCriterion("duifhh <", value, "duifhh");
            return (Criteria) this;
        }

        public Criteria andDuifhhLessThanOrEqualTo(String value) {
            addCriterion("duifhh <=", value, "duifhh");
            return (Criteria) this;
        }

        public Criteria andDuifhhLike(String value) {
            addCriterion("duifhh like", value, "duifhh");
            return (Criteria) this;
        }

        public Criteria andDuifhhNotLike(String value) {
            addCriterion("duifhh not like", value, "duifhh");
            return (Criteria) this;
        }

        public Criteria andDuifhhIn(List<String> values) {
            addCriterion("duifhh in", values, "duifhh");
            return (Criteria) this;
        }

        public Criteria andDuifhhNotIn(List<String> values) {
            addCriterion("duifhh not in", values, "duifhh");
            return (Criteria) this;
        }

        public Criteria andDuifhhBetween(String value1, String value2) {
            addCriterion("duifhh between", value1, value2, "duifhh");
            return (Criteria) this;
        }

        public Criteria andDuifhhNotBetween(String value1, String value2) {
            addCriterion("duifhh not between", value1, value2, "duifhh");
            return (Criteria) this;
        }

        public Criteria andJigulbIsNull() {
            addCriterion("jigulb is null");
            return (Criteria) this;
        }

        public Criteria andJigulbIsNotNull() {
            addCriterion("jigulb is not null");
            return (Criteria) this;
        }

        public Criteria andJigulbEqualTo(String value) {
            addCriterion("jigulb =", value, "jigulb");
            return (Criteria) this;
        }

        public Criteria andJigulbNotEqualTo(String value) {
            addCriterion("jigulb <>", value, "jigulb");
            return (Criteria) this;
        }

        public Criteria andJigulbGreaterThan(String value) {
            addCriterion("jigulb >", value, "jigulb");
            return (Criteria) this;
        }

        public Criteria andJigulbGreaterThanOrEqualTo(String value) {
            addCriterion("jigulb >=", value, "jigulb");
            return (Criteria) this;
        }

        public Criteria andJigulbLessThan(String value) {
            addCriterion("jigulb <", value, "jigulb");
            return (Criteria) this;
        }

        public Criteria andJigulbLessThanOrEqualTo(String value) {
            addCriterion("jigulb <=", value, "jigulb");
            return (Criteria) this;
        }

        public Criteria andJigulbLike(String value) {
            addCriterion("jigulb like", value, "jigulb");
            return (Criteria) this;
        }

        public Criteria andJigulbNotLike(String value) {
            addCriterion("jigulb not like", value, "jigulb");
            return (Criteria) this;
        }

        public Criteria andJigulbIn(List<String> values) {
            addCriterion("jigulb in", values, "jigulb");
            return (Criteria) this;
        }

        public Criteria andJigulbNotIn(List<String> values) {
            addCriterion("jigulb not in", values, "jigulb");
            return (Criteria) this;
        }

        public Criteria andJigulbBetween(String value1, String value2) {
            addCriterion("jigulb between", value1, value2, "jigulb");
            return (Criteria) this;
        }

        public Criteria andJigulbNotBetween(String value1, String value2) {
            addCriterion("jigulb not between", value1, value2, "jigulb");
            return (Criteria) this;
        }

        public Criteria andZhdkjeIsNull() {
            addCriterion("zhdkje is null");
            return (Criteria) this;
        }

        public Criteria andZhdkjeIsNotNull() {
            addCriterion("zhdkje is not null");
            return (Criteria) this;
        }

        public Criteria andZhdkjeEqualTo(java.math.BigDecimal value) {
            addCriterion("zhdkje =", value, "zhdkje");
            return (Criteria) this;
        }

        public Criteria andZhdkjeNotEqualTo(java.math.BigDecimal value) {
            addCriterion("zhdkje <>", value, "zhdkje");
            return (Criteria) this;
        }

        public Criteria andZhdkjeGreaterThan(java.math.BigDecimal value) {
            addCriterion("zhdkje >", value, "zhdkje");
            return (Criteria) this;
        }

        public Criteria andZhdkjeGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("zhdkje >=", value, "zhdkje");
            return (Criteria) this;
        }

        public Criteria andZhdkjeLessThan(java.math.BigDecimal value) {
            addCriterion("zhdkje <", value, "zhdkje");
            return (Criteria) this;
        }

        public Criteria andZhdkjeLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("zhdkje <=", value, "zhdkje");
            return (Criteria) this;
        }

        public Criteria andZhdkjeLike(java.math.BigDecimal value) {
            addCriterion("zhdkje like", value, "zhdkje");
            return (Criteria) this;
        }

        public Criteria andZhdkjeNotLike(java.math.BigDecimal value) {
            addCriterion("zhdkje not like", value, "zhdkje");
            return (Criteria) this;
        }

        public Criteria andZhdkjeIn(List<java.math.BigDecimal> values) {
            addCriterion("zhdkje in", values, "zhdkje");
            return (Criteria) this;
        }

        public Criteria andZhdkjeNotIn(List<java.math.BigDecimal> values) {
            addCriterion("zhdkje not in", values, "zhdkje");
            return (Criteria) this;
        }

        public Criteria andZhdkjeBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("zhdkje between", value1, value2, "zhdkje");
            return (Criteria) this;
        }

        public Criteria andZhdkjeNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("zhdkje not between", value1, value2, "zhdkje");
            return (Criteria) this;
        }

        public Criteria andJiejuhIsNull() {
            addCriterion("jiejuh is null");
            return (Criteria) this;
        }

        public Criteria andJiejuhIsNotNull() {
            addCriterion("jiejuh is not null");
            return (Criteria) this;
        }

        public Criteria andJiejuhEqualTo(String value) {
            addCriterion("jiejuh =", value, "jiejuh");
            return (Criteria) this;
        }

        public Criteria andJiejuhNotEqualTo(String value) {
            addCriterion("jiejuh <>", value, "jiejuh");
            return (Criteria) this;
        }

        public Criteria andJiejuhGreaterThan(String value) {
            addCriterion("jiejuh >", value, "jiejuh");
            return (Criteria) this;
        }

        public Criteria andJiejuhGreaterThanOrEqualTo(String value) {
            addCriterion("jiejuh >=", value, "jiejuh");
            return (Criteria) this;
        }

        public Criteria andJiejuhLessThan(String value) {
            addCriterion("jiejuh <", value, "jiejuh");
            return (Criteria) this;
        }

        public Criteria andJiejuhLessThanOrEqualTo(String value) {
            addCriterion("jiejuh <=", value, "jiejuh");
            return (Criteria) this;
        }

        public Criteria andJiejuhLike(String value) {
            addCriterion("jiejuh like", value, "jiejuh");
            return (Criteria) this;
        }

        public Criteria andJiejuhNotLike(String value) {
            addCriterion("jiejuh not like", value, "jiejuh");
            return (Criteria) this;
        }

        public Criteria andJiejuhIn(List<String> values) {
            addCriterion("jiejuh in", values, "jiejuh");
            return (Criteria) this;
        }

        public Criteria andJiejuhNotIn(List<String> values) {
            addCriterion("jiejuh not in", values, "jiejuh");
            return (Criteria) this;
        }

        public Criteria andJiejuhBetween(String value1, String value2) {
            addCriterion("jiejuh between", value1, value2, "jiejuh");
            return (Criteria) this;
        }

        public Criteria andJiejuhNotBetween(String value1, String value2) {
            addCriterion("jiejuh not between", value1, value2, "jiejuh");
            return (Criteria) this;
        }

        public Criteria andDnknbzIsNull() {
            addCriterion("dnknbz is null");
            return (Criteria) this;
        }

        public Criteria andDnknbzIsNotNull() {
            addCriterion("dnknbz is not null");
            return (Criteria) this;
        }

        public Criteria andDnknbzEqualTo(String value) {
            addCriterion("dnknbz =", value, "dnknbz");
            return (Criteria) this;
        }

        public Criteria andDnknbzNotEqualTo(String value) {
            addCriterion("dnknbz <>", value, "dnknbz");
            return (Criteria) this;
        }

        public Criteria andDnknbzGreaterThan(String value) {
            addCriterion("dnknbz >", value, "dnknbz");
            return (Criteria) this;
        }

        public Criteria andDnknbzGreaterThanOrEqualTo(String value) {
            addCriterion("dnknbz >=", value, "dnknbz");
            return (Criteria) this;
        }

        public Criteria andDnknbzLessThan(String value) {
            addCriterion("dnknbz <", value, "dnknbz");
            return (Criteria) this;
        }

        public Criteria andDnknbzLessThanOrEqualTo(String value) {
            addCriterion("dnknbz <=", value, "dnknbz");
            return (Criteria) this;
        }

        public Criteria andDnknbzLike(String value) {
            addCriterion("dnknbz like", value, "dnknbz");
            return (Criteria) this;
        }

        public Criteria andDnknbzNotLike(String value) {
            addCriterion("dnknbz not like", value, "dnknbz");
            return (Criteria) this;
        }

        public Criteria andDnknbzIn(List<String> values) {
            addCriterion("dnknbz in", values, "dnknbz");
            return (Criteria) this;
        }

        public Criteria andDnknbzNotIn(List<String> values) {
            addCriterion("dnknbz not in", values, "dnknbz");
            return (Criteria) this;
        }

        public Criteria andDnknbzBetween(String value1, String value2) {
            addCriterion("dnknbz between", value1, value2, "dnknbz");
            return (Criteria) this;
        }

        public Criteria andDnknbzNotBetween(String value1, String value2) {
            addCriterion("dnknbz not between", value1, value2, "dnknbz");
            return (Criteria) this;
        }

        public Criteria andXinx01IsNull() {
            addCriterion("xinx01 is null");
            return (Criteria) this;
        }

        public Criteria andXinx01IsNotNull() {
            addCriterion("xinx01 is not null");
            return (Criteria) this;
        }

        public Criteria andXinx01EqualTo(String value) {
            addCriterion("xinx01 =", value, "xinx01");
            return (Criteria) this;
        }

        public Criteria andXinx01NotEqualTo(String value) {
            addCriterion("xinx01 <>", value, "xinx01");
            return (Criteria) this;
        }

        public Criteria andXinx01GreaterThan(String value) {
            addCriterion("xinx01 >", value, "xinx01");
            return (Criteria) this;
        }

        public Criteria andXinx01GreaterThanOrEqualTo(String value) {
            addCriterion("xinx01 >=", value, "xinx01");
            return (Criteria) this;
        }

        public Criteria andXinx01LessThan(String value) {
            addCriterion("xinx01 <", value, "xinx01");
            return (Criteria) this;
        }

        public Criteria andXinx01LessThanOrEqualTo(String value) {
            addCriterion("xinx01 <=", value, "xinx01");
            return (Criteria) this;
        }

        public Criteria andXinx01Like(String value) {
            addCriterion("xinx01 like", value, "xinx01");
            return (Criteria) this;
        }

        public Criteria andXinx01NotLike(String value) {
            addCriterion("xinx01 not like", value, "xinx01");
            return (Criteria) this;
        }

        public Criteria andXinx01In(List<String> values) {
            addCriterion("xinx01 in", values, "xinx01");
            return (Criteria) this;
        }

        public Criteria andXinx01NotIn(List<String> values) {
            addCriterion("xinx01 not in", values, "xinx01");
            return (Criteria) this;
        }

        public Criteria andXinx01Between(String value1, String value2) {
            addCriterion("xinx01 between", value1, value2, "xinx01");
            return (Criteria) this;
        }

        public Criteria andXinx01NotBetween(String value1, String value2) {
            addCriterion("xinx01 not between", value1, value2, "xinx01");
            return (Criteria) this;
        }

        public Criteria andFxcdjeIsNull() {
            addCriterion("fxcdje is null");
            return (Criteria) this;
        }

        public Criteria andFxcdjeIsNotNull() {
            addCriterion("fxcdje is not null");
            return (Criteria) this;
        }

        public Criteria andFxcdjeEqualTo(java.math.BigDecimal value) {
            addCriterion("fxcdje =", value, "fxcdje");
            return (Criteria) this;
        }

        public Criteria andFxcdjeNotEqualTo(java.math.BigDecimal value) {
            addCriterion("fxcdje <>", value, "fxcdje");
            return (Criteria) this;
        }

        public Criteria andFxcdjeGreaterThan(java.math.BigDecimal value) {
            addCriterion("fxcdje >", value, "fxcdje");
            return (Criteria) this;
        }

        public Criteria andFxcdjeGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("fxcdje >=", value, "fxcdje");
            return (Criteria) this;
        }

        public Criteria andFxcdjeLessThan(java.math.BigDecimal value) {
            addCriterion("fxcdje <", value, "fxcdje");
            return (Criteria) this;
        }

        public Criteria andFxcdjeLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("fxcdje <=", value, "fxcdje");
            return (Criteria) this;
        }

        public Criteria andFxcdjeLike(java.math.BigDecimal value) {
            addCriterion("fxcdje like", value, "fxcdje");
            return (Criteria) this;
        }

        public Criteria andFxcdjeNotLike(java.math.BigDecimal value) {
            addCriterion("fxcdje not like", value, "fxcdje");
            return (Criteria) this;
        }

        public Criteria andFxcdjeIn(List<java.math.BigDecimal> values) {
            addCriterion("fxcdje in", values, "fxcdje");
            return (Criteria) this;
        }

        public Criteria andFxcdjeNotIn(List<java.math.BigDecimal> values) {
            addCriterion("fxcdje not in", values, "fxcdje");
            return (Criteria) this;
        }

        public Criteria andFxcdjeBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("fxcdje between", value1, value2, "fxcdje");
            return (Criteria) this;
        }

        public Criteria andFxcdjeNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("fxcdje not between", value1, value2, "fxcdje");
            return (Criteria) this;
        }

        public Criteria andDzywbhIsNull() {
            addCriterion("dzywbh is null");
            return (Criteria) this;
        }

        public Criteria andDzywbhIsNotNull() {
            addCriterion("dzywbh is not null");
            return (Criteria) this;
        }

        public Criteria andDzywbhEqualTo(String value) {
            addCriterion("dzywbh =", value, "dzywbh");
            return (Criteria) this;
        }

        public Criteria andDzywbhNotEqualTo(String value) {
            addCriterion("dzywbh <>", value, "dzywbh");
            return (Criteria) this;
        }

        public Criteria andDzywbhGreaterThan(String value) {
            addCriterion("dzywbh >", value, "dzywbh");
            return (Criteria) this;
        }

        public Criteria andDzywbhGreaterThanOrEqualTo(String value) {
            addCriterion("dzywbh >=", value, "dzywbh");
            return (Criteria) this;
        }

        public Criteria andDzywbhLessThan(String value) {
            addCriterion("dzywbh <", value, "dzywbh");
            return (Criteria) this;
        }

        public Criteria andDzywbhLessThanOrEqualTo(String value) {
            addCriterion("dzywbh <=", value, "dzywbh");
            return (Criteria) this;
        }

        public Criteria andDzywbhLike(String value) {
            addCriterion("dzywbh like", value, "dzywbh");
            return (Criteria) this;
        }

        public Criteria andDzywbhNotLike(String value) {
            addCriterion("dzywbh not like", value, "dzywbh");
            return (Criteria) this;
        }

        public Criteria andDzywbhIn(List<String> values) {
            addCriterion("dzywbh in", values, "dzywbh");
            return (Criteria) this;
        }

        public Criteria andDzywbhNotIn(List<String> values) {
            addCriterion("dzywbh not in", values, "dzywbh");
            return (Criteria) this;
        }

        public Criteria andDzywbhBetween(String value1, String value2) {
            addCriterion("dzywbh between", value1, value2, "dzywbh");
            return (Criteria) this;
        }

        public Criteria andDzywbhNotBetween(String value1, String value2) {
            addCriterion("dzywbh not between", value1, value2, "dzywbh");
            return (Criteria) this;
        }

        public Criteria andDuifhmIsNull() {
            addCriterion("duifhm is null");
            return (Criteria) this;
        }

        public Criteria andDuifhmIsNotNull() {
            addCriterion("duifhm is not null");
            return (Criteria) this;
        }

        public Criteria andDuifhmEqualTo(String value) {
            addCriterion("duifhm =", value, "duifhm");
            return (Criteria) this;
        }

        public Criteria andDuifhmNotEqualTo(String value) {
            addCriterion("duifhm <>", value, "duifhm");
            return (Criteria) this;
        }

        public Criteria andDuifhmGreaterThan(String value) {
            addCriterion("duifhm >", value, "duifhm");
            return (Criteria) this;
        }

        public Criteria andDuifhmGreaterThanOrEqualTo(String value) {
            addCriterion("duifhm >=", value, "duifhm");
            return (Criteria) this;
        }

        public Criteria andDuifhmLessThan(String value) {
            addCriterion("duifhm <", value, "duifhm");
            return (Criteria) this;
        }

        public Criteria andDuifhmLessThanOrEqualTo(String value) {
            addCriterion("duifhm <=", value, "duifhm");
            return (Criteria) this;
        }

        public Criteria andDuifhmLike(String value) {
            addCriterion("duifhm like", value, "duifhm");
            return (Criteria) this;
        }

        public Criteria andDuifhmNotLike(String value) {
            addCriterion("duifhm not like", value, "duifhm");
            return (Criteria) this;
        }

        public Criteria andDuifhmIn(List<String> values) {
            addCriterion("duifhm in", values, "duifhm");
            return (Criteria) this;
        }

        public Criteria andDuifhmNotIn(List<String> values) {
            addCriterion("duifhm not in", values, "duifhm");
            return (Criteria) this;
        }

        public Criteria andDuifhmBetween(String value1, String value2) {
            addCriterion("duifhm between", value1, value2, "duifhm");
            return (Criteria) this;
        }

        public Criteria andDuifhmNotBetween(String value1, String value2) {
            addCriterion("duifhm not between", value1, value2, "duifhm");
            return (Criteria) this;
        }

        public Criteria andMffxzhIsNull() {
            addCriterion("mffxzh is null");
            return (Criteria) this;
        }

        public Criteria andMffxzhIsNotNull() {
            addCriterion("mffxzh is not null");
            return (Criteria) this;
        }

        public Criteria andMffxzhEqualTo(String value) {
            addCriterion("mffxzh =", value, "mffxzh");
            return (Criteria) this;
        }

        public Criteria andMffxzhNotEqualTo(String value) {
            addCriterion("mffxzh <>", value, "mffxzh");
            return (Criteria) this;
        }

        public Criteria andMffxzhGreaterThan(String value) {
            addCriterion("mffxzh >", value, "mffxzh");
            return (Criteria) this;
        }

        public Criteria andMffxzhGreaterThanOrEqualTo(String value) {
            addCriterion("mffxzh >=", value, "mffxzh");
            return (Criteria) this;
        }

        public Criteria andMffxzhLessThan(String value) {
            addCriterion("mffxzh <", value, "mffxzh");
            return (Criteria) this;
        }

        public Criteria andMffxzhLessThanOrEqualTo(String value) {
            addCriterion("mffxzh <=", value, "mffxzh");
            return (Criteria) this;
        }

        public Criteria andMffxzhLike(String value) {
            addCriterion("mffxzh like", value, "mffxzh");
            return (Criteria) this;
        }

        public Criteria andMffxzhNotLike(String value) {
            addCriterion("mffxzh not like", value, "mffxzh");
            return (Criteria) this;
        }

        public Criteria andMffxzhIn(List<String> values) {
            addCriterion("mffxzh in", values, "mffxzh");
            return (Criteria) this;
        }

        public Criteria andMffxzhNotIn(List<String> values) {
            addCriterion("mffxzh not in", values, "mffxzh");
            return (Criteria) this;
        }

        public Criteria andMffxzhBetween(String value1, String value2) {
            addCriterion("mffxzh between", value1, value2, "mffxzh");
            return (Criteria) this;
        }

        public Criteria andMffxzhNotBetween(String value1, String value2) {
            addCriterion("mffxzh not between", value1, value2, "mffxzh");
            return (Criteria) this;
        }

        public Criteria andMffxblIsNull() {
            addCriterion("mffxbl is null");
            return (Criteria) this;
        }

        public Criteria andMffxblIsNotNull() {
            addCriterion("mffxbl is not null");
            return (Criteria) this;
        }

        public Criteria andMffxblEqualTo(java.math.BigDecimal value) {
            addCriterion("mffxbl =", value, "mffxbl");
            return (Criteria) this;
        }

        public Criteria andMffxblNotEqualTo(java.math.BigDecimal value) {
            addCriterion("mffxbl <>", value, "mffxbl");
            return (Criteria) this;
        }

        public Criteria andMffxblGreaterThan(java.math.BigDecimal value) {
            addCriterion("mffxbl >", value, "mffxbl");
            return (Criteria) this;
        }

        public Criteria andMffxblGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("mffxbl >=", value, "mffxbl");
            return (Criteria) this;
        }

        public Criteria andMffxblLessThan(java.math.BigDecimal value) {
            addCriterion("mffxbl <", value, "mffxbl");
            return (Criteria) this;
        }

        public Criteria andMffxblLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("mffxbl <=", value, "mffxbl");
            return (Criteria) this;
        }

        public Criteria andMffxblLike(java.math.BigDecimal value) {
            addCriterion("mffxbl like", value, "mffxbl");
            return (Criteria) this;
        }

        public Criteria andMffxblNotLike(java.math.BigDecimal value) {
            addCriterion("mffxbl not like", value, "mffxbl");
            return (Criteria) this;
        }

        public Criteria andMffxblIn(List<java.math.BigDecimal> values) {
            addCriterion("mffxbl in", values, "mffxbl");
            return (Criteria) this;
        }

        public Criteria andMffxblNotIn(List<java.math.BigDecimal> values) {
            addCriterion("mffxbl not in", values, "mffxbl");
            return (Criteria) this;
        }

        public Criteria andMffxblBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("mffxbl between", value1, value2, "mffxbl");
            return (Criteria) this;
        }

        public Criteria andMffxblNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("mffxbl not between", value1, value2, "mffxbl");
            return (Criteria) this;
        }

        public Criteria andTxfxfsIsNull() {
            addCriterion("txfxfs is null");
            return (Criteria) this;
        }

        public Criteria andTxfxfsIsNotNull() {
            addCriterion("txfxfs is not null");
            return (Criteria) this;
        }

        public Criteria andTxfxfsEqualTo(String value) {
            addCriterion("txfxfs =", value, "txfxfs");
            return (Criteria) this;
        }

        public Criteria andTxfxfsNotEqualTo(String value) {
            addCriterion("txfxfs <>", value, "txfxfs");
            return (Criteria) this;
        }

        public Criteria andTxfxfsGreaterThan(String value) {
            addCriterion("txfxfs >", value, "txfxfs");
            return (Criteria) this;
        }

        public Criteria andTxfxfsGreaterThanOrEqualTo(String value) {
            addCriterion("txfxfs >=", value, "txfxfs");
            return (Criteria) this;
        }

        public Criteria andTxfxfsLessThan(String value) {
            addCriterion("txfxfs <", value, "txfxfs");
            return (Criteria) this;
        }

        public Criteria andTxfxfsLessThanOrEqualTo(String value) {
            addCriterion("txfxfs <=", value, "txfxfs");
            return (Criteria) this;
        }

        public Criteria andTxfxfsLike(String value) {
            addCriterion("txfxfs like", value, "txfxfs");
            return (Criteria) this;
        }

        public Criteria andTxfxfsNotLike(String value) {
            addCriterion("txfxfs not like", value, "txfxfs");
            return (Criteria) this;
        }

        public Criteria andTxfxfsIn(List<String> values) {
            addCriterion("txfxfs in", values, "txfxfs");
            return (Criteria) this;
        }

        public Criteria andTxfxfsNotIn(List<String> values) {
            addCriterion("txfxfs not in", values, "txfxfs");
            return (Criteria) this;
        }

        public Criteria andTxfxfsBetween(String value1, String value2) {
            addCriterion("txfxfs between", value1, value2, "txfxfs");
            return (Criteria) this;
        }

        public Criteria andTxfxfsNotBetween(String value1, String value2) {
            addCriterion("txfxfs not between", value1, value2, "txfxfs");
            return (Criteria) this;
        }

        public Criteria andCxcfbhIsNull() {
            addCriterion("cxcfbh is null");
            return (Criteria) this;
        }

        public Criteria andCxcfbhIsNotNull() {
            addCriterion("cxcfbh is not null");
            return (Criteria) this;
        }

        public Criteria andCxcfbhEqualTo(String value) {
            addCriterion("cxcfbh =", value, "cxcfbh");
            return (Criteria) this;
        }

        public Criteria andCxcfbhNotEqualTo(String value) {
            addCriterion("cxcfbh <>", value, "cxcfbh");
            return (Criteria) this;
        }

        public Criteria andCxcfbhGreaterThan(String value) {
            addCriterion("cxcfbh >", value, "cxcfbh");
            return (Criteria) this;
        }

        public Criteria andCxcfbhGreaterThanOrEqualTo(String value) {
            addCriterion("cxcfbh >=", value, "cxcfbh");
            return (Criteria) this;
        }

        public Criteria andCxcfbhLessThan(String value) {
            addCriterion("cxcfbh <", value, "cxcfbh");
            return (Criteria) this;
        }

        public Criteria andCxcfbhLessThanOrEqualTo(String value) {
            addCriterion("cxcfbh <=", value, "cxcfbh");
            return (Criteria) this;
        }

        public Criteria andCxcfbhLike(String value) {
            addCriterion("cxcfbh like", value, "cxcfbh");
            return (Criteria) this;
        }

        public Criteria andCxcfbhNotLike(String value) {
            addCriterion("cxcfbh not like", value, "cxcfbh");
            return (Criteria) this;
        }

        public Criteria andCxcfbhIn(List<String> values) {
            addCriterion("cxcfbh in", values, "cxcfbh");
            return (Criteria) this;
        }

        public Criteria andCxcfbhNotIn(List<String> values) {
            addCriterion("cxcfbh not in", values, "cxcfbh");
            return (Criteria) this;
        }

        public Criteria andCxcfbhBetween(String value1, String value2) {
            addCriterion("cxcfbh between", value1, value2, "cxcfbh");
            return (Criteria) this;
        }

        public Criteria andCxcfbhNotBetween(String value1, String value2) {
            addCriterion("cxcfbh not between", value1, value2, "cxcfbh");
            return (Criteria) this;
        }

        public Criteria andSfxthcIsNull() {
            addCriterion("sfxthc is null");
            return (Criteria) this;
        }

        public Criteria andSfxthcIsNotNull() {
            addCriterion("sfxthc is not null");
            return (Criteria) this;
        }

        public Criteria andSfxthcEqualTo(String value) {
            addCriterion("sfxthc =", value, "sfxthc");
            return (Criteria) this;
        }

        public Criteria andSfxthcNotEqualTo(String value) {
            addCriterion("sfxthc <>", value, "sfxthc");
            return (Criteria) this;
        }

        public Criteria andSfxthcGreaterThan(String value) {
            addCriterion("sfxthc >", value, "sfxthc");
            return (Criteria) this;
        }

        public Criteria andSfxthcGreaterThanOrEqualTo(String value) {
            addCriterion("sfxthc >=", value, "sfxthc");
            return (Criteria) this;
        }

        public Criteria andSfxthcLessThan(String value) {
            addCriterion("sfxthc <", value, "sfxthc");
            return (Criteria) this;
        }

        public Criteria andSfxthcLessThanOrEqualTo(String value) {
            addCriterion("sfxthc <=", value, "sfxthc");
            return (Criteria) this;
        }

        public Criteria andSfxthcLike(String value) {
            addCriterion("sfxthc like", value, "sfxthc");
            return (Criteria) this;
        }

        public Criteria andSfxthcNotLike(String value) {
            addCriterion("sfxthc not like", value, "sfxthc");
            return (Criteria) this;
        }

        public Criteria andSfxthcIn(List<String> values) {
            addCriterion("sfxthc in", values, "sfxthc");
            return (Criteria) this;
        }

        public Criteria andSfxthcNotIn(List<String> values) {
            addCriterion("sfxthc not in", values, "sfxthc");
            return (Criteria) this;
        }

        public Criteria andSfxthcBetween(String value1, String value2) {
            addCriterion("sfxthc between", value1, value2, "sfxthc");
            return (Criteria) this;
        }

        public Criteria andSfxthcNotBetween(String value1, String value2) {
            addCriterion("sfxthc not between", value1, value2, "sfxthc");
            return (Criteria) this;
        }

        public Criteria andJieszhIsNull() {
            addCriterion("jieszh is null");
            return (Criteria) this;
        }

        public Criteria andJieszhIsNotNull() {
            addCriterion("jieszh is not null");
            return (Criteria) this;
        }

        public Criteria andJieszhEqualTo(String value) {
            addCriterion("jieszh =", value, "jieszh");
            return (Criteria) this;
        }

        public Criteria andJieszhNotEqualTo(String value) {
            addCriterion("jieszh <>", value, "jieszh");
            return (Criteria) this;
        }

        public Criteria andJieszhGreaterThan(String value) {
            addCriterion("jieszh >", value, "jieszh");
            return (Criteria) this;
        }

        public Criteria andJieszhGreaterThanOrEqualTo(String value) {
            addCriterion("jieszh >=", value, "jieszh");
            return (Criteria) this;
        }

        public Criteria andJieszhLessThan(String value) {
            addCriterion("jieszh <", value, "jieszh");
            return (Criteria) this;
        }

        public Criteria andJieszhLessThanOrEqualTo(String value) {
            addCriterion("jieszh <=", value, "jieszh");
            return (Criteria) this;
        }

        public Criteria andJieszhLike(String value) {
            addCriterion("jieszh like", value, "jieszh");
            return (Criteria) this;
        }

        public Criteria andJieszhNotLike(String value) {
            addCriterion("jieszh not like", value, "jieszh");
            return (Criteria) this;
        }

        public Criteria andJieszhIn(List<String> values) {
            addCriterion("jieszh in", values, "jieszh");
            return (Criteria) this;
        }

        public Criteria andJieszhNotIn(List<String> values) {
            addCriterion("jieszh not in", values, "jieszh");
            return (Criteria) this;
        }

        public Criteria andJieszhBetween(String value1, String value2) {
            addCriterion("jieszh between", value1, value2, "jieszh");
            return (Criteria) this;
        }

        public Criteria andJieszhNotBetween(String value1, String value2) {
            addCriterion("jieszh not between", value1, value2, "jieszh");
            return (Criteria) this;
        }

        public Criteria andXctzrqIsNull() {
            addCriterion("xctzrq is null");
            return (Criteria) this;
        }

        public Criteria andXctzrqIsNotNull() {
            addCriterion("xctzrq is not null");
            return (Criteria) this;
        }

        public Criteria andXctzrqEqualTo(String value) {
            addCriterion("xctzrq =", value, "xctzrq");
            return (Criteria) this;
        }

        public Criteria andXctzrqNotEqualTo(String value) {
            addCriterion("xctzrq <>", value, "xctzrq");
            return (Criteria) this;
        }

        public Criteria andXctzrqGreaterThan(String value) {
            addCriterion("xctzrq >", value, "xctzrq");
            return (Criteria) this;
        }

        public Criteria andXctzrqGreaterThanOrEqualTo(String value) {
            addCriterion("xctzrq >=", value, "xctzrq");
            return (Criteria) this;
        }

        public Criteria andXctzrqLessThan(String value) {
            addCriterion("xctzrq <", value, "xctzrq");
            return (Criteria) this;
        }

        public Criteria andXctzrqLessThanOrEqualTo(String value) {
            addCriterion("xctzrq <=", value, "xctzrq");
            return (Criteria) this;
        }

        public Criteria andXctzrqLike(String value) {
            addCriterion("xctzrq like", value, "xctzrq");
            return (Criteria) this;
        }

        public Criteria andXctzrqNotLike(String value) {
            addCriterion("xctzrq not like", value, "xctzrq");
            return (Criteria) this;
        }

        public Criteria andXctzrqIn(List<String> values) {
            addCriterion("xctzrq in", values, "xctzrq");
            return (Criteria) this;
        }

        public Criteria andXctzrqNotIn(List<String> values) {
            addCriterion("xctzrq not in", values, "xctzrq");
            return (Criteria) this;
        }

        public Criteria andXctzrqBetween(String value1, String value2) {
            addCriterion("xctzrq between", value1, value2, "xctzrq");
            return (Criteria) this;
        }

        public Criteria andXctzrqNotBetween(String value1, String value2) {
            addCriterion("xctzrq not between", value1, value2, "xctzrq");
            return (Criteria) this;
        }

        public Criteria andSctzrqIsNull() {
            addCriterion("sctzrq is null");
            return (Criteria) this;
        }

        public Criteria andSctzrqIsNotNull() {
            addCriterion("sctzrq is not null");
            return (Criteria) this;
        }

        public Criteria andSctzrqEqualTo(String value) {
            addCriterion("sctzrq =", value, "sctzrq");
            return (Criteria) this;
        }

        public Criteria andSctzrqNotEqualTo(String value) {
            addCriterion("sctzrq <>", value, "sctzrq");
            return (Criteria) this;
        }

        public Criteria andSctzrqGreaterThan(String value) {
            addCriterion("sctzrq >", value, "sctzrq");
            return (Criteria) this;
        }

        public Criteria andSctzrqGreaterThanOrEqualTo(String value) {
            addCriterion("sctzrq >=", value, "sctzrq");
            return (Criteria) this;
        }

        public Criteria andSctzrqLessThan(String value) {
            addCriterion("sctzrq <", value, "sctzrq");
            return (Criteria) this;
        }

        public Criteria andSctzrqLessThanOrEqualTo(String value) {
            addCriterion("sctzrq <=", value, "sctzrq");
            return (Criteria) this;
        }

        public Criteria andSctzrqLike(String value) {
            addCriterion("sctzrq like", value, "sctzrq");
            return (Criteria) this;
        }

        public Criteria andSctzrqNotLike(String value) {
            addCriterion("sctzrq not like", value, "sctzrq");
            return (Criteria) this;
        }

        public Criteria andSctzrqIn(List<String> values) {
            addCriterion("sctzrq in", values, "sctzrq");
            return (Criteria) this;
        }

        public Criteria andSctzrqNotIn(List<String> values) {
            addCriterion("sctzrq not in", values, "sctzrq");
            return (Criteria) this;
        }

        public Criteria andSctzrqBetween(String value1, String value2) {
            addCriterion("sctzrq between", value1, value2, "sctzrq");
            return (Criteria) this;
        }

        public Criteria andSctzrqNotBetween(String value1, String value2) {
            addCriterion("sctzrq not between", value1, value2, "sctzrq");
            return (Criteria) this;
        }

        public Criteria andBcmdllIsNull() {
            addCriterion("bcmdll is null");
            return (Criteria) this;
        }

        public Criteria andBcmdllIsNotNull() {
            addCriterion("bcmdll is not null");
            return (Criteria) this;
        }

        public Criteria andBcmdllEqualTo(java.math.BigDecimal value) {
            addCriterion("bcmdll =", value, "bcmdll");
            return (Criteria) this;
        }

        public Criteria andBcmdllNotEqualTo(java.math.BigDecimal value) {
            addCriterion("bcmdll <>", value, "bcmdll");
            return (Criteria) this;
        }

        public Criteria andBcmdllGreaterThan(java.math.BigDecimal value) {
            addCriterion("bcmdll >", value, "bcmdll");
            return (Criteria) this;
        }

        public Criteria andBcmdllGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("bcmdll >=", value, "bcmdll");
            return (Criteria) this;
        }

        public Criteria andBcmdllLessThan(java.math.BigDecimal value) {
            addCriterion("bcmdll <", value, "bcmdll");
            return (Criteria) this;
        }

        public Criteria andBcmdllLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("bcmdll <=", value, "bcmdll");
            return (Criteria) this;
        }

        public Criteria andBcmdllLike(java.math.BigDecimal value) {
            addCriterion("bcmdll like", value, "bcmdll");
            return (Criteria) this;
        }

        public Criteria andBcmdllNotLike(java.math.BigDecimal value) {
            addCriterion("bcmdll not like", value, "bcmdll");
            return (Criteria) this;
        }

        public Criteria andBcmdllIn(List<java.math.BigDecimal> values) {
            addCriterion("bcmdll in", values, "bcmdll");
            return (Criteria) this;
        }

        public Criteria andBcmdllNotIn(List<java.math.BigDecimal> values) {
            addCriterion("bcmdll not in", values, "bcmdll");
            return (Criteria) this;
        }

        public Criteria andBcmdllBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("bcmdll between", value1, value2, "bcmdll");
            return (Criteria) this;
        }

        public Criteria andBcmdllNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("bcmdll not between", value1, value2, "bcmdll");
            return (Criteria) this;
        }

        public Criteria andBcmdlxIsNull() {
            addCriterion("bcmdlx is null");
            return (Criteria) this;
        }

        public Criteria andBcmdlxIsNotNull() {
            addCriterion("bcmdlx is not null");
            return (Criteria) this;
        }

        public Criteria andBcmdlxEqualTo(java.math.BigDecimal value) {
            addCriterion("bcmdlx =", value, "bcmdlx");
            return (Criteria) this;
        }

        public Criteria andBcmdlxNotEqualTo(java.math.BigDecimal value) {
            addCriterion("bcmdlx <>", value, "bcmdlx");
            return (Criteria) this;
        }

        public Criteria andBcmdlxGreaterThan(java.math.BigDecimal value) {
            addCriterion("bcmdlx >", value, "bcmdlx");
            return (Criteria) this;
        }

        public Criteria andBcmdlxGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("bcmdlx >=", value, "bcmdlx");
            return (Criteria) this;
        }

        public Criteria andBcmdlxLessThan(java.math.BigDecimal value) {
            addCriterion("bcmdlx <", value, "bcmdlx");
            return (Criteria) this;
        }

        public Criteria andBcmdlxLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("bcmdlx <=", value, "bcmdlx");
            return (Criteria) this;
        }

        public Criteria andBcmdlxLike(java.math.BigDecimal value) {
            addCriterion("bcmdlx like", value, "bcmdlx");
            return (Criteria) this;
        }

        public Criteria andBcmdlxNotLike(java.math.BigDecimal value) {
            addCriterion("bcmdlx not like", value, "bcmdlx");
            return (Criteria) this;
        }

        public Criteria andBcmdlxIn(List<java.math.BigDecimal> values) {
            addCriterion("bcmdlx in", values, "bcmdlx");
            return (Criteria) this;
        }

        public Criteria andBcmdlxNotIn(List<java.math.BigDecimal> values) {
            addCriterion("bcmdlx not in", values, "bcmdlx");
            return (Criteria) this;
        }

        public Criteria andBcmdlxBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("bcmdlx between", value1, value2, "bcmdlx");
            return (Criteria) this;
        }

        public Criteria andBcmdlxNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("bcmdlx not between", value1, value2, "bcmdlx");
            return (Criteria) this;
        }

        public Criteria andZhungtIsNull() {
            addCriterion("zhungt is null");
            return (Criteria) this;
        }

        public Criteria andZhungtIsNotNull() {
            addCriterion("zhungt is not null");
            return (Criteria) this;
        }

        public Criteria andZhungtEqualTo(String value) {
            addCriterion("zhungt =", value, "zhungt");
            return (Criteria) this;
        }

        public Criteria andZhungtNotEqualTo(String value) {
            addCriterion("zhungt <>", value, "zhungt");
            return (Criteria) this;
        }

        public Criteria andZhungtGreaterThan(String value) {
            addCriterion("zhungt >", value, "zhungt");
            return (Criteria) this;
        }

        public Criteria andZhungtGreaterThanOrEqualTo(String value) {
            addCriterion("zhungt >=", value, "zhungt");
            return (Criteria) this;
        }

        public Criteria andZhungtLessThan(String value) {
            addCriterion("zhungt <", value, "zhungt");
            return (Criteria) this;
        }

        public Criteria andZhungtLessThanOrEqualTo(String value) {
            addCriterion("zhungt <=", value, "zhungt");
            return (Criteria) this;
        }

        public Criteria andZhungtLike(String value) {
            addCriterion("zhungt like", value, "zhungt");
            return (Criteria) this;
        }

        public Criteria andZhungtNotLike(String value) {
            addCriterion("zhungt not like", value, "zhungt");
            return (Criteria) this;
        }

        public Criteria andZhungtIn(List<String> values) {
            addCriterion("zhungt in", values, "zhungt");
            return (Criteria) this;
        }

        public Criteria andZhungtNotIn(List<String> values) {
            addCriterion("zhungt not in", values, "zhungt");
            return (Criteria) this;
        }

        public Criteria andZhungtBetween(String value1, String value2) {
            addCriterion("zhungt between", value1, value2, "zhungt");
            return (Criteria) this;
        }

        public Criteria andZhungtNotBetween(String value1, String value2) {
            addCriterion("zhungt not between", value1, value2, "zhungt");
            return (Criteria) this;
        }

        public Criteria andMxxhaoIsNull() {
            addCriterion("mxxhao is null");
            return (Criteria) this;
        }

        public Criteria andMxxhaoIsNotNull() {
            addCriterion("mxxhao is not null");
            return (Criteria) this;
        }

        public Criteria andMxxhaoEqualTo(java.math.BigDecimal value) {
            addCriterion("mxxhao =", value, "mxxhao");
            return (Criteria) this;
        }

        public Criteria andMxxhaoNotEqualTo(java.math.BigDecimal value) {
            addCriterion("mxxhao <>", value, "mxxhao");
            return (Criteria) this;
        }

        public Criteria andMxxhaoGreaterThan(java.math.BigDecimal value) {
            addCriterion("mxxhao >", value, "mxxhao");
            return (Criteria) this;
        }

        public Criteria andMxxhaoGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("mxxhao >=", value, "mxxhao");
            return (Criteria) this;
        }

        public Criteria andMxxhaoLessThan(java.math.BigDecimal value) {
            addCriterion("mxxhao <", value, "mxxhao");
            return (Criteria) this;
        }

        public Criteria andMxxhaoLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("mxxhao <=", value, "mxxhao");
            return (Criteria) this;
        }

        public Criteria andMxxhaoLike(java.math.BigDecimal value) {
            addCriterion("mxxhao like", value, "mxxhao");
            return (Criteria) this;
        }

        public Criteria andMxxhaoNotLike(java.math.BigDecimal value) {
            addCriterion("mxxhao not like", value, "mxxhao");
            return (Criteria) this;
        }

        public Criteria andMxxhaoIn(List<java.math.BigDecimal> values) {
            addCriterion("mxxhao in", values, "mxxhao");
            return (Criteria) this;
        }

        public Criteria andMxxhaoNotIn(List<java.math.BigDecimal> values) {
            addCriterion("mxxhao not in", values, "mxxhao");
            return (Criteria) this;
        }

        public Criteria andMxxhaoBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("mxxhao between", value1, value2, "mxxhao");
            return (Criteria) this;
        }

        public Criteria andMxxhaoNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("mxxhao not between", value1, value2, "mxxhao");
            return (Criteria) this;
        }

        public Criteria andLururqIsNull() {
            addCriterion("lururq is null");
            return (Criteria) this;
        }

        public Criteria andLururqIsNotNull() {
            addCriterion("lururq is not null");
            return (Criteria) this;
        }

        public Criteria andLururqEqualTo(String value) {
            addCriterion("lururq =", value, "lururq");
            return (Criteria) this;
        }

        public Criteria andLururqNotEqualTo(String value) {
            addCriterion("lururq <>", value, "lururq");
            return (Criteria) this;
        }

        public Criteria andLururqGreaterThan(String value) {
            addCriterion("lururq >", value, "lururq");
            return (Criteria) this;
        }

        public Criteria andLururqGreaterThanOrEqualTo(String value) {
            addCriterion("lururq >=", value, "lururq");
            return (Criteria) this;
        }

        public Criteria andLururqLessThan(String value) {
            addCriterion("lururq <", value, "lururq");
            return (Criteria) this;
        }

        public Criteria andLururqLessThanOrEqualTo(String value) {
            addCriterion("lururq <=", value, "lururq");
            return (Criteria) this;
        }

        public Criteria andLururqLike(String value) {
            addCriterion("lururq like", value, "lururq");
            return (Criteria) this;
        }

        public Criteria andLururqNotLike(String value) {
            addCriterion("lururq not like", value, "lururq");
            return (Criteria) this;
        }

        public Criteria andLururqIn(List<String> values) {
            addCriterion("lururq in", values, "lururq");
            return (Criteria) this;
        }

        public Criteria andLururqNotIn(List<String> values) {
            addCriterion("lururq not in", values, "lururq");
            return (Criteria) this;
        }

        public Criteria andLururqBetween(String value1, String value2) {
            addCriterion("lururq between", value1, value2, "lururq");
            return (Criteria) this;
        }

        public Criteria andLururqNotBetween(String value1, String value2) {
            addCriterion("lururq not between", value1, value2, "lururq");
            return (Criteria) this;
        }

        public Criteria andLurugyIsNull() {
            addCriterion("lurugy is null");
            return (Criteria) this;
        }

        public Criteria andLurugyIsNotNull() {
            addCriterion("lurugy is not null");
            return (Criteria) this;
        }

        public Criteria andLurugyEqualTo(String value) {
            addCriterion("lurugy =", value, "lurugy");
            return (Criteria) this;
        }

        public Criteria andLurugyNotEqualTo(String value) {
            addCriterion("lurugy <>", value, "lurugy");
            return (Criteria) this;
        }

        public Criteria andLurugyGreaterThan(String value) {
            addCriterion("lurugy >", value, "lurugy");
            return (Criteria) this;
        }

        public Criteria andLurugyGreaterThanOrEqualTo(String value) {
            addCriterion("lurugy >=", value, "lurugy");
            return (Criteria) this;
        }

        public Criteria andLurugyLessThan(String value) {
            addCriterion("lurugy <", value, "lurugy");
            return (Criteria) this;
        }

        public Criteria andLurugyLessThanOrEqualTo(String value) {
            addCriterion("lurugy <=", value, "lurugy");
            return (Criteria) this;
        }

        public Criteria andLurugyLike(String value) {
            addCriterion("lurugy like", value, "lurugy");
            return (Criteria) this;
        }

        public Criteria andLurugyNotLike(String value) {
            addCriterion("lurugy not like", value, "lurugy");
            return (Criteria) this;
        }

        public Criteria andLurugyIn(List<String> values) {
            addCriterion("lurugy in", values, "lurugy");
            return (Criteria) this;
        }

        public Criteria andLurugyNotIn(List<String> values) {
            addCriterion("lurugy not in", values, "lurugy");
            return (Criteria) this;
        }

        public Criteria andLurugyBetween(String value1, String value2) {
            addCriterion("lurugy between", value1, value2, "lurugy");
            return (Criteria) this;
        }

        public Criteria andLurugyNotBetween(String value1, String value2) {
            addCriterion("lurugy not between", value1, value2, "lurugy");
            return (Criteria) this;
        }

        public Criteria andFuherqIsNull() {
            addCriterion("fuherq is null");
            return (Criteria) this;
        }

        public Criteria andFuherqIsNotNull() {
            addCriterion("fuherq is not null");
            return (Criteria) this;
        }

        public Criteria andFuherqEqualTo(String value) {
            addCriterion("fuherq =", value, "fuherq");
            return (Criteria) this;
        }

        public Criteria andFuherqNotEqualTo(String value) {
            addCriterion("fuherq <>", value, "fuherq");
            return (Criteria) this;
        }

        public Criteria andFuherqGreaterThan(String value) {
            addCriterion("fuherq >", value, "fuherq");
            return (Criteria) this;
        }

        public Criteria andFuherqGreaterThanOrEqualTo(String value) {
            addCriterion("fuherq >=", value, "fuherq");
            return (Criteria) this;
        }

        public Criteria andFuherqLessThan(String value) {
            addCriterion("fuherq <", value, "fuherq");
            return (Criteria) this;
        }

        public Criteria andFuherqLessThanOrEqualTo(String value) {
            addCriterion("fuherq <=", value, "fuherq");
            return (Criteria) this;
        }

        public Criteria andFuherqLike(String value) {
            addCriterion("fuherq like", value, "fuherq");
            return (Criteria) this;
        }

        public Criteria andFuherqNotLike(String value) {
            addCriterion("fuherq not like", value, "fuherq");
            return (Criteria) this;
        }

        public Criteria andFuherqIn(List<String> values) {
            addCriterion("fuherq in", values, "fuherq");
            return (Criteria) this;
        }

        public Criteria andFuherqNotIn(List<String> values) {
            addCriterion("fuherq not in", values, "fuherq");
            return (Criteria) this;
        }

        public Criteria andFuherqBetween(String value1, String value2) {
            addCriterion("fuherq between", value1, value2, "fuherq");
            return (Criteria) this;
        }

        public Criteria andFuherqNotBetween(String value1, String value2) {
            addCriterion("fuherq not between", value1, value2, "fuherq");
            return (Criteria) this;
        }

        public Criteria andFuhegyIsNull() {
            addCriterion("fuhegy is null");
            return (Criteria) this;
        }

        public Criteria andFuhegyIsNotNull() {
            addCriterion("fuhegy is not null");
            return (Criteria) this;
        }

        public Criteria andFuhegyEqualTo(String value) {
            addCriterion("fuhegy =", value, "fuhegy");
            return (Criteria) this;
        }

        public Criteria andFuhegyNotEqualTo(String value) {
            addCriterion("fuhegy <>", value, "fuhegy");
            return (Criteria) this;
        }

        public Criteria andFuhegyGreaterThan(String value) {
            addCriterion("fuhegy >", value, "fuhegy");
            return (Criteria) this;
        }

        public Criteria andFuhegyGreaterThanOrEqualTo(String value) {
            addCriterion("fuhegy >=", value, "fuhegy");
            return (Criteria) this;
        }

        public Criteria andFuhegyLessThan(String value) {
            addCriterion("fuhegy <", value, "fuhegy");
            return (Criteria) this;
        }

        public Criteria andFuhegyLessThanOrEqualTo(String value) {
            addCriterion("fuhegy <=", value, "fuhegy");
            return (Criteria) this;
        }

        public Criteria andFuhegyLike(String value) {
            addCriterion("fuhegy like", value, "fuhegy");
            return (Criteria) this;
        }

        public Criteria andFuhegyNotLike(String value) {
            addCriterion("fuhegy not like", value, "fuhegy");
            return (Criteria) this;
        }

        public Criteria andFuhegyIn(List<String> values) {
            addCriterion("fuhegy in", values, "fuhegy");
            return (Criteria) this;
        }

        public Criteria andFuhegyNotIn(List<String> values) {
            addCriterion("fuhegy not in", values, "fuhegy");
            return (Criteria) this;
        }

        public Criteria andFuhegyBetween(String value1, String value2) {
            addCriterion("fuhegy between", value1, value2, "fuhegy");
            return (Criteria) this;
        }

        public Criteria andFuhegyNotBetween(String value1, String value2) {
            addCriterion("fuhegy not between", value1, value2, "fuhegy");
            return (Criteria) this;
        }

        public Criteria andWeihrqIsNull() {
            addCriterion("weihrq is null");
            return (Criteria) this;
        }

        public Criteria andWeihrqIsNotNull() {
            addCriterion("weihrq is not null");
            return (Criteria) this;
        }

        public Criteria andWeihrqEqualTo(String value) {
            addCriterion("weihrq =", value, "weihrq");
            return (Criteria) this;
        }

        public Criteria andWeihrqNotEqualTo(String value) {
            addCriterion("weihrq <>", value, "weihrq");
            return (Criteria) this;
        }

        public Criteria andWeihrqGreaterThan(String value) {
            addCriterion("weihrq >", value, "weihrq");
            return (Criteria) this;
        }

        public Criteria andWeihrqGreaterThanOrEqualTo(String value) {
            addCriterion("weihrq >=", value, "weihrq");
            return (Criteria) this;
        }

        public Criteria andWeihrqLessThan(String value) {
            addCriterion("weihrq <", value, "weihrq");
            return (Criteria) this;
        }

        public Criteria andWeihrqLessThanOrEqualTo(String value) {
            addCriterion("weihrq <=", value, "weihrq");
            return (Criteria) this;
        }

        public Criteria andWeihrqLike(String value) {
            addCriterion("weihrq like", value, "weihrq");
            return (Criteria) this;
        }

        public Criteria andWeihrqNotLike(String value) {
            addCriterion("weihrq not like", value, "weihrq");
            return (Criteria) this;
        }

        public Criteria andWeihrqIn(List<String> values) {
            addCriterion("weihrq in", values, "weihrq");
            return (Criteria) this;
        }

        public Criteria andWeihrqNotIn(List<String> values) {
            addCriterion("weihrq not in", values, "weihrq");
            return (Criteria) this;
        }

        public Criteria andWeihrqBetween(String value1, String value2) {
            addCriterion("weihrq between", value1, value2, "weihrq");
            return (Criteria) this;
        }

        public Criteria andWeihrqNotBetween(String value1, String value2) {
            addCriterion("weihrq not between", value1, value2, "weihrq");
            return (Criteria) this;
        }

        public Criteria andWeihgyIsNull() {
            addCriterion("weihgy is null");
            return (Criteria) this;
        }

        public Criteria andWeihgyIsNotNull() {
            addCriterion("weihgy is not null");
            return (Criteria) this;
        }

        public Criteria andWeihgyEqualTo(String value) {
            addCriterion("weihgy =", value, "weihgy");
            return (Criteria) this;
        }

        public Criteria andWeihgyNotEqualTo(String value) {
            addCriterion("weihgy <>", value, "weihgy");
            return (Criteria) this;
        }

        public Criteria andWeihgyGreaterThan(String value) {
            addCriterion("weihgy >", value, "weihgy");
            return (Criteria) this;
        }

        public Criteria andWeihgyGreaterThanOrEqualTo(String value) {
            addCriterion("weihgy >=", value, "weihgy");
            return (Criteria) this;
        }

        public Criteria andWeihgyLessThan(String value) {
            addCriterion("weihgy <", value, "weihgy");
            return (Criteria) this;
        }

        public Criteria andWeihgyLessThanOrEqualTo(String value) {
            addCriterion("weihgy <=", value, "weihgy");
            return (Criteria) this;
        }

        public Criteria andWeihgyLike(String value) {
            addCriterion("weihgy like", value, "weihgy");
            return (Criteria) this;
        }

        public Criteria andWeihgyNotLike(String value) {
            addCriterion("weihgy not like", value, "weihgy");
            return (Criteria) this;
        }

        public Criteria andWeihgyIn(List<String> values) {
            addCriterion("weihgy in", values, "weihgy");
            return (Criteria) this;
        }

        public Criteria andWeihgyNotIn(List<String> values) {
            addCriterion("weihgy not in", values, "weihgy");
            return (Criteria) this;
        }

        public Criteria andWeihgyBetween(String value1, String value2) {
            addCriterion("weihgy between", value1, value2, "weihgy");
            return (Criteria) this;
        }

        public Criteria andWeihgyNotBetween(String value1, String value2) {
            addCriterion("weihgy not between", value1, value2, "weihgy");
            return (Criteria) this;
        }

        public Criteria andJioyrqIsNull() {
            addCriterion("jioyrq is null");
            return (Criteria) this;
        }

        public Criteria andJioyrqIsNotNull() {
            addCriterion("jioyrq is not null");
            return (Criteria) this;
        }

        public Criteria andJioyrqEqualTo(String value) {
            addCriterion("jioyrq =", value, "jioyrq");
            return (Criteria) this;
        }

        public Criteria andJioyrqNotEqualTo(String value) {
            addCriterion("jioyrq <>", value, "jioyrq");
            return (Criteria) this;
        }

        public Criteria andJioyrqGreaterThan(String value) {
            addCriterion("jioyrq >", value, "jioyrq");
            return (Criteria) this;
        }

        public Criteria andJioyrqGreaterThanOrEqualTo(String value) {
            addCriterion("jioyrq >=", value, "jioyrq");
            return (Criteria) this;
        }

        public Criteria andJioyrqLessThan(String value) {
            addCriterion("jioyrq <", value, "jioyrq");
            return (Criteria) this;
        }

        public Criteria andJioyrqLessThanOrEqualTo(String value) {
            addCriterion("jioyrq <=", value, "jioyrq");
            return (Criteria) this;
        }

        public Criteria andJioyrqLike(String value) {
            addCriterion("jioyrq like", value, "jioyrq");
            return (Criteria) this;
        }

        public Criteria andJioyrqNotLike(String value) {
            addCriterion("jioyrq not like", value, "jioyrq");
            return (Criteria) this;
        }

        public Criteria andJioyrqIn(List<String> values) {
            addCriterion("jioyrq in", values, "jioyrq");
            return (Criteria) this;
        }

        public Criteria andJioyrqNotIn(List<String> values) {
            addCriterion("jioyrq not in", values, "jioyrq");
            return (Criteria) this;
        }

        public Criteria andJioyrqBetween(String value1, String value2) {
            addCriterion("jioyrq between", value1, value2, "jioyrq");
            return (Criteria) this;
        }

        public Criteria andJioyrqNotBetween(String value1, String value2) {
            addCriterion("jioyrq not between", value1, value2, "jioyrq");
            return (Criteria) this;
        }

        public Criteria andWeihjgIsNull() {
            addCriterion("weihjg is null");
            return (Criteria) this;
        }

        public Criteria andWeihjgIsNotNull() {
            addCriterion("weihjg is not null");
            return (Criteria) this;
        }

        public Criteria andWeihjgEqualTo(String value) {
            addCriterion("weihjg =", value, "weihjg");
            return (Criteria) this;
        }

        public Criteria andWeihjgNotEqualTo(String value) {
            addCriterion("weihjg <>", value, "weihjg");
            return (Criteria) this;
        }

        public Criteria andWeihjgGreaterThan(String value) {
            addCriterion("weihjg >", value, "weihjg");
            return (Criteria) this;
        }

        public Criteria andWeihjgGreaterThanOrEqualTo(String value) {
            addCriterion("weihjg >=", value, "weihjg");
            return (Criteria) this;
        }

        public Criteria andWeihjgLessThan(String value) {
            addCriterion("weihjg <", value, "weihjg");
            return (Criteria) this;
        }

        public Criteria andWeihjgLessThanOrEqualTo(String value) {
            addCriterion("weihjg <=", value, "weihjg");
            return (Criteria) this;
        }

        public Criteria andWeihjgLike(String value) {
            addCriterion("weihjg like", value, "weihjg");
            return (Criteria) this;
        }

        public Criteria andWeihjgNotLike(String value) {
            addCriterion("weihjg not like", value, "weihjg");
            return (Criteria) this;
        }

        public Criteria andWeihjgIn(List<String> values) {
            addCriterion("weihjg in", values, "weihjg");
            return (Criteria) this;
        }

        public Criteria andWeihjgNotIn(List<String> values) {
            addCriterion("weihjg not in", values, "weihjg");
            return (Criteria) this;
        }

        public Criteria andWeihjgBetween(String value1, String value2) {
            addCriterion("weihjg between", value1, value2, "weihjg");
            return (Criteria) this;
        }

        public Criteria andWeihjgNotBetween(String value1, String value2) {
            addCriterion("weihjg not between", value1, value2, "weihjg");
            return (Criteria) this;
        }

        public Criteria andWeihsjIsNull() {
            addCriterion("weihsj is null");
            return (Criteria) this;
        }

        public Criteria andWeihsjIsNotNull() {
            addCriterion("weihsj is not null");
            return (Criteria) this;
        }

        public Criteria andWeihsjEqualTo(java.math.BigDecimal value) {
            addCriterion("weihsj =", value, "weihsj");
            return (Criteria) this;
        }

        public Criteria andWeihsjNotEqualTo(java.math.BigDecimal value) {
            addCriterion("weihsj <>", value, "weihsj");
            return (Criteria) this;
        }

        public Criteria andWeihsjGreaterThan(java.math.BigDecimal value) {
            addCriterion("weihsj >", value, "weihsj");
            return (Criteria) this;
        }

        public Criteria andWeihsjGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("weihsj >=", value, "weihsj");
            return (Criteria) this;
        }

        public Criteria andWeihsjLessThan(java.math.BigDecimal value) {
            addCriterion("weihsj <", value, "weihsj");
            return (Criteria) this;
        }

        public Criteria andWeihsjLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("weihsj <=", value, "weihsj");
            return (Criteria) this;
        }

        public Criteria andWeihsjLike(java.math.BigDecimal value) {
            addCriterion("weihsj like", value, "weihsj");
            return (Criteria) this;
        }

        public Criteria andWeihsjNotLike(java.math.BigDecimal value) {
            addCriterion("weihsj not like", value, "weihsj");
            return (Criteria) this;
        }

        public Criteria andWeihsjIn(List<java.math.BigDecimal> values) {
            addCriterion("weihsj in", values, "weihsj");
            return (Criteria) this;
        }

        public Criteria andWeihsjNotIn(List<java.math.BigDecimal> values) {
            addCriterion("weihsj not in", values, "weihsj");
            return (Criteria) this;
        }

        public Criteria andWeihsjBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("weihsj between", value1, value2, "weihsj");
            return (Criteria) this;
        }

        public Criteria andWeihsjNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("weihsj not between", value1, value2, "weihsj");
            return (Criteria) this;
        }

        public Criteria andShinchIsNull() {
            addCriterion("shinch is null");
            return (Criteria) this;
        }

        public Criteria andShinchIsNotNull() {
            addCriterion("shinch is not null");
            return (Criteria) this;
        }

        public Criteria andShinchEqualTo(java.math.BigDecimal value) {
            addCriterion("shinch =", value, "shinch");
            return (Criteria) this;
        }

        public Criteria andShinchNotEqualTo(java.math.BigDecimal value) {
            addCriterion("shinch <>", value, "shinch");
            return (Criteria) this;
        }

        public Criteria andShinchGreaterThan(java.math.BigDecimal value) {
            addCriterion("shinch >", value, "shinch");
            return (Criteria) this;
        }

        public Criteria andShinchGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("shinch >=", value, "shinch");
            return (Criteria) this;
        }

        public Criteria andShinchLessThan(java.math.BigDecimal value) {
            addCriterion("shinch <", value, "shinch");
            return (Criteria) this;
        }

        public Criteria andShinchLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("shinch <=", value, "shinch");
            return (Criteria) this;
        }

        public Criteria andShinchLike(java.math.BigDecimal value) {
            addCriterion("shinch like", value, "shinch");
            return (Criteria) this;
        }

        public Criteria andShinchNotLike(java.math.BigDecimal value) {
            addCriterion("shinch not like", value, "shinch");
            return (Criteria) this;
        }

        public Criteria andShinchIn(List<java.math.BigDecimal> values) {
            addCriterion("shinch in", values, "shinch");
            return (Criteria) this;
        }

        public Criteria andShinchNotIn(List<java.math.BigDecimal> values) {
            addCriterion("shinch not in", values, "shinch");
            return (Criteria) this;
        }

        public Criteria andShinchBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("shinch between", value1, value2, "shinch");
            return (Criteria) this;
        }

        public Criteria andShinchNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("shinch not between", value1, value2, "shinch");
            return (Criteria) this;
        }

        public Criteria andJiluztIsNull() {
            addCriterion("jiluzt is null");
            return (Criteria) this;
        }

        public Criteria andJiluztIsNotNull() {
            addCriterion("jiluzt is not null");
            return (Criteria) this;
        }

        public Criteria andJiluztEqualTo(String value) {
            addCriterion("jiluzt =", value, "jiluzt");
            return (Criteria) this;
        }

        public Criteria andJiluztNotEqualTo(String value) {
            addCriterion("jiluzt <>", value, "jiluzt");
            return (Criteria) this;
        }

        public Criteria andJiluztGreaterThan(String value) {
            addCriterion("jiluzt >", value, "jiluzt");
            return (Criteria) this;
        }

        public Criteria andJiluztGreaterThanOrEqualTo(String value) {
            addCriterion("jiluzt >=", value, "jiluzt");
            return (Criteria) this;
        }

        public Criteria andJiluztLessThan(String value) {
            addCriterion("jiluzt <", value, "jiluzt");
            return (Criteria) this;
        }

        public Criteria andJiluztLessThanOrEqualTo(String value) {
            addCriterion("jiluzt <=", value, "jiluzt");
            return (Criteria) this;
        }

        public Criteria andJiluztLike(String value) {
            addCriterion("jiluzt like", value, "jiluzt");
            return (Criteria) this;
        }

        public Criteria andJiluztNotLike(String value) {
            addCriterion("jiluzt not like", value, "jiluzt");
            return (Criteria) this;
        }

        public Criteria andJiluztIn(List<String> values) {
            addCriterion("jiluzt in", values, "jiluzt");
            return (Criteria) this;
        }

        public Criteria andJiluztNotIn(List<String> values) {
            addCriterion("jiluzt not in", values, "jiluzt");
            return (Criteria) this;
        }

        public Criteria andJiluztBetween(String value1, String value2) {
            addCriterion("jiluzt between", value1, value2, "jiluzt");
            return (Criteria) this;
        }

        public Criteria andJiluztNotBetween(String value1, String value2) {
            addCriterion("jiluzt not between", value1, value2, "jiluzt");
            return (Criteria) this;
        }

        public Criteria andKuanxqIsNull() {
            addCriterion("kuanxq is null");
            return (Criteria) this;
        }

        public Criteria andKuanxqIsNotNull() {
            addCriterion("kuanxq is not null");
            return (Criteria) this;
        }

        public Criteria andKuanxqEqualTo(java.math.BigDecimal value) {
            addCriterion("kuanxq =", value, "kuanxq");
            return (Criteria) this;
        }

        public Criteria andKuanxqNotEqualTo(java.math.BigDecimal value) {
            addCriterion("kuanxq <>", value, "kuanxq");
            return (Criteria) this;
        }

        public Criteria andKuanxqGreaterThan(java.math.BigDecimal value) {
            addCriterion("kuanxq >", value, "kuanxq");
            return (Criteria) this;
        }

        public Criteria andKuanxqGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("kuanxq >=", value, "kuanxq");
            return (Criteria) this;
        }

        public Criteria andKuanxqLessThan(java.math.BigDecimal value) {
            addCriterion("kuanxq <", value, "kuanxq");
            return (Criteria) this;
        }

        public Criteria andKuanxqLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("kuanxq <=", value, "kuanxq");
            return (Criteria) this;
        }

        public Criteria andKuanxqLike(java.math.BigDecimal value) {
            addCriterion("kuanxq like", value, "kuanxq");
            return (Criteria) this;
        }

        public Criteria andKuanxqNotLike(java.math.BigDecimal value) {
            addCriterion("kuanxq not like", value, "kuanxq");
            return (Criteria) this;
        }

        public Criteria andKuanxqIn(List<java.math.BigDecimal> values) {
            addCriterion("kuanxq in", values, "kuanxq");
            return (Criteria) this;
        }

        public Criteria andKuanxqNotIn(List<java.math.BigDecimal> values) {
            addCriterion("kuanxq not in", values, "kuanxq");
            return (Criteria) this;
        }

        public Criteria andKuanxqBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("kuanxq between", value1, value2, "kuanxq");
            return (Criteria) this;
        }

        public Criteria andKuanxqNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("kuanxq not between", value1, value2, "kuanxq");
            return (Criteria) this;
        }

        public Criteria andTxnjjhIsNull() {
            addCriterion("txnjjh is null");
            return (Criteria) this;
        }

        public Criteria andTxnjjhIsNotNull() {
            addCriterion("txnjjh is not null");
            return (Criteria) this;
        }

        public Criteria andTxnjjhEqualTo(String value) {
            addCriterion("txnjjh =", value, "txnjjh");
            return (Criteria) this;
        }

        public Criteria andTxnjjhNotEqualTo(String value) {
            addCriterion("txnjjh <>", value, "txnjjh");
            return (Criteria) this;
        }

        public Criteria andTxnjjhGreaterThan(String value) {
            addCriterion("txnjjh >", value, "txnjjh");
            return (Criteria) this;
        }

        public Criteria andTxnjjhGreaterThanOrEqualTo(String value) {
            addCriterion("txnjjh >=", value, "txnjjh");
            return (Criteria) this;
        }

        public Criteria andTxnjjhLessThan(String value) {
            addCriterion("txnjjh <", value, "txnjjh");
            return (Criteria) this;
        }

        public Criteria andTxnjjhLessThanOrEqualTo(String value) {
            addCriterion("txnjjh <=", value, "txnjjh");
            return (Criteria) this;
        }

        public Criteria andTxnjjhLike(String value) {
            addCriterion("txnjjh like", value, "txnjjh");
            return (Criteria) this;
        }

        public Criteria andTxnjjhNotLike(String value) {
            addCriterion("txnjjh not like", value, "txnjjh");
            return (Criteria) this;
        }

        public Criteria andTxnjjhIn(List<String> values) {
            addCriterion("txnjjh in", values, "txnjjh");
            return (Criteria) this;
        }

        public Criteria andTxnjjhNotIn(List<String> values) {
            addCriterion("txnjjh not in", values, "txnjjh");
            return (Criteria) this;
        }

        public Criteria andTxnjjhBetween(String value1, String value2) {
            addCriterion("txnjjh between", value1, value2, "txnjjh");
            return (Criteria) this;
        }

        public Criteria andTxnjjhNotBetween(String value1, String value2) {
            addCriterion("txnjjh not between", value1, value2, "txnjjh");
            return (Criteria) this;
        }

        public Criteria andTiexzhIsNull() {
            addCriterion("tiexzh is null");
            return (Criteria) this;
        }

        public Criteria andTiexzhIsNotNull() {
            addCriterion("tiexzh is not null");
            return (Criteria) this;
        }

        public Criteria andTiexzhEqualTo(String value) {
            addCriterion("tiexzh =", value, "tiexzh");
            return (Criteria) this;
        }

        public Criteria andTiexzhNotEqualTo(String value) {
            addCriterion("tiexzh <>", value, "tiexzh");
            return (Criteria) this;
        }

        public Criteria andTiexzhGreaterThan(String value) {
            addCriterion("tiexzh >", value, "tiexzh");
            return (Criteria) this;
        }

        public Criteria andTiexzhGreaterThanOrEqualTo(String value) {
            addCriterion("tiexzh >=", value, "tiexzh");
            return (Criteria) this;
        }

        public Criteria andTiexzhLessThan(String value) {
            addCriterion("tiexzh <", value, "tiexzh");
            return (Criteria) this;
        }

        public Criteria andTiexzhLessThanOrEqualTo(String value) {
            addCriterion("tiexzh <=", value, "tiexzh");
            return (Criteria) this;
        }

        public Criteria andTiexzhLike(String value) {
            addCriterion("tiexzh like", value, "tiexzh");
            return (Criteria) this;
        }

        public Criteria andTiexzhNotLike(String value) {
            addCriterion("tiexzh not like", value, "tiexzh");
            return (Criteria) this;
        }

        public Criteria andTiexzhIn(List<String> values) {
            addCriterion("tiexzh in", values, "tiexzh");
            return (Criteria) this;
        }

        public Criteria andTiexzhNotIn(List<String> values) {
            addCriterion("tiexzh not in", values, "tiexzh");
            return (Criteria) this;
        }

        public Criteria andTiexzhBetween(String value1, String value2) {
            addCriterion("tiexzh between", value1, value2, "tiexzh");
            return (Criteria) this;
        }

        public Criteria andTiexzhNotBetween(String value1, String value2) {
            addCriterion("tiexzh not between", value1, value2, "tiexzh");
            return (Criteria) this;
        }

        public Criteria andTxclzlIsNull() {
            addCriterion("txclzl is null");
            return (Criteria) this;
        }

        public Criteria andTxclzlIsNotNull() {
            addCriterion("txclzl is not null");
            return (Criteria) this;
        }

        public Criteria andTxclzlEqualTo(String value) {
            addCriterion("txclzl =", value, "txclzl");
            return (Criteria) this;
        }

        public Criteria andTxclzlNotEqualTo(String value) {
            addCriterion("txclzl <>", value, "txclzl");
            return (Criteria) this;
        }

        public Criteria andTxclzlGreaterThan(String value) {
            addCriterion("txclzl >", value, "txclzl");
            return (Criteria) this;
        }

        public Criteria andTxclzlGreaterThanOrEqualTo(String value) {
            addCriterion("txclzl >=", value, "txclzl");
            return (Criteria) this;
        }

        public Criteria andTxclzlLessThan(String value) {
            addCriterion("txclzl <", value, "txclzl");
            return (Criteria) this;
        }

        public Criteria andTxclzlLessThanOrEqualTo(String value) {
            addCriterion("txclzl <=", value, "txclzl");
            return (Criteria) this;
        }

        public Criteria andTxclzlLike(String value) {
            addCriterion("txclzl like", value, "txclzl");
            return (Criteria) this;
        }

        public Criteria andTxclzlNotLike(String value) {
            addCriterion("txclzl not like", value, "txclzl");
            return (Criteria) this;
        }

        public Criteria andTxclzlIn(List<String> values) {
            addCriterion("txclzl in", values, "txclzl");
            return (Criteria) this;
        }

        public Criteria andTxclzlNotIn(List<String> values) {
            addCriterion("txclzl not in", values, "txclzl");
            return (Criteria) this;
        }

        public Criteria andTxclzlBetween(String value1, String value2) {
            addCriterion("txclzl between", value1, value2, "txclzl");
            return (Criteria) this;
        }

        public Criteria andTxclzlNotBetween(String value1, String value2) {
            addCriterion("txclzl not between", value1, value2, "txclzl");
            return (Criteria) this;
        }

        public Criteria andTxywzlIsNull() {
            addCriterion("txywzl is null");
            return (Criteria) this;
        }

        public Criteria andTxywzlIsNotNull() {
            addCriterion("txywzl is not null");
            return (Criteria) this;
        }

        public Criteria andTxywzlEqualTo(String value) {
            addCriterion("txywzl =", value, "txywzl");
            return (Criteria) this;
        }

        public Criteria andTxywzlNotEqualTo(String value) {
            addCriterion("txywzl <>", value, "txywzl");
            return (Criteria) this;
        }

        public Criteria andTxywzlGreaterThan(String value) {
            addCriterion("txywzl >", value, "txywzl");
            return (Criteria) this;
        }

        public Criteria andTxywzlGreaterThanOrEqualTo(String value) {
            addCriterion("txywzl >=", value, "txywzl");
            return (Criteria) this;
        }

        public Criteria andTxywzlLessThan(String value) {
            addCriterion("txywzl <", value, "txywzl");
            return (Criteria) this;
        }

        public Criteria andTxywzlLessThanOrEqualTo(String value) {
            addCriterion("txywzl <=", value, "txywzl");
            return (Criteria) this;
        }

        public Criteria andTxywzlLike(String value) {
            addCriterion("txywzl like", value, "txywzl");
            return (Criteria) this;
        }

        public Criteria andTxywzlNotLike(String value) {
            addCriterion("txywzl not like", value, "txywzl");
            return (Criteria) this;
        }

        public Criteria andTxywzlIn(List<String> values) {
            addCriterion("txywzl in", values, "txywzl");
            return (Criteria) this;
        }

        public Criteria andTxywzlNotIn(List<String> values) {
            addCriterion("txywzl not in", values, "txywzl");
            return (Criteria) this;
        }

        public Criteria andTxywzlBetween(String value1, String value2) {
            addCriterion("txywzl between", value1, value2, "txywzl");
            return (Criteria) this;
        }

        public Criteria andTxywzlNotBetween(String value1, String value2) {
            addCriterion("txywzl not between", value1, value2, "txywzl");
            return (Criteria) this;
        }

        public Criteria andPiojzhIsNull() {
            addCriterion("piojzh is null");
            return (Criteria) this;
        }

        public Criteria andPiojzhIsNotNull() {
            addCriterion("piojzh is not null");
            return (Criteria) this;
        }

        public Criteria andPiojzhEqualTo(String value) {
            addCriterion("piojzh =", value, "piojzh");
            return (Criteria) this;
        }

        public Criteria andPiojzhNotEqualTo(String value) {
            addCriterion("piojzh <>", value, "piojzh");
            return (Criteria) this;
        }

        public Criteria andPiojzhGreaterThan(String value) {
            addCriterion("piojzh >", value, "piojzh");
            return (Criteria) this;
        }

        public Criteria andPiojzhGreaterThanOrEqualTo(String value) {
            addCriterion("piojzh >=", value, "piojzh");
            return (Criteria) this;
        }

        public Criteria andPiojzhLessThan(String value) {
            addCriterion("piojzh <", value, "piojzh");
            return (Criteria) this;
        }

        public Criteria andPiojzhLessThanOrEqualTo(String value) {
            addCriterion("piojzh <=", value, "piojzh");
            return (Criteria) this;
        }

        public Criteria andPiojzhLike(String value) {
            addCriterion("piojzh like", value, "piojzh");
            return (Criteria) this;
        }

        public Criteria andPiojzhNotLike(String value) {
            addCriterion("piojzh not like", value, "piojzh");
            return (Criteria) this;
        }

        public Criteria andPiojzhIn(List<String> values) {
            addCriterion("piojzh in", values, "piojzh");
            return (Criteria) this;
        }

        public Criteria andPiojzhNotIn(List<String> values) {
            addCriterion("piojzh not in", values, "piojzh");
            return (Criteria) this;
        }

        public Criteria andPiojzhBetween(String value1, String value2) {
            addCriterion("piojzh between", value1, value2, "piojzh");
            return (Criteria) this;
        }

        public Criteria andPiojzhNotBetween(String value1, String value2) {
            addCriterion("piojzh not between", value1, value2, "piojzh");
            return (Criteria) this;
        }

        public Criteria andKehhaoIsNull() {
            addCriterion("kehhao is null");
            return (Criteria) this;
        }

        public Criteria andKehhaoIsNotNull() {
            addCriterion("kehhao is not null");
            return (Criteria) this;
        }

        public Criteria andKehhaoEqualTo(String value) {
            addCriterion("kehhao =", value, "kehhao");
            return (Criteria) this;
        }

        public Criteria andKehhaoNotEqualTo(String value) {
            addCriterion("kehhao <>", value, "kehhao");
            return (Criteria) this;
        }

        public Criteria andKehhaoGreaterThan(String value) {
            addCriterion("kehhao >", value, "kehhao");
            return (Criteria) this;
        }

        public Criteria andKehhaoGreaterThanOrEqualTo(String value) {
            addCriterion("kehhao >=", value, "kehhao");
            return (Criteria) this;
        }

        public Criteria andKehhaoLessThan(String value) {
            addCriterion("kehhao <", value, "kehhao");
            return (Criteria) this;
        }

        public Criteria andKehhaoLessThanOrEqualTo(String value) {
            addCriterion("kehhao <=", value, "kehhao");
            return (Criteria) this;
        }

        public Criteria andKehhaoLike(String value) {
            addCriterion("kehhao like", value, "kehhao");
            return (Criteria) this;
        }

        public Criteria andKehhaoNotLike(String value) {
            addCriterion("kehhao not like", value, "kehhao");
            return (Criteria) this;
        }

        public Criteria andKehhaoIn(List<String> values) {
            addCriterion("kehhao in", values, "kehhao");
            return (Criteria) this;
        }

        public Criteria andKehhaoNotIn(List<String> values) {
            addCriterion("kehhao not in", values, "kehhao");
            return (Criteria) this;
        }

        public Criteria andKehhaoBetween(String value1, String value2) {
            addCriterion("kehhao between", value1, value2, "kehhao");
            return (Criteria) this;
        }

        public Criteria andKehhaoNotBetween(String value1, String value2) {
            addCriterion("kehhao not between", value1, value2, "kehhao");
            return (Criteria) this;
        }

        public Criteria andKehzwmIsNull() {
            addCriterion("kehzwm is null");
            return (Criteria) this;
        }

        public Criteria andKehzwmIsNotNull() {
            addCriterion("kehzwm is not null");
            return (Criteria) this;
        }

        public Criteria andKehzwmEqualTo(String value) {
            addCriterion("kehzwm =", value, "kehzwm");
            return (Criteria) this;
        }

        public Criteria andKehzwmNotEqualTo(String value) {
            addCriterion("kehzwm <>", value, "kehzwm");
            return (Criteria) this;
        }

        public Criteria andKehzwmGreaterThan(String value) {
            addCriterion("kehzwm >", value, "kehzwm");
            return (Criteria) this;
        }

        public Criteria andKehzwmGreaterThanOrEqualTo(String value) {
            addCriterion("kehzwm >=", value, "kehzwm");
            return (Criteria) this;
        }

        public Criteria andKehzwmLessThan(String value) {
            addCriterion("kehzwm <", value, "kehzwm");
            return (Criteria) this;
        }

        public Criteria andKehzwmLessThanOrEqualTo(String value) {
            addCriterion("kehzwm <=", value, "kehzwm");
            return (Criteria) this;
        }

        public Criteria andKehzwmLike(String value) {
            addCriterion("kehzwm like", value, "kehzwm");
            return (Criteria) this;
        }

        public Criteria andKehzwmNotLike(String value) {
            addCriterion("kehzwm not like", value, "kehzwm");
            return (Criteria) this;
        }

        public Criteria andKehzwmIn(List<String> values) {
            addCriterion("kehzwm in", values, "kehzwm");
            return (Criteria) this;
        }

        public Criteria andKehzwmNotIn(List<String> values) {
            addCriterion("kehzwm not in", values, "kehzwm");
            return (Criteria) this;
        }

        public Criteria andKehzwmBetween(String value1, String value2) {
            addCriterion("kehzwm between", value1, value2, "kehzwm");
            return (Criteria) this;
        }

        public Criteria andKehzwmNotBetween(String value1, String value2) {
            addCriterion("kehzwm not between", value1, value2, "kehzwm");
            return (Criteria) this;
        }

        public Criteria andYngyjgIsNull() {
            addCriterion("yngyjg is null");
            return (Criteria) this;
        }

        public Criteria andYngyjgIsNotNull() {
            addCriterion("yngyjg is not null");
            return (Criteria) this;
        }

        public Criteria andYngyjgEqualTo(String value) {
            addCriterion("yngyjg =", value, "yngyjg");
            return (Criteria) this;
        }

        public Criteria andYngyjgNotEqualTo(String value) {
            addCriterion("yngyjg <>", value, "yngyjg");
            return (Criteria) this;
        }

        public Criteria andYngyjgGreaterThan(String value) {
            addCriterion("yngyjg >", value, "yngyjg");
            return (Criteria) this;
        }

        public Criteria andYngyjgGreaterThanOrEqualTo(String value) {
            addCriterion("yngyjg >=", value, "yngyjg");
            return (Criteria) this;
        }

        public Criteria andYngyjgLessThan(String value) {
            addCriterion("yngyjg <", value, "yngyjg");
            return (Criteria) this;
        }

        public Criteria andYngyjgLessThanOrEqualTo(String value) {
            addCriterion("yngyjg <=", value, "yngyjg");
            return (Criteria) this;
        }

        public Criteria andYngyjgLike(String value) {
            addCriterion("yngyjg like", value, "yngyjg");
            return (Criteria) this;
        }

        public Criteria andYngyjgNotLike(String value) {
            addCriterion("yngyjg not like", value, "yngyjg");
            return (Criteria) this;
        }

        public Criteria andYngyjgIn(List<String> values) {
            addCriterion("yngyjg in", values, "yngyjg");
            return (Criteria) this;
        }

        public Criteria andYngyjgNotIn(List<String> values) {
            addCriterion("yngyjg not in", values, "yngyjg");
            return (Criteria) this;
        }

        public Criteria andYngyjgBetween(String value1, String value2) {
            addCriterion("yngyjg between", value1, value2, "yngyjg");
            return (Criteria) this;
        }

        public Criteria andYngyjgNotBetween(String value1, String value2) {
            addCriterion("yngyjg not between", value1, value2, "yngyjg");
            return (Criteria) this;
        }

        public Criteria andZhngjgIsNull() {
            addCriterion("zhngjg is null");
            return (Criteria) this;
        }

        public Criteria andZhngjgIsNotNull() {
            addCriterion("zhngjg is not null");
            return (Criteria) this;
        }

        public Criteria andZhngjgEqualTo(String value) {
            addCriterion("zhngjg =", value, "zhngjg");
            return (Criteria) this;
        }

        public Criteria andZhngjgNotEqualTo(String value) {
            addCriterion("zhngjg <>", value, "zhngjg");
            return (Criteria) this;
        }

        public Criteria andZhngjgGreaterThan(String value) {
            addCriterion("zhngjg >", value, "zhngjg");
            return (Criteria) this;
        }

        public Criteria andZhngjgGreaterThanOrEqualTo(String value) {
            addCriterion("zhngjg >=", value, "zhngjg");
            return (Criteria) this;
        }

        public Criteria andZhngjgLessThan(String value) {
            addCriterion("zhngjg <", value, "zhngjg");
            return (Criteria) this;
        }

        public Criteria andZhngjgLessThanOrEqualTo(String value) {
            addCriterion("zhngjg <=", value, "zhngjg");
            return (Criteria) this;
        }

        public Criteria andZhngjgLike(String value) {
            addCriterion("zhngjg like", value, "zhngjg");
            return (Criteria) this;
        }

        public Criteria andZhngjgNotLike(String value) {
            addCriterion("zhngjg not like", value, "zhngjg");
            return (Criteria) this;
        }

        public Criteria andZhngjgIn(List<String> values) {
            addCriterion("zhngjg in", values, "zhngjg");
            return (Criteria) this;
        }

        public Criteria andZhngjgNotIn(List<String> values) {
            addCriterion("zhngjg not in", values, "zhngjg");
            return (Criteria) this;
        }

        public Criteria andZhngjgBetween(String value1, String value2) {
            addCriterion("zhngjg between", value1, value2, "zhngjg");
            return (Criteria) this;
        }

        public Criteria andZhngjgNotBetween(String value1, String value2) {
            addCriterion("zhngjg not between", value1, value2, "zhngjg");
            return (Criteria) this;
        }

        public Criteria andRuzhjgIsNull() {
            addCriterion("ruzhjg is null");
            return (Criteria) this;
        }

        public Criteria andRuzhjgIsNotNull() {
            addCriterion("ruzhjg is not null");
            return (Criteria) this;
        }

        public Criteria andRuzhjgEqualTo(String value) {
            addCriterion("ruzhjg =", value, "ruzhjg");
            return (Criteria) this;
        }

        public Criteria andRuzhjgNotEqualTo(String value) {
            addCriterion("ruzhjg <>", value, "ruzhjg");
            return (Criteria) this;
        }

        public Criteria andRuzhjgGreaterThan(String value) {
            addCriterion("ruzhjg >", value, "ruzhjg");
            return (Criteria) this;
        }

        public Criteria andRuzhjgGreaterThanOrEqualTo(String value) {
            addCriterion("ruzhjg >=", value, "ruzhjg");
            return (Criteria) this;
        }

        public Criteria andRuzhjgLessThan(String value) {
            addCriterion("ruzhjg <", value, "ruzhjg");
            return (Criteria) this;
        }

        public Criteria andRuzhjgLessThanOrEqualTo(String value) {
            addCriterion("ruzhjg <=", value, "ruzhjg");
            return (Criteria) this;
        }

        public Criteria andRuzhjgLike(String value) {
            addCriterion("ruzhjg like", value, "ruzhjg");
            return (Criteria) this;
        }

        public Criteria andRuzhjgNotLike(String value) {
            addCriterion("ruzhjg not like", value, "ruzhjg");
            return (Criteria) this;
        }

        public Criteria andRuzhjgIn(List<String> values) {
            addCriterion("ruzhjg in", values, "ruzhjg");
            return (Criteria) this;
        }

        public Criteria andRuzhjgNotIn(List<String> values) {
            addCriterion("ruzhjg not in", values, "ruzhjg");
            return (Criteria) this;
        }

        public Criteria andRuzhjgBetween(String value1, String value2) {
            addCriterion("ruzhjg between", value1, value2, "ruzhjg");
            return (Criteria) this;
        }

        public Criteria andRuzhjgNotBetween(String value1, String value2) {
            addCriterion("ruzhjg not between", value1, value2, "ruzhjg");
            return (Criteria) this;
        }

        public Criteria andSyzcjgIsNull() {
            addCriterion("syzcjg is null");
            return (Criteria) this;
        }

        public Criteria andSyzcjgIsNotNull() {
            addCriterion("syzcjg is not null");
            return (Criteria) this;
        }

        public Criteria andSyzcjgEqualTo(String value) {
            addCriterion("syzcjg =", value, "syzcjg");
            return (Criteria) this;
        }

        public Criteria andSyzcjgNotEqualTo(String value) {
            addCriterion("syzcjg <>", value, "syzcjg");
            return (Criteria) this;
        }

        public Criteria andSyzcjgGreaterThan(String value) {
            addCriterion("syzcjg >", value, "syzcjg");
            return (Criteria) this;
        }

        public Criteria andSyzcjgGreaterThanOrEqualTo(String value) {
            addCriterion("syzcjg >=", value, "syzcjg");
            return (Criteria) this;
        }

        public Criteria andSyzcjgLessThan(String value) {
            addCriterion("syzcjg <", value, "syzcjg");
            return (Criteria) this;
        }

        public Criteria andSyzcjgLessThanOrEqualTo(String value) {
            addCriterion("syzcjg <=", value, "syzcjg");
            return (Criteria) this;
        }

        public Criteria andSyzcjgLike(String value) {
            addCriterion("syzcjg like", value, "syzcjg");
            return (Criteria) this;
        }

        public Criteria andSyzcjgNotLike(String value) {
            addCriterion("syzcjg not like", value, "syzcjg");
            return (Criteria) this;
        }

        public Criteria andSyzcjgIn(List<String> values) {
            addCriterion("syzcjg in", values, "syzcjg");
            return (Criteria) this;
        }

        public Criteria andSyzcjgNotIn(List<String> values) {
            addCriterion("syzcjg not in", values, "syzcjg");
            return (Criteria) this;
        }

        public Criteria andSyzcjgBetween(String value1, String value2) {
            addCriterion("syzcjg between", value1, value2, "syzcjg");
            return (Criteria) this;
        }

        public Criteria andSyzcjgNotBetween(String value1, String value2) {
            addCriterion("syzcjg not between", value1, value2, "syzcjg");
            return (Criteria) this;
        }

        public Criteria andSyrzjgIsNull() {
            addCriterion("syrzjg is null");
            return (Criteria) this;
        }

        public Criteria andSyrzjgIsNotNull() {
            addCriterion("syrzjg is not null");
            return (Criteria) this;
        }

        public Criteria andSyrzjgEqualTo(String value) {
            addCriterion("syrzjg =", value, "syrzjg");
            return (Criteria) this;
        }

        public Criteria andSyrzjgNotEqualTo(String value) {
            addCriterion("syrzjg <>", value, "syrzjg");
            return (Criteria) this;
        }

        public Criteria andSyrzjgGreaterThan(String value) {
            addCriterion("syrzjg >", value, "syrzjg");
            return (Criteria) this;
        }

        public Criteria andSyrzjgGreaterThanOrEqualTo(String value) {
            addCriterion("syrzjg >=", value, "syrzjg");
            return (Criteria) this;
        }

        public Criteria andSyrzjgLessThan(String value) {
            addCriterion("syrzjg <", value, "syrzjg");
            return (Criteria) this;
        }

        public Criteria andSyrzjgLessThanOrEqualTo(String value) {
            addCriterion("syrzjg <=", value, "syrzjg");
            return (Criteria) this;
        }

        public Criteria andSyrzjgLike(String value) {
            addCriterion("syrzjg like", value, "syrzjg");
            return (Criteria) this;
        }

        public Criteria andSyrzjgNotLike(String value) {
            addCriterion("syrzjg not like", value, "syrzjg");
            return (Criteria) this;
        }

        public Criteria andSyrzjgIn(List<String> values) {
            addCriterion("syrzjg in", values, "syrzjg");
            return (Criteria) this;
        }

        public Criteria andSyrzjgNotIn(List<String> values) {
            addCriterion("syrzjg not in", values, "syrzjg");
            return (Criteria) this;
        }

        public Criteria andSyrzjgBetween(String value1, String value2) {
            addCriterion("syrzjg between", value1, value2, "syrzjg");
            return (Criteria) this;
        }

        public Criteria andSyrzjgNotBetween(String value1, String value2) {
            addCriterion("syrzjg not between", value1, value2, "syrzjg");
            return (Criteria) this;
        }

        public Criteria andHuobdhIsNull() {
            addCriterion("huobdh is null");
            return (Criteria) this;
        }

        public Criteria andHuobdhIsNotNull() {
            addCriterion("huobdh is not null");
            return (Criteria) this;
        }

        public Criteria andHuobdhEqualTo(String value) {
            addCriterion("huobdh =", value, "huobdh");
            return (Criteria) this;
        }

        public Criteria andHuobdhNotEqualTo(String value) {
            addCriterion("huobdh <>", value, "huobdh");
            return (Criteria) this;
        }

        public Criteria andHuobdhGreaterThan(String value) {
            addCriterion("huobdh >", value, "huobdh");
            return (Criteria) this;
        }

        public Criteria andHuobdhGreaterThanOrEqualTo(String value) {
            addCriterion("huobdh >=", value, "huobdh");
            return (Criteria) this;
        }

        public Criteria andHuobdhLessThan(String value) {
            addCriterion("huobdh <", value, "huobdh");
            return (Criteria) this;
        }

        public Criteria andHuobdhLessThanOrEqualTo(String value) {
            addCriterion("huobdh <=", value, "huobdh");
            return (Criteria) this;
        }

        public Criteria andHuobdhLike(String value) {
            addCriterion("huobdh like", value, "huobdh");
            return (Criteria) this;
        }

        public Criteria andHuobdhNotLike(String value) {
            addCriterion("huobdh not like", value, "huobdh");
            return (Criteria) this;
        }

        public Criteria andHuobdhIn(List<String> values) {
            addCriterion("huobdh in", values, "huobdh");
            return (Criteria) this;
        }

        public Criteria andHuobdhNotIn(List<String> values) {
            addCriterion("huobdh not in", values, "huobdh");
            return (Criteria) this;
        }

        public Criteria andHuobdhBetween(String value1, String value2) {
            addCriterion("huobdh between", value1, value2, "huobdh");
            return (Criteria) this;
        }

        public Criteria andHuobdhNotBetween(String value1, String value2) {
            addCriterion("huobdh not between", value1, value2, "huobdh");
            return (Criteria) this;
        }

        public Criteria andTxqxrqIsNull() {
            addCriterion("txqxrq is null");
            return (Criteria) this;
        }

        public Criteria andTxqxrqIsNotNull() {
            addCriterion("txqxrq is not null");
            return (Criteria) this;
        }

        public Criteria andTxqxrqEqualTo(String value) {
            addCriterion("txqxrq =", value, "txqxrq");
            return (Criteria) this;
        }

        public Criteria andTxqxrqNotEqualTo(String value) {
            addCriterion("txqxrq <>", value, "txqxrq");
            return (Criteria) this;
        }

        public Criteria andTxqxrqGreaterThan(String value) {
            addCriterion("txqxrq >", value, "txqxrq");
            return (Criteria) this;
        }

        public Criteria andTxqxrqGreaterThanOrEqualTo(String value) {
            addCriterion("txqxrq >=", value, "txqxrq");
            return (Criteria) this;
        }

        public Criteria andTxqxrqLessThan(String value) {
            addCriterion("txqxrq <", value, "txqxrq");
            return (Criteria) this;
        }

        public Criteria andTxqxrqLessThanOrEqualTo(String value) {
            addCriterion("txqxrq <=", value, "txqxrq");
            return (Criteria) this;
        }

        public Criteria andTxqxrqLike(String value) {
            addCriterion("txqxrq like", value, "txqxrq");
            return (Criteria) this;
        }

        public Criteria andTxqxrqNotLike(String value) {
            addCriterion("txqxrq not like", value, "txqxrq");
            return (Criteria) this;
        }

        public Criteria andTxqxrqIn(List<String> values) {
            addCriterion("txqxrq in", values, "txqxrq");
            return (Criteria) this;
        }

        public Criteria andTxqxrqNotIn(List<String> values) {
            addCriterion("txqxrq not in", values, "txqxrq");
            return (Criteria) this;
        }

        public Criteria andTxqxrqBetween(String value1, String value2) {
            addCriterion("txqxrq between", value1, value2, "txqxrq");
            return (Criteria) this;
        }

        public Criteria andTxqxrqNotBetween(String value1, String value2) {
            addCriterion("txqxrq not between", value1, value2, "txqxrq");
            return (Criteria) this;
        }

        public Criteria andTxdqrqIsNull() {
            addCriterion("txdqrq is null");
            return (Criteria) this;
        }

        public Criteria andTxdqrqIsNotNull() {
            addCriterion("txdqrq is not null");
            return (Criteria) this;
        }

        public Criteria andTxdqrqEqualTo(String value) {
            addCriterion("txdqrq =", value, "txdqrq");
            return (Criteria) this;
        }

        public Criteria andTxdqrqNotEqualTo(String value) {
            addCriterion("txdqrq <>", value, "txdqrq");
            return (Criteria) this;
        }

        public Criteria andTxdqrqGreaterThan(String value) {
            addCriterion("txdqrq >", value, "txdqrq");
            return (Criteria) this;
        }

        public Criteria andTxdqrqGreaterThanOrEqualTo(String value) {
            addCriterion("txdqrq >=", value, "txdqrq");
            return (Criteria) this;
        }

        public Criteria andTxdqrqLessThan(String value) {
            addCriterion("txdqrq <", value, "txdqrq");
            return (Criteria) this;
        }

        public Criteria andTxdqrqLessThanOrEqualTo(String value) {
            addCriterion("txdqrq <=", value, "txdqrq");
            return (Criteria) this;
        }

        public Criteria andTxdqrqLike(String value) {
            addCriterion("txdqrq like", value, "txdqrq");
            return (Criteria) this;
        }

        public Criteria andTxdqrqNotLike(String value) {
            addCriterion("txdqrq not like", value, "txdqrq");
            return (Criteria) this;
        }

        public Criteria andTxdqrqIn(List<String> values) {
            addCriterion("txdqrq in", values, "txdqrq");
            return (Criteria) this;
        }

        public Criteria andTxdqrqNotIn(List<String> values) {
            addCriterion("txdqrq not in", values, "txdqrq");
            return (Criteria) this;
        }

        public Criteria andTxdqrqBetween(String value1, String value2) {
            addCriterion("txdqrq between", value1, value2, "txdqrq");
            return (Criteria) this;
        }

        public Criteria andTxdqrqNotBetween(String value1, String value2) {
            addCriterion("txdqrq not between", value1, value2, "txdqrq");
            return (Criteria) this;
        }

        public Criteria andFaredmIsNull() {
            addCriterion("faredm is null");
            return (Criteria) this;
        }

        public Criteria andFaredmIsNotNull() {
            addCriterion("faredm is not null");
            return (Criteria) this;
        }

        public Criteria andFaredmEqualTo(String value) {
            addCriterion("faredm =", value, "faredm");
            return (Criteria) this;
        }

        public Criteria andFaredmNotEqualTo(String value) {
            addCriterion("faredm <>", value, "faredm");
            return (Criteria) this;
        }

        public Criteria andFaredmGreaterThan(String value) {
            addCriterion("faredm >", value, "faredm");
            return (Criteria) this;
        }

        public Criteria andFaredmGreaterThanOrEqualTo(String value) {
            addCriterion("faredm >=", value, "faredm");
            return (Criteria) this;
        }

        public Criteria andFaredmLessThan(String value) {
            addCriterion("faredm <", value, "faredm");
            return (Criteria) this;
        }

        public Criteria andFaredmLessThanOrEqualTo(String value) {
            addCriterion("faredm <=", value, "faredm");
            return (Criteria) this;
        }

        public Criteria andFaredmLike(String value) {
            addCriterion("faredm like", value, "faredm");
            return (Criteria) this;
        }

        public Criteria andFaredmNotLike(String value) {
            addCriterion("faredm not like", value, "faredm");
            return (Criteria) this;
        }

        public Criteria andFaredmIn(List<String> values) {
            addCriterion("faredm in", values, "faredm");
            return (Criteria) this;
        }

        public Criteria andFaredmNotIn(List<String> values) {
            addCriterion("faredm not in", values, "faredm");
            return (Criteria) this;
        }

        public Criteria andFaredmBetween(String value1, String value2) {
            addCriterion("faredm between", value1, value2, "faredm");
            return (Criteria) this;
        }

        public Criteria andFaredmNotBetween(String value1, String value2) {
            addCriterion("faredm not between", value1, value2, "faredm");
            return (Criteria) this;
        }

        public Criteria andLilvbhIsNull() {
            addCriterion("lilvbh is null");
            return (Criteria) this;
        }

        public Criteria andLilvbhIsNotNull() {
            addCriterion("lilvbh is not null");
            return (Criteria) this;
        }

        public Criteria andLilvbhEqualTo(String value) {
            addCriterion("lilvbh =", value, "lilvbh");
            return (Criteria) this;
        }

        public Criteria andLilvbhNotEqualTo(String value) {
            addCriterion("lilvbh <>", value, "lilvbh");
            return (Criteria) this;
        }

        public Criteria andLilvbhGreaterThan(String value) {
            addCriterion("lilvbh >", value, "lilvbh");
            return (Criteria) this;
        }

        public Criteria andLilvbhGreaterThanOrEqualTo(String value) {
            addCriterion("lilvbh >=", value, "lilvbh");
            return (Criteria) this;
        }

        public Criteria andLilvbhLessThan(String value) {
            addCriterion("lilvbh <", value, "lilvbh");
            return (Criteria) this;
        }

        public Criteria andLilvbhLessThanOrEqualTo(String value) {
            addCriterion("lilvbh <=", value, "lilvbh");
            return (Criteria) this;
        }

        public Criteria andLilvbhLike(String value) {
            addCriterion("lilvbh like", value, "lilvbh");
            return (Criteria) this;
        }

        public Criteria andLilvbhNotLike(String value) {
            addCriterion("lilvbh not like", value, "lilvbh");
            return (Criteria) this;
        }

        public Criteria andLilvbhIn(List<String> values) {
            addCriterion("lilvbh in", values, "lilvbh");
            return (Criteria) this;
        }

        public Criteria andLilvbhNotIn(List<String> values) {
            addCriterion("lilvbh not in", values, "lilvbh");
            return (Criteria) this;
        }

        public Criteria andLilvbhBetween(String value1, String value2) {
            addCriterion("lilvbh between", value1, value2, "lilvbh");
            return (Criteria) this;
        }

        public Criteria andLilvbhNotBetween(String value1, String value2) {
            addCriterion("lilvbh not between", value1, value2, "lilvbh");
            return (Criteria) this;
        }

        public Criteria andNyuellIsNull() {
            addCriterion("nyuell is null");
            return (Criteria) this;
        }

        public Criteria andNyuellIsNotNull() {
            addCriterion("nyuell is not null");
            return (Criteria) this;
        }

        public Criteria andNyuellEqualTo(String value) {
            addCriterion("nyuell =", value, "nyuell");
            return (Criteria) this;
        }

        public Criteria andNyuellNotEqualTo(String value) {
            addCriterion("nyuell <>", value, "nyuell");
            return (Criteria) this;
        }

        public Criteria andNyuellGreaterThan(String value) {
            addCriterion("nyuell >", value, "nyuell");
            return (Criteria) this;
        }

        public Criteria andNyuellGreaterThanOrEqualTo(String value) {
            addCriterion("nyuell >=", value, "nyuell");
            return (Criteria) this;
        }

        public Criteria andNyuellLessThan(String value) {
            addCriterion("nyuell <", value, "nyuell");
            return (Criteria) this;
        }

        public Criteria andNyuellLessThanOrEqualTo(String value) {
            addCriterion("nyuell <=", value, "nyuell");
            return (Criteria) this;
        }

        public Criteria andNyuellLike(String value) {
            addCriterion("nyuell like", value, "nyuell");
            return (Criteria) this;
        }

        public Criteria andNyuellNotLike(String value) {
            addCriterion("nyuell not like", value, "nyuell");
            return (Criteria) this;
        }

        public Criteria andNyuellIn(List<String> values) {
            addCriterion("nyuell in", values, "nyuell");
            return (Criteria) this;
        }

        public Criteria andNyuellNotIn(List<String> values) {
            addCriterion("nyuell not in", values, "nyuell");
            return (Criteria) this;
        }

        public Criteria andNyuellBetween(String value1, String value2) {
            addCriterion("nyuell between", value1, value2, "nyuell");
            return (Criteria) this;
        }

        public Criteria andNyuellNotBetween(String value1, String value2) {
            addCriterion("nyuell not between", value1, value2, "nyuell");
            return (Criteria) this;
        }

        public Criteria andTiexllIsNull() {
            addCriterion("tiexll is null");
            return (Criteria) this;
        }

        public Criteria andTiexllIsNotNull() {
            addCriterion("tiexll is not null");
            return (Criteria) this;
        }

        public Criteria andTiexllEqualTo(java.math.BigDecimal value) {
            addCriterion("tiexll =", value, "tiexll");
            return (Criteria) this;
        }

        public Criteria andTiexllNotEqualTo(java.math.BigDecimal value) {
            addCriterion("tiexll <>", value, "tiexll");
            return (Criteria) this;
        }

        public Criteria andTiexllGreaterThan(java.math.BigDecimal value) {
            addCriterion("tiexll >", value, "tiexll");
            return (Criteria) this;
        }

        public Criteria andTiexllGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("tiexll >=", value, "tiexll");
            return (Criteria) this;
        }

        public Criteria andTiexllLessThan(java.math.BigDecimal value) {
            addCriterion("tiexll <", value, "tiexll");
            return (Criteria) this;
        }

        public Criteria andTiexllLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("tiexll <=", value, "tiexll");
            return (Criteria) this;
        }

        public Criteria andTiexllLike(java.math.BigDecimal value) {
            addCriterion("tiexll like", value, "tiexll");
            return (Criteria) this;
        }

        public Criteria andTiexllNotLike(java.math.BigDecimal value) {
            addCriterion("tiexll not like", value, "tiexll");
            return (Criteria) this;
        }

        public Criteria andTiexllIn(List<java.math.BigDecimal> values) {
            addCriterion("tiexll in", values, "tiexll");
            return (Criteria) this;
        }

        public Criteria andTiexllNotIn(List<java.math.BigDecimal> values) {
            addCriterion("tiexll not in", values, "tiexll");
            return (Criteria) this;
        }

        public Criteria andTiexllBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("tiexll between", value1, value2, "tiexll");
            return (Criteria) this;
        }

        public Criteria andTiexllNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("tiexll not between", value1, value2, "tiexll");
            return (Criteria) this;
        }

        public Criteria andTxztyeIsNull() {
            addCriterion("txztye is null");
            return (Criteria) this;
        }

        public Criteria andTxztyeIsNotNull() {
            addCriterion("txztye is not null");
            return (Criteria) this;
        }

        public Criteria andTxztyeEqualTo(java.math.BigDecimal value) {
            addCriterion("txztye =", value, "txztye");
            return (Criteria) this;
        }

        public Criteria andTxztyeNotEqualTo(java.math.BigDecimal value) {
            addCriterion("txztye <>", value, "txztye");
            return (Criteria) this;
        }

        public Criteria andTxztyeGreaterThan(java.math.BigDecimal value) {
            addCriterion("txztye >", value, "txztye");
            return (Criteria) this;
        }

        public Criteria andTxztyeGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("txztye >=", value, "txztye");
            return (Criteria) this;
        }

        public Criteria andTxztyeLessThan(java.math.BigDecimal value) {
            addCriterion("txztye <", value, "txztye");
            return (Criteria) this;
        }

        public Criteria andTxztyeLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("txztye <=", value, "txztye");
            return (Criteria) this;
        }

        public Criteria andTxztyeLike(java.math.BigDecimal value) {
            addCriterion("txztye like", value, "txztye");
            return (Criteria) this;
        }

        public Criteria andTxztyeNotLike(java.math.BigDecimal value) {
            addCriterion("txztye not like", value, "txztye");
            return (Criteria) this;
        }

        public Criteria andTxztyeIn(List<java.math.BigDecimal> values) {
            addCriterion("txztye in", values, "txztye");
            return (Criteria) this;
        }

        public Criteria andTxztyeNotIn(List<java.math.BigDecimal> values) {
            addCriterion("txztye not in", values, "txztye");
            return (Criteria) this;
        }

        public Criteria andTxztyeBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("txztye between", value1, value2, "txztye");
            return (Criteria) this;
        }

        public Criteria andTxztyeNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("txztye not between", value1, value2, "txztye");
            return (Criteria) this;
        }

        public Criteria andShfujeIsNull() {
            addCriterion("shfuje is null");
            return (Criteria) this;
        }

        public Criteria andShfujeIsNotNull() {
            addCriterion("shfuje is not null");
            return (Criteria) this;
        }

        public Criteria andShfujeEqualTo(java.math.BigDecimal value) {
            addCriterion("shfuje =", value, "shfuje");
            return (Criteria) this;
        }

        public Criteria andShfujeNotEqualTo(java.math.BigDecimal value) {
            addCriterion("shfuje <>", value, "shfuje");
            return (Criteria) this;
        }

        public Criteria andShfujeGreaterThan(java.math.BigDecimal value) {
            addCriterion("shfuje >", value, "shfuje");
            return (Criteria) this;
        }

        public Criteria andShfujeGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("shfuje >=", value, "shfuje");
            return (Criteria) this;
        }

        public Criteria andShfujeLessThan(java.math.BigDecimal value) {
            addCriterion("shfuje <", value, "shfuje");
            return (Criteria) this;
        }

        public Criteria andShfujeLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("shfuje <=", value, "shfuje");
            return (Criteria) this;
        }

        public Criteria andShfujeLike(java.math.BigDecimal value) {
            addCriterion("shfuje like", value, "shfuje");
            return (Criteria) this;
        }

        public Criteria andShfujeNotLike(java.math.BigDecimal value) {
            addCriterion("shfuje not like", value, "shfuje");
            return (Criteria) this;
        }

        public Criteria andShfujeIn(List<java.math.BigDecimal> values) {
            addCriterion("shfuje in", values, "shfuje");
            return (Criteria) this;
        }

        public Criteria andShfujeNotIn(List<java.math.BigDecimal> values) {
            addCriterion("shfuje not in", values, "shfuje");
            return (Criteria) this;
        }

        public Criteria andShfujeBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("shfuje between", value1, value2, "shfuje");
            return (Criteria) this;
        }

        public Criteria andShfujeNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("shfuje not between", value1, value2, "shfuje");
            return (Criteria) this;
        }

        public Criteria andSxtxlxIsNull() {
            addCriterion("sxtxlx is null");
            return (Criteria) this;
        }

        public Criteria andSxtxlxIsNotNull() {
            addCriterion("sxtxlx is not null");
            return (Criteria) this;
        }

        public Criteria andSxtxlxEqualTo(java.math.BigDecimal value) {
            addCriterion("sxtxlx =", value, "sxtxlx");
            return (Criteria) this;
        }

        public Criteria andSxtxlxNotEqualTo(java.math.BigDecimal value) {
            addCriterion("sxtxlx <>", value, "sxtxlx");
            return (Criteria) this;
        }

        public Criteria andSxtxlxGreaterThan(java.math.BigDecimal value) {
            addCriterion("sxtxlx >", value, "sxtxlx");
            return (Criteria) this;
        }

        public Criteria andSxtxlxGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("sxtxlx >=", value, "sxtxlx");
            return (Criteria) this;
        }

        public Criteria andSxtxlxLessThan(java.math.BigDecimal value) {
            addCriterion("sxtxlx <", value, "sxtxlx");
            return (Criteria) this;
        }

        public Criteria andSxtxlxLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("sxtxlx <=", value, "sxtxlx");
            return (Criteria) this;
        }

        public Criteria andSxtxlxLike(java.math.BigDecimal value) {
            addCriterion("sxtxlx like", value, "sxtxlx");
            return (Criteria) this;
        }

        public Criteria andSxtxlxNotLike(java.math.BigDecimal value) {
            addCriterion("sxtxlx not like", value, "sxtxlx");
            return (Criteria) this;
        }

        public Criteria andSxtxlxIn(List<java.math.BigDecimal> values) {
            addCriterion("sxtxlx in", values, "sxtxlx");
            return (Criteria) this;
        }

        public Criteria andSxtxlxNotIn(List<java.math.BigDecimal> values) {
            addCriterion("sxtxlx not in", values, "sxtxlx");
            return (Criteria) this;
        }

        public Criteria andSxtxlxBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("sxtxlx between", value1, value2, "sxtxlx");
            return (Criteria) this;
        }

        public Criteria andSxtxlxNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("sxtxlx not between", value1, value2, "sxtxlx");
            return (Criteria) this;
        }

        public Criteria andLjlxsrIsNull() {
            addCriterion("ljlxsr is null");
            return (Criteria) this;
        }

        public Criteria andLjlxsrIsNotNull() {
            addCriterion("ljlxsr is not null");
            return (Criteria) this;
        }

        public Criteria andLjlxsrEqualTo(java.math.BigDecimal value) {
            addCriterion("ljlxsr =", value, "ljlxsr");
            return (Criteria) this;
        }

        public Criteria andLjlxsrNotEqualTo(java.math.BigDecimal value) {
            addCriterion("ljlxsr <>", value, "ljlxsr");
            return (Criteria) this;
        }

        public Criteria andLjlxsrGreaterThan(java.math.BigDecimal value) {
            addCriterion("ljlxsr >", value, "ljlxsr");
            return (Criteria) this;
        }

        public Criteria andLjlxsrGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ljlxsr >=", value, "ljlxsr");
            return (Criteria) this;
        }

        public Criteria andLjlxsrLessThan(java.math.BigDecimal value) {
            addCriterion("ljlxsr <", value, "ljlxsr");
            return (Criteria) this;
        }

        public Criteria andLjlxsrLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ljlxsr <=", value, "ljlxsr");
            return (Criteria) this;
        }

        public Criteria andLjlxsrLike(java.math.BigDecimal value) {
            addCriterion("ljlxsr like", value, "ljlxsr");
            return (Criteria) this;
        }

        public Criteria andLjlxsrNotLike(java.math.BigDecimal value) {
            addCriterion("ljlxsr not like", value, "ljlxsr");
            return (Criteria) this;
        }

        public Criteria andLjlxsrIn(List<java.math.BigDecimal> values) {
            addCriterion("ljlxsr in", values, "ljlxsr");
            return (Criteria) this;
        }

        public Criteria andLjlxsrNotIn(List<java.math.BigDecimal> values) {
            addCriterion("ljlxsr not in", values, "ljlxsr");
            return (Criteria) this;
        }

        public Criteria andLjlxsrBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ljlxsr between", value1, value2, "ljlxsr");
            return (Criteria) this;
        }

        public Criteria andLjlxsrNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ljlxsr not between", value1, value2, "ljlxsr");
            return (Criteria) this;
        }

        public Criteria andTxrzzqIsNull() {
            addCriterion("txrzzq is null");
            return (Criteria) this;
        }

        public Criteria andTxrzzqIsNotNull() {
            addCriterion("txrzzq is not null");
            return (Criteria) this;
        }

        public Criteria andTxrzzqEqualTo(String value) {
            addCriterion("txrzzq =", value, "txrzzq");
            return (Criteria) this;
        }

        public Criteria andTxrzzqNotEqualTo(String value) {
            addCriterion("txrzzq <>", value, "txrzzq");
            return (Criteria) this;
        }

        public Criteria andTxrzzqGreaterThan(String value) {
            addCriterion("txrzzq >", value, "txrzzq");
            return (Criteria) this;
        }

        public Criteria andTxrzzqGreaterThanOrEqualTo(String value) {
            addCriterion("txrzzq >=", value, "txrzzq");
            return (Criteria) this;
        }

        public Criteria andTxrzzqLessThan(String value) {
            addCriterion("txrzzq <", value, "txrzzq");
            return (Criteria) this;
        }

        public Criteria andTxrzzqLessThanOrEqualTo(String value) {
            addCriterion("txrzzq <=", value, "txrzzq");
            return (Criteria) this;
        }

        public Criteria andTxrzzqLike(String value) {
            addCriterion("txrzzq like", value, "txrzzq");
            return (Criteria) this;
        }

        public Criteria andTxrzzqNotLike(String value) {
            addCriterion("txrzzq not like", value, "txrzzq");
            return (Criteria) this;
        }

        public Criteria andTxrzzqIn(List<String> values) {
            addCriterion("txrzzq in", values, "txrzzq");
            return (Criteria) this;
        }

        public Criteria andTxrzzqNotIn(List<String> values) {
            addCriterion("txrzzq not in", values, "txrzzq");
            return (Criteria) this;
        }

        public Criteria andTxrzzqBetween(String value1, String value2) {
            addCriterion("txrzzq between", value1, value2, "txrzzq");
            return (Criteria) this;
        }

        public Criteria andTxrzzqNotBetween(String value1, String value2) {
            addCriterion("txrzzq not between", value1, value2, "txrzzq");
            return (Criteria) this;
        }

        public Criteria andDtsryeIsNull() {
            addCriterion("dtsrye is null");
            return (Criteria) this;
        }

        public Criteria andDtsryeIsNotNull() {
            addCriterion("dtsrye is not null");
            return (Criteria) this;
        }

        public Criteria andDtsryeEqualTo(java.math.BigDecimal value) {
            addCriterion("dtsrye =", value, "dtsrye");
            return (Criteria) this;
        }

        public Criteria andDtsryeNotEqualTo(java.math.BigDecimal value) {
            addCriterion("dtsrye <>", value, "dtsrye");
            return (Criteria) this;
        }

        public Criteria andDtsryeGreaterThan(java.math.BigDecimal value) {
            addCriterion("dtsrye >", value, "dtsrye");
            return (Criteria) this;
        }

        public Criteria andDtsryeGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("dtsrye >=", value, "dtsrye");
            return (Criteria) this;
        }

        public Criteria andDtsryeLessThan(java.math.BigDecimal value) {
            addCriterion("dtsrye <", value, "dtsrye");
            return (Criteria) this;
        }

        public Criteria andDtsryeLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("dtsrye <=", value, "dtsrye");
            return (Criteria) this;
        }

        public Criteria andDtsryeLike(java.math.BigDecimal value) {
            addCriterion("dtsrye like", value, "dtsrye");
            return (Criteria) this;
        }

        public Criteria andDtsryeNotLike(java.math.BigDecimal value) {
            addCriterion("dtsrye not like", value, "dtsrye");
            return (Criteria) this;
        }

        public Criteria andDtsryeIn(List<java.math.BigDecimal> values) {
            addCriterion("dtsrye in", values, "dtsrye");
            return (Criteria) this;
        }

        public Criteria andDtsryeNotIn(List<java.math.BigDecimal> values) {
            addCriterion("dtsrye not in", values, "dtsrye");
            return (Criteria) this;
        }

        public Criteria andDtsryeBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("dtsrye between", value1, value2, "dtsrye");
            return (Criteria) this;
        }

        public Criteria andDtsryeNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("dtsrye not between", value1, value2, "dtsrye");
            return (Criteria) this;
        }

        public Criteria andSctsrqIsNull() {
            addCriterion("sctsrq is null");
            return (Criteria) this;
        }

        public Criteria andSctsrqIsNotNull() {
            addCriterion("sctsrq is not null");
            return (Criteria) this;
        }

        public Criteria andSctsrqEqualTo(String value) {
            addCriterion("sctsrq =", value, "sctsrq");
            return (Criteria) this;
        }

        public Criteria andSctsrqNotEqualTo(String value) {
            addCriterion("sctsrq <>", value, "sctsrq");
            return (Criteria) this;
        }

        public Criteria andSctsrqGreaterThan(String value) {
            addCriterion("sctsrq >", value, "sctsrq");
            return (Criteria) this;
        }

        public Criteria andSctsrqGreaterThanOrEqualTo(String value) {
            addCriterion("sctsrq >=", value, "sctsrq");
            return (Criteria) this;
        }

        public Criteria andSctsrqLessThan(String value) {
            addCriterion("sctsrq <", value, "sctsrq");
            return (Criteria) this;
        }

        public Criteria andSctsrqLessThanOrEqualTo(String value) {
            addCriterion("sctsrq <=", value, "sctsrq");
            return (Criteria) this;
        }

        public Criteria andSctsrqLike(String value) {
            addCriterion("sctsrq like", value, "sctsrq");
            return (Criteria) this;
        }

        public Criteria andSctsrqNotLike(String value) {
            addCriterion("sctsrq not like", value, "sctsrq");
            return (Criteria) this;
        }

        public Criteria andSctsrqIn(List<String> values) {
            addCriterion("sctsrq in", values, "sctsrq");
            return (Criteria) this;
        }

        public Criteria andSctsrqNotIn(List<String> values) {
            addCriterion("sctsrq not in", values, "sctsrq");
            return (Criteria) this;
        }

        public Criteria andSctsrqBetween(String value1, String value2) {
            addCriterion("sctsrq between", value1, value2, "sctsrq");
            return (Criteria) this;
        }

        public Criteria andSctsrqNotBetween(String value1, String value2) {
            addCriterion("sctsrq not between", value1, value2, "sctsrq");
            return (Criteria) this;
        }

        public Criteria andXctsroIsNull() {
            addCriterion("xctsro is null");
            return (Criteria) this;
        }

        public Criteria andXctsroIsNotNull() {
            addCriterion("xctsro is not null");
            return (Criteria) this;
        }

        public Criteria andXctsroEqualTo(String value) {
            addCriterion("xctsro =", value, "xctsro");
            return (Criteria) this;
        }

        public Criteria andXctsroNotEqualTo(String value) {
            addCriterion("xctsro <>", value, "xctsro");
            return (Criteria) this;
        }

        public Criteria andXctsroGreaterThan(String value) {
            addCriterion("xctsro >", value, "xctsro");
            return (Criteria) this;
        }

        public Criteria andXctsroGreaterThanOrEqualTo(String value) {
            addCriterion("xctsro >=", value, "xctsro");
            return (Criteria) this;
        }

        public Criteria andXctsroLessThan(String value) {
            addCriterion("xctsro <", value, "xctsro");
            return (Criteria) this;
        }

        public Criteria andXctsroLessThanOrEqualTo(String value) {
            addCriterion("xctsro <=", value, "xctsro");
            return (Criteria) this;
        }

        public Criteria andXctsroLike(String value) {
            addCriterion("xctsro like", value, "xctsro");
            return (Criteria) this;
        }

        public Criteria andXctsroNotLike(String value) {
            addCriterion("xctsro not like", value, "xctsro");
            return (Criteria) this;
        }

        public Criteria andXctsroIn(List<String> values) {
            addCriterion("xctsro in", values, "xctsro");
            return (Criteria) this;
        }

        public Criteria andXctsroNotIn(List<String> values) {
            addCriterion("xctsro not in", values, "xctsro");
            return (Criteria) this;
        }

        public Criteria andXctsroBetween(String value1, String value2) {
            addCriterion("xctsro between", value1, value2, "xctsro");
            return (Criteria) this;
        }

        public Criteria andXctsroNotBetween(String value1, String value2) {
            addCriterion("xctsro not between", value1, value2, "xctsro");
            return (Criteria) this;
        }

        public Criteria andShshjeIsNull() {
            addCriterion("shshje is null");
            return (Criteria) this;
        }

        public Criteria andShshjeIsNotNull() {
            addCriterion("shshje is not null");
            return (Criteria) this;
        }

        public Criteria andShshjeEqualTo(java.math.BigDecimal value) {
            addCriterion("shshje =", value, "shshje");
            return (Criteria) this;
        }

        public Criteria andShshjeNotEqualTo(java.math.BigDecimal value) {
            addCriterion("shshje <>", value, "shshje");
            return (Criteria) this;
        }

        public Criteria andShshjeGreaterThan(java.math.BigDecimal value) {
            addCriterion("shshje >", value, "shshje");
            return (Criteria) this;
        }

        public Criteria andShshjeGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("shshje >=", value, "shshje");
            return (Criteria) this;
        }

        public Criteria andShshjeLessThan(java.math.BigDecimal value) {
            addCriterion("shshje <", value, "shshje");
            return (Criteria) this;
        }

        public Criteria andShshjeLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("shshje <=", value, "shshje");
            return (Criteria) this;
        }

        public Criteria andShshjeLike(java.math.BigDecimal value) {
            addCriterion("shshje like", value, "shshje");
            return (Criteria) this;
        }

        public Criteria andShshjeNotLike(java.math.BigDecimal value) {
            addCriterion("shshje not like", value, "shshje");
            return (Criteria) this;
        }

        public Criteria andShshjeIn(List<java.math.BigDecimal> values) {
            addCriterion("shshje in", values, "shshje");
            return (Criteria) this;
        }

        public Criteria andShshjeNotIn(List<java.math.BigDecimal> values) {
            addCriterion("shshje not in", values, "shshje");
            return (Criteria) this;
        }

        public Criteria andShshjeBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("shshje between", value1, value2, "shshje");
            return (Criteria) this;
        }

        public Criteria andShshjeNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("shshje not between", value1, value2, "shshje");
            return (Criteria) this;
        }

        public Criteria andSftxlxIsNull() {
            addCriterion("sftxlx is null");
            return (Criteria) this;
        }

        public Criteria andSftxlxIsNotNull() {
            addCriterion("sftxlx is not null");
            return (Criteria) this;
        }

        public Criteria andSftxlxEqualTo(java.math.BigDecimal value) {
            addCriterion("sftxlx =", value, "sftxlx");
            return (Criteria) this;
        }

        public Criteria andSftxlxNotEqualTo(java.math.BigDecimal value) {
            addCriterion("sftxlx <>", value, "sftxlx");
            return (Criteria) this;
        }

        public Criteria andSftxlxGreaterThan(java.math.BigDecimal value) {
            addCriterion("sftxlx >", value, "sftxlx");
            return (Criteria) this;
        }

        public Criteria andSftxlxGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("sftxlx >=", value, "sftxlx");
            return (Criteria) this;
        }

        public Criteria andSftxlxLessThan(java.math.BigDecimal value) {
            addCriterion("sftxlx <", value, "sftxlx");
            return (Criteria) this;
        }

        public Criteria andSftxlxLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("sftxlx <=", value, "sftxlx");
            return (Criteria) this;
        }

        public Criteria andSftxlxLike(java.math.BigDecimal value) {
            addCriterion("sftxlx like", value, "sftxlx");
            return (Criteria) this;
        }

        public Criteria andSftxlxNotLike(java.math.BigDecimal value) {
            addCriterion("sftxlx not like", value, "sftxlx");
            return (Criteria) this;
        }

        public Criteria andSftxlxIn(List<java.math.BigDecimal> values) {
            addCriterion("sftxlx in", values, "sftxlx");
            return (Criteria) this;
        }

        public Criteria andSftxlxNotIn(List<java.math.BigDecimal> values) {
            addCriterion("sftxlx not in", values, "sftxlx");
            return (Criteria) this;
        }

        public Criteria andSftxlxBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("sftxlx between", value1, value2, "sftxlx");
            return (Criteria) this;
        }

        public Criteria andSftxlxNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("sftxlx not between", value1, value2, "sftxlx");
            return (Criteria) this;
        }

        public Criteria andLjlxzcIsNull() {
            addCriterion("ljlxzc is null");
            return (Criteria) this;
        }

        public Criteria andLjlxzcIsNotNull() {
            addCriterion("ljlxzc is not null");
            return (Criteria) this;
        }

        public Criteria andLjlxzcEqualTo(java.math.BigDecimal value) {
            addCriterion("ljlxzc =", value, "ljlxzc");
            return (Criteria) this;
        }

        public Criteria andLjlxzcNotEqualTo(java.math.BigDecimal value) {
            addCriterion("ljlxzc <>", value, "ljlxzc");
            return (Criteria) this;
        }

        public Criteria andLjlxzcGreaterThan(java.math.BigDecimal value) {
            addCriterion("ljlxzc >", value, "ljlxzc");
            return (Criteria) this;
        }

        public Criteria andLjlxzcGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ljlxzc >=", value, "ljlxzc");
            return (Criteria) this;
        }

        public Criteria andLjlxzcLessThan(java.math.BigDecimal value) {
            addCriterion("ljlxzc <", value, "ljlxzc");
            return (Criteria) this;
        }

        public Criteria andLjlxzcLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ljlxzc <=", value, "ljlxzc");
            return (Criteria) this;
        }

        public Criteria andLjlxzcLike(java.math.BigDecimal value) {
            addCriterion("ljlxzc like", value, "ljlxzc");
            return (Criteria) this;
        }

        public Criteria andLjlxzcNotLike(java.math.BigDecimal value) {
            addCriterion("ljlxzc not like", value, "ljlxzc");
            return (Criteria) this;
        }

        public Criteria andLjlxzcIn(List<java.math.BigDecimal> values) {
            addCriterion("ljlxzc in", values, "ljlxzc");
            return (Criteria) this;
        }

        public Criteria andLjlxzcNotIn(List<java.math.BigDecimal> values) {
            addCriterion("ljlxzc not in", values, "ljlxzc");
            return (Criteria) this;
        }

        public Criteria andLjlxzcBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ljlxzc between", value1, value2, "ljlxzc");
            return (Criteria) this;
        }

        public Criteria andLjlxzcNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ljlxzc not between", value1, value2, "ljlxzc");
            return (Criteria) this;
        }

        public Criteria andDtzcyeIsNull() {
            addCriterion("dtzcye is null");
            return (Criteria) this;
        }

        public Criteria andDtzcyeIsNotNull() {
            addCriterion("dtzcye is not null");
            return (Criteria) this;
        }

        public Criteria andDtzcyeEqualTo(java.math.BigDecimal value) {
            addCriterion("dtzcye =", value, "dtzcye");
            return (Criteria) this;
        }

        public Criteria andDtzcyeNotEqualTo(java.math.BigDecimal value) {
            addCriterion("dtzcye <>", value, "dtzcye");
            return (Criteria) this;
        }

        public Criteria andDtzcyeGreaterThan(java.math.BigDecimal value) {
            addCriterion("dtzcye >", value, "dtzcye");
            return (Criteria) this;
        }

        public Criteria andDtzcyeGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("dtzcye >=", value, "dtzcye");
            return (Criteria) this;
        }

        public Criteria andDtzcyeLessThan(java.math.BigDecimal value) {
            addCriterion("dtzcye <", value, "dtzcye");
            return (Criteria) this;
        }

        public Criteria andDtzcyeLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("dtzcye <=", value, "dtzcye");
            return (Criteria) this;
        }

        public Criteria andDtzcyeLike(java.math.BigDecimal value) {
            addCriterion("dtzcye like", value, "dtzcye");
            return (Criteria) this;
        }

        public Criteria andDtzcyeNotLike(java.math.BigDecimal value) {
            addCriterion("dtzcye not like", value, "dtzcye");
            return (Criteria) this;
        }

        public Criteria andDtzcyeIn(List<java.math.BigDecimal> values) {
            addCriterion("dtzcye in", values, "dtzcye");
            return (Criteria) this;
        }

        public Criteria andDtzcyeNotIn(List<java.math.BigDecimal> values) {
            addCriterion("dtzcye not in", values, "dtzcye");
            return (Criteria) this;
        }

        public Criteria andDtzcyeBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("dtzcye between", value1, value2, "dtzcye");
            return (Criteria) this;
        }

        public Criteria andDtzcyeNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("dtzcye not between", value1, value2, "dtzcye");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}