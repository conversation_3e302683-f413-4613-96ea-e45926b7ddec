package com.hsjry.core.limit.batch.dal.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 电票系统-落地表-贴现余额信息Example
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
public class LbSElcblDsctBalInfoExample {
    /** 排序字段 */
    protected String orderByClause;

    /** 是否只查询记录不同的数据 */
    protected boolean distinct;

    /** or条件查询集合 */
    protected List<Criteria> oredCriteria;

    /**
     * 初始化or条件查询集合
     */
    public LbSElcblDsctBalInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * 排序字段set方法
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * 排序字段get方法
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * 不同记录set方法.
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * 不同记录get方法.
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * 排序集合get方法
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * or
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * or
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * 清除数据
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andOrgNoIsNull() {
            addCriterion("org_no is null");
            return (Criteria) this;
        }

        public Criteria andOrgNoIsNotNull() {
            addCriterion("org_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrgNoEqualTo(String value) {
            addCriterion("org_no =", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoNotEqualTo(String value) {
            addCriterion("org_no <>", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoGreaterThan(String value) {
            addCriterion("org_no >", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoGreaterThanOrEqualTo(String value) {
            addCriterion("org_no >=", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoLessThan(String value) {
            addCriterion("org_no <", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoLessThanOrEqualTo(String value) {
            addCriterion("org_no <=", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoLike(String value) {
            addCriterion("org_no like", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoNotLike(String value) {
            addCriterion("org_no not like", value, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoIn(List<String> values) {
            addCriterion("org_no in", values, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoNotIn(List<String> values) {
            addCriterion("org_no not in", values, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoBetween(String value1, String value2) {
            addCriterion("org_no between", value1, value2, "orgNo");
            return (Criteria) this;
        }

        public Criteria andOrgNoNotBetween(String value1, String value2) {
            addCriterion("org_no not between", value1, value2, "orgNo");
            return (Criteria) this;
        }

        public Criteria andDicCnoIsNull() {
            addCriterion("dic_cno is null");
            return (Criteria) this;
        }

        public Criteria andDicCnoIsNotNull() {
            addCriterion("dic_cno is not null");
            return (Criteria) this;
        }

        public Criteria andDicCnoEqualTo(String value) {
            addCriterion("dic_cno =", value, "dicCno");
            return (Criteria) this;
        }

        public Criteria andDicCnoNotEqualTo(String value) {
            addCriterion("dic_cno <>", value, "dicCno");
            return (Criteria) this;
        }

        public Criteria andDicCnoGreaterThan(String value) {
            addCriterion("dic_cno >", value, "dicCno");
            return (Criteria) this;
        }

        public Criteria andDicCnoGreaterThanOrEqualTo(String value) {
            addCriterion("dic_cno >=", value, "dicCno");
            return (Criteria) this;
        }

        public Criteria andDicCnoLessThan(String value) {
            addCriterion("dic_cno <", value, "dicCno");
            return (Criteria) this;
        }

        public Criteria andDicCnoLessThanOrEqualTo(String value) {
            addCriterion("dic_cno <=", value, "dicCno");
            return (Criteria) this;
        }

        public Criteria andDicCnoLike(String value) {
            addCriterion("dic_cno like", value, "dicCno");
            return (Criteria) this;
        }

        public Criteria andDicCnoNotLike(String value) {
            addCriterion("dic_cno not like", value, "dicCno");
            return (Criteria) this;
        }

        public Criteria andDicCnoIn(List<String> values) {
            addCriterion("dic_cno in", values, "dicCno");
            return (Criteria) this;
        }

        public Criteria andDicCnoNotIn(List<String> values) {
            addCriterion("dic_cno not in", values, "dicCno");
            return (Criteria) this;
        }

        public Criteria andDicCnoBetween(String value1, String value2) {
            addCriterion("dic_cno between", value1, value2, "dicCno");
            return (Criteria) this;
        }

        public Criteria andDicCnoNotBetween(String value1, String value2) {
            addCriterion("dic_cno not between", value1, value2, "dicCno");
            return (Criteria) this;
        }

        public Criteria andBillNoIsNull() {
            addCriterion("bill_no is null");
            return (Criteria) this;
        }

        public Criteria andBillNoIsNotNull() {
            addCriterion("bill_no is not null");
            return (Criteria) this;
        }

        public Criteria andBillNoEqualTo(String value) {
            addCriterion("bill_no =", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoNotEqualTo(String value) {
            addCriterion("bill_no <>", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoGreaterThan(String value) {
            addCriterion("bill_no >", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoGreaterThanOrEqualTo(String value) {
            addCriterion("bill_no >=", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoLessThan(String value) {
            addCriterion("bill_no <", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoLessThanOrEqualTo(String value) {
            addCriterion("bill_no <=", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoLike(String value) {
            addCriterion("bill_no like", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoNotLike(String value) {
            addCriterion("bill_no not like", value, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoIn(List<String> values) {
            addCriterion("bill_no in", values, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoNotIn(List<String> values) {
            addCriterion("bill_no not in", values, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoBetween(String value1, String value2) {
            addCriterion("bill_no between", value1, value2, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillNoNotBetween(String value1, String value2) {
            addCriterion("bill_no not between", value1, value2, "billNo");
            return (Criteria) this;
        }

        public Criteria andBillRangeStartIsNull() {
            addCriterion("bill_range_start is null");
            return (Criteria) this;
        }

        public Criteria andBillRangeStartIsNotNull() {
            addCriterion("bill_range_start is not null");
            return (Criteria) this;
        }

        public Criteria andBillRangeStartEqualTo(String value) {
            addCriterion("bill_range_start =", value, "billRangeStart");
            return (Criteria) this;
        }

        public Criteria andBillRangeStartNotEqualTo(String value) {
            addCriterion("bill_range_start <>", value, "billRangeStart");
            return (Criteria) this;
        }

        public Criteria andBillRangeStartGreaterThan(String value) {
            addCriterion("bill_range_start >", value, "billRangeStart");
            return (Criteria) this;
        }

        public Criteria andBillRangeStartGreaterThanOrEqualTo(String value) {
            addCriterion("bill_range_start >=", value, "billRangeStart");
            return (Criteria) this;
        }

        public Criteria andBillRangeStartLessThan(String value) {
            addCriterion("bill_range_start <", value, "billRangeStart");
            return (Criteria) this;
        }

        public Criteria andBillRangeStartLessThanOrEqualTo(String value) {
            addCriterion("bill_range_start <=", value, "billRangeStart");
            return (Criteria) this;
        }

        public Criteria andBillRangeStartLike(String value) {
            addCriterion("bill_range_start like", value, "billRangeStart");
            return (Criteria) this;
        }

        public Criteria andBillRangeStartNotLike(String value) {
            addCriterion("bill_range_start not like", value, "billRangeStart");
            return (Criteria) this;
        }

        public Criteria andBillRangeStartIn(List<String> values) {
            addCriterion("bill_range_start in", values, "billRangeStart");
            return (Criteria) this;
        }

        public Criteria andBillRangeStartNotIn(List<String> values) {
            addCriterion("bill_range_start not in", values, "billRangeStart");
            return (Criteria) this;
        }

        public Criteria andBillRangeStartBetween(String value1, String value2) {
            addCriterion("bill_range_start between", value1, value2, "billRangeStart");
            return (Criteria) this;
        }

        public Criteria andBillRangeStartNotBetween(String value1, String value2) {
            addCriterion("bill_range_start not between", value1, value2, "billRangeStart");
            return (Criteria) this;
        }

        public Criteria andBillRangeEndIsNull() {
            addCriterion("bill_range_end is null");
            return (Criteria) this;
        }

        public Criteria andBillRangeEndIsNotNull() {
            addCriterion("bill_range_end is not null");
            return (Criteria) this;
        }

        public Criteria andBillRangeEndEqualTo(String value) {
            addCriterion("bill_range_end =", value, "billRangeEnd");
            return (Criteria) this;
        }

        public Criteria andBillRangeEndNotEqualTo(String value) {
            addCriterion("bill_range_end <>", value, "billRangeEnd");
            return (Criteria) this;
        }

        public Criteria andBillRangeEndGreaterThan(String value) {
            addCriterion("bill_range_end >", value, "billRangeEnd");
            return (Criteria) this;
        }

        public Criteria andBillRangeEndGreaterThanOrEqualTo(String value) {
            addCriterion("bill_range_end >=", value, "billRangeEnd");
            return (Criteria) this;
        }

        public Criteria andBillRangeEndLessThan(String value) {
            addCriterion("bill_range_end <", value, "billRangeEnd");
            return (Criteria) this;
        }

        public Criteria andBillRangeEndLessThanOrEqualTo(String value) {
            addCriterion("bill_range_end <=", value, "billRangeEnd");
            return (Criteria) this;
        }

        public Criteria andBillRangeEndLike(String value) {
            addCriterion("bill_range_end like", value, "billRangeEnd");
            return (Criteria) this;
        }

        public Criteria andBillRangeEndNotLike(String value) {
            addCriterion("bill_range_end not like", value, "billRangeEnd");
            return (Criteria) this;
        }

        public Criteria andBillRangeEndIn(List<String> values) {
            addCriterion("bill_range_end in", values, "billRangeEnd");
            return (Criteria) this;
        }

        public Criteria andBillRangeEndNotIn(List<String> values) {
            addCriterion("bill_range_end not in", values, "billRangeEnd");
            return (Criteria) this;
        }

        public Criteria andBillRangeEndBetween(String value1, String value2) {
            addCriterion("bill_range_end between", value1, value2, "billRangeEnd");
            return (Criteria) this;
        }

        public Criteria andBillRangeEndNotBetween(String value1, String value2) {
            addCriterion("bill_range_end not between", value1, value2, "billRangeEnd");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNull() {
            addCriterion("user_name is null");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNotNull() {
            addCriterion("user_name is not null");
            return (Criteria) this;
        }

        public Criteria andUserNameEqualTo(String value) {
            addCriterion("user_name =", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotEqualTo(String value) {
            addCriterion("user_name <>", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThan(String value) {
            addCriterion("user_name >", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("user_name >=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThan(String value) {
            addCriterion("user_name <", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThanOrEqualTo(String value) {
            addCriterion("user_name <=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLike(String value) {
            addCriterion("user_name like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotLike(String value) {
            addCriterion("user_name not like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameIn(List<String> values) {
            addCriterion("user_name in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotIn(List<String> values) {
            addCriterion("user_name not in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameBetween(String value1, String value2) {
            addCriterion("user_name between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotBetween(String value1, String value2) {
            addCriterion("user_name not between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNull() {
            addCriterion("currency is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyIsNotNull() {
            addCriterion("currency is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyEqualTo(String value) {
            addCriterion("currency =", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotEqualTo(String value) {
            addCriterion("currency <>", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThan(String value) {
            addCriterion("currency >", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyGreaterThanOrEqualTo(String value) {
            addCriterion("currency >=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThan(String value) {
            addCriterion("currency <", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLessThanOrEqualTo(String value) {
            addCriterion("currency <=", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyLike(String value) {
            addCriterion("currency like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotLike(String value) {
            addCriterion("currency not like", value, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyIn(List<String> values) {
            addCriterion("currency in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotIn(List<String> values) {
            addCriterion("currency not in", values, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyBetween(String value1, String value2) {
            addCriterion("currency between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andCurrencyNotBetween(String value1, String value2) {
            addCriterion("currency not between", value1, value2, "currency");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtIsNull() {
            addCriterion("discount_amt is null");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtIsNotNull() {
            addCriterion("discount_amt is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtEqualTo(java.math.BigDecimal value) {
            addCriterion("discount_amt =", value, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtNotEqualTo(java.math.BigDecimal value) {
            addCriterion("discount_amt <>", value, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtGreaterThan(java.math.BigDecimal value) {
            addCriterion("discount_amt >", value, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("discount_amt >=", value, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtLessThan(java.math.BigDecimal value) {
            addCriterion("discount_amt <", value, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("discount_amt <=", value, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtLike(java.math.BigDecimal value) {
            addCriterion("discount_amt like", value, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtNotLike(java.math.BigDecimal value) {
            addCriterion("discount_amt not like", value, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtIn(List<java.math.BigDecimal> values) {
            addCriterion("discount_amt in", values, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtNotIn(List<java.math.BigDecimal> values) {
            addCriterion("discount_amt not in", values, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("discount_amt between", value1, value2, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountAmtNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("discount_amt not between", value1, value2, "discountAmt");
            return (Criteria) this;
        }

        public Criteria andDiscountBalIsNull() {
            addCriterion("discount_bal is null");
            return (Criteria) this;
        }

        public Criteria andDiscountBalIsNotNull() {
            addCriterion("discount_bal is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountBalEqualTo(java.math.BigDecimal value) {
            addCriterion("discount_bal =", value, "discountBal");
            return (Criteria) this;
        }

        public Criteria andDiscountBalNotEqualTo(java.math.BigDecimal value) {
            addCriterion("discount_bal <>", value, "discountBal");
            return (Criteria) this;
        }

        public Criteria andDiscountBalGreaterThan(java.math.BigDecimal value) {
            addCriterion("discount_bal >", value, "discountBal");
            return (Criteria) this;
        }

        public Criteria andDiscountBalGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("discount_bal >=", value, "discountBal");
            return (Criteria) this;
        }

        public Criteria andDiscountBalLessThan(java.math.BigDecimal value) {
            addCriterion("discount_bal <", value, "discountBal");
            return (Criteria) this;
        }

        public Criteria andDiscountBalLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("discount_bal <=", value, "discountBal");
            return (Criteria) this;
        }

        public Criteria andDiscountBalLike(java.math.BigDecimal value) {
            addCriterion("discount_bal like", value, "discountBal");
            return (Criteria) this;
        }

        public Criteria andDiscountBalNotLike(java.math.BigDecimal value) {
            addCriterion("discount_bal not like", value, "discountBal");
            return (Criteria) this;
        }

        public Criteria andDiscountBalIn(List<java.math.BigDecimal> values) {
            addCriterion("discount_bal in", values, "discountBal");
            return (Criteria) this;
        }

        public Criteria andDiscountBalNotIn(List<java.math.BigDecimal> values) {
            addCriterion("discount_bal not in", values, "discountBal");
            return (Criteria) this;
        }

        public Criteria andDiscountBalBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("discount_bal between", value1, value2, "discountBal");
            return (Criteria) this;
        }

        public Criteria andDiscountBalNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("discount_bal not between", value1, value2, "discountBal");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(String value) {
            addCriterion("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(String value) {
            addCriterion("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(String value) {
            addCriterion("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(String value) {
            addCriterion("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(String value) {
            addCriterion("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(String value) {
            addCriterion("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLike(String value) {
            addCriterion("start_date like", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotLike(String value) {
            addCriterion("start_date not like", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<String> values) {
            addCriterion("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<String> values) {
            addCriterion("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(String value1, String value2) {
            addCriterion("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(String value1, String value2) {
            addCriterion("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("end_date is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("end_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(String value) {
            addCriterion("end_date =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(String value) {
            addCriterion("end_date <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(String value) {
            addCriterion("end_date >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(String value) {
            addCriterion("end_date >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(String value) {
            addCriterion("end_date <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(String value) {
            addCriterion("end_date <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLike(String value) {
            addCriterion("end_date like", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotLike(String value) {
            addCriterion("end_date not like", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<String> values) {
            addCriterion("end_date in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<String> values) {
            addCriterion("end_date not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(String value1, String value2) {
            addCriterion("end_date between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(String value1, String value2) {
            addCriterion("end_date not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(String value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(String value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(String value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(String value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(String value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(String value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLike(String value) {
            addCriterion("create_time like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotLike(String value) {
            addCriterion("create_time not like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<String> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<String> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(String value1, String value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(String value1, String value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(String value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(String value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(String value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(String value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(String value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(String value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLike(String value) {
            addCriterion("update_time like", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotLike(String value) {
            addCriterion("update_time not like", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<String> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<String> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(String value1, String value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(String value1, String value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}