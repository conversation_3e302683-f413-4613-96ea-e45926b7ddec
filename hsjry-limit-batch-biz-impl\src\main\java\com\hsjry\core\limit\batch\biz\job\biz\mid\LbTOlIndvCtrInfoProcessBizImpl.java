/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.biz.mid;

import java.util.Objects;

import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.sharding.JobCoreBusinessFactory;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;

import lombok.extern.slf4j.Slf4j;

/**
 * 额度中心-中间表-网贷系统个人合同信息处理业务实现类</br>
 * 负责调度和管理网贷系统个人合同信息处理任务
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/16
 */
@Slf4j
@Service("lbTOlIndvCtrInfoProcessBizImpl")
public class LbTOlIndvCtrInfoProcessBizImpl implements BaseOrdinaryBiz {

    /**
     * 获取任务交易码
     *
     * @return 网贷系统个人合同信息处理的任务交易码
     */
    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.LB_T_OL_INDV_CTR_INFO_PROCESS;
    }

    /**
     * 执行网贷系统个人合同信息处理的基础任务
     * 此方法是任务的入口点,负责协调整个数据处理流程
     *
     * @param jobInitDto 任务初始化参数,包含业务日期、批次流水号等信息
     */
    @Override
    public void execBaseJob(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();

        String prefixLog = String.format("营运日期:[%s],批量流水号:[%s],任务交易码:[%s-%s]", businessDate,
            batchSerialNo, jobTradeCode, jobTradeDesc);

        log.info("{}========开始执行网贷系统个人合同信息处理任务========", prefixLog);

        try {
            // 通过工厂获取具体的网贷系统个人合同信息处理实现
            JobCoreBusiness jobCoreBusiness = JobCoreBusinessFactory.getJobCoreBusiness(jobTradeCode);
            if (Objects.isNull(jobCoreBusiness)) {
                String errorMsg = String.format("未找到任务交易码[%s]对应的网贷系统个人合同信息处理实现类",
                    jobTradeCode);
                log.error("{}{}", prefixLog, errorMsg);
                throw new RuntimeException(errorMsg);
            }

            // 执行前置处理
            log.info("{}开始执行前置处理", prefixLog);
            jobCoreBusiness.preHandle(jobInitDto);
            log.info("{}前置处理完成", prefixLog);

            log.info("{}网贷系统个人合同信息处理任务执行完成,具体分片处理将由分片任务完成", prefixLog);

        } catch (Exception e) {
            log.error("{}网贷系统个人合同信息处理任务执行失败", prefixLog, e);
            throw new RuntimeException(String.format("网贷系统个人合同信息处理任务执行失败: %s", e.getMessage()), e);
        } finally {
            try {
                // 执行后置处理
                JobCoreBusiness jobCoreBusiness = JobCoreBusinessFactory.getJobCoreBusiness(jobTradeCode);
                if (Objects.nonNull(jobCoreBusiness)) {
                    log.info("{}开始执行后置处理", prefixLog);
                    jobCoreBusiness.afterHandle(jobInitDto);
                    log.info("{}后置处理完成", prefixLog);
                }
            } catch (Exception e) {
                log.error("{}后置处理执行失败", prefixLog, e);
                // 后置处理失败不影响主流程
            }
        }

        log.info("{}========网贷系统个人合同信息处理任务结束========", prefixLog);
    }
} 