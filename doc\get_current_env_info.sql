create FUNCTION get_current_env_info
    RETURN VARCHAR2
    IS
    v_info VARCHAR2 (500);
v_tenant_id NUMBER;
v_db_name VARCHAR2(128);
v_db_version VARCHAR2(128);
v_is_oceanbase VARCHAR2(20) := 'Oracle';
BEGIN
-- 尝试获取租户ID来判断是否为OceanBase
BEGIN
SELECT TO_NUMBER(SYS_CONTEXT('USERENV', 'TENANT_ID'))
INTO v_tenant_id
FROM DUAL;
IF v_tenant_id IS NOT NULL AND v_tenant_id > 0 THEN
            v_is_oceanbase := 'OceanBase';
END IF;
EXCEPTION
        WHEN OTHERS THEN
            v_tenant_id := NULL;
END;

    -- 获取数据库版本
BEGIN
SELECT BANNER
INTO v_db_version
FROM V$VERSION
WHERE ROWNUM = 1;
EXCEPTION
        WHEN OTHERS THEN
            v_db_version := 'Unknown';
END;

    -- 获取当前Schema
SELECT SYS_CONTEXT('USERENV', 'CURRENT_SCHEMA')
INTO v_db_name
FROM DUAL;

-- 组合信息
v_info := 'Database: ' || v_is_oceanbase ||
              ', Tenant ID: ' || NVL(TO_CHAR(v_tenant_id), '000(default)') ||
              ', Schema: ' || NVL(v_db_name, 'N/A') ||
              ', Session ID: ' || USERENV('SESSIONID');

RETURN v_info;
END get_current_env_info;
/

