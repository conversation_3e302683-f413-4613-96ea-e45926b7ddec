package com.hsjry.core.limit.batch.dal.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 电票系统-历史表-同业客户额度同步Example
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
public class LbHElcblIbnkLmtSynzExample {
    /** 排序字段 */
    protected String orderByClause;

    /** 是否只查询记录不同的数据 */
    protected boolean distinct;

    /** or条件查询集合 */
    protected List<Criteria> oredCriteria;

    /**
     * 初始化or条件查询集合
     */
    public LbHElcblIbnkLmtSynzExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * 排序字段set方法
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * 排序字段get方法
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * 不同记录set方法.
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * 不同记录get方法.
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * 排序集合get方法
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * or
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * or
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * 清除数据
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIbnkUserIdIsNull() {
            addCriterion("ibnk_user_id is null");
            return (Criteria) this;
        }

        public Criteria andIbnkUserIdIsNotNull() {
            addCriterion("ibnk_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andIbnkUserIdEqualTo(String value) {
            addCriterion("ibnk_user_id =", value, "ibnkUserId");
            return (Criteria) this;
        }

        public Criteria andIbnkUserIdNotEqualTo(String value) {
            addCriterion("ibnk_user_id <>", value, "ibnkUserId");
            return (Criteria) this;
        }

        public Criteria andIbnkUserIdGreaterThan(String value) {
            addCriterion("ibnk_user_id >", value, "ibnkUserId");
            return (Criteria) this;
        }

        public Criteria andIbnkUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("ibnk_user_id >=", value, "ibnkUserId");
            return (Criteria) this;
        }

        public Criteria andIbnkUserIdLessThan(String value) {
            addCriterion("ibnk_user_id <", value, "ibnkUserId");
            return (Criteria) this;
        }

        public Criteria andIbnkUserIdLessThanOrEqualTo(String value) {
            addCriterion("ibnk_user_id <=", value, "ibnkUserId");
            return (Criteria) this;
        }

        public Criteria andIbnkUserIdLike(String value) {
            addCriterion("ibnk_user_id like", value, "ibnkUserId");
            return (Criteria) this;
        }

        public Criteria andIbnkUserIdNotLike(String value) {
            addCriterion("ibnk_user_id not like", value, "ibnkUserId");
            return (Criteria) this;
        }

        public Criteria andIbnkUserIdIn(List<String> values) {
            addCriterion("ibnk_user_id in", values, "ibnkUserId");
            return (Criteria) this;
        }

        public Criteria andIbnkUserIdNotIn(List<String> values) {
            addCriterion("ibnk_user_id not in", values, "ibnkUserId");
            return (Criteria) this;
        }

        public Criteria andIbnkUserIdBetween(String value1, String value2) {
            addCriterion("ibnk_user_id between", value1, value2, "ibnkUserId");
            return (Criteria) this;
        }

        public Criteria andIbnkUserIdNotBetween(String value1, String value2) {
            addCriterion("ibnk_user_id not between", value1, value2, "ibnkUserId");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateKindIsNull() {
            addCriterion("ibnk_user_certificate_kind is null");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateKindIsNotNull() {
            addCriterion("ibnk_user_certificate_kind is not null");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateKindEqualTo(String value) {
            addCriterion("ibnk_user_certificate_kind =", value, "ibnkUserCertificateKind");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateKindNotEqualTo(String value) {
            addCriterion("ibnk_user_certificate_kind <>", value, "ibnkUserCertificateKind");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateKindGreaterThan(String value) {
            addCriterion("ibnk_user_certificate_kind >", value, "ibnkUserCertificateKind");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateKindGreaterThanOrEqualTo(String value) {
            addCriterion("ibnk_user_certificate_kind >=", value, "ibnkUserCertificateKind");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateKindLessThan(String value) {
            addCriterion("ibnk_user_certificate_kind <", value, "ibnkUserCertificateKind");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateKindLessThanOrEqualTo(String value) {
            addCriterion("ibnk_user_certificate_kind <=", value, "ibnkUserCertificateKind");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateKindLike(String value) {
            addCriterion("ibnk_user_certificate_kind like", value, "ibnkUserCertificateKind");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateKindNotLike(String value) {
            addCriterion("ibnk_user_certificate_kind not like", value, "ibnkUserCertificateKind");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateKindIn(List<String> values) {
            addCriterion("ibnk_user_certificate_kind in", values, "ibnkUserCertificateKind");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateKindNotIn(List<String> values) {
            addCriterion("ibnk_user_certificate_kind not in", values, "ibnkUserCertificateKind");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateKindBetween(String value1, String value2) {
            addCriterion("ibnk_user_certificate_kind between", value1, value2, "ibnkUserCertificateKind");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateKindNotBetween(String value1, String value2) {
            addCriterion("ibnk_user_certificate_kind not between", value1, value2, "ibnkUserCertificateKind");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateNoIsNull() {
            addCriterion("ibnk_user_certificate_no is null");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateNoIsNotNull() {
            addCriterion("ibnk_user_certificate_no is not null");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateNoEqualTo(String value) {
            addCriterion("ibnk_user_certificate_no =", value, "ibnkUserCertificateNo");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateNoNotEqualTo(String value) {
            addCriterion("ibnk_user_certificate_no <>", value, "ibnkUserCertificateNo");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateNoGreaterThan(String value) {
            addCriterion("ibnk_user_certificate_no >", value, "ibnkUserCertificateNo");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateNoGreaterThanOrEqualTo(String value) {
            addCriterion("ibnk_user_certificate_no >=", value, "ibnkUserCertificateNo");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateNoLessThan(String value) {
            addCriterion("ibnk_user_certificate_no <", value, "ibnkUserCertificateNo");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateNoLessThanOrEqualTo(String value) {
            addCriterion("ibnk_user_certificate_no <=", value, "ibnkUserCertificateNo");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateNoLike(String value) {
            addCriterion("ibnk_user_certificate_no like", value, "ibnkUserCertificateNo");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateNoNotLike(String value) {
            addCriterion("ibnk_user_certificate_no not like", value, "ibnkUserCertificateNo");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateNoIn(List<String> values) {
            addCriterion("ibnk_user_certificate_no in", values, "ibnkUserCertificateNo");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateNoNotIn(List<String> values) {
            addCriterion("ibnk_user_certificate_no not in", values, "ibnkUserCertificateNo");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateNoBetween(String value1, String value2) {
            addCriterion("ibnk_user_certificate_no between", value1, value2, "ibnkUserCertificateNo");
            return (Criteria) this;
        }

        public Criteria andIbnkUserCertificateNoNotBetween(String value1, String value2) {
            addCriterion("ibnk_user_certificate_no not between", value1, value2, "ibnkUserCertificateNo");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIsNull() {
            addCriterion("total_amount is null");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIsNotNull() {
            addCriterion("total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTotalAmountEqualTo(java.math.BigDecimal value) {
            addCriterion("total_amount =", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotEqualTo(java.math.BigDecimal value) {
            addCriterion("total_amount <>", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountGreaterThan(java.math.BigDecimal value) {
            addCriterion("total_amount >", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("total_amount >=", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLessThan(java.math.BigDecimal value) {
            addCriterion("total_amount <", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("total_amount <=", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLike(java.math.BigDecimal value) {
            addCriterion("total_amount like", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotLike(java.math.BigDecimal value) {
            addCriterion("total_amount not like", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIn(List<java.math.BigDecimal> values) {
            addCriterion("total_amount in", values, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotIn(List<java.math.BigDecimal> values) {
            addCriterion("total_amount not in", values, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("total_amount between", value1, value2, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("total_amount not between", value1, value2, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andAvailableAmountIsNull() {
            addCriterion("available_amount is null");
            return (Criteria) this;
        }

        public Criteria andAvailableAmountIsNotNull() {
            addCriterion("available_amount is not null");
            return (Criteria) this;
        }

        public Criteria andAvailableAmountEqualTo(java.math.BigDecimal value) {
            addCriterion("available_amount =", value, "availableAmount");
            return (Criteria) this;
        }

        public Criteria andAvailableAmountNotEqualTo(java.math.BigDecimal value) {
            addCriterion("available_amount <>", value, "availableAmount");
            return (Criteria) this;
        }

        public Criteria andAvailableAmountGreaterThan(java.math.BigDecimal value) {
            addCriterion("available_amount >", value, "availableAmount");
            return (Criteria) this;
        }

        public Criteria andAvailableAmountGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("available_amount >=", value, "availableAmount");
            return (Criteria) this;
        }

        public Criteria andAvailableAmountLessThan(java.math.BigDecimal value) {
            addCriterion("available_amount <", value, "availableAmount");
            return (Criteria) this;
        }

        public Criteria andAvailableAmountLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("available_amount <=", value, "availableAmount");
            return (Criteria) this;
        }

        public Criteria andAvailableAmountLike(java.math.BigDecimal value) {
            addCriterion("available_amount like", value, "availableAmount");
            return (Criteria) this;
        }

        public Criteria andAvailableAmountNotLike(java.math.BigDecimal value) {
            addCriterion("available_amount not like", value, "availableAmount");
            return (Criteria) this;
        }

        public Criteria andAvailableAmountIn(List<java.math.BigDecimal> values) {
            addCriterion("available_amount in", values, "availableAmount");
            return (Criteria) this;
        }

        public Criteria andAvailableAmountNotIn(List<java.math.BigDecimal> values) {
            addCriterion("available_amount not in", values, "availableAmount");
            return (Criteria) this;
        }

        public Criteria andAvailableAmountBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("available_amount between", value1, value2, "availableAmount");
            return (Criteria) this;
        }

        public Criteria andAvailableAmountNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("available_amount not between", value1, value2, "availableAmount");
            return (Criteria) this;
        }

        public Criteria andUseOccupyAmountIsNull() {
            addCriterion("use_occupy_amount is null");
            return (Criteria) this;
        }

        public Criteria andUseOccupyAmountIsNotNull() {
            addCriterion("use_occupy_amount is not null");
            return (Criteria) this;
        }

        public Criteria andUseOccupyAmountEqualTo(java.math.BigDecimal value) {
            addCriterion("use_occupy_amount =", value, "useOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andUseOccupyAmountNotEqualTo(java.math.BigDecimal value) {
            addCriterion("use_occupy_amount <>", value, "useOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andUseOccupyAmountGreaterThan(java.math.BigDecimal value) {
            addCriterion("use_occupy_amount >", value, "useOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andUseOccupyAmountGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("use_occupy_amount >=", value, "useOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andUseOccupyAmountLessThan(java.math.BigDecimal value) {
            addCriterion("use_occupy_amount <", value, "useOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andUseOccupyAmountLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("use_occupy_amount <=", value, "useOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andUseOccupyAmountLike(java.math.BigDecimal value) {
            addCriterion("use_occupy_amount like", value, "useOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andUseOccupyAmountNotLike(java.math.BigDecimal value) {
            addCriterion("use_occupy_amount not like", value, "useOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andUseOccupyAmountIn(List<java.math.BigDecimal> values) {
            addCriterion("use_occupy_amount in", values, "useOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andUseOccupyAmountNotIn(List<java.math.BigDecimal> values) {
            addCriterion("use_occupy_amount not in", values, "useOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andUseOccupyAmountBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("use_occupy_amount between", value1, value2, "useOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andUseOccupyAmountNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("use_occupy_amount not between", value1, value2, "useOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andCoreInstNoIsNull() {
            addCriterion("core_inst_no is null");
            return (Criteria) this;
        }

        public Criteria andCoreInstNoIsNotNull() {
            addCriterion("core_inst_no is not null");
            return (Criteria) this;
        }

        public Criteria andCoreInstNoEqualTo(String value) {
            addCriterion("core_inst_no =", value, "coreInstNo");
            return (Criteria) this;
        }

        public Criteria andCoreInstNoNotEqualTo(String value) {
            addCriterion("core_inst_no <>", value, "coreInstNo");
            return (Criteria) this;
        }

        public Criteria andCoreInstNoGreaterThan(String value) {
            addCriterion("core_inst_no >", value, "coreInstNo");
            return (Criteria) this;
        }

        public Criteria andCoreInstNoGreaterThanOrEqualTo(String value) {
            addCriterion("core_inst_no >=", value, "coreInstNo");
            return (Criteria) this;
        }

        public Criteria andCoreInstNoLessThan(String value) {
            addCriterion("core_inst_no <", value, "coreInstNo");
            return (Criteria) this;
        }

        public Criteria andCoreInstNoLessThanOrEqualTo(String value) {
            addCriterion("core_inst_no <=", value, "coreInstNo");
            return (Criteria) this;
        }

        public Criteria andCoreInstNoLike(String value) {
            addCriterion("core_inst_no like", value, "coreInstNo");
            return (Criteria) this;
        }

        public Criteria andCoreInstNoNotLike(String value) {
            addCriterion("core_inst_no not like", value, "coreInstNo");
            return (Criteria) this;
        }

        public Criteria andCoreInstNoIn(List<String> values) {
            addCriterion("core_inst_no in", values, "coreInstNo");
            return (Criteria) this;
        }

        public Criteria andCoreInstNoNotIn(List<String> values) {
            addCriterion("core_inst_no not in", values, "coreInstNo");
            return (Criteria) this;
        }

        public Criteria andCoreInstNoBetween(String value1, String value2) {
            addCriterion("core_inst_no between", value1, value2, "coreInstNo");
            return (Criteria) this;
        }

        public Criteria andCoreInstNoNotBetween(String value1, String value2) {
            addCriterion("core_inst_no not between", value1, value2, "coreInstNo");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(String value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(String value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(String value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(String value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(String value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(String value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLike(String value) {
            addCriterion("create_time like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotLike(String value) {
            addCriterion("create_time not like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<String> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<String> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(String value1, String value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(String value1, String value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(String value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(String value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(String value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(String value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(String value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(String value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLike(String value) {
            addCriterion("update_time like", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotLike(String value) {
            addCriterion("update_time not like", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<String> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<String> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(String value1, String value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(String value1, String value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andDataDateIsNull() {
            addCriterion("data_date is null");
            return (Criteria) this;
        }

        public Criteria andDataDateIsNotNull() {
            addCriterion("data_date is not null");
            return (Criteria) this;
        }

        public Criteria andDataDateEqualTo(String value) {
            addCriterion("data_date =", value, "dataDate");
            return (Criteria) this;
        }

        public Criteria andDataDateNotEqualTo(String value) {
            addCriterion("data_date <>", value, "dataDate");
            return (Criteria) this;
        }

        public Criteria andDataDateGreaterThan(String value) {
            addCriterion("data_date >", value, "dataDate");
            return (Criteria) this;
        }

        public Criteria andDataDateGreaterThanOrEqualTo(String value) {
            addCriterion("data_date >=", value, "dataDate");
            return (Criteria) this;
        }

        public Criteria andDataDateLessThan(String value) {
            addCriterion("data_date <", value, "dataDate");
            return (Criteria) this;
        }

        public Criteria andDataDateLessThanOrEqualTo(String value) {
            addCriterion("data_date <=", value, "dataDate");
            return (Criteria) this;
        }

        public Criteria andDataDateLike(String value) {
            addCriterion("data_date like", value, "dataDate");
            return (Criteria) this;
        }

        public Criteria andDataDateNotLike(String value) {
            addCriterion("data_date not like", value, "dataDate");
            return (Criteria) this;
        }

        public Criteria andDataDateIn(List<String> values) {
            addCriterion("data_date in", values, "dataDate");
            return (Criteria) this;
        }

        public Criteria andDataDateNotIn(List<String> values) {
            addCriterion("data_date not in", values, "dataDate");
            return (Criteria) this;
        }

        public Criteria andDataDateBetween(String value1, String value2) {
            addCriterion("data_date between", value1, value2, "dataDate");
            return (Criteria) this;
        }

        public Criteria andDataDateNotBetween(String value1, String value2) {
            addCriterion("data_date not between", value1, value2, "dataDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}