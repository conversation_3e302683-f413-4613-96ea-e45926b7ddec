package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 网贷系统-中间表-个人借据信息主键
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
@Table(name = "lb_t_ol_indv_loan_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbTOlIndvLoanInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1950902007284039683L;
    /** 客户编号 */
    @Id
    @Column(name = "cust_no")
    private String custNo;
    /** 额度编号 */
    @Id
    @Column(name = "cust_limit_id")
    private String custLimitId;
    /** 实体编号 */
    @Id
    @Column(name = "entity_id")
    private String entityId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}