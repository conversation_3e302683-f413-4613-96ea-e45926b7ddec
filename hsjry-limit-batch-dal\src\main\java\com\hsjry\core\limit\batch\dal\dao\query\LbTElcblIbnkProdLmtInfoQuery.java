package com.hsjry.core.limit.batch.dal.dao.query;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * 电票系统-中间表-同业客户产品层额度信息查询条件
 *
 * <AUTHOR>
 * @date 2025-07-28 11:16:05
 */
@Data
@Builder
public class LbTElcblIbnkProdLmtInfoQuery implements Serializable {

    /** serialVersionUID */
    private static final long serialVersionUID = 1949790961362206720L;

    /** 额度编号 */
    private String custLimitId;
    /** 所属组织编号 */
    private String ownOrganId;
    /** 操作人编号 */
    private String operatorId;
    /** 核心机构号 */
    private String coreInstNo;
    /** 额度实例金额信息中实占额度 */
    private java.math.BigDecimal realOccupyAmount;
    /** 额度状态 */
    private String limitStatus;
    /** 客户编号 */
    private String custNo;
    /** 已占用额度 */
    private java.math.BigDecimal useOccupyAmount;
    /** 可用额度 */
    private java.math.BigDecimal availableAmount;
    /** 总额度 */
    private java.math.BigDecimal totalAmount;
    /** 证件号码 */
    private String certNo;
    /** 证件类型 */
    private String certTyp;
    /** 客户名称 */
    private String custNm;
    /** 客户类型 */
    private String custTyp;
}
