package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbSCoreAtxzhDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbSCoreAtxzhMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreAtxzhDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreAtxzhExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreAtxzhKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSCoreAtxzhQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 核心系统-落地表-贴现账户主文件数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Repository
public class LbSCoreAtxzhDaoImpl extends AbstractBaseDaoImpl<LbSCoreAtxzhDo, LbSCoreAtxzhMapper>
    implements LbSCoreAtxzhDao {
    /**
     * 分页查询
     *
     * @param lbSCoreAtxzh 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbSCoreAtxzhDo> selectPage(LbSCoreAtxzhQuery lbSCoreAtxzh, PageParam pageParam) {
        LbSCoreAtxzhExample example = buildExample(lbSCoreAtxzh);
        return PageHelper.<LbSCoreAtxzhDo>startPage(pageParam.getPageNum(), pageParam.getPageSize()).doSelectPageInfo(
            () -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询核心系统-落地表-贴现账户主文件
     *
     * @param faredm
     * @param txnjjh
     * @return
     */
    @Override
    public LbSCoreAtxzhDo selectByKey(String faredm, String txnjjh) {
        LbSCoreAtxzhKeyDo lbSCoreAtxzhKeyDo = new LbSCoreAtxzhKeyDo();
        lbSCoreAtxzhKeyDo.setFaredm(faredm);
        lbSCoreAtxzhKeyDo.setTxnjjh(txnjjh);
        return getMapper().selectByPrimaryKey(lbSCoreAtxzhKeyDo);
    }

    /**
     * 根据key删除核心系统-落地表-贴现账户主文件
     *
     * @param faredm
     * @param txnjjh
     * @return
     */
    @Override
    public int deleteByKey(String faredm, String txnjjh) {
        LbSCoreAtxzhKeyDo lbSCoreAtxzhKeyDo = new LbSCoreAtxzhKeyDo();
        lbSCoreAtxzhKeyDo.setFaredm(faredm);
        lbSCoreAtxzhKeyDo.setTxnjjh(txnjjh);
        return getMapper().deleteByPrimaryKey(lbSCoreAtxzhKeyDo);
    }

    /**
     * 查询核心系统-落地表-贴现账户主文件信息
     *
     * @param lbSCoreAtxzh 条件
     * @return List<LbSCoreAtxzhDo>
     */
    @Override
    public List<LbSCoreAtxzhDo> selectByExample(LbSCoreAtxzhQuery lbSCoreAtxzh) {
        return getMapper().selectByExample(buildExample(lbSCoreAtxzh));
    }

    /**
     * 新增核心系统-落地表-贴现账户主文件信息
     *
     * @param lbSCoreAtxzh 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbSCoreAtxzhDo lbSCoreAtxzh) {
        if (lbSCoreAtxzh == null) {
            return -1;
        }
        return getMapper().insertSelective(lbSCoreAtxzh);
    }

    /**
     * 修改核心系统-落地表-贴现账户主文件信息
     *
     * @param lbSCoreAtxzh
     * @return
     */
    @Override
    public int updateBySelective(LbSCoreAtxzhDo lbSCoreAtxzh) {
        if (lbSCoreAtxzh == null) {
            return -1;
        }
        return getMapper().updateByPrimaryKeySelective(lbSCoreAtxzh);
    }

    @Override
    public int updateBySelectiveByExample(LbSCoreAtxzhDo lbSCoreAtxzh, LbSCoreAtxzhQuery lbSCoreAtxzhQuery) {
        return getMapper().updateByExampleSelective(lbSCoreAtxzh, buildExample(lbSCoreAtxzhQuery));
    }

    /**
     * 构建核心系统-落地表-贴现账户主文件Example信息
     *
     * @param lbSCoreAtxzh
     * @return
     */
    public LbSCoreAtxzhExample buildExample(LbSCoreAtxzhQuery lbSCoreAtxzh) {
        LbSCoreAtxzhExample example = new LbSCoreAtxzhExample();
        LbSCoreAtxzhExample.Criteria criteria = example.createCriteria();
        if (lbSCoreAtxzh != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getTxfxbz())) {
                criteria.andTxfxbzEqualTo(lbSCoreAtxzh.getTxfxbz());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getDuifhh())) {
                criteria.andDuifhhEqualTo(lbSCoreAtxzh.getDuifhh());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getJigulb())) {
                criteria.andJigulbEqualTo(lbSCoreAtxzh.getJigulb());
            }
            if (null != lbSCoreAtxzh.getZhdkje()) {
                criteria.andZhdkjeEqualTo(lbSCoreAtxzh.getZhdkje());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getJiejuh())) {
                criteria.andJiejuhEqualTo(lbSCoreAtxzh.getJiejuh());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getDnknbz())) {
                criteria.andDnknbzEqualTo(lbSCoreAtxzh.getDnknbz());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getXinx01())) {
                criteria.andXinx01EqualTo(lbSCoreAtxzh.getXinx01());
            }
            if (null != lbSCoreAtxzh.getFxcdje()) {
                criteria.andFxcdjeEqualTo(lbSCoreAtxzh.getFxcdje());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getDzywbh())) {
                criteria.andDzywbhEqualTo(lbSCoreAtxzh.getDzywbh());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getDuifhm())) {
                criteria.andDuifhmEqualTo(lbSCoreAtxzh.getDuifhm());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getMffxzh())) {
                criteria.andMffxzhEqualTo(lbSCoreAtxzh.getMffxzh());
            }
            if (null != lbSCoreAtxzh.getMffxbl()) {
                criteria.andMffxblEqualTo(lbSCoreAtxzh.getMffxbl());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getTxfxfs())) {
                criteria.andTxfxfsEqualTo(lbSCoreAtxzh.getTxfxfs());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getCxcfbh())) {
                criteria.andCxcfbhEqualTo(lbSCoreAtxzh.getCxcfbh());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getSfxthc())) {
                criteria.andSfxthcEqualTo(lbSCoreAtxzh.getSfxthc());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getJieszh())) {
                criteria.andJieszhEqualTo(lbSCoreAtxzh.getJieszh());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getXctzrq())) {
                criteria.andXctzrqEqualTo(lbSCoreAtxzh.getXctzrq());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getSctzrq())) {
                criteria.andSctzrqEqualTo(lbSCoreAtxzh.getSctzrq());
            }
            if (null != lbSCoreAtxzh.getBcmdll()) {
                criteria.andBcmdllEqualTo(lbSCoreAtxzh.getBcmdll());
            }
            if (null != lbSCoreAtxzh.getBcmdlx()) {
                criteria.andBcmdlxEqualTo(lbSCoreAtxzh.getBcmdlx());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getZhungt())) {
                criteria.andZhungtEqualTo(lbSCoreAtxzh.getZhungt());
            }
            if (null != lbSCoreAtxzh.getMxxhao()) {
                criteria.andMxxhaoEqualTo(lbSCoreAtxzh.getMxxhao());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getLururq())) {
                criteria.andLururqEqualTo(lbSCoreAtxzh.getLururq());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getLurugy())) {
                criteria.andLurugyEqualTo(lbSCoreAtxzh.getLurugy());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getFuherq())) {
                criteria.andFuherqEqualTo(lbSCoreAtxzh.getFuherq());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getFuhegy())) {
                criteria.andFuhegyEqualTo(lbSCoreAtxzh.getFuhegy());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getWeihrq())) {
                criteria.andWeihrqEqualTo(lbSCoreAtxzh.getWeihrq());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getWeihgy())) {
                criteria.andWeihgyEqualTo(lbSCoreAtxzh.getWeihgy());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getJioyrq())) {
                criteria.andJioyrqEqualTo(lbSCoreAtxzh.getJioyrq());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getWeihjg())) {
                criteria.andWeihjgEqualTo(lbSCoreAtxzh.getWeihjg());
            }
            if (null != lbSCoreAtxzh.getWeihsj()) {
                criteria.andWeihsjEqualTo(lbSCoreAtxzh.getWeihsj());
            }
            if (null != lbSCoreAtxzh.getShinch()) {
                criteria.andShinchEqualTo(lbSCoreAtxzh.getShinch());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getJiluzt())) {
                criteria.andJiluztEqualTo(lbSCoreAtxzh.getJiluzt());
            }
            if (null != lbSCoreAtxzh.getKuanxq()) {
                criteria.andKuanxqEqualTo(lbSCoreAtxzh.getKuanxq());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getTxnjjh())) {
                criteria.andTxnjjhEqualTo(lbSCoreAtxzh.getTxnjjh());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getTiexzh())) {
                criteria.andTiexzhEqualTo(lbSCoreAtxzh.getTiexzh());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getTxclzl())) {
                criteria.andTxclzlEqualTo(lbSCoreAtxzh.getTxclzl());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getTxywzl())) {
                criteria.andTxywzlEqualTo(lbSCoreAtxzh.getTxywzl());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getPiojzh())) {
                criteria.andPiojzhEqualTo(lbSCoreAtxzh.getPiojzh());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getKehhao())) {
                criteria.andKehhaoEqualTo(lbSCoreAtxzh.getKehhao());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getKehzwm())) {
                criteria.andKehzwmEqualTo(lbSCoreAtxzh.getKehzwm());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getYngyjg())) {
                criteria.andYngyjgEqualTo(lbSCoreAtxzh.getYngyjg());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getZhngjg())) {
                criteria.andZhngjgEqualTo(lbSCoreAtxzh.getZhngjg());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getRuzhjg())) {
                criteria.andRuzhjgEqualTo(lbSCoreAtxzh.getRuzhjg());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getSyzcjg())) {
                criteria.andSyzcjgEqualTo(lbSCoreAtxzh.getSyzcjg());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getSyrzjg())) {
                criteria.andSyrzjgEqualTo(lbSCoreAtxzh.getSyrzjg());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getHuobdh())) {
                criteria.andHuobdhEqualTo(lbSCoreAtxzh.getHuobdh());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getTxqxrq())) {
                criteria.andTxqxrqEqualTo(lbSCoreAtxzh.getTxqxrq());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getTxdqrq())) {
                criteria.andTxdqrqEqualTo(lbSCoreAtxzh.getTxdqrq());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getFaredm())) {
                criteria.andFaredmEqualTo(lbSCoreAtxzh.getFaredm());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getLilvbh())) {
                criteria.andLilvbhEqualTo(lbSCoreAtxzh.getLilvbh());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getNyuell())) {
                criteria.andNyuellEqualTo(lbSCoreAtxzh.getNyuell());
            }
            if (null != lbSCoreAtxzh.getTiexll()) {
                criteria.andTiexllEqualTo(lbSCoreAtxzh.getTiexll());
            }
            if (null != lbSCoreAtxzh.getTxztye()) {
                criteria.andTxztyeEqualTo(lbSCoreAtxzh.getTxztye());
            }
            if (null != lbSCoreAtxzh.getShfuje()) {
                criteria.andShfujeEqualTo(lbSCoreAtxzh.getShfuje());
            }
            if (null != lbSCoreAtxzh.getSxtxlx()) {
                criteria.andSxtxlxEqualTo(lbSCoreAtxzh.getSxtxlx());
            }
            if (null != lbSCoreAtxzh.getLjlxsr()) {
                criteria.andLjlxsrEqualTo(lbSCoreAtxzh.getLjlxsr());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getTxrzzq())) {
                criteria.andTxrzzqEqualTo(lbSCoreAtxzh.getTxrzzq());
            }
            if (null != lbSCoreAtxzh.getDtsrye()) {
                criteria.andDtsryeEqualTo(lbSCoreAtxzh.getDtsrye());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getSctsrq())) {
                criteria.andSctsrqEqualTo(lbSCoreAtxzh.getSctsrq());
            }
            if (StringUtil.isNotEmpty(lbSCoreAtxzh.getXctsro())) {
                criteria.andXctsroEqualTo(lbSCoreAtxzh.getXctsro());
            }
            if (null != lbSCoreAtxzh.getShshje()) {
                criteria.andShshjeEqualTo(lbSCoreAtxzh.getShshje());
            }
            if (null != lbSCoreAtxzh.getSftxlx()) {
                criteria.andSftxlxEqualTo(lbSCoreAtxzh.getSftxlx());
            }
            if (null != lbSCoreAtxzh.getLjlxzc()) {
                criteria.andLjlxzcEqualTo(lbSCoreAtxzh.getLjlxzc());
            }
            if (null != lbSCoreAtxzh.getDtzcye()) {
                criteria.andDtzcyeEqualTo(lbSCoreAtxzh.getDtzcye());
            }
        }
        buildExampleExt(lbSCoreAtxzh, criteria);
        return example;
    }

    /**
     * 构建核心系统-落地表-贴现账户主文件ExampleExt方法
     *
     * @param lbSCoreAtxzh
     * @return
     */
    public void buildExampleExt(LbSCoreAtxzhQuery lbSCoreAtxzh, LbSCoreAtxzhExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 批量插入核心系统贴现账户主文件-落地信息
     *
     * @param lbSCoreAtxzhList 批量数据
     * @return int
     */
    @Override
    public int insertList(List<LbSCoreAtxzhDo> lbSCoreAtxzhList) {
        if (lbSCoreAtxzhList.isEmpty()) {
            return 0;
        }
        return getMapper().insertList(lbSCoreAtxzhList);
    }

    /**
     * 清空核心系统贴现账户主文件-落地所有数据
     *
     * @return int
     */
    @Override
    public int deleteAll() {
        return getMapper().deleteAll();
    }
}
