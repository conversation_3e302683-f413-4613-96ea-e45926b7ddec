package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcEntityOperateSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcEntityOperateSerialQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 实体操作流水数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcEntityOperateSerialDao extends IBaseDao<LcEntityOperateSerialDo> {
    /**
     * 分页查询实体操作流水
     *
     * @param lcEntityOperateSerialQuery 条件
     * @return PageInfo<LcEntityOperateSerialDo>
     */
    PageInfo<LcEntityOperateSerialDo> selectPage(LcEntityOperateSerialQuery lcEntityOperateSerialQuery,
        PageParam pageParam);

    /**
     * 根据key查询实体操作流水
     *
     * @param leosSerialNo
     * @return
     */
    LcEntityOperateSerialDo selectByKey(String leosSerialNo);

    /**
     * 根据key删除实体操作流水
     *
     * @param leosSerialNo
     * @return
     */
    int deleteByKey(String leosSerialNo);

    /**
     * 查询实体操作流水信息
     *
     * @param lcEntityOperateSerialQuery 条件
     * @return List<LcEntityOperateSerialDo>
     */
    List<LcEntityOperateSerialDo> selectByExample(LcEntityOperateSerialQuery lcEntityOperateSerialQuery);

    /**
     * 新增实体操作流水信息
     *
     * @param lcEntityOperateSerial 条件
     * @return int>
     */
    int insertBySelective(LcEntityOperateSerialDo lcEntityOperateSerial);

    /**
     * 修改实体操作流水信息
     *
     * @param lcEntityOperateSerial
     * @return
     */
    int updateBySelective(LcEntityOperateSerialDo lcEntityOperateSerial);

    /**
     * 修改实体操作流水信息
     *
     * @param lcEntityOperateSerial
     * @param lcEntityOperateSerialQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcEntityOperateSerialDo lcEntityOperateSerial,
        LcEntityOperateSerialQuery lcEntityOperateSerialQuery);

    /**
     * 删除实体操作流水信息
     *
     * @param lcEntityOperateSerialQuery 条件
     * @return List<LcEntityOperateSerialDo>
     */
    int deleteByExample(LcEntityOperateSerialQuery lcEntityOperateSerialQuery);

    /**
     * 批量更新客户编号
     *
     * @return
     */
    int updateCustNo();
}
