package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTUserGroupMemberDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTUserGroupMemberQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-集团客户成员数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-15 03:50:12
 */
public interface LbTUserGroupMemberDao extends IBaseDao<LbTUserGroupMemberDo> {
    /**
     * 分页查询额度中心-中间表-集团客户成员
     *
     * @param lbTUserGroupMemberQuery 条件
     * @return PageInfo<LbTUserGroupMemberDo>
     */
    PageInfo<LbTUserGroupMemberDo> selectPage(LbTUserGroupMemberQuery lbTUserGroupMemberQuery, PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-集团客户成员
     *
     * @param resourceId
     * @return
     */
    LbTUserGroupMemberDo selectByKey(String resourceId);

    /**
     * 根据key删除额度中心-中间表-集团客户成员
     *
     * @param resourceId
     * @return
     */
    int deleteByKey(String resourceId);

    /**
     * 查询额度中心-中间表-集团客户成员信息
     *
     * @param lbTUserGroupMemberQuery 条件
     * @return List<LbTUserGroupMemberDo>
     */
    List<LbTUserGroupMemberDo> selectByExample(LbTUserGroupMemberQuery lbTUserGroupMemberQuery);

    /**
     * 新增额度中心-中间表-集团客户成员信息
     *
     * @param lbTUserGroupMember 条件
     * @return int>
     */
    int insertBySelective(LbTUserGroupMemberDo lbTUserGroupMember);

    /**
     * 修改额度中心-中间表-集团客户成员信息
     *
     * @param lbTUserGroupMember
     * @return
     */
    int updateBySelective(LbTUserGroupMemberDo lbTUserGroupMember);

    /**
     * 修改额度中心-中间表-集团客户成员信息
     *
     * @param lbTUserGroupMember
     * @param lbTUserGroupMemberQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTUserGroupMemberDo lbTUserGroupMember,
        LbTUserGroupMemberQuery lbTUserGroupMemberQuery);
}
