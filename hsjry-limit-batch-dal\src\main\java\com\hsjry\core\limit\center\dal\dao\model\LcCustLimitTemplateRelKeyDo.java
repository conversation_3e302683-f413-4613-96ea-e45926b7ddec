package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度体系模板关联主键
 *
 * <AUTHOR>
 * @date 2022-12-06 03:12:50
 */
@Table(name = "lc_cust_limit_template_rel")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcCustLimitTemplateRelKeyDo implements Serializable {

    private static final long serialVersionUID = 1599965058241331200L;
    /** 体系模板关联编号 */
    @Id
    @Column(name = "template_relation_id")
    private String templateRelationId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}