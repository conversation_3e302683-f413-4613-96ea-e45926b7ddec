/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.io.File;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.core.limit.batch.biz.convert.file.LbSOlLoanInfoConverter;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.biz.entity.LbSOlLoanInfoData;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.AbstractFileBaseShardingPrepareBizImpl;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.constants.CharsetConstants;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.dal.dao.intf.LbSOlLoanInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlLoanInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.sequence.SequenceTool;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
/**
 * 网贷借据信息文件同步实现类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 16:30
 */
@Slf4j
@Service("lbSOlLoanInfoFileSyncImpl")
@RequiredArgsConstructor
public class LbSOlLoanInfoFileSyncImpl extends AbstractFileBaseShardingPrepareBizImpl<LbSOlLoanInfoData> {

    // CSV文件字段列索引常量（假设按SQL脚本中的字段顺序）
    /** 资产借据编号列数 */
    private static final int LOAN_INVOICE_ID_NUM = 1;
    /** 支用申请编号列数 */
    private static final int LOAN_APPLY_ID_NUM = 2;
    /** 用户编号列数 */
    private static final int USER_ID_NUM = 3;
    /** 用户姓名列数 */
    private static final int USER_NAME_NUM = 4;
    /** 借款金额列数 */
    private static final int LOAN_AMOUNT_NUM = 5;
    /** 借款利率列数 */
    private static final int RATE_NUM = 6;
    /** 产品编号列数 */
    private static final int PRODUCT_ID_NUM = 7;
    /** 产品名称列数 */
    private static final int PRODUCT_NAME_NUM = 8;
    /** 产品种类列数 */
    private static final int PRODUCT_CATALOG_NUM = 9;
    /** 借款起始时间列数 */
    private static final int LOAN_START_TIME_NUM = 10;
    /** 借款到期时间列数 */
    private static final int LOAN_END_TIME_NUM = 11;
    /** 借据状态列数 */
    private static final int STATUS_NUM = 12;
    /** 放款方式列数 */
    private static final int LOAN_TYPE_NUM = 13;
    /** 商户编号列数 */
    private static final int MERCHANT_ID_NUM = 14;
    /** 商户名称列数 */
    private static final int MERCHANT_NAME_NUM = 15;
    /** 门店id列数 */
    private static final int STORE_ID_NUM = 16;
    /** 门店名称列数 */
    private static final int STORE_NAME_NUM = 17;
    /** 还款日列数 */
    private static final int REPAY_DAY_NUM = 18;
    /** 渠道编号列数 */
    private static final int CHANNEL_NO_NUM = 19;
    /** 分期金额列数 */
    private static final int INSTALLMENT_AMOUNT_NUM = 20;
    /** 分期期数列数 */
    private static final int INSTALLMENT_NUM_NUM = 21;
    /** 放款支付时间列数 */
    private static final int LOAN_PAY_TIME_NUM = 22;
    /** 营销中心编号列数 */
    private static final int MARKET_CENTER_ID_NUM = 23;
    /** 结清日期列数 */
    private static final int SETTLE_DATE_NUM = 24;
    /** 操作者编号列数 */
    private static final int OPERATOR_ID_NUM = 25;
    /** 所属组织id列数 */
    private static final int OWN_ORGAN_ID_NUM = 26;
    /** 租户号列数 */
    private static final int TENANT_ID_NUM = 27;
    /** 创建时间列数 */
    private static final int CREATE_TIME_NUM = 28;
    /** 更新时间列数 */
    private static final int UPDATE_TIME_NUM = 29;
    /** 客户经理编号列数 */
    private static final int CUST_MGR_ID_NUM = 30;
    /** 客户手机号码列数 */
    private static final int USER_TEL_NUM = 31;
    /** 客户证件类型列数 */
    private static final int CERTIFICATE_TYPE_NUM = 32;
    /** 客户证件号码列数 */
    private static final int CERTIFICATE_NO_NUM = 33;
    /** 代扣协议编号列数 */
    private static final int WITHHOLD_PROTOCOL_ID_NUM = 34;
    /** 业务标识列数 */
    private static final int BUSINESS_SIGN_NUM = 35;
    /** 贷款分类列数 */
    private static final int CLASSIFICATION_NUM = 36;
    /** 合同编号列数 */
    private static final int CONTRACT_ID_NUM = 37;
    /** 授信申请编号列数 */
    private static final int CREDIT_APPLY_ID_NUM = 38;
    // /** 客户经理机构编号列数 */
    // private static final int CUST_MGR_ORGAN_ID_NUM = 39;

    /** 最小字段数量 */
    private static final int MIN_FIELD_COUNT = 38;
    /** 分隔符 */
    private static final String FIELD_SEPARATOR = "\u0003";
    /** 批处理大小 */
    private static final int BATCH_SIZE = 1000;

    private final LbSOlLoanInfoDao lbSOlLoanInfoDao;

    @Value("${project.ol.loan_info.filename:ICM_CREDIT_LOAN_INVOICE_[DATE].txt}")
    private String fileName;
    @Value("${limit.batch.remoteFilePath:/app/bftfiles/cfs/recv/ebbs/}")
    private String remoteFilePathDefine;

    @Override
    public ShardingResult<LbSOlLoanInfoData> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info("{}开始查询分片数据", prefixLog);

        ShardingResult<LbSOlLoanInfoData> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        try {
            // 读取文件分片数据
            FileLineData fileLineData = GsonUtil.json2Obj(jobShared.getExtParam(), FileLineData.class);
            log.info(prefixLog + "开始读取文件数据分片[{}]", GsonUtil.objToStrForLog(fileLineData));
            List<String> originData = FileShardingUtils.readFileSharedData(jobShared, skipFirst(), CharsetConstants.GBK);

            // 使用并行流处理数据，提升性能
            List<LbSOlLoanInfoData> fileDataList = processOriginDataParallel(originData, prefixLog);

            log.info(prefixLog + "读取文件数据分片总量[{}]", fileDataList.size());
            shardingResult.setShardingResultList(fileDataList);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        jobShared.setBatchSerialNo(inParam.getString(SEQUENCE_NO));
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbSOlLoanInfoData> shardingResult) {
        LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
        List<LbSOlLoanInfoData> dataList = shardingResult.getShardingResultList();
        if (CollectionUtil.isEmpty(dataList)) {
            log.warn("分片数据为空,跳过处理");
            return;
        }

        String prefixLog = "执行借据信息文件同步核心业务逻辑,";
        log.info("{}开始处理{}条数据", prefixLog, dataList.size());

        // 确保分片流水对象的状态字段不为null
        if (sliceBatchSerialDo.getSharedStatus() == null) {
            log.debug(prefixLog + "分片流水状态为null,设置为处理中状态");
            sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
        }

        try {
            // 数据转换
            List<LbSOlLoanInfoDo> doList = LbSOlLoanInfoConverter.dataListToDoList(dataList);

            // 数据验证和过滤
            List<LbSOlLoanInfoDo> validDoList = doList.stream()
                .filter(this::validateData)
                .collect(Collectors.toList());

            if (validDoList.isEmpty()) {
                log.warn("{}没有有效数据,跳过数据库操作", prefixLog);
                return;
            }

            // 分批处理数据库操作
            processBatchInsert(validDoList, prefixLog);

            log.info("{}完成数据处理,共处理{}条有效数据", prefixLog, validDoList.size());

        } catch (Exception e) {
            log.error("{}处理数据异常", prefixLog, e);
            throw new HsjryBizException(EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode(),
                EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription() + ": " + e.getMessage());
        }
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.S_OL_LOAN_INFO;
    }

    /** 常量：序列号 */
    private final String SEQUENCE_NO = "SEQUENCE_NO";

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        // 确保fixNum被正确设置
        if (jobInitDto.getFixNum() == null) {
            log.warn(prefixLog + "fixNum为null,设置默认值1000");
            jobInitDto.setFixNum(1000);
        }

        JSONObject param = JSON.parseObject(jobInitDto.getInPara());
        //设置流水ID,解决多次从redis获取唯一值的性能问题
        param.put(SEQUENCE_NO, SequenceTool.nextId());
        //更新jobInitDto的inpara参数
        jobInitDto.setInPara(param.toJSONString());

        List<JobShared> sharedList = Lists.newArrayList();
        log.info(prefixLog + "[{}]文件处理", jobTradeDesc);
        String localFilePath = FileShardingUtils.getLocalFilePath(jobInitDto.getBusinessDate(),
            remoteFilePathDefine + FileShardingUtils.ACCT_DATE_CODE_MARK + File.separator);
        Integer acctDate = jobInitDto.getBusinessDate();
        String localFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK, String.valueOf(acctDate));

        try {
            String fileAttr = FIELD_SEPARATOR;
            String filePath = localFilePath + localFileName;
            log.info("实际查找的文件路径: [{}]", filePath);

            // 检查目录是否存在
            File directory = new File(localFilePath);
            if (!directory.exists()) {
                log.info(prefixLog + "目录[{}]不存在,尝试创建", localFilePath);
                directory.mkdirs();
            } else {
                log.info(prefixLog + "目录[{}]已存在", localFilePath);
                // 列出目录中的文件
                File[] files = directory.listFiles();
                if (files != null && files.length > 0) {
                    log.info(prefixLog + "目录[{}]中的文件列表:", localFilePath);
                    for (File f : files) {
                        log.info(prefixLog + " - {}", f.getName());
                    }
                } else {
                    log.info(prefixLog + "目录[{}]为空", localFilePath);
                }
            }

            File localFile = new File(filePath);
            log.info(prefixLog + "检查文件[{}]是否存在: {}", filePath, localFile.exists());

            //判断本地文件是否存在，不存在则直接报错
            if(!localFile.exists()){
                log.error(prefixLog + "本地文件[{}]不存在,请确认文件路径是否正确", filePath);
                String errorCode = EnumBatchJobError.FILE_PATH_NOT_EXIST.getCode();
                String errorMsg = EnumBatchJobError.FILE_PATH_NOT_EXIST.getDescription();
                throw new HsjryBizException(errorCode, errorMsg);
            }

            // 首次处理需要清空表数据
            if (shouldClearTable()) {
                log.info("{}开始清空借据信息表数据", prefixLog);
                int deletedCount = lbSOlLoanInfoDao.deleteAll();
                log.info("{}清空借据信息表数据完成,删除{}条记录", prefixLog, deletedCount);
            }

            // 文件存在，直接进行分片处理
            log.info(prefixLog + "[{}]开始[{}]文件数据分片处理", jobTradeDesc, filePath);
            log.info(prefixLog + "分片参数: fixNum=[{}], fileAttr=[{}], skipFirst=[{}]",
                jobInitDto.getFixNum(), fileAttr, skipFirst());

            List<JobShared> jobShareds = FileShardingUtils.getFileSharedData(jobInitDto, localFile, fileAttr,
                skipFirst());
            log.info(prefixLog + "[{}]结束[{}]文件数据分片当前分片数[{}]", jobTradeDesc, filePath,
                jobShareds.size());
            sharedList.addAll(jobShareds);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }
        return sharedList;
    }

    /**
     * 是否跳过第一行（标题行）
     *
     * @return true-跳过第一行，false-不跳过
     */
    private boolean skipFirst() {
        return true; // 通常CSV文件有标题行，需要跳过
    }

    /**
     * 是否需要清空表数据
     *
     * @return true-需要清空，false-不需要
     */
    private boolean shouldClearTable() {
        return true; // 文件同步通常需要全量替换
    }

    /**
     * 并行处理原始数据解析
     *
     * @param originData 原始数据列表
     * @param prefixLog 日志前缀
     * @return 解析后的数据列表
     */
    private List<LbSOlLoanInfoData> processOriginDataParallel(List<String> originData, String prefixLog) {
        if (CollectionUtil.isEmpty(originData)) {
            return Lists.newArrayList();
        }

        AtomicInteger invalidCount = new AtomicInteger(0);
        AtomicInteger parseErrorCount = new AtomicInteger(0);

        List<LbSOlLoanInfoData> result = originData.parallelStream()
            .filter(StringUtil::isNotBlank)
            .map(item -> parseLineData(item, prefixLog, invalidCount, parseErrorCount))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        log.info("{}数据解析完成,有效数据:{}条,无效数据:{}条,解析错误:{}条",
            prefixLog, result.size(), invalidCount.get(), parseErrorCount.get());

        return result;
    }

    /**
     * 解析单行数据
     *
     * @param item 单行数据
     * @param prefixLog 日志前缀
     * @param invalidCount 无效数据计数器
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的数据对象
     */
    private LbSOlLoanInfoData parseLineData(String item, String prefixLog, AtomicInteger invalidCount,
        AtomicInteger parseErrorCount) {
        String[] split = item.split(FIELD_SEPARATOR);
        if (split.length < MIN_FIELD_COUNT) {
            invalidCount.incrementAndGet();
            log.warn("{}数据字段不足,期望:{},实际:{},数据:{}", prefixLog, MIN_FIELD_COUNT, split.length, item);
            return null;
        }

        try {
            LbSOlLoanInfoData fileData = new LbSOlLoanInfoData();

            // 设置字符串字段
            fileData.setLoanInvoiceId(getFieldValue(split, LOAN_INVOICE_ID_NUM - 1));
            fileData.setLoanApplyId(getFieldValue(split, LOAN_APPLY_ID_NUM - 1));
            fileData.setUserId(getFieldValue(split, USER_ID_NUM - 1));
            fileData.setUserName(getFieldValue(split, USER_NAME_NUM - 1));
            fileData.setProductId(getFieldValue(split, PRODUCT_ID_NUM - 1));
            fileData.setProductName(getFieldValue(split, PRODUCT_NAME_NUM - 1));
            fileData.setProductCatalog(getFieldValue(split, PRODUCT_CATALOG_NUM - 1));
            fileData.setLoanStartTime(getFieldValue(split, LOAN_START_TIME_NUM - 1));
            fileData.setLoanEndTime(getFieldValue(split, LOAN_END_TIME_NUM - 1));
            fileData.setStatus(getFieldValue(split, STATUS_NUM - 1));
            fileData.setLoanType(getFieldValue(split, LOAN_TYPE_NUM - 1));
            fileData.setMerchantId(getFieldValue(split, MERCHANT_ID_NUM - 1));
            fileData.setMerchantName(getFieldValue(split, MERCHANT_NAME_NUM - 1));
            fileData.setStoreId(getFieldValue(split, STORE_ID_NUM - 1));
            fileData.setStoreName(getFieldValue(split, STORE_NAME_NUM - 1));
            fileData.setChannelNo(getFieldValue(split, CHANNEL_NO_NUM - 1));
            fileData.setLoanPayTime(getFieldValue(split, LOAN_PAY_TIME_NUM - 1));
            fileData.setMarketCenterId(getFieldValue(split, MARKET_CENTER_ID_NUM - 1));
            fileData.setSettleDate(getFieldValue(split, SETTLE_DATE_NUM - 1));
            fileData.setOperatorId(getFieldValue(split, OPERATOR_ID_NUM - 1));
            fileData.setOwnOrganId(getFieldValue(split, OWN_ORGAN_ID_NUM - 1));
            fileData.setTenantId(getFieldValue(split, TENANT_ID_NUM - 1));
            fileData.setCreateTime(getFieldValue(split, CREATE_TIME_NUM - 1));
            fileData.setUpdateTime(getFieldValue(split, UPDATE_TIME_NUM - 1));
            fileData.setCustMgrId(getFieldValue(split, CUST_MGR_ID_NUM - 1));
            fileData.setUserTel(getFieldValue(split, USER_TEL_NUM - 1));
            fileData.setCertificateType(getFieldValue(split, CERTIFICATE_TYPE_NUM - 1));
            fileData.setCertificateNo(getFieldValue(split, CERTIFICATE_NO_NUM - 1));
            fileData.setWithholdProtocolId(getFieldValue(split, WITHHOLD_PROTOCOL_ID_NUM - 1));
            fileData.setBusinessSign(getFieldValue(split, BUSINESS_SIGN_NUM - 1));
            fileData.setClassification(getFieldValue(split, CLASSIFICATION_NUM - 1));
            fileData.setContractId(getFieldValue(split, CONTRACT_ID_NUM - 1));
            fileData.setCreditApplyId(getFieldValue(split, CREDIT_APPLY_ID_NUM - 1));
            // fileData.setCustMgrOrganId(getFieldValue(split, CUST_MGR_ORGAN_ID_NUM - 1));

            // 安全解析数字字段
            fileData.setLoanAmount(parseBigDecimalSafely(getFieldValue(split, LOAN_AMOUNT_NUM - 1), parseErrorCount));
            fileData.setRate(parseBigDecimalSafely(getFieldValue(split, RATE_NUM - 1), parseErrorCount));
            fileData.setInstallmentAmount(parseBigDecimalSafely(getFieldValue(split, INSTALLMENT_AMOUNT_NUM - 1), parseErrorCount));
            fileData.setRepayDay(parseIntegerSafely(getFieldValue(split, REPAY_DAY_NUM - 1), parseErrorCount));
            fileData.setInstallmentNum(parseIntegerSafely(getFieldValue(split, INSTALLMENT_NUM_NUM - 1), parseErrorCount));

            return fileData;

        } catch (Exception e) {
            parseErrorCount.incrementAndGet();
            log.warn("{}解析行数据异常,数据:{}", prefixLog, item, e);
            return null;
        }
    }

    /**
     * 获取字段值，处理空值
     *
     * @param split 分割后的数组
     * @param index 索引
     * @return 字段值
     */
    private String getFieldValue(String[] split, int index) {
        if (index < 0 || index >= split.length) {
            return null;
        }
        String value = split[index];
        return StringUtil.isBlank(value) ? null : value.trim();
    }

    /**
     * 安全解析BigDecimal
     *
     * @param value 待解析的字符串值
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的BigDecimal值
     */
    private BigDecimal parseBigDecimalSafely(String value, AtomicInteger parseErrorCount) {
        try {
            return StringUtil.isBlank(value) ? BigDecimal.ZERO : new BigDecimal(value);
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return BigDecimal.ZERO;
        }
    }

    /**
     * 安全解析Integer
     *
     * @param value 待解析的字符串值
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的Integer值
     */
    private Integer parseIntegerSafely(String value, AtomicInteger parseErrorCount) {
        try {
            return StringUtil.isBlank(value) ? null : Integer.valueOf(value);
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return null;
        }
    }

    /**
     * 数据验证方法
     *
     * @param data 待验证的数据
     * @return 是否有效
     */
    private boolean validateData(LbSOlLoanInfoDo data) {
        if (data == null) {
            return false;
        }

        // 验证必填字段
        if (StringUtil.isBlank(data.getLoanApplyId()) || StringUtil.isBlank(data.getTenantId())) {
            log.warn("必填字段为空,loanApplyId:{}, tenantId:{}", data.getLoanApplyId(), data.getTenantId());
            return false;
        }

        return true;
    }

    /**
     * 分批处理数据库插入
     *
     * @param doList 待插入的数据列表
     * @param prefixLog 日志前缀
     */
    private void processBatchInsert(List<LbSOlLoanInfoDo> doList, String prefixLog) {
        if (CollectionUtil.isEmpty(doList)) {
            return;
        }

        int totalCount = doList.size();
        int processedCount = 0;

        // 分批处理
        for (int i = 0; i < totalCount; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, totalCount);
            List<LbSOlLoanInfoDo> batchList = doList.subList(i, endIndex);

            try {
                int insertCount = lbSOlLoanInfoDao.insertList(batchList);
                processedCount += insertCount;
                log.info("{}批量插入第{}批数据,本批{}条,累计{}条", prefixLog, (i / BATCH_SIZE + 1), insertCount,
                    processedCount);

            } catch (Exception e) {
                log.error("{}批量插入第{}批数据异常", prefixLog, (i / BATCH_SIZE + 1), e);
                throw new HsjryBizException(EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode(),
                    EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription() + ": " + e.getMessage());
            }
        }

        log.info("{}分批插入完成,总计处理{}条数据", prefixLog, processedCount);
    }
}
