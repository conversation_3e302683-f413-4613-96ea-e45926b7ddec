package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 单一限额模板主键
 *
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
@Table(name = "lc_single_limit_template")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcSingleLimitTemplateKeyDo implements Serializable {

    private static final long serialVersionUID = 1673314101088157699L;
        /** 单一限额模板编号 */
    @Id
    @Column(name = "single_limit_template_id")
    private String singleLimitTemplateId;
        /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    }