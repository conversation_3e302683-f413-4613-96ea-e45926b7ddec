package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 实体信息主键
 *
 * <AUTHOR>
 * @date 2023-10-30 09:23:55
 */
@Table(name = "lc_entity_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcEntityInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1718921664835813376L;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    /** 实体编号 */
    @Id
    @Column(name = "entity_id")
    private String entityId;
}