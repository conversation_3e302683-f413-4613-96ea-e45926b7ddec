package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcEntityStatsRetryRecordDo;
import com.hsjry.core.limit.center.dal.dao.query.LcEntityStatsRetryRecordQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 实体统计重算记录数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcEntityStatsRetryRecordDao extends IBaseDao<LcEntityStatsRetryRecordDo> {
    /**
     * 分页查询实体统计重算记录
     *
     * @param lcEntityStatsRetryRecordQuery 条件
     * @return PageInfo<LcEntityStatsRetryRecordDo>
     */
    PageInfo<LcEntityStatsRetryRecordDo> selectPage(LcEntityStatsRetryRecordQuery lcEntityStatsRetryRecordQuery,
        PageParam pageParam);

    /**
     * 根据key查询实体统计重算记录
     *
     * @param statisticsId
     * @return
     */
    LcEntityStatsRetryRecordDo selectByKey(String statisticsId);

    /**
     * 根据key删除实体统计重算记录
     *
     * @param statisticsId
     * @return
     */
    int deleteByKey(String statisticsId);

    /**
     * 查询实体统计重算记录信息
     *
     * @param lcEntityStatsRetryRecordQuery 条件
     * @return List<LcEntityStatsRetryRecordDo>
     */
    List<LcEntityStatsRetryRecordDo> selectByExample(LcEntityStatsRetryRecordQuery lcEntityStatsRetryRecordQuery);

    /**
     * 新增实体统计重算记录信息
     *
     * @param lcEntityStatsRetryRecord 条件
     * @return int>
     */
    int insertBySelective(LcEntityStatsRetryRecordDo lcEntityStatsRetryRecord);

    /**
     * 修改实体统计重算记录信息
     *
     * @param lcEntityStatsRetryRecord
     * @return
     */
    int updateBySelective(LcEntityStatsRetryRecordDo lcEntityStatsRetryRecord);

    /**
     * 修改实体统计重算记录信息
     *
     * @param lcEntityStatsRetryRecord
     * @param lcEntityStatsRetryRecordQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcEntityStatsRetryRecordDo lcEntityStatsRetryRecord,
        LcEntityStatsRetryRecordQuery lcEntityStatsRetryRecordQuery);
}
