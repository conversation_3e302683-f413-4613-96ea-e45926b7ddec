package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTOlCorpProdLmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTOlCorpProdLmtInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlCorpProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlCorpProdLmtInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlCorpProdLmtInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTOlCorpProdLmtInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

import lombok.extern.slf4j.Slf4j;

/**
 * 网贷系统-中间表-对公产品层额度信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
@Slf4j
@Repository
public class LbTOlCorpProdLmtInfoDaoImpl extends AbstractBaseDaoImpl<LbTOlCorpProdLmtInfoDo, LbTOlCorpProdLmtInfoMapper>
    implements LbTOlCorpProdLmtInfoDao {
    /**
     * 分页查询
     *
     * @param lbTOlCorpProdLmtInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTOlCorpProdLmtInfoDo> selectPage(LbTOlCorpProdLmtInfoQuery lbTOlCorpProdLmtInfo,
        PageParam pageParam) {
        LbTOlCorpProdLmtInfoExample example = buildExample(lbTOlCorpProdLmtInfo);
        return PageHelper.<LbTOlCorpProdLmtInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询网贷系统-中间表-对公产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTOlCorpProdLmtInfoDo selectByKey(String custNo, String custLimitId) {
        LbTOlCorpProdLmtInfoKeyDo lbTOlCorpProdLmtInfoKeyDo = new LbTOlCorpProdLmtInfoKeyDo();
        lbTOlCorpProdLmtInfoKeyDo.setCustNo(custNo);
        lbTOlCorpProdLmtInfoKeyDo.setCustLimitId(custLimitId);
        lbTOlCorpProdLmtInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTOlCorpProdLmtInfoKeyDo);
    }

    /**
     * 根据key删除网贷系统-中间表-对公产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTOlCorpProdLmtInfoKeyDo lbTOlCorpProdLmtInfoKeyDo = new LbTOlCorpProdLmtInfoKeyDo();
        lbTOlCorpProdLmtInfoKeyDo.setCustNo(custNo);
        lbTOlCorpProdLmtInfoKeyDo.setCustLimitId(custLimitId);
        lbTOlCorpProdLmtInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTOlCorpProdLmtInfoKeyDo);
    }

    /**
     * 查询网贷系统-中间表-对公产品层额度信息信息
     *
     * @param lbTOlCorpProdLmtInfo 条件
     * @return List<LbTOlCorpProdLmtInfoDo>
     */
    @Override
    public List<LbTOlCorpProdLmtInfoDo> selectByExample(LbTOlCorpProdLmtInfoQuery lbTOlCorpProdLmtInfo) {
        return getMapper().selectByExample(buildExample(lbTOlCorpProdLmtInfo));
    }

    /**
     * 新增网贷系统-中间表-对公产品层额度信息信息
     *
     * @param lbTOlCorpProdLmtInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTOlCorpProdLmtInfoDo lbTOlCorpProdLmtInfo) {
        if (lbTOlCorpProdLmtInfo == null) {
            return -1;
        }

        lbTOlCorpProdLmtInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTOlCorpProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTOlCorpProdLmtInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTOlCorpProdLmtInfo);
    }

    /**
     * 修改网贷系统-中间表-对公产品层额度信息信息
     *
     * @param lbTOlCorpProdLmtInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTOlCorpProdLmtInfoDo lbTOlCorpProdLmtInfo) {
        if (lbTOlCorpProdLmtInfo == null) {
            return -1;
        }
        lbTOlCorpProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTOlCorpProdLmtInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTOlCorpProdLmtInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTOlCorpProdLmtInfoDo lbTOlCorpProdLmtInfo,
        LbTOlCorpProdLmtInfoQuery lbTOlCorpProdLmtInfoQuery) {
        lbTOlCorpProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTOlCorpProdLmtInfo, buildExample(lbTOlCorpProdLmtInfoQuery));
    }

    /**
     * 构建网贷系统-中间表-对公产品层额度信息Example信息
     *
     * @param lbTOlCorpProdLmtInfo
     * @return
     */
    public LbTOlCorpProdLmtInfoExample buildExample(LbTOlCorpProdLmtInfoQuery lbTOlCorpProdLmtInfo) {
        LbTOlCorpProdLmtInfoExample example = new LbTOlCorpProdLmtInfoExample();
        LbTOlCorpProdLmtInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTOlCorpProdLmtInfo != null) {
            //添加查询条件
            if (null != lbTOlCorpProdLmtInfo.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTOlCorpProdLmtInfo.getLowRiskAmount());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpProdLmtInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTOlCorpProdLmtInfo.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpProdLmtInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTOlCorpProdLmtInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpProdLmtInfo.getEntityId())) {
                criteria.andEntityIdEqualTo(lbTOlCorpProdLmtInfo.getEntityId());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpProdLmtInfo.getVirtualContractFlag())) {
                criteria.andVirtualContractFlagEqualTo(lbTOlCorpProdLmtInfo.getVirtualContractFlag());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpProdLmtInfo.getRelationId())) {
                criteria.andRelationIdEqualTo(lbTOlCorpProdLmtInfo.getRelationId());
            }
            if (null != lbTOlCorpProdLmtInfo.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTOlCorpProdLmtInfo.getRealOccupyLowRiskAmt());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpProdLmtInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTOlCorpProdLmtInfo.getCustNo());
            }
            if (null != lbTOlCorpProdLmtInfo.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTOlCorpProdLmtInfo.getRealOccupyAmount());
            }
            if (null != lbTOlCorpProdLmtInfo.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTOlCorpProdLmtInfo.getTotalAmount());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpProdLmtInfo.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTOlCorpProdLmtInfo.getLimitStatus());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpProdLmtInfo.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTOlCorpProdLmtInfo.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpProdLmtInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTOlCorpProdLmtInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpProdLmtInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTOlCorpProdLmtInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpProdLmtInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTOlCorpProdLmtInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpProdLmtInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTOlCorpProdLmtInfo.getCustTyp());
            }
        }
        buildExampleExt(lbTOlCorpProdLmtInfo, criteria);
        return example;
    }

    /**
     * 构建网贷系统-中间表-对公产品层额度信息ExampleExt方法
     *
     * @param lbTOlCorpProdLmtInfo
     * @return
     */
    public void buildExampleExt(LbTOlCorpProdLmtInfoQuery lbTOlCorpProdLmtInfo,
        LbTOlCorpProdLmtInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 将网贷对公客户额度同步数据插入到产品层额度信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_ol_corp_prod_lmt_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    @Override
    public int insertOlCorpProdLmtInfo(List<String> templateNodeIdList, List<String> custLimitIdList) {
        if (CollectionUtils.isEmpty(templateNodeIdList) || CollectionUtils.isEmpty(custLimitIdList)) {
            log.warn("模板节点ID列表或客户额度ID列表为空，跳过插入操作");
            return 0;
        }
        return getMapper().insertOlCorpProdLmtInfo(templateNodeIdList, custLimitIdList);
    }

    /**
     * 更新额度实例金额信息表
     * 根据网贷系统中间表数据更新LC_CUST_LIMIT_AMT_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    @Override
    public int updateCustLimitAmtInfo(List<String> custLimitIdList) {
        if (CollectionUtils.isEmpty(custLimitIdList)) {
            log.warn("客户额度ID列表为空，跳过更新额度实例金额信息操作");
            return 0;
        }
        return getMapper().updateCustLimitAmtInfo(custLimitIdList);
    }

}
