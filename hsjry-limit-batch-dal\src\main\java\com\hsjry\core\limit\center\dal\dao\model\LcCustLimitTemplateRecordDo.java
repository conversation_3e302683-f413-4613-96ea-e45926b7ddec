package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度体系模板记录Do
 *
 * <AUTHOR>
 * @date 2023-05-23 09:58:53
 */
@Table(name = "lc_cust_limit_template_record")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitTemplateRecordDo extends LcCustLimitTemplateRecordKeyDo implements Serializable {
    private static final long serialVersionUID = 1660948407298228224L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 额度体系模板编号 */
    @Column(name = "limit_template_id")
    private String limitTemplateId;
    /** 体系信息 */
    @Column(name = "template_context")
    private String templateContext;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 备注 */
    @Column(name = "remark")
    private String remark;
}
