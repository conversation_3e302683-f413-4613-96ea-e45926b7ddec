package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度串用流水主键
 *
 * <AUTHOR>
 * @date 2023-06-01 03:00:26
 */
@Table(name = "lc_cust_limit_share_serial")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcCustLimitShareSerialKeyDo implements Serializable {

    private static final long serialVersionUID = 1664104590133624832L;
        /** 操作流水编号 */
    @Id
    @Column(name = "clss_serial_no")
    private String clssSerialNo;
        /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    }