package com.hsjry.core.limit.center.dal.dao.intf;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitShareSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitShareSerialQuery;


import java.util.List;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度串用流水数据库操作接口
 * 
 * <AUTHOR>
 * @date 2023-06-01 02:19:57
 */
public interface LcCustLimitShareSerialDao extends IBaseDao<LcCustLimitShareSerialDo> {
    /**
     * 分页查询额度串用流水
     *
     * @param lcCustLimitShareSerialQuery 条件
     * @return PageInfo<LcCustLimitShareSerialDo>
     */
    PageInfo<LcCustLimitShareSerialDo> selectPage(LcCustLimitShareSerialQuery lcCustLimitShareSerialQuery,PageParam pageParam);
	  /**
     * 根据key查询额度串用流水
     *
     	 	 * @param clssSerialNo
	 	 	 	      * @return
     */
	LcCustLimitShareSerialDo selectByKey(String clssSerialNo);
    /**
     * 根据key删除额度串用流水
     *
               * @param clssSerialNo
                      * @return
     */
    int deleteByKey(String clssSerialNo);

    /**
     * 查询额度串用流水信息
     *
     * @param lcCustLimitShareSerialQuery 条件
     * @return List<LcCustLimitShareSerialDo>
     */
    List<LcCustLimitShareSerialDo> selectByExample(LcCustLimitShareSerialQuery lcCustLimitShareSerialQuery);

    /**
     * 新增额度串用流水信息
     *
     * @param lcCustLimitShareSerial 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitShareSerialDo lcCustLimitShareSerial);

    /**
     * 修改额度串用流水信息
     *
     * @param lcCustLimitShareSerial
     * @return
     */
    int updateBySelective(LcCustLimitShareSerialDo lcCustLimitShareSerial);
    /**
     * 修改额度串用流水信息
     *
     * @param lcCustLimitShareSerial
     * @param lcCustLimitShareSerialQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitShareSerialDo lcCustLimitShareSerial,
    LcCustLimitShareSerialQuery lcCustLimitShareSerialQuery);
}
