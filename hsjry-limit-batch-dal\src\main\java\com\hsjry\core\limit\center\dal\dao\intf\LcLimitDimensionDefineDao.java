package com.hsjry.core.limit.center.dal.dao.intf;

import com.hsjry.core.limit.center.dal.dao.model.LcLimitDimensionDefineDo;
import com.hsjry.core.limit.center.dal.dao.query.LcLimitDimensionDefineQuery;


import java.util.List;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 限额维度定义数据库操作接口
 * 
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
public interface LcLimitDimensionDefineDao extends IBaseDao<LcLimitDimensionDefineDo> {
    /**
     * 分页查询限额维度定义
     *
     * @param lcLimitDimensionDefineQuery 条件
     * @return PageInfo<LcLimitDimensionDefineDo>
     */
    PageInfo<LcLimitDimensionDefineDo> selectPage(LcLimitDimensionDefineQuery lcLimitDimensionDefineQuery,PageParam pageParam);
	  /**
     * 根据key查询限额维度定义
     *
     	 	 * @param dimensionCode
	 	 	 	      * @return
     */
	LcLimitDimensionDefineDo selectByKey(String dimensionCode);
    /**
     * 根据key删除限额维度定义
     *
               * @param dimensionCode
                      * @return
     */
    int deleteByKey(String dimensionCode);

    /**
     * 查询限额维度定义信息
     *
     * @param lcLimitDimensionDefineQuery 条件
     * @return List<LcLimitDimensionDefineDo>
     */
    List<LcLimitDimensionDefineDo> selectByExample(LcLimitDimensionDefineQuery lcLimitDimensionDefineQuery);

    /**
     * 新增限额维度定义信息
     *
     * @param lcLimitDimensionDefine 条件
     * @return int>
     */
    int insertBySelective(LcLimitDimensionDefineDo lcLimitDimensionDefine);

    /**
     * 修改限额维度定义信息
     *
     * @param lcLimitDimensionDefine
     * @return
     */
    int updateBySelective(LcLimitDimensionDefineDo lcLimitDimensionDefine);
    /**
     * 修改限额维度定义信息
     *
     * @param lcLimitDimensionDefine
     * @param lcLimitDimensionDefineQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcLimitDimensionDefineDo lcLimitDimensionDefine,
    LcLimitDimensionDefineQuery lcLimitDimensionDefineQuery);
}
