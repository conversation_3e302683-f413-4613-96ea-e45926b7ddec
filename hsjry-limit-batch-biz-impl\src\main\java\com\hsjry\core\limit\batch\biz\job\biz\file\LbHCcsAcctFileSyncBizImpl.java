/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.biz.file;

import java.util.Objects;

import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.sharding.JobCoreBusinessFactory;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;

import lombok.extern.slf4j.Slf4j;

/**
 * 信用卡-历史表-第一币种贷记帐户文件同步业务实现类
 * 负责调度和管理信用卡历史表文件的同步处理任务
 * 
 * 🎯 核心职责：
 * - 作为文件同步作业的业务协调器
 * - 获取并调用具体的分片实现类
 * - 统一的任务流程管理和异常处理
 * - 提供完整的任务执行日志
 * 
 * 🔄 执行流程：
 * 1. 接收作业初始化参数
 * 2. 通过工厂获取分片实现类
 * 3. 执行前置处理逻辑
 * 4. 委托分片任务进行具体处理
 * 
 * ⚡ 特性优势：
 * - 职责单一，专注业务协调
 * - 支持分片并行处理
 * - 统一的异常处理机制
 * - 完整的执行链路监控
 * 
 * 📊 适用场景：
 * - 信用卡历史数据文件批量同步
 * - 大规模信用卡账户数据导入
 * - 数据仓库ETL中的文件处理环节
 *
 * <AUTHOR>
 * @version V4.0.1
 * @since 4.0.1 2025/1/21
 */
@Slf4j
@Service("lbHCcsAcctFileSyncBizImpl")
public class LbHCcsAcctFileSyncBizImpl implements BaseOrdinaryBiz {

    /**
     * 构造函数
     * 记录Bean创建日志，便于系统初始化监控
     */
    public LbHCcsAcctFileSyncBizImpl() {
        log.info("LbHCcsAcctFileSyncBizImpl Bean 正在创建...");
    }

    /**
     * 获取任务交易码
     * 返回信用卡历史表文件同步的唯一标识
     *
     * @return 信用卡-历史表-第一币种贷记帐户文件同步的任务交易码
     */
    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.H_CCS_ACCT;
    }

    /**
     * 执行信用卡-历史表-第一币种贷记帐户文件同步的基础任务
     * 此方法是任务的入口点，负责协调整个文件同步流程
     * 
     * 🔧 处理逻辑：
     * 1. 解析任务初始化参数
     * 2. 构建统一的日志前缀
     * 3. 通过工厂获取分片实现类
     * 4. 执行前置处理逻辑
     * 5. 委托分片任务进行数据处理
     *
     * @param jobInitDto 任务初始化参数，包含业务日期、批次流水号等信息
     * @throws RuntimeException 当任务执行失败时抛出
     */
    @Override
    public void execBaseJob(JobInitDto jobInitDto) {
        // 解析任务参数
        Integer businessDate = jobInitDto.getBusinessDate();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();

        // 构建统一的日志前缀，便于链路跟踪
        String prefixLog = String.format("营运日期:[%s],批量流水号:[%s],任务交易码:[%s-%s]", businessDate,
            batchSerialNo, jobTradeCode, jobTradeDesc);

        log.info("{}========开始执行信用卡-历史表-第一币种贷记帐户文件同步任务========", prefixLog);

        try {
            // 通过工厂获取具体的核心业务实现
            JobCoreBusiness<?> jobCoreBusiness = JobCoreBusinessFactory.getJobCoreBusiness(jobTradeCode);
            if (Objects.isNull(jobCoreBusiness)) {
                String errorMsg = String.format("未找到任务交易码[%s]对应的核心业务实现类", jobTradeCode);
                log.error("{}{}", prefixLog, errorMsg);
                throw new RuntimeException(errorMsg);
            }

            log.info("{}成功获取核心业务实现类: {}", prefixLog, jobCoreBusiness.getClass().getSimpleName());

            // 执行前置处理
            log.info("{}开始执行前置处理", prefixLog);
            jobCoreBusiness.preHandle(jobInitDto);
            log.info("{}前置处理完成", prefixLog);

            log.info("{}信用卡-历史表-第一币种贷记帐户文件同步任务执行完成,具体分片处理将由分片任务完成", prefixLog);

        } catch (Exception e) {
            log.error("{}信用卡-历史表-第一币种贷记帐户文件同步任务执行失败,异常信息:[{}]", prefixLog, e.getMessage(), e);
            throw new RuntimeException(String.format("信用卡-历史表-第一币种贷记帐户文件同步任务执行失败: %s", e.getMessage()), e);
        } finally {
            try {
                // 执行后置处理
                JobCoreBusiness<?> jobCoreBusiness = JobCoreBusinessFactory.getJobCoreBusiness(jobTradeCode);
                if (Objects.nonNull(jobCoreBusiness)) {
                    log.info("{}开始执行后置处理", prefixLog);
                    jobCoreBusiness.afterHandle(jobInitDto);
                    log.info("{}后置处理完成", prefixLog);
                }
            } catch (Exception e) {
                log.error("{}后置处理执行失败", prefixLog, e);
                // 后置处理失败不影响主流程
            }
        }

        log.info("{}========信用卡-历史表-第一币种贷记帐户文件同步任务执行结束========", prefixLog);
    }
} 