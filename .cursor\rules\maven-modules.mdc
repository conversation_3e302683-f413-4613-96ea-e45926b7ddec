# Maven模块开发规范

## 模块依赖层次

项目采用分层架构，依赖关系如下（从上到下）：

```
deploy/starter (部署层)
    ↓
controller (控制层)
    ↓
facade (外观层)
    ↓
biz-impl (业务实现层)
    ↓
biz (业务接口层)
    ↓
core-impl (核心实现层)
    ↓
core (核心接口层)
    ↓
dal-impl (数据访问实现层)
    ↓
dal (数据访问接口层)
    ↓
common (公共组件层)
```

## 模块职责

### 部署模块 (deploy/starter)
- [hsjry-limit-batch-deploy](mdc:hsjry-limit-batch-deploy) - 主要部署模块
- [hsjry-limit-batch-starter](mdc:hsjry-limit-batch-starter) - 独立启动器
- 包含启动类和配置文件
- 依赖所有其他业务模块

### 控制层 (controller)
- [hsjry-limit-batch-controller](mdc:hsjry-limit-batch-controller)
- 处理RPC调用和HTTP请求
- 依赖facade和biz-impl模块

### 外观层 (facade)
- [hsjry-limit-batch-facade](mdc:hsjry-limit-batch-facade)
- 定义对外接口和DTO
- 仅依赖common模块

### 业务层 (biz/biz-impl)
- [hsjry-limit-batch-biz](mdc:hsjry-limit-batch-biz) - 业务接口
- [hsjry-limit-batch-biz-impl](mdc:hsjry-limit-batch-biz-impl) - 业务实现
- biz-impl依赖biz、core-impl、dal、common
- biz仅依赖core、common

### 核心层 (core/core-impl)
- [hsjry-limit-batch-core](mdc:hsjry-limit-batch-core) - 核心接口
- [hsjry-limit-batch-core-impl](mdc:hsjry-limit-batch-core-impl) - 核心实现
- core-impl依赖core、common
- core仅依赖common

### 数据层 (dal/dal-impl)
- [hsjry-limit-batch-dal](mdc:hsjry-limit-batch-dal) - DAO接口和模型
- [hsjry-limit-batch-dal-impl](mdc:hsjry-limit-batch-dal-impl) - MyBatis实现
- dal-impl依赖dal、common
- dal仅依赖common

### 公共层 (common)
- [hsjry-limit-batch-common](mdc:hsjry-limit-batch-common)
- 包含常量、枚举、工具类
- 不依赖其他业务模块

### 测试模块 (test)
- [hsjry-limit-batch-test](mdc:hsjry-limit-batch-test)
- 包含集成测试和单元测试
- 依赖所有需要测试的模块

## POM文件规范

### 父POM配置
[主POM](mdc:pom.xml) 负责：
- 版本统一管理（使用revision属性）
- 依赖版本管理（dependencyManagement）
- 插件版本管理（pluginManagement）
- 模块聚合（modules）

### 子模块POM规范
```xml
<!-- 继承父模块 -->
<parent>
    <groupId>com.hsjry.core.limit.batch</groupId>
    <artifactId>hsjry-limit-batch</artifactId>
    <version>${revision}</version>
</parent>

<!-- 当前模块信息 -->
<artifactId>hsjry-limit-batch-biz-impl</artifactId>
<packaging>jar</packaging>
<name>hsjry-limit-batch-biz-impl</name>

<!-- 依赖管理：只引入直接依赖 -->
<dependencies>
    <!-- 业务接口层 -->
    <dependency>
        <groupId>com.hsjry.core.limit.batch</groupId>
        <artifactId>hsjry-limit-batch-biz</artifactId>
    </dependency>
    
    <!-- 数据访问层 -->
    <dependency>
        <groupId>com.hsjry.core.limit.batch</groupId>
        <artifactId>hsjry-limit-batch-dal</artifactId>
    </dependency>
</dependencies>
```

## 版本管理

### 版本号策略
- 使用 `${revision}` 统一管理版本
- 格式：`主版本.次版本.修订版本-SNAPSHOT`
- 示例：`4.1.0-SNAPSHOT`

### 依赖版本管理
- 所有依赖版本在父POM的 `<dependencyManagement>` 中管理
- 子模块不指定version，继承父模块管理的版本
- 第三方依赖版本使用属性管理

## 构建规范

### 编译配置
```xml
<properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
</properties>
```

### 插件配置
- `maven-compiler-plugin`: Java编译
- `spring-boot-maven-plugin`: Spring Boot打包（仅deploy模块）
- `maven-surefire-plugin`: 单元测试
- `maven-failsafe-plugin`: 集成测试

## 模块开发最佳实践

### 1. 依赖原则
- **向下依赖**: 上层模块可以依赖下层模块
- **禁止向上依赖**: 下层模块不能依赖上层模块
- **同层依赖**: 需要明确定义接口层和实现层的关系

### 2. 包名规范
- 基础包名：`com.hsjry.core.limit.batch`
- 各模块在基础包名下添加层级标识：
  - `com.hsjry.core.limit.batch.controller`
  - `com.hsjry.core.limit.batch.biz`
  - `com.hsjry.core.limit.batch.dal`

### 3. 资源文件管理
- 配置文件放在对应模块的 `src/main/resources`
- MyBatis映射文件：`dal-impl/src/main/resources/com/hsjry/core/limit/batch/dal/dao/sqlmap/`
- 启动配置文件：`deploy/src/main/resources/`

### 4. 测试代码组织
- 单元测试：各模块的 `src/test/java`
- 集成测试：统一放在 `test` 模块
- 测试资源：`test/src/test/resources`
