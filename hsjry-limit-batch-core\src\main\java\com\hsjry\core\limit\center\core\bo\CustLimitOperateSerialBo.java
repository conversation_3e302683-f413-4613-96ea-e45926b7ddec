package com.hsjry.core.limit.center.core.bo;

import java.util.List;

import lombok.Builder;
import lombok.Data;

/**
 * 额度操作流水Bo
 * 该Bo和LcCustLimitOperateSerialDo一一对应
 *
 * <AUTHOR>
 * @version V4.0
 * @see com.hsjry.core.limit.center.dal.dao.model.LcCustLimitOperateSerialDo
 * @since 4.0.1 2022/12/20 11:18
 */
@Data
@Builder
public class CustLimitOperateSerialBo {
    /** 租户号 */
    private String tenantId;
    /** 操作流水编号 */
    private String closSerialNo;
    /** 操作金额编号 */
    private String operateAmountId;
    /** 操作路径 */
    private String operatePath;
    /** 合同额度汇率重算标记 */
    private String contractRecalFlag;
    /** 汇率版本 */
    private Integer exchangeRateVersion;
    /** 实体编号 */
    private String entityId;
    /** 备注 */
    private String remark;
    /** 失败原因 */
    private String failReason;
    /** 操作方向 */
    private String operateDirection;
    /** 额度编号 */
    private String custLimitId;
    /** 前置业务关联流水 */
    private String lastInboundSerialNo;
    /** 关联编号 */
    private String relationId;
    /** 操作低风险币种 */
    private String operateLowRiskCurrency;
    /** 操作低风险金额编号 */
    private String operateLowRiskAmtId;
    /** 操作低风险金额 */
    private java.math.BigDecimal operateLowRiskAmount;
    /** 操作金额币种 */
    private String operateAmountCurrency;
    /** 全局流水号 */
    private String globalSerialNo;
    /** 操作金额 */
    private java.math.BigDecimal operateAmount;
    /** 操作类型;EnumCustLimitOperateType */
    private String operateType;
    /** 所属组织编号 */
    private String ownOrganId;
    /** 操作人编号 */
    private String operatorId;
    /** 状态 */
    private String status;
    /** 更新时间 */
    private java.util.Date updateTime;
    /** 创建时间 */
    private java.util.Date createTime;
    /** 前置业务时间 */
    private java.util.Date inboundSerialDatetime;
    /** 前置业务流水 */
    private String inboundSerialNo;
    /** 业务时间 */
    private java.util.Date bizDatetime;
    /** 渠道号 */
    private String channelNo;
    /** 业务流水号 */
    private String serialNo;

    /** 客户编号 */
    private String custNo;
    /** 客户编号列表 */
    private List<String> custNoList;
}
