package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度实例关联主键
 *
 * <AUTHOR>
 * @date 2022-12-13 03:09:49
 */
@Table(name = "lc_cust_limit_relation")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcCustLimitRelationKeyDo implements Serializable {

    private static final long serialVersionUID = 1602501015033282563L;
    /** 额度关系编号 */
    @Id
    @Column(name = "limit_relation_id")
    private String limitRelationId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}