/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.query.EntityOperateSerialBatchQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityOperateSerialDo;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/14 10:20
 */
public interface EntityOperateSerialBatchDao {

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcEntityOperateSerialDo> selectShardList(EntityOperateSerialBatchQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcEntityOperateSerialDo selectFirstOne(EntityOperateSerialBatchQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(EntityOperateSerialBatchQuery query);

}
