package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTOlIndvProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTOlIndvProdLmtInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-中间表-个人产品层额度信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
public interface LbTOlIndvProdLmtInfoDao extends IBaseDao<LbTOlIndvProdLmtInfoDo> {
    /**
     * 分页查询网贷系统-中间表-个人产品层额度信息
     *
     * @param lbTOlIndvProdLmtInfoQuery 条件
     * @return PageInfo<LbTOlIndvProdLmtInfoDo>
     */
    PageInfo<LbTOlIndvProdLmtInfoDo> selectPage(LbTOlIndvProdLmtInfoQuery lbTOlIndvProdLmtInfoQuery,
        PageParam pageParam);

    /**
     * 根据key查询网贷系统-中间表-个人产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTOlIndvProdLmtInfoDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除网贷系统-中间表-个人产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询网贷系统-中间表-个人产品层额度信息信息
     *
     * @param lbTOlIndvProdLmtInfoQuery 条件
     * @return List<LbTOlIndvProdLmtInfoDo>
     */
    List<LbTOlIndvProdLmtInfoDo> selectByExample(LbTOlIndvProdLmtInfoQuery lbTOlIndvProdLmtInfoQuery);

    /**
     * 新增网贷系统-中间表-个人产品层额度信息信息
     *
     * @param lbTOlIndvProdLmtInfo 条件
     * @return int>
     */
    int insertBySelective(LbTOlIndvProdLmtInfoDo lbTOlIndvProdLmtInfo);

    /**
     * 修改网贷系统-中间表-个人产品层额度信息信息
     *
     * @param lbTOlIndvProdLmtInfo
     * @return
     */
    int updateBySelective(LbTOlIndvProdLmtInfoDo lbTOlIndvProdLmtInfo);

    /**
     * 修改网贷系统-中间表-个人产品层额度信息信息
     *
     * @param lbTOlIndvProdLmtInfo
     * @param lbTOlIndvProdLmtInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTOlIndvProdLmtInfoDo lbTOlIndvProdLmtInfo,
        LbTOlIndvProdLmtInfoQuery lbTOlIndvProdLmtInfoQuery);

    /**
     * 将网贷个人客户额度同步数据插入到产品层额度信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_ol_indv_prod_lmt_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    int insertOlIndvProdLmtInfo(List<String> templateNodeIdList, List<String> custLimitIdList);

    /**
     * 更新额度实例金额信息表
     * 根据网贷系统中间表数据更新LC_CUST_LIMIT_AMT_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    int updateCustLimitAmtInfo(List<String> custLimitIdList);
}
