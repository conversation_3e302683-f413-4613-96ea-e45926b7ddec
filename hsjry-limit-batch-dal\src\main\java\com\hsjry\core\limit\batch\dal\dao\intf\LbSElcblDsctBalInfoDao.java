package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblDsctBalInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSElcblDsctBalInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-落地表-贴现余额信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
public interface LbSElcblDsctBalInfoDao extends IBaseDao<LbSElcblDsctBalInfoDo> {
    /**
     * 分页查询电票系统-落地表-贴现余额信息
     *
     * @param lbSElcblDsctBalInfoQuery 条件
     * @return PageInfo<LbSElcblDsctBalInfoDo>
     */
    PageInfo<LbSElcblDsctBalInfoDo> selectPage(LbSElcblDsctBalInfoQuery lbSElcblDsctBalInfoQuery, PageParam pageParam);

    /**
     * 根据key查询电票系统-落地表-贴现余额信息
     *
     * @param dicCno
     * @param billNo
     * @param userId
     * @return
     */
    LbSElcblDsctBalInfoDo selectByKey(String dicCno, String billNo, String userId);

    /**
     * 根据key删除电票系统-落地表-贴现余额信息
     *
     * @param dicCno
     * @param billNo
     * @param userId
     * @return
     */
    int deleteByKey(String dicCno, String billNo, String userId);

    /**
     * 查询电票系统-落地表-贴现余额信息信息
     *
     * @param lbSElcblDsctBalInfoQuery 条件
     * @return List<LbSElcblDsctBalInfoDo>
     */
    List<LbSElcblDsctBalInfoDo> selectByExample(LbSElcblDsctBalInfoQuery lbSElcblDsctBalInfoQuery);

    /**
     * 新增电票系统-落地表-贴现余额信息信息
     *
     * @param lbSElcblDsctBalInfo 条件
     * @return int>
     */
    int insertBySelective(LbSElcblDsctBalInfoDo lbSElcblDsctBalInfo);

    /**
     * 修改电票系统-落地表-贴现余额信息信息
     *
     * @param lbSElcblDsctBalInfo
     * @return
     */
    int updateBySelective(LbSElcblDsctBalInfoDo lbSElcblDsctBalInfo);

    /**
     * 修改电票系统-落地表-贴现余额信息信息
     *
     * @param lbSElcblDsctBalInfo
     * @param lbSElcblDsctBalInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSElcblDsctBalInfoDo lbSElcblDsctBalInfo,
        LbSElcblDsctBalInfoQuery lbSElcblDsctBalInfoQuery);

    /**
     * 批量插入电票系统-贴现余额信息-落地表
     *
     * @param lbSElcblDsctBalInfoList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbSElcblDsctBalInfoDo> lbSElcblDsctBalInfoList);

    /**
     * 清空电票系统-贴现余额信息-落地表所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 获取第一个对象，用于分片查询
     * 根据复合主键排序，获取指定偏移量的第一条记录
     *
     * @param query 查询条件
     * @return 第一条记录，如果没有则返回null
     */
    LbSElcblDsctBalInfoDo selectFirstOne(LbSElcblDsctBalInfoQuery query);

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据复合主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含主键范围
     * @return 当前分片的数据量
     */
    Integer selectCountByCurrentGroup(LbSElcblDsctBalInfoQuery query);
}
