package com.hsjry.core.limit.batch.biz.job.job.mid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 额度中心-中间表-电票系统对公客户额度信息处理任务
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/16
 */
@Slf4j
@Service("lbTElcblCorpLmtInfoProcessJob")
public class LbTElcblCorpLmtInfoProcessJob extends AbstractBaseBatchJob {
    public LbTElcblCorpLmtInfoProcessJob() {
        log.info("LbTElcblCorpLmtInfoProcessJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbTElcblCorpLmtInfoProcessBizImpl")
    private BaseOrdinaryBiz baseOrdinaryBiz;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return baseOrdinaryBiz;
    }
} 