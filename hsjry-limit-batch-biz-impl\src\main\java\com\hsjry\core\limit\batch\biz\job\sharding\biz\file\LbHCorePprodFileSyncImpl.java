/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.core.limit.batch.biz.convert.file.LbHCorePprodConverter;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.biz.entity.LbHCorePprodData;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.AbstractFileBaseShardingPrepareBizImpl;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.constants.CharsetConstants;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.dal.dao.intf.LbHCorePprodDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCorePprodDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.sequence.SequenceTool;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
/**
 * 核心产品定义文件同步实现类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/11/7 10:27
 */
@Slf4j
@Service("lbHCorePprodFileSyncImpl")
@RequiredArgsConstructor
public class LbHCorePprodFileSyncImpl extends AbstractFileBaseShardingPrepareBizImpl<LbHCorePprodData> {

    /** 法人代码列数 */
    private static final int FAREDM_NUM = 1;
    /** 产品编号列数 */
    private static final int CHAPBH_NUM = 2;
    /** 产品描述列数 */
    private static final int CHAPMX_NUM = 3;
    /** 模块列数 */
    private static final int MODULE_NUM = 4;
    /** 维护日期列数 */
    private static final int WEIHRQ_NUM = 5;
    /** 维护时间列数 */
    private static final int WEIHSJ_NUM = 6;
    /** 维护工号列数 */
    private static final int WEIHGY_NUM = 7;
    /** 维护机构列数 */
    private static final int WEIHJG_NUM = 8;
    /** 行ID列数 */
    private static final int ROWIDD_NUM = 9;
    /** 时间戳列数 */
    private static final int SHJNCH_NUM = 10;
    /** 记录状态列数 */
    private static final int JILUZT_NUM = 11;
    /** 最小字段数量 */
    private static final int MIN_FIELD_COUNT = 11;
    /** 分隔符 */
    private static final String FIELD_SEPARATOR = "\\|\\+\\|";
    /** 批处理大小 - 性能优化：从1000调整为10000，提升批量插入性能 */
    private static final int BATCH_SIZE = 10000;
    private final String SEQUENCE_NO = "SEQUENCE_NO";

    private final LbHCorePprodDao lbHCorePprodDao;
    @Value("${project.core.pprod.filename:CBS_PPROD_[DATE].dat}")
    private String fileName;
    @Value("${limit.batch.remoteFilePath:/app/bftfiles/cfs/recv/ebbs/}")
    private String remoteFilePathDefine;

    @Override
    public ShardingResult<LbHCorePprodData> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始文件数据分片查询[{}]", GsonUtil.objToStrForLog(jobShared));

        ShardingResult<LbHCorePprodData> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        try {
            // 读取文件分片数据
            FileLineData fileLineData = GsonUtil.json2Obj(jobShared.getExtParam(), FileLineData.class);
            log.info(prefixLog + "开始读取文件数据分片[{}]", GsonUtil.objToStrForLog(fileLineData));
            List<String> originData = FileShardingUtils.readFileSharedData(jobShared, skipFirst(), CharsetConstants.GBK);

            // 使用并行流处理数据，提升性能
            List<LbHCorePprodData> fileDataList = processOriginDataParallel(originData, prefixLog);

            log.info(prefixLog + "读取文件数据分片总量[{}]", fileDataList.size());
            shardingResult.setShardingResultList(fileDataList);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        jobShared.setBatchSerialNo(inParam.getString(SEQUENCE_NO));
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbHCorePprodData> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
        List<LbHCorePprodData> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "核心产品数据文件处理:文件分片数据为空,执行中断。");
            return;
        }

        log.info(prefixLog + "核心产品数据文件处理:开始执行分片数据操作,数据量:[{}]", dataList.size());
        // 设置数据日期
        String dataDateStr = String.valueOf(businessDate);
        dataList.forEach(data -> data.setDataDate(dataDateStr));
        // 检查是否是第一个分片，如果是则清空表
        if (sliceBatchSerialDo.getBatchNum() == 1) {
            log.info(prefixLog + "第一个分片,清空目标表 lb_h_core_pprod");
            lbHCorePprodDao.deleteByDataDate(dataDateStr);
        }

        // 确保分片流水对象的状态字段不为null
        if (sliceBatchSerialDo.getSharedStatus() == null) {
            log.debug(prefixLog + "分片流水状态为null,设置为处理中状态");
            sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
        }


        // 使用并行流进行数据转换和验证
        List<LbHCorePprodDo> insertList = dataList.parallelStream().map(LbHCorePprodConverter::data2Do)//
            .filter(this::validateData).collect(Collectors.toCollection(ArrayList::new));

        if (CollectionUtil.isNotEmpty(insertList)) {
            // 批量插入数据
            processBatchInsert(insertList, prefixLog);
            log.info(prefixLog + "插入[{}]条核心产品数据", insertList.size());
        }

        // 更新分片流水成功
        normalUpdateSliceSerial(dataList.size(), sliceBatchSerialDo);
        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataList.size());
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.H_CORE_PPROD_FILE_SYNC;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        JSONObject param = JSON.parseObject(jobInitDto.getInPara());
        //设置流水ID,解决多次从redis获取唯一值的性能问题
        param.put(SEQUENCE_NO, SequenceTool.nextId());
        //更新jobInitDto的inpara参数
        jobInitDto.setInPara(param.toJSONString());

        // 确保fixNum被正确设置
        if (jobInitDto.getFixNum() == null) {
            log.warn(prefixLog + "fixNum为null,设置默认值50000");
            jobInitDto.setFixNum(50000);  // 性能优化：从1000调整为50000
        }

        List<JobShared> sharedList = Lists.newArrayList();
        log.info(prefixLog + "[{}]文件处理", jobTradeDesc);
        String localFilePath = FileShardingUtils.getLocalFilePath(jobInitDto.getBusinessDate(),
            remoteFilePathDefine + FileShardingUtils.ACCT_DATE_CODE_MARK + File.separator);
        Integer acctDate = jobInitDto.getBusinessDate();
        String localFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK, String.valueOf(acctDate));
        String actualLocalFilePath = localFilePath.replace(FileShardingUtils.ACCT_DATE_CODE_MARK, String.valueOf(acctDate));

        log.info(prefixLog + "转换后的本地文件路径: [{}]", actualLocalFilePath);

        try {
            String fileAttr = FIELD_SEPARATOR;
            String filePath = actualLocalFilePath + localFileName;
            log.info("实际查找的文件路径: [{}]", filePath);

            // 检查目录是否存在
            File directory = new File(actualLocalFilePath);
            if (!directory.exists()) {
                log.info(prefixLog + "目录[{}]不存在,尝试创建", actualLocalFilePath);
                directory.mkdirs();
            } else {
                log.info(prefixLog + "目录[{}]已存在", actualLocalFilePath);
                // 列出目录中的文件
                File[] files = directory.listFiles();
                if (files != null && files.length > 0) {
                    log.info(prefixLog + "目录[{}]中的文件列表:", actualLocalFilePath);
                    for (File f : files) {
                        log.info(prefixLog + " - {}", f.getName());
                    }
                } else {
                    log.info(prefixLog + "目录[{}]为空", actualLocalFilePath);
                }
            }

            File localFile = new File(filePath);
            log.info(prefixLog + "检查文件[{}]是否存在: {}", filePath, localFile.exists());

            //判断本地文件是否存在，不存在则直接报错
            if(!localFile.exists()){
                log.error(prefixLog + "本地文件[{}]不存在,请确认文件路径是否正确", filePath);
                String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
                String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
                throw new HsjryBizException(errorCode, errorMsg);
            }

            // 文件存在，直接进行分片处理
            log.info(prefixLog + "[{}]开始[{}]文件数据分片处理", jobTradeDesc, filePath);
            log.info(prefixLog + "分片参数: fixNum=[{}], fileAttr=[{}], skipFirst=[{}]",
                jobInitDto.getFixNum(), fileAttr, skipFirst());

            List<JobShared> jobShareds = FileShardingUtils.getFileSharedData(jobInitDto, localFile, fileAttr,
                skipFirst());
            log.info(prefixLog + "[{}]结束[{}]文件数据分片当前分片数[{}]", jobTradeDesc, filePath,
                jobShareds.size());
            sharedList.addAll(jobShareds);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }
        return sharedList;
    }

    private boolean skipFirst() {
        return true; // 跳过标题行
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 并行处理原始数据
     * 使用并行流提升数据处理性能，同时保证线程安全
     *
     * @param originData 原始数据列表
     * @param prefixLog 日志前缀
     * @return 处理后的数据列表
     */
    private List<LbHCorePprodData> processOriginDataParallel(List<String> originData, String prefixLog) {
        if (CollectionUtil.isEmpty(originData)) {
            return new ArrayList<>();
        }

        // 使用线程安全的计数器
        AtomicInteger invalidCount = new AtomicInteger(0);
        AtomicInteger parseErrorCount = new AtomicInteger(0);

        List<LbHCorePprodData> fileDataList = originData.parallelStream().filter(Objects::nonNull).filter(
            item -> !StringUtil.isBlank(item)).map(
            item -> parseLineData(item, prefixLog, invalidCount, parseErrorCount)).filter(Objects::nonNull).collect(
            Collectors.toCollection(ArrayList::new));

        // 记录统计信息
        if (invalidCount.get() > 0) {
            log.warn(prefixLog + "数据格式不正确记录数量:[{}]", invalidCount.get());
        }
        if (parseErrorCount.get() > 0) {
            log.warn(prefixLog + "数字字段解析失败记录数量:[{}]", parseErrorCount.get());
        }

        return fileDataList;
    }

    /**
     * 解析单行数据
     * 提取单行数据解析逻辑，提高代码复用性和可维护性
     *
     * @param item 单行数据
     * @param prefixLog 日志前缀
     * @param invalidCount 无效数据计数器
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的数据对象
     */
    private LbHCorePprodData parseLineData(String item, String prefixLog, AtomicInteger invalidCount,
        AtomicInteger parseErrorCount) {
        String[] split = item.split(FIELD_SEPARATOR);
        if (split.length < MIN_FIELD_COUNT) {
            invalidCount.incrementAndGet();
            return null;
        }

        LbHCorePprodData fileData = new LbHCorePprodData();
        // 设置字符串字段
        fileData.setFaredm(split[FAREDM_NUM - 1]);
        fileData.setChapbh(split[CHAPBH_NUM - 1]);
        fileData.setChapmx(split[CHAPMX_NUM - 1]);
        fileData.setModule(split[MODULE_NUM - 1]);
        fileData.setWeihrq(split[WEIHRQ_NUM - 1]);
        fileData.setWeihgy(split[WEIHGY_NUM - 1]);
        fileData.setWeihjg(split[WEIHJG_NUM - 1]);
        fileData.setRowidd(split[ROWIDD_NUM - 1]);
        fileData.setJiluzt(split[JILUZT_NUM - 1]);

        // 安全解析数字字段
        fileData.setWeihsj(parseBigDecimalSafely(split[WEIHSJ_NUM - 1], parseErrorCount));
        fileData.setShjnch(parseBigDecimalSafely(split[SHJNCH_NUM - 1], parseErrorCount));

        return fileData;
    }

    /**
     * 安全解析BigDecimal
     * 统一的数字字段解析逻辑，避免代码重复
     *
     * @param value 待解析的字符串值
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的BigDecimal值
     */
    private BigDecimal parseBigDecimalSafely(String value, AtomicInteger parseErrorCount) {
        try {
            return StringUtil.isBlank(value) ? BigDecimal.ZERO :new BigDecimal(value);
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return BigDecimal.ZERO;
        }
    }

    /**
     * 增强的数据验证方法
     * 使用Objects工具类和优化的验证逻辑
     *
     * @param data 待验证的数据
     * @return 是否有效
     */
    private boolean validateData(LbHCorePprodDo data) {
        if (Objects.isNull(data)) {
            return false;
        }

        if (StringUtil.isBlank(data.getChapbh())) {
            log.warn("产品编号为空,数据无效");
            return false;
        }

        if (StringUtil.isBlank(data.getChapmx())) {
            log.warn("产品描述为空,产品编号:[{}]", data.getChapbh());
            return false;
        }

        // 额外的业务验证逻辑
        if (Objects.nonNull(data.getFaredm()) && data.getFaredm().length() > 20) {
            log.warn("法人代码长度超限,产品编号:[{}]", data.getChapbh());
            return false;
        }

        return true;
    }

    /**
     * 批量插入处理
     * 分批处理大量数据，避免内存溢出和数据库连接超时
     *
     * @param insertList 待插入数据列表
     * @param prefixLog 日志前缀
     */
    private void processBatchInsert(List<LbHCorePprodDo> insertList, String prefixLog) {
        if (CollectionUtil.isEmpty(insertList)) {
            return;
        }

        int totalSize = insertList.size();
        int batchCount = (totalSize + BATCH_SIZE - 1) / BATCH_SIZE;

        log.info(prefixLog + "开始批量插入数据,总数据量:[{}],分批数量:[{}],每批大小:[{}]", totalSize, batchCount,
            BATCH_SIZE);

        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * BATCH_SIZE;
            int toIndex = Math.min(fromIndex + BATCH_SIZE, totalSize);
            List<LbHCorePprodDo> batchList = insertList.subList(fromIndex, toIndex);

            try {
                lbHCorePprodDao.insertList(batchList);
                log.debug(prefixLog + "批次[{}]插入完成,数据量:[{}]", i + 1, batchList.size());
            } catch (Exception e) {
                log.error(prefixLog + "批次[{}]插入失败,数据量:[{}]", i + 1, batchList.size(), e);
                throw e;
            }
        }
    }

}
