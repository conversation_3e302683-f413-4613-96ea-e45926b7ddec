package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 电票系统-落地表-同业客户额度同步Do
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Table(name = "lb_s_elcbl_ibnk_lmt_synz")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbSElcblIbnkLmtSynzDo extends LbSElcblIbnkLmtSynzKeyDo implements Serializable {
    private static final long serialVersionUID = 1945747816769060868L;
    /** 同业客户证件类型 */
    @Column(name = "ibnk_user_certificate_kind")
    private String ibnkUserCertificateKind;
    /** 同业客户证件号码 */
    @Column(name = "ibnk_user_certificate_no")
    private String ibnkUserCertificateNo;
    /** 总额度 */
    @Column(name = "total_amount")
    private java.math.BigDecimal totalAmount;
    /** 可用额度 */
    @Column(name = "available_amount")
    private java.math.BigDecimal availableAmount;
    /** 已占用额度 */
    @Column(name = "use_occupy_amount")
    private java.math.BigDecimal useOccupyAmount;
    /** 核心机构号 */
    @Column(name = "core_inst_no")
    private String coreInstNo;
    /** 创建时间 */
    @Column(name = "create_time")
    private String createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private String updateTime;
}
