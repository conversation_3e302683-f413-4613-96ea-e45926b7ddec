package com.hsjry.core.limit.center.core.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.center.core.bo.CustLimitOperateSerialBo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitOperateSerialDo;

@Mapper(componentModel = "spring")
public interface LcCustLimitOperateSerialDoCnvs {
    LcCustLimitOperateSerialDoCnvs INSTANCE = Mappers.getMapper(LcCustLimitOperateSerialDoCnvs.class);

    /** 将[额度操作流水Do]转换为[额度操作流水Bo] */
    CustLimitOperateSerialBo cnvsDoToBo(LcCustLimitOperateSerialDo dataObject);

    /** 将[额度操作流水Do列表]转换为[额度操作流水Bo列表] */
    List<CustLimitOperateSerialBo> cnvsDoListToBoList(List<LcCustLimitOperateSerialDo> doList);

    /** 将[额度操作流水Bo]转换为[额度操作流水Do] */
    LcCustLimitOperateSerialDo cnvsBoToDo(CustLimitOperateSerialBo bo);

    /** 将[额度操作流水Bo列表]转换为[额度操作流水Do列表] */
    List<LcCustLimitOperateSerialDo> cnvsBoListToDoList(List<CustLimitOperateSerialBo> boList);
}
