package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 电票系统-落地表-贴现余额信息Do
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Table(name = "lb_s_elcbl_dsct_bal_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbSElcblDsctBalInfoDo extends LbSElcblDsctBalInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1945747816769060864L;
    /** 法人行机构编号 */
    @Column(name = "org_no")
    private String orgNo;
    /** 子票区间起始 */
    @Column(name = "bill_range_start")
    private String billRangeStart;
    /** 子票区间截止 */
    @Column(name = "bill_range_end")
    private String billRangeEnd;
    /** 贴现客户名称(对公客户名称) */
    @Column(name = "user_name")
    private String userName;
    /** 票据币种 */
    @Column(name = "currency")
    private String currency;
    /** 贴现金额(票面金额) */
    @Column(name = "discount_amt")
    private java.math.BigDecimal discountAmt;
    /** 贴现余额 */
    @Column(name = "discount_bal")
    private java.math.BigDecimal discountBal;
    /** 贴现起始日期 */
    @Column(name = "start_date")
    private String startDate;
    /** 贴现到期日期 */
    @Column(name = "end_date")
    private String endDate;
    /** 创建时间 */
    @Column(name = "create_time")
    private String createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private String updateTime;
}
