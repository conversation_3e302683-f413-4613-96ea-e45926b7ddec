package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度体系模板关联Do
 *
 * <AUTHOR>
 * @date 2022-12-06 03:12:50
 */
@Table(name = "lc_cust_limit_template_rel")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitTemplateRelDo extends LcCustLimitTemplateRelKeyDo implements Serializable {
    private static final long serialVersionUID = 1599965058241331200L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 当前节点体系模板编号 */
    @Column(name = "current_limit_template_id")
    private String currentLimitTemplateId;
    /** 当前节点编号 */
    @Column(name = "current_node_id")
    private String currentNodeId;
    /** 体系额度模板编号 */
    @Column(name = "limit_template_id")
    private String limitTemplateId;
    /** 父节点体系模板编号 */
    @Column(name = "parent_limit_template_id")
    private String parentLimitTemplateId;
    /** 父节点编号 */
    @Column(name = "parent_node_id")
    private String parentNodeId;
    /** 关系类型;EnumLimitTemplateRelationType:001-全部本体系、002-仅父级本体系、003-仅子级本体系 */
    @Column(name = "relation_type")
    private String relationType;
    /** 关系展示配置 */
    @Column(name = "show_config")
    private String showConfig;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
}
