package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 实体操作流水Do
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
@Table(name = "lc_entity_operate_serial")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcEntityOperateSerialDo extends LcEntityOperateSerialKeyDo implements Serializable {
    private static final long serialVersionUID = 1602582710713516048L;
    /** 前置业务流水 */
    @Column(name = "inbound_serial_no")
    private String inboundSerialNo;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 实体所属系统 */
    @Column(name = "system_sign")
    private String systemSign;
    /** 业务流水号 */
    @Column(name = "serial_no")
    private String serialNo;
    /** 预发放截止时间;YYYYMMdd */
    @Column(name = "pre_grant_expiry_date_str")
    private String preGrantExpiryDateStr;
    /** 操作类型;EnumEntityOperateType(001-强制发放,002-预发放,003-发放,004-归还,005-强制发放取消,006-预发放取消,007-发放取消,008-归还取消) */
    @Column(name = "operate_type")
    private String operateType;
    /** 状态;EnumEntityOperatorStatus：010-处理中，020-成功，030-失败，040-取消 */
    @Column(name = "operate_status")
    private String operateStatus;
    /** 低风险币种 */
    @Column(name = "low_risk_currency")
    private String lowRiskCurrency;
    /** 操作低风险金额编号 */
    @Column(name = "low_risk_amount_id")
    private String lowRiskAmountId;
    /** 操作低风险 */
    @Column(name = "low_risk_amount")
    private java.math.BigDecimal lowRiskAmount;
    /** 前置业务关联流水 */
    @Column(name = "last_inbound_serial_no")
    private String lastInboundSerialNo;
    /** 操作金额 */
    @Column(name = "amount")
    private java.math.BigDecimal amount;
    /** 前置业务时间 */
    @Column(name = "inbound_serial_datetime")
    private java.util.Date inboundSerialDatetime;
    /** 全局流水号 */
    @Column(name = "global_serial_no")
    private String globalSerialNo;
    /** 失败原因 */
    @Column(name = "fail_reason")
    private String failReason;
    /** 实体业务编号 */
    @Column(name = "entity_relation_id")
    private String entityRelationId;
    /** 实体编号 */
    @Column(name = "entity_id")
    private String entityId;
    /** 实体申请编号 */
    @Column(name = "entity_apply_id")
    private String entityApplyId;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 渠道号 */
    @Column(name = "channel_no")
    private String channelNo;
    /** 业务时间 */
    @Column(name = "biz_datetime")
    private java.util.Date bizDatetime;
    /** 操作金额编号 */
    @Column(name = "amount_id")
    private String amountId;
    /** 金额币种 */
    @Column(name = "amount_currency")
    private String amountCurrency;

    /** 客户编号 */
    @Column(name = "cust_no")
    private String custNo;
}
