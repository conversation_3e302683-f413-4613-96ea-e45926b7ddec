package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 电票系统-中间表-同业客户产品层额度信息Do
 *
 * <AUTHOR>
 * @date 2025-07-28 11:16:05
 */
@Table(name = "lb_t_elcbl_ibnk_prod_lmt_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbTElcblIbnkProdLmtInfoDo extends LbTElcblIbnkProdLmtInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1949790961362206720L;
    /** 更新时间 */
    @Column(name = "update_time")
    private Date updateTime;
    /** 创建时间 */
    @Column(name = "create_time")
    private Date createTime;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 核心机构号 */
    @Column(name = "core_inst_no")
    private String coreInstNo;
    /** 额度实例金额信息中实占额度 */
    @Column(name = "real_occupy_amount")
    private java.math.BigDecimal realOccupyAmount;
    /** 额度状态 */
    @Column(name = "limit_status")
    private String limitStatus;
    /** 已占用额度 */
    @Column(name = "use_occupy_amount")
    private java.math.BigDecimal useOccupyAmount;
    /** 可用额度 */
    @Column(name = "available_amount")
    private java.math.BigDecimal availableAmount;
    /** 总额度 */
    @Column(name = "total_amount")
    private java.math.BigDecimal totalAmount;
    /** 证件号码 */
    @Column(name = "cert_no")
    private String certNo;
    /** 证件类型 */
    @Column(name = "cert_typ")
    private String certTyp;
    /** 客户名称 */
    @Column(name = "cust_nm")
    private String custNm;
    /** 客户类型 */
    @Column(name = "cust_typ")
    private String custTyp;
}
