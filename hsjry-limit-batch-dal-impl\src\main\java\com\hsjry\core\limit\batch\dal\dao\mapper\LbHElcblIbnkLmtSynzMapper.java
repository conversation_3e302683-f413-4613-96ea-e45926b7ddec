package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblIbnkLmtSynzDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 电票系统-历史表-同业客户额度同步mapper
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
public interface LbHElcblIbnkLmtSynzMapper extends CommonMapper<LbHElcblIbnkLmtSynzDo> {
    /**
     * 批量插入电票系统-历史表-同业客户额度同步信息
     *
     * @param lbHElcblIbnkLmtSynzList 批量数据
     * @return int
     */
    @Override
    int insertList(@Param("list") List<LbHElcblIbnkLmtSynzDo> lbHElcblIbnkLmtSynzList);

    /**
     * 清空电票系统-历史表-同业客户额度同步表所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 根据数据日期删除记录
     *
     * @param dataDate 数据日期
     * @return int
     */
    int deleteByDataDate(String dataDate);
}