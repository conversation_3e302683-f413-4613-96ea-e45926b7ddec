package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTOlCorpCtrInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTOlCorpCtrInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlCorpCtrInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlCorpCtrInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlCorpCtrInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTOlCorpCtrInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

import lombok.extern.slf4j.Slf4j;

/**
 * 网贷系统-中间表-对公合同信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
@Slf4j
@Repository
public class LbTOlCorpCtrInfoDaoImpl extends AbstractBaseDaoImpl<LbTOlCorpCtrInfoDo, LbTOlCorpCtrInfoMapper>
    implements LbTOlCorpCtrInfoDao {
    /**
     * 分页查询
     *
     * @param lbTOlCorpCtrInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTOlCorpCtrInfoDo> selectPage(LbTOlCorpCtrInfoQuery lbTOlCorpCtrInfo, PageParam pageParam) {
        LbTOlCorpCtrInfoExample example = buildExample(lbTOlCorpCtrInfo);
        return PageHelper.<LbTOlCorpCtrInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询网贷系统-中间表-对公合同信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTOlCorpCtrInfoDo selectByKey(String custNo, String custLimitId) {
        LbTOlCorpCtrInfoKeyDo lbTOlCorpCtrInfoKeyDo = new LbTOlCorpCtrInfoKeyDo();
        lbTOlCorpCtrInfoKeyDo.setCustNo(custNo);
        lbTOlCorpCtrInfoKeyDo.setCustLimitId(custLimitId);
        lbTOlCorpCtrInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTOlCorpCtrInfoKeyDo);
    }

    /**
     * 根据key删除网贷系统-中间表-对公合同信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTOlCorpCtrInfoKeyDo lbTOlCorpCtrInfoKeyDo = new LbTOlCorpCtrInfoKeyDo();
        lbTOlCorpCtrInfoKeyDo.setCustNo(custNo);
        lbTOlCorpCtrInfoKeyDo.setCustLimitId(custLimitId);
        lbTOlCorpCtrInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTOlCorpCtrInfoKeyDo);
    }

    /**
     * 查询网贷系统-中间表-对公合同信息信息
     *
     * @param lbTOlCorpCtrInfo 条件
     * @return List<LbTOlCorpCtrInfoDo>
     */
    @Override
    public List<LbTOlCorpCtrInfoDo> selectByExample(LbTOlCorpCtrInfoQuery lbTOlCorpCtrInfo) {
        return getMapper().selectByExample(buildExample(lbTOlCorpCtrInfo));
    }

    /**
     * 新增网贷系统-中间表-对公合同信息信息
     *
     * @param lbTOlCorpCtrInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTOlCorpCtrInfoDo lbTOlCorpCtrInfo) {
        if (lbTOlCorpCtrInfo == null) {
            return -1;
        }

        lbTOlCorpCtrInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTOlCorpCtrInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTOlCorpCtrInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTOlCorpCtrInfo);
    }

    /**
     * 修改网贷系统-中间表-对公合同信息信息
     *
     * @param lbTOlCorpCtrInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTOlCorpCtrInfoDo lbTOlCorpCtrInfo) {
        if (lbTOlCorpCtrInfo == null) {
            return -1;
        }
        lbTOlCorpCtrInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTOlCorpCtrInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTOlCorpCtrInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTOlCorpCtrInfoDo lbTOlCorpCtrInfo,
        LbTOlCorpCtrInfoQuery lbTOlCorpCtrInfoQuery) {
        lbTOlCorpCtrInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTOlCorpCtrInfo, buildExample(lbTOlCorpCtrInfoQuery));
    }

    /**
     * 构建网贷系统-中间表-对公合同信息Example信息
     *
     * @param lbTOlCorpCtrInfo
     * @return
     */
    public LbTOlCorpCtrInfoExample buildExample(LbTOlCorpCtrInfoQuery lbTOlCorpCtrInfo) {
        LbTOlCorpCtrInfoExample example = new LbTOlCorpCtrInfoExample();
        LbTOlCorpCtrInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTOlCorpCtrInfo != null) {
            //添加查询条件
            if (null != lbTOlCorpCtrInfo.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTOlCorpCtrInfo.getLowRiskAmount());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpCtrInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTOlCorpCtrInfo.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpCtrInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTOlCorpCtrInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpCtrInfo.getEntityId())) {
                criteria.andEntityIdEqualTo(lbTOlCorpCtrInfo.getEntityId());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpCtrInfo.getVirtualContractFlag())) {
                criteria.andVirtualContractFlagEqualTo(lbTOlCorpCtrInfo.getVirtualContractFlag());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpCtrInfo.getRelationId())) {
                criteria.andRelationIdEqualTo(lbTOlCorpCtrInfo.getRelationId());
            }
            if (null != lbTOlCorpCtrInfo.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTOlCorpCtrInfo.getRealOccupyLowRiskAmt());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpCtrInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTOlCorpCtrInfo.getCustNo());
            }
            if (null != lbTOlCorpCtrInfo.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTOlCorpCtrInfo.getRealOccupyAmount());
            }
            if (null != lbTOlCorpCtrInfo.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTOlCorpCtrInfo.getTotalAmount());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpCtrInfo.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTOlCorpCtrInfo.getLimitStatus());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpCtrInfo.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTOlCorpCtrInfo.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpCtrInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTOlCorpCtrInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpCtrInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTOlCorpCtrInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpCtrInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTOlCorpCtrInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpCtrInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTOlCorpCtrInfo.getCustTyp());
            }
        }
        buildExampleExt(lbTOlCorpCtrInfo, criteria);
        return example;
    }

    /**
     * 构建网贷系统-中间表-对公合同信息ExampleExt方法
     *
     * @param lbTOlCorpCtrInfo
     * @return
     */
    public void buildExampleExt(LbTOlCorpCtrInfoQuery lbTOlCorpCtrInfo, LbTOlCorpCtrInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 将网贷对公客户额度同步数据插入到合同信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_ol_corp_ctr_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    @Override
    public int insertOlCorpCtrInfo(List<String> templateNodeIdList, List<String> custLimitIdList) {
        if (CollectionUtils.isEmpty(templateNodeIdList) || CollectionUtils.isEmpty(custLimitIdList)) {
            log.warn("模板节点ID列表或客户额度ID列表为空，跳过插入操作");
            return 0;
        }
        return getMapper().insertOlCorpCtrInfo(templateNodeIdList, custLimitIdList);
    }

    /**
     * 更新额度实例金额信息表
     * 根据网贷系统中间表数据更新LC_CUST_LIMIT_AMT_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    @Override
    public int updateCustLimitAmtInfo(List<String> custLimitIdList) {
        if (CollectionUtils.isEmpty(custLimitIdList)) {
            log.warn("客户额度ID列表为空，跳过更新额度实例金额信息操作");
            return 0;
        }
        return getMapper().updateCustLimitAmtInfo(custLimitIdList);
    }

}
