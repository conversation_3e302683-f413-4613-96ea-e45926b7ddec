package com.hsjry.core.limit.center.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.center.dal.dao.query.LcSingleLimitRuleQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcSingleLimitRuleDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 单一限额规则mapper
 *
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
public interface LcSingleLimitRuleBatchMapper extends CommonMapper<LcSingleLimitRuleDo> {

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(@Param("query") LcSingleLimitRuleQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcSingleLimitRuleDo selectFirstOne(@Param("query") LcSingleLimitRuleQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcSingleLimitRuleDo> selectShardList(@Param("query") LcSingleLimitRuleQuery query);
}