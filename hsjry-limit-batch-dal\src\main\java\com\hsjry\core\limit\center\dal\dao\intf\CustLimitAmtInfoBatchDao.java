/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.query.CustLimitAmtInfoQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitAmtInfoDo;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/17 9:54
 */
public interface CustLimitAmtInfoBatchDao {
    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitAmtInfoDo> selectShardList(CustLimitAmtInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitAmtInfoDo selectFirstOne(CustLimitAmtInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(CustLimitAmtInfoQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitAmtInfoDo> selectObjectShardList(CustLimitAmtInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitAmtInfoDo selectObjectFirstOne(CustLimitAmtInfoQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitAmtInfoDo> selectExpireShardList(CustLimitAmtInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitAmtInfoDo selectExpireFirstOne(CustLimitAmtInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectObjectCountByCurrentGroup(CustLimitAmtInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectExpireCountByCurrentGroup(CustLimitAmtInfoQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitAmtInfoDo> selectNotUsedShardList(CustLimitAmtInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitAmtInfoDo selectNotUsedFirstOne(CustLimitAmtInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNotUsedCountByCurrentGroup(CustLimitAmtInfoQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitAmtInfoDo> selectNodeShardList(CustLimitAmtInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitAmtInfoDo selectNodeFirstOne(CustLimitAmtInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNodeCountByCurrentGroup(CustLimitAmtInfoQuery query);

    List<LcCustLimitAmtInfoDo> selectExpireLimitInfoByObjectId(CustLimitAmtInfoQuery query);
}
