package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcRuleDimensionDo;
import com.hsjry.core.limit.center.dal.dao.query.LcRuleDimensionQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 规则维度信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcRuleDimensionDao extends IBaseDao<LcRuleDimensionDo> {
    /**
     * 分页查询规则维度信息
     *
     * @param lcRuleDimensionQuery 条件
     * @return PageInfo<LcRuleDimensionDo>
     */
    PageInfo<LcRuleDimensionDo> selectPage(LcRuleDimensionQuery lcRuleDimensionQuery, PageParam pageParam);

    /**
     * 根据key查询规则维度信息
     *
     * @param ruleDimensionId
     * @return
     */
    LcRuleDimensionDo selectByKey(String ruleDimensionId);

    /**
     * 根据key删除规则维度信息
     *
     * @param ruleDimensionId
     * @return
     */
    int deleteByKey(String ruleDimensionId);

    /**
     * 查询规则维度信息信息
     *
     * @param lcRuleDimensionQuery 条件
     * @return List<LcRuleDimensionDo>
     */
    List<LcRuleDimensionDo> selectByExample(LcRuleDimensionQuery lcRuleDimensionQuery);

    /**
     * 新增规则维度信息信息
     *
     * @param lcRuleDimension 条件
     * @return int>
     */
    int insertBySelective(LcRuleDimensionDo lcRuleDimension);

    /**
     * 修改规则维度信息信息
     *
     * @param lcRuleDimension
     * @return
     */
    int updateBySelective(LcRuleDimensionDo lcRuleDimension);

    /**
     * 修改规则维度信息信息
     *
     * @param lcRuleDimension
     * @param lcRuleDimensionQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcRuleDimensionDo lcRuleDimension, LcRuleDimensionQuery lcRuleDimensionQuery);

    /**
     * 删除规则维度信息信息
     *
     * @param lcRuleDimensionQuery 条件
     * @return List<LcRuleDimensionDo>
     */
    int deleteByExample(LcRuleDimensionQuery lcRuleDimensionQuery);
}
