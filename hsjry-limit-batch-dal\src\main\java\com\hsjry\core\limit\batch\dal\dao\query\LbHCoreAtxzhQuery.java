package com.hsjry.core.limit.batch.dal.dao.query;

import java.io.Serializable;

import lombok.Data;
import lombok.Builder;

/**
 * 核心系统-历史表-贴现账户主文件查询条件
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Data
@Builder
public class LbHCoreAtxzhQuery implements Serializable {

    /** serialVersionUID */
    private static final long serialVersionUID = 1945747816769060865L;

    /** 抵质押物编号 */
    private String dzywbh;
    /** 对手行行名 */
    private String duifhm;
    /** 对手行行号 */
    private String duifhh;
    /** 对手行类别 */
    private String jigulb;
    /** 转垫款金额 */
    private java.math.BigDecimal zhdkje;
    /** 垫款借据编号 */
    private String jiejuh;
    /** 垫款标志 */
    private String dnknbz;
    /** 逆回购转入时原组号 */
    private String xinx01;
    /** 本次买卖利息金额 */
    private java.math.BigDecimal fxcdje;
    /** 内部转贴现本次卖断利率 */
    private java.math.BigDecimal bcmdll;
    /** 贴现风险标志 */
    private String txfxbz;
    /** 买方付息帐号 */
    private String mffxzh;
    /** 买方付息比例 */
    private java.math.BigDecimal mffxbl;
    /** 付息方式 */
    private String txfxfs;
    /** 查询查复编号 */
    private String cxcfbh;
    /** 是否先贴后查 */
    private String sfxthc;
    /** 客户结算帐号 */
    private String jieszh;
    /** 下次摊销支出日 */
    private String xctzrq;
    /** 内部转贴现本次卖断利息 */
    private java.math.BigDecimal bcmdlx;
    /** 贴现状态 */
    private String zhungt;
    /** 明细序号 */
    private java.math.BigDecimal mxxhao;
    /** 录入日期 */
    private String lururq;
    /** 录入柜员 */
    private String lurugy;
    /** 复核日期 */
    private String fuherq;
    /** 复核柜员 */
    private String fuhegy;
    /** 维护日期 */
    private String weihrq;
    /** 维护柜员 */
    private String weihgy;
    /** 交易日期 */
    private String jioyrq;
    /** 维护机构 */
    private String weihjg;
    /** 维护时间 */
    private java.math.BigDecimal weihsj;
    /** 时间戳 */
    private java.math.BigDecimal shinch;
    /** 记录状态 */
    private String jiluzt;
    /** 数据日期 */
    private String dataDate;
    /** 营业机构 */
    private String yngyjg;
    /** 宽限期 */
    private java.math.BigDecimal kuanxq;
    /** 贴现到期日 */
    private String txdqrq;
    /** 贴现起息日 */
    private String txqxrq;
    /** 货币代号 */
    private String huobdh;
    /** 损益入帐机构 */
    private String syrzjg;
    /** 损益支出机构 */
    private String syzcjg;
    /** 入帐机构 */
    private String ruzhjg;
    /** 帐务机构 */
    private String zhngjg;
    /** 利率编号 */
    private String lilvbh;
    /** 客户名 */
    private String kehzwm;
    /** 客户号 */
    private String kehhao;
    /** 票据包号 */
    private String piojzh;
    /** 贴现业务种类 */
    private String txywzl;
    /** 贴现处理种类 */
    private String txclzl;
    /** 贴现帐号 */
    private String tiexzh;
    /** 贴现借据号 */
    private String txnjjh;
    /** 法人代码 */
    private String faredm;
    /** 年月利率 */
    private String nyuell;
    /** 贴现利率 */
    private java.math.BigDecimal tiexll;
    /** 贴现余额 */
    private java.math.BigDecimal txztye;
    /** 实付金额 */
    private java.math.BigDecimal shfuje;
    /** 实收贴现利息 */
    private java.math.BigDecimal sxtxlx;
    /** 累计利息收入 */
    private java.math.BigDecimal ljlxsr;
    /** 利息摊销周期 */
    private String txrzzq;
    /** 待摊销收入余额 */
    private java.math.BigDecimal dtsrye;
    /** 上次摊销收入日 */
    private String sctsrq;
    /** 下次摊销收入日 */
    private String xctsro;
    /** 实收金额 */
    private java.math.BigDecimal shshje;
    /** 实付贴现利息 */
    private java.math.BigDecimal sftxlx;
    /** 累计利息支出 */
    private java.math.BigDecimal ljlxzc;
    /** 待摊销支出余额 */
    private java.math.BigDecimal dtzcye;
    /** 上次摊销支出日 */
    private String sctzrq;
}
