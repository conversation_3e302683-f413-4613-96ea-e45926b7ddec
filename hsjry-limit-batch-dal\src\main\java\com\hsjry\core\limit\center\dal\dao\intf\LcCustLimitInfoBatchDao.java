package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度实例信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-15 07:51:56
 */
public interface LcCustLimitInfoBatchDao extends IBaseDao<LcCustLimitInfoDo> {
    /**
     * 分页查询额度实例信息
     *
     * @param lcCustLimitInfoQuery 条件
     * @return PageInfo<LcCustLimitInfoDo>
     */
    PageInfo<LcCustLimitInfoDo> selectPage(LcCustLimitInfoQuery lcCustLimitInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度实例信息
     *
     * @param custLimitId
     * @return
     */
    LcCustLimitInfoDo selectByKey(String custLimitId);

    /**
     * 根据key删除额度实例信息
     *
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custLimitId);

    /**
     * 查询额度实例信息信息
     *
     * @param lcCustLimitInfoQuery 条件
     * @return List<LcCustLimitInfoDo>
     */
    List<LcCustLimitInfoDo> selectByExample(LcCustLimitInfoQuery lcCustLimitInfoQuery);

    /**
     * 查询额度实例信息信息,额度到期日倒序
     *
     * @param lcCustLimitInfoQuery 条件
     * @return List<LcCustLimitInfoDo>
     */
    List<LcCustLimitInfoDo> selectByExampleOrderByLastTime(LcCustLimitInfoQuery lcCustLimitInfoQuery);

    /**
     * 新增额度实例信息信息
     *
     * @param lcCustLimitInfo 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitInfoDo lcCustLimitInfo);

    /**
     * 修改额度实例信息信息
     *
     * @param lcCustLimitInfo
     * @return
     */
    int updateBySelective(LcCustLimitInfoDo lcCustLimitInfo);

    /**
     * 修改额度实例信息信息
     *
     * @param lcCustLimitInfo
     * @param lcCustLimitInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitInfoDo lcCustLimitInfo, LcCustLimitInfoQuery lcCustLimitInfoQuery);

    Integer selectCountByCurrentGroup(LcCustLimitInfoQuery query);

    LcCustLimitInfoDo selectFirstOne(LcCustLimitInfoQuery query);

    List<LcCustLimitInfoDo> selectShardList(LcCustLimitInfoQuery query);
}
