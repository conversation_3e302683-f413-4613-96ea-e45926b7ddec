<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbTElcblCorpProdLmtInfoMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpProdLmtInfoDo">
        <result property="lowRiskAmount" column="low_risk_amount" jdbcType="DECIMAL"/> <!-- 总低风险额度 -->
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/> <!-- 租户号 -->
        <result property="ownOrganId" column="own_organ_id" jdbcType="VARCHAR"/> <!-- 所属组织编号 -->
        <result property="operatorId" column="operator_id" jdbcType="VARCHAR"/> <!-- 操作人编号 -->
        <result property="entityId" column="entity_id" jdbcType="VARCHAR"/> <!-- 实体编号 -->
        <result property="virtualContractFlag" column="virtual_contract_flag"
                jdbcType="CHAR"/> <!-- 虚拟合同标识;EnumBool(Y-是，N-否) -->
        <result property="relationId" column="relation_id" jdbcType="VARCHAR"/> <!-- 关联编号 -->
        <result property="realOccupyLowRiskAmt" column="real_occupy_low_risk_amt" jdbcType="DECIMAL"/> <!-- 实占低风险 -->
        <result property="custNo" column="cust_no" jdbcType="VARCHAR"/> <!-- 客户编号 -->
        <result property="realOccupyAmount" column="real_occupy_amount" jdbcType="DECIMAL"/> <!-- 实占额度 -->
        <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/> <!-- 总额度 -->
        <result property="limitStatus" column="limit_status" jdbcType="VARCHAR"/> <!-- 额度状态 -->
        <result property="custLimitId" column="cust_limit_id" jdbcType="VARCHAR"/> <!-- 额度编号 -->
        <result property="certNo" column="cert_no" jdbcType="VARCHAR"/> <!-- 证件号码 -->
        <result property="certTyp" column="cert_typ" jdbcType="VARCHAR"/> <!-- 证件类型 -->
        <result property="custNm" column="cust_nm" jdbcType="VARCHAR"/> <!-- 客户名称 -->
        <result property="custTyp" column="cust_typ" jdbcType="VARCHAR"/> <!-- 客户类型 -->
    </resultMap>
    <sql id="Base_Column_List">
        low_risk_amount
        , update_time
                , create_time
                , tenant_id
                , own_organ_id
                , operator_id
                , entity_id
                , virtual_contract_flag
                , relation_id
                , real_occupy_low_risk_amt
                , cust_no
                , real_occupy_amount
                , total_amount
                , limit_status
                , cust_limit_id
                , cert_no
                , cert_typ
                , cust_nm
                , cust_typ
    </sql>

    <!-- 将电票对公客户额度同步数据插入到产品层额度信息表 -->
    <insert id="insertElcblCorpProdLmtInfo">
        insert into lb_t_elcbl_corp_prod_lmt_info(
        cust_no, cust_typ, cust_nm, cert_typ, cert_no, cust_limit_id, limit_status,
        total_amount, real_occupy_amount, low_risk_amount, real_occupy_low_risk_amt,
        relation_id, virtual_contract_flag, entity_id, operator_id, own_organ_id,
        tenant_id, create_time, update_time
        )
        select ltici.cust_no,
        ltici.cust_typ,
        ltici.cust_nm,
        ltici.cert_typ,
        ltici.cert_no,
        lcli.cust_limit_id,
        lcli.limit_status,
        lsedbi.discount_amt as total_amount,
        lsedbi.discount_amt as real_occupy_amount,
        lsedbi.discount_amt as low_risk_amount,
        lsedbi.discount_amt as real_occupy_low_risk_amt,
        lsedbi.bill_no || '_' || lsedbi.bill_range_start || '_' || lsedbi.bill_range_end as relation_id,
        lcli.virtual_contract_flag as virtual_contract_flag,
        null as entity_id,
        ltici.operator_id,
        ltici.own_organ_id,
        ltici.tenant_id,
        sysdate as create_time,
        sysdate as update_time
        from lb_s_elcbl_dsct_bal_info lsedbi
        inner join lb_t_ibnk_cust_info ltici on lsedbi.user_id = ltici.cust_no
        inner join lc_cust_limit_info lcli on lcli.limit_object_id = ltici.cust_no
        where lcli.template_node_id in
        <if test="templateNodeIdList != null and templateNodeIdList.size() > 0">
            <foreach collection="templateNodeIdList" item="templateNodeId" open="(" separator="," close=")">
                #{templateNodeId}
            </foreach>
        </if>
        <if test="templateNodeIdList == null or templateNodeIdList.size() == 0">
            (null)
        </if>
        and lcli.cust_limit_id in
        <if test="custLimitIdList != null and custLimitIdList.size() > 0">
            <foreach collection="custLimitIdList" item="custLimitId" open="(" separator="," close=")">
                #{custLimitId}
            </foreach>
        </if>
        <if test="custLimitIdList == null or custLimitIdList.size() == 0">
            (null)
        </if>
    </insert>

    <!-- 更新额度实例金额信息表 -->
    <update id="updateCustLimitAmtInfo">
        MERGE INTO LC_CUST_LIMIT_AMT_INFO lclai
        USING lb_t_elcbl_corp_prod_lmt_info ltecci
        ON (lclai.CUST_LIMIT_ID = ltecci.CUST_LIMIT_ID AND lclai.CUST_NO = ltecci.CUST_NO)
        WHEN MATCHED THEN
        UPDATE
        SET lclai.total_amount = ltecci.total_amount,
        lclai.real_occupy_amount = ltecci.real_occupy_amount,
        lclai.low_risk_amount = ltecci.low_risk_amount,
        lclai.real_occupy_low_risk_amt = ltecci.real_occupy_low_risk_amt
        WHERE lclai.CUST_LIMIT_ID IN
        <if test="custLimitIdList != null and custLimitIdList.size() > 0">
            <foreach collection="custLimitIdList" item="custLimitId" open="(" separator="," close=")">
                #{custLimitId}
            </foreach>
        </if>
        <if test="custLimitIdList == null or custLimitIdList.size() == 0">
            (null)
        </if>
    </update>
</mapper>