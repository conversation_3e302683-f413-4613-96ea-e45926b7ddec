package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTCptlIbnkProdLmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTCptlIbnkProdLmtInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTCptlIbnkProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTCptlIbnkProdLmtInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTCptlIbnkProdLmtInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTCptlIbnkProdLmtInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 资金系统-中间表-同业客户产品层额度信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-30 02:03:23
 */
@Repository
public class LbTCptlIbnkProdLmtInfoDaoImpl
    extends AbstractBaseDaoImpl<LbTCptlIbnkProdLmtInfoDo, LbTCptlIbnkProdLmtInfoMapper>
    implements LbTCptlIbnkProdLmtInfoDao {
    /**
     * 分页查询
     *
     * @param lbTCptlIbnkProdLmtInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTCptlIbnkProdLmtInfoDo> selectPage(LbTCptlIbnkProdLmtInfoQuery lbTCptlIbnkProdLmtInfo,
        PageParam pageParam) {
        LbTCptlIbnkProdLmtInfoExample example = buildExample(lbTCptlIbnkProdLmtInfo);
        return PageHelper.<LbTCptlIbnkProdLmtInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询资金系统-中间表-同业客户产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTCptlIbnkProdLmtInfoDo selectByKey(String custNo, String custLimitId) {
        LbTCptlIbnkProdLmtInfoKeyDo lbTCptlIbnkProdLmtInfoKeyDo = new LbTCptlIbnkProdLmtInfoKeyDo();
        lbTCptlIbnkProdLmtInfoKeyDo.setCustNo(custNo);
        lbTCptlIbnkProdLmtInfoKeyDo.setCustLimitId(custLimitId);
        lbTCptlIbnkProdLmtInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTCptlIbnkProdLmtInfoKeyDo);
    }

    /**
     * 根据key删除资金系统-中间表-同业客户产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTCptlIbnkProdLmtInfoKeyDo lbTCptlIbnkProdLmtInfoKeyDo = new LbTCptlIbnkProdLmtInfoKeyDo();
        lbTCptlIbnkProdLmtInfoKeyDo.setCustNo(custNo);
        lbTCptlIbnkProdLmtInfoKeyDo.setCustLimitId(custLimitId);
        lbTCptlIbnkProdLmtInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTCptlIbnkProdLmtInfoKeyDo);
    }

    /**
     * 查询资金系统-中间表-同业客户产品层额度信息信息
     *
     * @param lbTCptlIbnkProdLmtInfo 条件
     * @return List<LbTCptlIbnkProdLmtInfoDo>
     */
    @Override
    public List<LbTCptlIbnkProdLmtInfoDo> selectByExample(LbTCptlIbnkProdLmtInfoQuery lbTCptlIbnkProdLmtInfo) {
        return getMapper().selectByExample(buildExample(lbTCptlIbnkProdLmtInfo));
    }

    /**
     * 新增资金系统-中间表-同业客户产品层额度信息信息
     *
     * @param lbTCptlIbnkProdLmtInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTCptlIbnkProdLmtInfoDo lbTCptlIbnkProdLmtInfo) {
        if (lbTCptlIbnkProdLmtInfo == null) {
            return -1;
        }

        lbTCptlIbnkProdLmtInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTCptlIbnkProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTCptlIbnkProdLmtInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTCptlIbnkProdLmtInfo);
    }

    /**
     * 修改资金系统-中间表-同业客户产品层额度信息信息
     *
     * @param lbTCptlIbnkProdLmtInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTCptlIbnkProdLmtInfoDo lbTCptlIbnkProdLmtInfo) {
        if (lbTCptlIbnkProdLmtInfo == null) {
            return -1;
        }
        lbTCptlIbnkProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTCptlIbnkProdLmtInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTCptlIbnkProdLmtInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTCptlIbnkProdLmtInfoDo lbTCptlIbnkProdLmtInfo,
        LbTCptlIbnkProdLmtInfoQuery lbTCptlIbnkProdLmtInfoQuery) {
        lbTCptlIbnkProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTCptlIbnkProdLmtInfo, buildExample(lbTCptlIbnkProdLmtInfoQuery));
    }

    /**
     * 构建资金系统-中间表-同业客户产品层额度信息Example信息
     *
     * @param lbTCptlIbnkProdLmtInfo
     * @return
     */
    public LbTCptlIbnkProdLmtInfoExample buildExample(LbTCptlIbnkProdLmtInfoQuery lbTCptlIbnkProdLmtInfo) {
        LbTCptlIbnkProdLmtInfoExample example = new LbTCptlIbnkProdLmtInfoExample();
        LbTCptlIbnkProdLmtInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTCptlIbnkProdLmtInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTCptlIbnkProdLmtInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTCptlIbnkProdLmtInfo.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTCptlIbnkProdLmtInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTCptlIbnkProdLmtInfo.getCustTyp());
            }
            if (StringUtil.isNotEmpty(lbTCptlIbnkProdLmtInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTCptlIbnkProdLmtInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTCptlIbnkProdLmtInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTCptlIbnkProdLmtInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTCptlIbnkProdLmtInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTCptlIbnkProdLmtInfo.getCertNo());
            }
            if (null != lbTCptlIbnkProdLmtInfo.getUsedAmount()) {
                criteria.andUsedAmountEqualTo(lbTCptlIbnkProdLmtInfo.getUsedAmount());
            }
            if (StringUtil.isNotEmpty(lbTCptlIbnkProdLmtInfo.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTCptlIbnkProdLmtInfo.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTCptlIbnkProdLmtInfo.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTCptlIbnkProdLmtInfo.getLimitStatus());
            }
            if (null != lbTCptlIbnkProdLmtInfo.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTCptlIbnkProdLmtInfo.getRealOccupyAmount());
            }
            if (StringUtil.isNotEmpty(lbTCptlIbnkProdLmtInfo.getCoreInstNo())) {
                criteria.andCoreInstNoEqualTo(lbTCptlIbnkProdLmtInfo.getCoreInstNo());
            }
            if (StringUtil.isNotEmpty(lbTCptlIbnkProdLmtInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTCptlIbnkProdLmtInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbTCptlIbnkProdLmtInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTCptlIbnkProdLmtInfo.getOwnOrganId());
            }
        }
        buildExampleExt(lbTCptlIbnkProdLmtInfo, criteria);
        return example;
    }

    /**
     * 构建资金系统-中间表-同业客户产品层额度信息ExampleExt方法
     *
     * @param lbTCptlIbnkProdLmtInfo
     * @return
     */
    public void buildExampleExt(LbTCptlIbnkProdLmtInfoQuery lbTCptlIbnkProdLmtInfo,
        LbTCptlIbnkProdLmtInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 往[资金系统-中间表-同业客户产品层额度信息]插入数据
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param limitIdList 额度ID列表
     * @return 插入条数
     */
    @Override
    public int insertCptlIbnkProdLmtInfo(List<String> templateNodeIdList, List<String> limitIdList) {
        return getMapper().insertCptlIbnkProdLmtInfo(templateNodeIdList, limitIdList);
    }

    /**
     * 更新[额度实例金额信息]中[实占额度]
     *
     * @param limitIdList 额度ID列表
     * @return 更新条数
     */
    @Override
    public int updateRealOccupyAmount(List<String> limitIdList) {
        return getMapper().updateRealOccupyAmount(limitIdList);
    }

}
