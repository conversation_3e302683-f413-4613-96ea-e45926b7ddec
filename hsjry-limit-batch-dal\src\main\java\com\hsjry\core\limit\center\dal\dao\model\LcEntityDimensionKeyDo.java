package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 实体维度信息主键
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
@Table(name = "lc_entity_dimension")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcEntityDimensionKeyDo implements Serializable {

    private static final long serialVersionUID = 1602582710713516051L;
    /** 限额规则维度信息编号 */
    @Id
    @Column(name = "rule_dimension_id")
    private String ruleDimensionId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}