package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度前置流水Do
 *
 * <AUTHOR>
 * @date 2023-07-25 11:58:40
 */
@Table(name = "lc_inbound_serial")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcInboundSerialDo extends LcInboundSerialKeyDo implements Serializable {
    private static final long serialVersionUID = 1683808986002030592L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 父级请求流水编号 */
    @Column(name = "parent_cis_serial_no")
    private String parentCisSerialNo;
    /** 请求交易编码;EnumInterfaceTrade */
    @Column(name = "trade_code")
    private String tradeCode;
    /** 请求方法（交易） */
    @Column(name = "request_method")
    private String requestMethod;
    /** 错误信息 */
    @Column(name = "err_msg")
    private String errMsg;
    /** 错误码 */
    @Column(name = "err_code")
    private String errCode;
    /** 交易状态;EnumTradeStatus(010-初始化, 020-处理中, 030-已处理, 040-处理失败, 050-交易取消, 060-交易取消中) */
    @Column(name = "trade_status")
    private String tradeStatus;
    /** 处理状态;EnumLimitHandlerStatus(010-待处理, 020-处理中, 030-成功, 040-失败, 050-无此交易, 060-已冲正) */
    @Column(name = "handler_status")
    private String handlerStatus;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 全局流水号 */
    @Column(name = "global_serial_no")
    private String globalSerialNo;
    /** 前置业务时间 */
    @Column(name = "inbound_serial_datetime")
    private java.util.Date inboundSerialDatetime;
    /** 前置业务流水 */
    @Column(name = "inbound_serial_no")
    private String inboundSerialNo;
    /** 业务时间 */
    @Column(name = "biz_datetime")
    private java.util.Date bizDatetime;
    /** 渠道号 */
    @Column(name = "channel_no")
    private String channelNo;
    /** 业务流水号 */
    @Column(name = "serial_no")
    private String serialNo;
}
