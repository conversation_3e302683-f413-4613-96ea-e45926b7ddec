package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度体系模板节点Do
 *
 * <AUTHOR>
 * @date 2023-08-11 07:45:57
 */
@Table(name = "lc_cust_limit_template_node")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitTemplateNodeDo extends LcCustLimitTemplateNodeKeyDo implements Serializable {
    private static final long serialVersionUID = 1689905980290301952L;
    /** 是否支持被串用;EnumBool(Y-是，N-否) */
    @Column(name = "support_shared_flag")
    private String supportSharedFlag;
    /** 视图样式;EnumViewStyle(001-总额度样式，002-明细样式) */
    @Column(name = "view_style")
    private String viewStyle;
    /** 额度视图展示名称 */
    @Column(name = "view_show_name")
    private String viewShowName;
    /** 单一限额模板编号 */
    @Column(name = "single_rule_template_id")
    private String singleRuleTemplateId;
    /** 是否校验单一限额 */
    @Column(name = "single_rule_check_flag")
    private String singleRuleCheckFlag;
    /** 统计分类;EnumStatisticsType(001-统一授信额度，002-授信额度，003-他用额度，004-产品额度) */
    @Column(name = "statistics_type")
    private String statisticsType;
    /** 总览统计;EnumBool(Y-是，N-否) */
    @Column(name = "overview_statistics_flag")
    private String overviewStatisticsFlag;
    /** 视图列表展示;EnumBool(Y-是，N-否) */
    @Column(name = "view_show_flag")
    private String viewShowFlag;
    /** 匹配规则;规则表达式 无规则默认为true */
    @Column(name = "matching_rule")
    private String matchingRule;
    /** 是否支持串用;EnumBool(Y-是，N-否) */
    @Column(name = "support_share_flag")
    private String supportShareFlag;
    /** 节点展示配置 */
    @Column(name = "show_config")
    private String showConfig;
    /** 额度类型编号 */
    @Column(name = "cust_limit_type_id")
    private String custLimitTypeId;
    /** 额度体系模板编号 */
    @Column(name = "limit_template_id")
    private String limitTemplateId;
    /** 体系模板节点名称 */
    @Column(name = "template_node_name")
    private String templateNodeName;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
}
