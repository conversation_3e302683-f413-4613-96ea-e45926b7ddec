/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.center.dal.dao.query.CustLimitAmtInfoQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitAmtInfoDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/17 9:58
 */
public interface CustLimitAmtInfoBatchMapper extends CommonMapper<LcCustLimitAmtInfoDo> {
    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitAmtInfoDo> selectShardList(@Param("query") CustLimitAmtInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitAmtInfoDo selectFirstOne(@Param("query") CustLimitAmtInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(@Param("query") CustLimitAmtInfoQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitAmtInfoDo> selectObjectShardList(@Param("query") CustLimitAmtInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitAmtInfoDo selectObjectFirstOne(@Param("query") CustLimitAmtInfoQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitAmtInfoDo> selectExpireShardList(@Param("query") CustLimitAmtInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitAmtInfoDo selectExpireFirstOne(@Param("query") CustLimitAmtInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectObjectCountByCurrentGroup(@Param("query") CustLimitAmtInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectExpireCountByCurrentGroup(@Param("query") CustLimitAmtInfoQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitAmtInfoDo> selectNotUsedShardList(@Param("query") CustLimitAmtInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitAmtInfoDo selectNotUsedFirstOne(@Param("query") CustLimitAmtInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNotUsedCountByCurrentGroup(@Param("query") CustLimitAmtInfoQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitAmtInfoDo> selectNodeShardList(@Param("query") CustLimitAmtInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitAmtInfoDo selectNodeFirstOne(@Param("query") CustLimitAmtInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNodeCountByCurrentGroup(@Param("query") CustLimitAmtInfoQuery query);

    List<LcCustLimitAmtInfoDo> selectExpireLimitInfoByObjectId(@Param("query") CustLimitAmtInfoQuery query);
}
