package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTOlIndvLoanInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTOlIndvLoanInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-中间表-个人借据信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
public interface LbTOlIndvLoanInfoDao extends IBaseDao<LbTOlIndvLoanInfoDo> {
    /**
     * 分页查询网贷系统-中间表-个人借据信息
     *
     * @param lbTOlIndvLoanInfoQuery 条件
     * @return PageInfo<LbTOlIndvLoanInfoDo>
     */
    PageInfo<LbTOlIndvLoanInfoDo> selectPage(LbTOlIndvLoanInfoQuery lbTOlIndvLoanInfoQuery, PageParam pageParam);

    /**
     * 根据key查询网贷系统-中间表-个人借据信息
     *
     * @param custNo
     * @param custLimitId
     * @param entityId
     * @return
     */
    LbTOlIndvLoanInfoDo selectByKey(String custNo, String custLimitId, String entityId);

    /**
     * 根据key删除网贷系统-中间表-个人借据信息
     *
     * @param custNo
     * @param custLimitId
     * @param entityId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId, String entityId);

    /**
     * 查询网贷系统-中间表-个人借据信息信息
     *
     * @param lbTOlIndvLoanInfoQuery 条件
     * @return List<LbTOlIndvLoanInfoDo>
     */
    List<LbTOlIndvLoanInfoDo> selectByExample(LbTOlIndvLoanInfoQuery lbTOlIndvLoanInfoQuery);

    /**
     * 新增网贷系统-中间表-个人借据信息信息
     *
     * @param lbTOlIndvLoanInfo 条件
     * @return int>
     */
    int insertBySelective(LbTOlIndvLoanInfoDo lbTOlIndvLoanInfo);

    /**
     * 修改网贷系统-中间表-个人借据信息信息
     *
     * @param lbTOlIndvLoanInfo
     * @return
     */
    int updateBySelective(LbTOlIndvLoanInfoDo lbTOlIndvLoanInfo);

    /**
     * 修改网贷系统-中间表-个人借据信息信息
     *
     * @param lbTOlIndvLoanInfo
     * @param lbTOlIndvLoanInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTOlIndvLoanInfoDo lbTOlIndvLoanInfo,
        LbTOlIndvLoanInfoQuery lbTOlIndvLoanInfoQuery);

    /**
     * 将网贷个人客户额度同步数据插入到借据信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_ol_indv_loan_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    int insertOlIndvLoanInfo(List<String> templateNodeIdList, List<String> custLimitIdList);

    /**
     * 更新实体信息表
     * 根据网贷系统中间表数据更新LC_ENTITY_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    int updateEntityInfo(List<String> custLimitIdList);

    /**
     * 更新实体操作流水表
     * 根据网贷系统中间表数据更新LC_ENTITY_OPERATE_SERIAL表
     *
     * @return int 更新的记录数
     */
    int updateEntityOperateSerial();
}
