package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblDsctBalInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHElcblDsctBalInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-历史表-贴现余额信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
public interface LbHElcblDsctBalInfoDao extends IBaseDao<LbHElcblDsctBalInfoDo> {
    /**
     * 分页查询电票系统-历史表-贴现余额信息
     *
     * @param lbHElcblDsctBalInfoQuery 条件
     * @return PageInfo<LbHElcblDsctBalInfoDo>
     */
    PageInfo<LbHElcblDsctBalInfoDo> selectPage(LbHElcblDsctBalInfoQuery lbHElcblDsctBalInfoQuery, PageParam pageParam);

    /**
     * 根据key查询电票系统-历史表-贴现余额信息
     *
     * @param dicCno
     * @param billNo
     * @param userId
     * @param dataDate
     * @return
     */
    LbHElcblDsctBalInfoDo selectByKey(String dicCno, String billNo, String userId, String dataDate);

    /**
     * 根据key删除电票系统-历史表-贴现余额信息
     *
     * @param dicCno
     * @param billNo
     * @param userId
     * @param dataDate
     * @return
     */
    int deleteByKey(String dicCno, String billNo, String userId, String dataDate);

    /**
     * 查询电票系统-历史表-贴现余额信息信息
     *
     * @param lbHElcblDsctBalInfoQuery 条件
     * @return List<LbHElcblDsctBalInfoDo>
     */
    List<LbHElcblDsctBalInfoDo> selectByExample(LbHElcblDsctBalInfoQuery lbHElcblDsctBalInfoQuery);

    /**
     * 新增电票系统-历史表-贴现余额信息信息
     *
     * @param lbHElcblDsctBalInfo 条件
     * @return int>
     */
    int insertBySelective(LbHElcblDsctBalInfoDo lbHElcblDsctBalInfo);

    /**
     * 修改电票系统-历史表-贴现余额信息信息
     *
     * @param lbHElcblDsctBalInfo
     * @return
     */
    int updateBySelective(LbHElcblDsctBalInfoDo lbHElcblDsctBalInfo);

    /**
     * 修改电票系统-历史表-贴现余额信息信息
     *
     * @param lbHElcblDsctBalInfo
     * @param lbHElcblDsctBalInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHElcblDsctBalInfoDo lbHElcblDsctBalInfo,
        LbHElcblDsctBalInfoQuery lbHElcblDsctBalInfoQuery);

    // 以下方法需要添加到 LbHElcblDsctBalInfoDao 接口中

    /**
     * 批量插入电票系统-历史表-贴现余额信息
     *
     * @param lbHElcblDsctBalInfoList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbHElcblDsctBalInfoDo> lbHElcblDsctBalInfoList);

    /**
     * 清空电票系统-历史表-贴现余额信息所有数据
     *
     * @return int
     */
    int deleteAll();

    int deleteByDataDate(String dataDate);
}
