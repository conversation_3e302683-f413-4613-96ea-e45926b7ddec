package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblIbnkLmtSynzDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHElcblIbnkLmtSynzQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-历史表-同业客户额度同步数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
public interface LbHElcblIbnkLmtSynzDao extends IBaseDao<LbHElcblIbnkLmtSynzDo> {
    /**
     * 分页查询电票系统-历史表-同业客户额度同步
     *
     * @param lbHElcblIbnkLmtSynzQuery 条件
     * @return PageInfo<LbHElcblIbnkLmtSynzDo>
     */
    PageInfo<LbHElcblIbnkLmtSynzDo> selectPage(LbHElcblIbnkLmtSynzQuery lbHElcblIbnkLmtSynzQuery, PageParam pageParam);

    /**
     * 根据key查询电票系统-历史表-同业客户额度同步
     *
     * @param id 主键ID
     * @return
     */
    LbHElcblIbnkLmtSynzDo selectByKey(String id);

    /**
     * 根据key删除电票系统-历史表-同业客户额度同步
     *
     * @param id 主键ID
     * @return
     */
    int deleteByKey(String id);

    /**
     * 查询电票系统-历史表-同业客户额度同步信息
     *
     * @param lbHElcblIbnkLmtSynzQuery 条件
     * @return List<LbHElcblIbnkLmtSynzDo>
     */
    List<LbHElcblIbnkLmtSynzDo> selectByExample(LbHElcblIbnkLmtSynzQuery lbHElcblIbnkLmtSynzQuery);

    /**
     * 新增电票系统-历史表-同业客户额度同步信息
     *
     * @param lbHElcblIbnkLmtSynz 条件
     * @return int>
     */
    int insertBySelective(LbHElcblIbnkLmtSynzDo lbHElcblIbnkLmtSynz);

    /**
     * 修改电票系统-历史表-同业客户额度同步信息
     *
     * @param lbHElcblIbnkLmtSynz
     * @return
     */
    int updateBySelective(LbHElcblIbnkLmtSynzDo lbHElcblIbnkLmtSynz);

    /**
     * 修改电票系统-历史表-同业客户额度同步信息
     *
     * @param lbHElcblIbnkLmtSynz
     * @param lbHElcblIbnkLmtSynzQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHElcblIbnkLmtSynzDo lbHElcblIbnkLmtSynz,
        LbHElcblIbnkLmtSynzQuery lbHElcblIbnkLmtSynzQuery);

    /**
     * 批量插入电票系统-历史表-同业客户额度同步信息
     *
     * @param lbHElcblIbnkLmtSynzList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbHElcblIbnkLmtSynzDo> lbHElcblIbnkLmtSynzList);

    /**
     * 清空电票系统-历史表-同业客户额度同步所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 删除历史表-同业客户额度同步当前业务日期的数据
     * @param dataDate
     * @return
     */
    int deleteByDataDate(String dataDate);
}
