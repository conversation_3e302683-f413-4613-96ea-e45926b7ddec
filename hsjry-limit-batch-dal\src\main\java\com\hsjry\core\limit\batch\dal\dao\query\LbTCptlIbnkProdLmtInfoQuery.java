package com.hsjry.core.limit.batch.dal.dao.query;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * 资金系统-中间表-同业客户产品层额度信息查询条件
 *
 * <AUTHOR>
 * @date 2025-07-30 02:03:23
 */
@Data
@Builder
public class LbTCptlIbnkProdLmtInfoQuery implements Serializable {

    /** serialVersionUID */
    private static final long serialVersionUID = 1950376645714182145L;

    /** 客户编号 */
    private String custNo;
    /** 客户类型 */
    private String custTyp;
    /** 客户名称 */
    private String custNm;
    /** 证件类型 */
    private String certTyp;
    /** 证件号码 */
    private String certNo;
    /** 已用额度 */
    private java.math.BigDecimal usedAmount;
    /** 额度编号 */
    private String custLimitId;
    /** 额度状态 */
    private String limitStatus;
    /** 额度实例金额信息中实占额度 */
    private java.math.BigDecimal realOccupyAmount;
    /** 核心机构号 */
    private String coreInstNo;
    /** 操作人编号 */
    private String operatorId;
    /** 所属组织编号 */
    private String ownOrganId;
}
