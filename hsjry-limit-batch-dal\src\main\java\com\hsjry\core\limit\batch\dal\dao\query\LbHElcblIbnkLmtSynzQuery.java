package com.hsjry.core.limit.batch.dal.dao.query;

import java.io.Serializable;

import lombok.Data;
import lombok.Builder;

/**
 * 电票系统-历史表-同业客户额度同步查询条件
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Data
@Builder
public class LbHElcblIbnkLmtSynzQuery implements Serializable {

    /** serialVersionUID */
    private static final long serialVersionUID = 1945747816769060866L;

    /** 同业客户编号 */
    private String ibnkUserId;
    /** 同业客户证件类型 */
    private String ibnkUserCertificateKind;
    /** 同业客户证件号码 */
    private String ibnkUserCertificateNo;
    /** 总额度 */
    private java.math.BigDecimal totalAmount;
    /** 可用额度 */
    private java.math.BigDecimal availableAmount;
    /** 已占用额度 */
    private java.math.BigDecimal useOccupyAmount;
    /** 核心机构号 */
    private String coreInstNo;
    /** 数据日期 */
    private String dataDate;
}
