package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitRecordInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 限额记录数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcAmtLimitRecordInfoDao extends IBaseDao<LcAmtLimitRecordInfoDo> {
    /**
     * 分页查询限额记录
     *
     * @param lcAmtLimitRecordInfoQuery 条件
     * @return PageInfo<LcAmtLimitRecordInfoDo>
     */
    PageInfo<LcAmtLimitRecordInfoDo> selectPage(LcAmtLimitRecordInfoQuery lcAmtLimitRecordInfoQuery,
        PageParam pageParam);

    /**
     * 根据key查询限额记录
     *
     * @param recordId
     * @return
     */
    LcAmtLimitRecordInfoDo selectByKey(String recordId);

    /**
     * 根据key删除限额记录
     *
     * @param recordId
     * @return
     */
    int deleteByKey(String recordId);

    /**
     * 查询限额记录信息
     *
     * @param lcAmtLimitRecordInfoQuery 条件
     * @return List<LcAmtLimitRecordInfoDo>
     */
    List<LcAmtLimitRecordInfoDo> selectByExample(LcAmtLimitRecordInfoQuery lcAmtLimitRecordInfoQuery);

    /**
     * 新增限额记录信息
     *
     * @param lcAmtLimitRecordInfo 条件
     * @return int>
     */
    int insertBySelective(LcAmtLimitRecordInfoDo lcAmtLimitRecordInfo);

    /**
     * 修改限额记录信息
     *
     * @param lcAmtLimitRecordInfo
     * @return
     */
    int updateBySelective(LcAmtLimitRecordInfoDo lcAmtLimitRecordInfo);

    /**
     * 修改限额记录信息
     *
     * @param lcAmtLimitRecordInfo
     * @param lcAmtLimitRecordInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcAmtLimitRecordInfoDo lcAmtLimitRecordInfo,
        LcAmtLimitRecordInfoQuery lcAmtLimitRecordInfoQuery);
}
