/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.query.CustLimitInfoQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/17 9:54
 */
public interface CustLimitInfoBatchDao {
    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitInfoDo> selectShardList(CustLimitInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitInfoDo selectFirstOne(CustLimitInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(CustLimitInfoQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitInfoDo> selectObjectShardList(CustLimitInfoQuery query);


    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitInfoDo selectObjectFirstOne(CustLimitInfoQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitInfoDo> selectExpireShardList(CustLimitInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitInfoDo selectExpireFirstOne(CustLimitInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectObjectCountByCurrentGroup(CustLimitInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectExpireCountByCurrentGroup(CustLimitInfoQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitInfoDo> selectNotUsedShardList(CustLimitInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitInfoDo selectNotUsedFirstOne(CustLimitInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNotUsedCountByCurrentGroup(CustLimitInfoQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitInfoDo> selectNodeShardList(CustLimitInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitInfoDo selectNodeFirstOne(CustLimitInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNodeCountByCurrentGroup(CustLimitInfoQuery query);

    List<LcCustLimitInfoDo> selectExpireLimitInfoByObjectId(CustLimitInfoQuery query);
}
