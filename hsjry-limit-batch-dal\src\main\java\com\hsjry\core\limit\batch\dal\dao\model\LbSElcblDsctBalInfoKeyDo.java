package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 电票系统-落地表-贴现余额信息主键
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Table(name = "lb_s_elcbl_dsct_bal_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbSElcblDsctBalInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1945747816769060864L;
    /** 贴现编号(核心借据号) */
    @Id
    @Column(name = "dic_cno")
    private String dicCno;
    /** 票据编号 */
    @Id
    @Column(name = "bill_no")
    private String billNo;
    /** 贴现客户编号(对公客户编号) */
    @Id
    @Column(name = "user_id")
    private String userId;
}