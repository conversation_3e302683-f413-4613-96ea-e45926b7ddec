package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 单一限额模板维度关联Do
 *
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
@Table(name = "lc_single_limit_temp_dim_rel")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcSingleLimitTempDimRelDo extends LcSingleLimitTempDimRelKeyDo implements Serializable {
    private static final long serialVersionUID = 1673314101088157696L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 维度编码 */
    @Column(name = "dimension_code")
    private String dimensionCode;
    /** 限额模板编号 */
    @Column(name = "template_id")
    private String templateId;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
}
