<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbHElcblIbnkLmtSynzMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbHElcblIbnkLmtSynzDo">
        <result property="id" column="id" jdbcType="VARCHAR"/> <!-- 主键ID -->
        <result property="ibnkUserId" column="ibnk_user_id" jdbcType="VARCHAR"/> <!-- 同业客户编号 -->
        <result property="ibnkUserCertificateKind" column="ibnk_user_certificate_kind"
                jdbcType="VARCHAR"/> <!-- 同业客户证件类型 -->
        <result property="ibnkUserCertificateNo" column="ibnk_user_certificate_no"
                jdbcType="VARCHAR"/> <!-- 同业客户证件号码 -->
        <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/> <!-- 总额度 -->
        <result property="availableAmount" column="available_amount" jdbcType="DECIMAL"/> <!-- 可用额度 -->
        <result property="useOccupyAmount" column="use_occupy_amount" jdbcType="DECIMAL"/> <!-- 已占用额度 -->
        <result property="coreInstNo" column="core_inst_no" jdbcType="VARCHAR"/> <!-- 核心机构号 -->
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/> <!-- 更新时间 -->
        <result property="dataDate" column="data_date" jdbcType="VARCHAR"/> <!-- 数据日期 -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        , ibnk_user_id
        , ibnk_user_certificate_kind
                , ibnk_user_certificate_no
                , total_amount
                , available_amount
                , use_occupy_amount
                , core_inst_no
                , create_time
                , update_time
                , data_date
    </sql>
    <!-- 批量插入电票系统-历史表-同业客户额度同步信息 -->
    <insert id="insertList" parameterType="java.util.List">
        INSERT ALL
        <foreach collection="list" item="item" separator="">
            INTO lb_h_elcbl_ibnk_lmt_synz (
            id, ibnk_user_id, ibnk_user_certificate_kind, ibnk_user_certificate_no, total_amount, available_amount,
            use_occupy_amount, core_inst_no, create_time, update_time, data_date
            ) VALUES (
            #{item.id, jdbcType=VARCHAR},
            #{item.ibnkUserId, jdbcType=VARCHAR},
            #{item.ibnkUserCertificateKind, jdbcType=VARCHAR},
            #{item.ibnkUserCertificateNo, jdbcType=VARCHAR},
            #{item.totalAmount, jdbcType=DECIMAL},
            #{item.availableAmount, jdbcType=DECIMAL},
            #{item.useOccupyAmount, jdbcType=DECIMAL},
            #{item.coreInstNo, jdbcType=VARCHAR},
            #{item.createTime, jdbcType=VARCHAR},
            #{item.updateTime, jdbcType=VARCHAR},
            #{item.dataDate, jdbcType=VARCHAR}
            )
        </foreach>
        SELECT * FROM DUAL
    </insert>

    <!-- 清空电票系统-历史表-同业客户额度同步表所有数据 -->
    <delete id="deleteAll" parameterType="java.lang.String">
        TRUNCATE TABLE lb_h_elcbl_ibnk_lmt_synz
    </delete>

    <!-- 根据数据日期删除记录 -->
    <delete id="deleteByDataDate" parameterType="java.lang.String">
        DELETE FROM lb_h_elcbl_ibnk_lmt_synz WHERE data_date = #{dataDate}
    </delete>
</mapper>