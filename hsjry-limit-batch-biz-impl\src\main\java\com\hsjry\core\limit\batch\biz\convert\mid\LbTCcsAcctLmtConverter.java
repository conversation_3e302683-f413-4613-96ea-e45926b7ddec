package com.hsjry.core.limit.batch.biz.convert.mid;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.common.dto.file.LbTCcsAcctLmtDto;
import com.hsjry.core.limit.batch.dal.dao.model.LbTCcsAcctLmtDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCcsAcctDo;

/**
 * 信用卡额度信息转换类
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
public class LbTCcsAcctLmtConverter {

    /**
     * DTO转DO
     *
     * @param dto DTO对象
     * @return DO对象
     */
    public LbTCcsAcctLmtDo dtoToDo(LbTCcsAcctLmtDto dto) {
        if (dto == null) {
            return null;
        }
        return LbTCcsAcctLmtCnvs.INSTANCE.dtoToDo(dto);
    }

    /**
     * DO转DTO
     *
     * @param ccsAcctLmtDo DO对象
     * @return DTO对象
     */
    public LbTCcsAcctLmtDto doToDto(LbTCcsAcctLmtDo ccsAcctLmtDo) {
        if (ccsAcctLmtDo == null) {
            return null;
        }
        return LbTCcsAcctLmtCnvs.INSTANCE.do2Dto(ccsAcctLmtDo);
    }

    /**
     * DTO列表转DO列表
     *
     * @param dtoList DTO列表
     * @return DO列表
     */
    public List<LbTCcsAcctLmtDo> dtoListToDoList(List<LbTCcsAcctLmtDto> dtoList) {
        if (dtoList == null) {
            return null;
        }

        List<LbTCcsAcctLmtDo> doList = new ArrayList<>(dtoList.size());
        for (LbTCcsAcctLmtDto dto : dtoList) {
            doList.add(dtoToDo(dto));
        }
        return doList;
    }

    /**
     * DO列表转DTO列表
     *
     * @param doList DO列表
     * @return DTO列表
     */
    public List<LbTCcsAcctLmtDto> doListToDtoList(List<LbTCcsAcctLmtDo> doList) {
        if (doList == null) {
            return null;
        }

        List<LbTCcsAcctLmtDto> dtoList = new ArrayList<>(doList.size());
        for (LbTCcsAcctLmtDo ccsAcctLmtDo : doList) {
            dtoList.add(doToDto(ccsAcctLmtDo));
        }
        return dtoList;
    }






    /**
     * LbSCcsAcctDo转LbTCcsAcctLmtDo
     *
     * @param lbSCcsAcctDo 源DO对象
     * @return 目标DO对象
     */
    public LbTCcsAcctLmtDo ccsAcctDoToLmtDo(LbSCcsAcctDo lbSCcsAcctDo) {
        if (lbSCcsAcctDo == null) {
            return null;
        }
        return LbTCcsAcctLmtCnvs.INSTANCE.ccsAcctDo2LmtDo(lbSCcsAcctDo);
    }

    /**
     * LbSCcsAcctDo转LbTCcsAcctLmtDo
     *
     * @param model 源DO对象
     * @return 目标DO对象
     */
    public static LbTCcsAcctLmtDo do2Copy(LbSCcsAcctDo model) {
        return LbTCcsAcctLmtCnvs.INSTANCE.ccsAcctDo2LmtDo(model);
    }

    /**
     * LbSCcsAcctDo列表转LbTCcsAcctLmtDo列表
     *
     * @param doList 源DO列表
     * @return 目标DO列表
     */
    public static List<LbTCcsAcctLmtDo> doList2CopyList(List<LbSCcsAcctDo> doList) {
        if (null == doList) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (doList.size() / 0.75f) + 1, 16);
        return doList.parallelStream().map(LbTCcsAcctLmtConverter::do2Copy)//
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }

    /**
     * Integer转BigDecimal工具方法
     */
    private BigDecimal integerToBigDecimal(Integer value) {
        return value == null ? null : new BigDecimal(value);
    }
} 