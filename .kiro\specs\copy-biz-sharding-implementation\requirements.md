# Requirements Document

## Introduction

本需求文档定义了为hsjry-limit-batch项目中copy目录下的bizImpl类完成分片执行方法的执行逻辑实现。该功能需要使用对应的sharding impl类的方法进行分片处理和分片落库逻辑执行，确保数据备份同步任务能够高效、可靠地处理大量数据。

## Requirements

### Requirement 1

**User Story:** 作为一个批处理系统开发者，我希望copy目录下的所有bizImpl类都能正确实现分片执行逻辑，以便能够高效处理大量数据备份同步任务。

#### Acceptance Criteria

1. WHEN 执行copy目录下的任何一个bizImpl类时 THEN 系统应该能够正确调用对应的sharding impl类进行分片处理
2. WHEN 分片处理开始时 THEN 系统应该能够生成合适的分片任务
3. WHEN 分片执行完成时 THEN 系统应该能够正确更新分片流水状态并记录处理结果
4. IF 分片执行过程中出现异常 THEN 系统应该能够正确处理异常并记录错误信息

### Requirement 2

**User Story:** 作为一个系统管理员，我希望能够监控和跟踪每个分片任务的执行状态，以便及时发现和处理问题。

#### Acceptance Criteria

1. WHEN 分片任务开始执行时 THEN 系统应该记录详细的日志信息包括任务参数和执行时间
2. WHEN 分片任务执行过程中 THEN 系统应该定期输出进度信息和处理统计
3. WHEN 分片任务完成时 THEN 系统应该记录执行结果和性能指标
4. IF 分片任务执行失败 THEN 系统应该记录详细的错误信息和堆栈跟踪

### Requirement 3

**User Story:** 作为一个数据处理专员，我希望分片执行逻辑能够正确处理数据转换和落库操作，以确保数据的完整性和一致性。

#### Acceptance Criteria

1. WHEN 执行数据查询时 THEN 系统应该使用正确的查询条件和分页参数
2. WHEN 进行数据转换时 THEN 系统应该使用对应的Converter类进行源数据到目标数据的转换
3. WHEN 执行数据插入时 THEN 系统应该使用批量插入方式提高性能
4. IF 是第一个分片 THEN 系统应该先清空目标表再进行数据插入
5. WHEN 数据处理完成时 THEN 系统应该更新分片流水记录处理状态

### Requirement 4

**User Story:** 作为一个系统架构师，我希望分片执行逻辑能够遵循现有的架构模式和代码规范，以保持系统的一致性和可维护性。

#### Acceptance Criteria

1. WHEN 实现分片执行逻辑时 THEN 代码应该遵循现有的AbstractShardingPrepareBiz抽象类模式
2. WHEN 处理业务逻辑时 THEN 应该正确实现JobCoreBusiness接口的所有方法
3. WHEN 进行依赖注入时 THEN 应该使用@RequiredArgsConstructor和final字段的模式
4. WHEN 记录日志时 THEN 应该使用统一的日志格式和前缀
5. WHEN 处理异常时 THEN 应该抛出HsjryBizException并使用标准的错误码

### Requirement 5

**User Story:** 作为一个性能优化专员，我希望分片执行逻辑能够高效处理大量数据，以满足批处理系统的性能要求。

#### Acceptance Criteria

1. WHEN 处理大量数据时 THEN 系统应该使用合适的批处理大小避免内存溢出
2. WHEN 执行数据库操作时 THEN 系统应该使用批量操作提高数据库访问效率
3. WHEN 进行数据转换时 THEN 系统应该避免不必要的对象创建和内存分配
4. IF 数据量为空 THEN 系统应该快速返回避免不必要的处理开销
5. WHEN 分片任务并发执行时 THEN 系统应该确保线程安全和数据一致性