package com.hsjry.core.limit.center.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度实例所属对象信息mapper
 *
 * <AUTHOR>
 * @date 2022-12-13 03:09:49
 */
public interface LcCustLimitObjectInfoBatchMapper extends CommonMapper<LcCustLimitObjectInfoDo> {

    /**
     * 查询额度对象-客户
     *
     * @param query
     * @return
     */
    List<LcCustLimitObjectInfoDo> selectPageUserDistinct(@Param("query") LcCustLimitObjectInfoQuery query);

    /**
     * 查询额度对象-产品
     *
     * @param query
     * @return
     */
    List<LcCustLimitObjectInfoDo> selectPageProductDistinct(@Param("query") LcCustLimitObjectInfoQuery query);


    /**
     * 查询第一个
     *
     * @param query 查询
     * @return {@link String }
     */
    LcCustLimitObjectInfoDo selectFirstOne(@Param("query") LcCustLimitObjectInfoQuery query);

    /**
     * 查询分片列表
     *
     * @param query 查询
     * @return {@link List }<{@link LcCustLimitObjectInfoDo }>
     */
    List<String> selectShardList(@Param("query") LcCustLimitObjectInfoQuery query);

    /**
     * 按当前组总数查询
     *
     * @param query 查询
     * @return {@link Integer }
     */
    Integer selectCountByCurrentGroup(@Param("query") LcCustLimitObjectInfoQuery query);
}
