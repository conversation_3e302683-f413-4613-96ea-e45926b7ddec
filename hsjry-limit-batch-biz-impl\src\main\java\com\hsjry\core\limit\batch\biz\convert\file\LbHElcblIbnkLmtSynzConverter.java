/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.biz.entity.LbHElcblIbnkLmtSynzData;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblIbnkLmtSynzDo;

import lombok.extern.slf4j.Slf4j;

/**
 * 电票系统-历史表-同业客户额度同步文件数据转换器
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/11/7 10:28
 */
@Slf4j
public class LbHElcblIbnkLmtSynzConverter {

    /** 默认初始容量 */
    private static final int DEFAULT_INITIAL_CAPACITY = 16;
    /** 负载因子 */
    private static final float LOAD_FACTOR = 0.75f;

    /**
     * Data 转换为 Do
     *
     * @param data 文件数据
     */
    public static LbHElcblIbnkLmtSynzDo data2Do(LbHElcblIbnkLmtSynzData data) {
        return LbHElcblIbnkLmtSynzCnvs.INSTANCE.data2Do(data);
    }

    /**
     * Data列表转DO列表
     *
     * @param dataList DTO列表
     * @return DO列表
     */
    public List<LbHElcblIbnkLmtSynzDo> dataListToDoList(List<LbHElcblIbnkLmtSynzData> dataList) {
        if (Objects.isNull(dataList)) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (dataList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        List<LbHElcblIbnkLmtSynzDo> result = dataList.parallelStream().map(LbHElcblIbnkLmtSynzConverter::data2Do)//
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
        
        // 确保所有记录都有UUID
        for (LbHElcblIbnkLmtSynzDo item : result) {
            if (item.getId() == null) {
                item.setId(java.util.UUID.randomUUID().toString());
            }
        }
        
        return result;
    }
}