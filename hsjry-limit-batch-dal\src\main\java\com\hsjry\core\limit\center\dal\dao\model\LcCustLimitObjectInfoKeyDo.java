package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度实例所属对象信息主键
 *
 * <AUTHOR>
 * @date 2022-12-13 03:09:49
 */
@Table(name = "lc_cust_limit_object_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcCustLimitObjectInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1602501015033282561L;
    /** 额度编号 */
    @Id
    @Column(name = "cust_limit_id")
    private String custLimitId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}