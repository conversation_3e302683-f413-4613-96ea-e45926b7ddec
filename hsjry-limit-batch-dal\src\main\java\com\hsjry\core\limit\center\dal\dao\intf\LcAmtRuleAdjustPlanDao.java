package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcAmtRuleAdjustPlanDo;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtRuleAdjustPlanQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 限额规则调整计划数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcAmtRuleAdjustPlanDao extends IBaseDao<LcAmtRuleAdjustPlanDo> {
    /**
     * 分页查询限额规则调整计划
     *
     * @param lcAmtRuleAdjustPlanQuery 条件
     * @return PageInfo<LcAmtRuleAdjustPlanDo>
     */
    PageInfo<LcAmtRuleAdjustPlanDo> selectPage(LcAmtRuleAdjustPlanQuery lcAmtRuleAdjustPlanQuery, PageParam pageParam);

    /**
     * 根据key查询限额规则调整计划
     *
     * @param ruleAdjustPlanId
     * @return
     */
    LcAmtRuleAdjustPlanDo selectByKey(String ruleAdjustPlanId);

    /**
     * 根据key删除限额规则调整计划
     *
     * @param ruleAdjustPlanId
     * @return
     */
    int deleteByKey(String ruleAdjustPlanId);

    /**
     * 查询限额规则调整计划信息
     *
     * @param lcAmtRuleAdjustPlanQuery 条件
     * @return List<LcAmtRuleAdjustPlanDo>
     */
    List<LcAmtRuleAdjustPlanDo> selectByExample(LcAmtRuleAdjustPlanQuery lcAmtRuleAdjustPlanQuery);

    /**
     * 新增限额规则调整计划信息
     *
     * @param lcAmtRuleAdjustPlan 条件
     * @return int>
     */
    int insertBySelective(LcAmtRuleAdjustPlanDo lcAmtRuleAdjustPlan);

    /**
     * 修改限额规则调整计划信息
     *
     * @param lcAmtRuleAdjustPlan
     * @return
     */
    int updateBySelective(LcAmtRuleAdjustPlanDo lcAmtRuleAdjustPlan);

    /**
     * 修改限额规则调整计划信息
     *
     * @param lcAmtRuleAdjustPlan
     * @param lcAmtRuleAdjustPlanQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcAmtRuleAdjustPlanDo lcAmtRuleAdjustPlan,
        LcAmtRuleAdjustPlanQuery lcAmtRuleAdjustPlanQuery);

    /**
     * 删除限额规则调整计划信息
     *
     * @param lcAmtRuleAdjustPlanQuery 条件
     * @return List<LcAmtRuleAdjustPlanDo>
     */
    int deleteByExample(LcAmtRuleAdjustPlanQuery lcAmtRuleAdjustPlanQuery);
}
