package com.hsjry.core.limit.center.core.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.hsjry.core.limit.center.core.ICustLimitInfoCore;
import com.hsjry.core.limit.center.core.bo.CustLimitInfoBo;
import com.hsjry.core.limit.center.core.convert.LcCustLimitInfoDoCnvs;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitInfoDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustLimitInfoCoreImpl implements ICustLimitInfoCore {
    private final LcCustLimitInfoDao custLimitInfoDao;

    @Override
    public List<CustLimitInfoBo> enqrByExample(LcCustLimitInfoQuery query) {
        List<LcCustLimitInfoDo> doList = custLimitInfoDao.selectByExample(query);
        return LcCustLimitInfoDoCnvs.INSTANCE.cnvsDoListToBoList(doList);
    }

}
