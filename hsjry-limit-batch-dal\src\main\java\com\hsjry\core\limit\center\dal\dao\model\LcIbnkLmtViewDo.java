package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 同业客户额度视图Do
 *
 * <AUTHOR>
 * @date 2025-07-07 12:58:26
 */
@Table(name = "lc_ibnk_lmt_view")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcIbnkLmtViewDo extends LcIbnkLmtViewKeyDo implements Serializable {
    private static final long serialVersionUID = 1942206575120941060L;
    /** 业务币种 */
    @Column(name = "cur")
    private String cur;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 冻结状态 */
    @Column(name = "frz_stat")
    private String frzStat;
    /** 额度状态 */
    @Column(name = "lmt_stat")
    private String lmtStat;
    /** 授信终止日期(授信到期日期) */
    @Column(name = "crdt_ddt")
    private java.util.Date crdtDdt;
    /** 授信起始日期 */
    @Column(name = "crdt_bgn_dt")
    private java.util.Date crdtBgnDt;
    /** 授信期限单位 */
    @Column(name = "crdt_term_unit")
    private String crdtTermUnit;
    /** 授信期限 */
    @Column(name = "crdt_term")
    private Integer crdtTerm;
    /** 可用金额(元) */
    @Column(name = "avl_amt")
    private java.math.BigDecimal avlAmt;
    /** 客户总额度(元) */
    @Column(name = "totl_crdt_lmt")
    private java.math.BigDecimal totlCrdtLmt;
    /** 所属法人全称 */
    @Column(name = "belg_lr_full_nm")
    private String belgLrFullNm;
    /** 所属集团编号 */
    @Column(name = "belg_grp_no")
    private String belgGrpNo;
    /** 所属集团名称 */
    @Column(name = "belg_grp_nm")
    private String belgGrpNm;
    /** 证件号码 */
    @Column(name = "cert_no")
    private String certNo;
    /** 证件类型 */
    @Column(name = "cert_typ")
    private String certTyp;
    /** 客户编号 */
    @Column(name = "cust_no")
    private String custNo;
    /** 客户名称 */
    @Column(name = "cust_nm")
    private String custNm;
    /** 客户风险标签 */
    @Column(name = "cust_risk_tag")
    private String custRiskTag;
}
