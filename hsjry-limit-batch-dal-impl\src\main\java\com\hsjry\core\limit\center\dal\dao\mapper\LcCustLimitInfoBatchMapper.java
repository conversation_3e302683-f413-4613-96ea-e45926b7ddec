package com.hsjry.core.limit.center.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度实例信息mapper
 *
 * <AUTHOR>
 * @date 2022-12-23 06:18:16
 */
public interface LcCustLimitInfoBatchMapper extends CommonMapper<LcCustLimitInfoDo> {

    Integer selectCountByCurrentGroup(@Param("query") LcCustLimitInfoQuery query);

    LcCustLimitInfoDo selectFirstOne(@Param("query") LcCustLimitInfoQuery query);

    List<LcCustLimitInfoDo> selectShardList(@Param("query") LcCustLimitInfoQuery query);
}