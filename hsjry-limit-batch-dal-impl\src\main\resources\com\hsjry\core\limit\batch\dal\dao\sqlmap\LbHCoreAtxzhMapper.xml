<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbHCoreAtxzhMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAtxzhDo">
        <result property="dzywbh" column="dzywbh" jdbcType="VARCHAR"/> <!-- 抵质押物编号 -->
        <result property="duifhm" column="duifhm" jdbcType="VARCHAR"/> <!-- 对手行行名 -->
        <result property="duifhh" column="duifhh" jdbcType="VARCHAR"/> <!-- 对手行行号 -->
        <result property="jigulb" column="jigulb" jdbcType="VARCHAR"/> <!-- 对手行类别 -->
        <result property="zhdkje" column="zhdkje" jdbcType="DECIMAL"/> <!-- 转垫款金额 -->
        <result property="jiejuh" column="jiejuh" jdbcType="VARCHAR"/> <!-- 垫款借据编号 -->
        <result property="dnknbz" column="dnknbz" jdbcType="VARCHAR"/> <!-- 垫款标志 -->
        <result property="xinx01" column="xinx01" jdbcType="VARCHAR"/> <!-- 逆回购转入时原组号 -->
        <result property="fxcdje" column="fxcdje" jdbcType="DECIMAL"/> <!-- 本次买卖利息金额 -->
        <result property="bcmdll" column="bcmdll" jdbcType="DECIMAL"/> <!-- 内部转贴现本次卖断利率 -->
        <result property="txfxbz" column="txfxbz" jdbcType="VARCHAR"/> <!-- 贴现风险标志 -->
        <result property="mffxzh" column="mffxzh" jdbcType="VARCHAR"/> <!-- 买方付息帐号 -->
        <result property="mffxbl" column="mffxbl" jdbcType="DECIMAL"/> <!-- 买方付息比例 -->
        <result property="txfxfs" column="txfxfs" jdbcType="VARCHAR"/> <!-- 付息方式 -->
        <result property="cxcfbh" column="cxcfbh" jdbcType="VARCHAR"/> <!-- 查询查复编号 -->
        <result property="sfxthc" column="sfxthc" jdbcType="VARCHAR"/> <!-- 是否先贴后查 -->
        <result property="jieszh" column="jieszh" jdbcType="VARCHAR"/> <!-- 客户结算帐号 -->
        <result property="xctzrq" column="xctzrq" jdbcType="VARCHAR"/> <!-- 下次摊销支出日 -->
        <result property="bcmdlx" column="bcmdlx" jdbcType="DECIMAL"/> <!-- 内部转贴现本次卖断利息 -->
        <result property="zhungt" column="zhungt" jdbcType="VARCHAR"/> <!-- 贴现状态 -->
        <result property="mxxhao" column="mxxhao" jdbcType="DECIMAL"/> <!-- 明细序号 -->
        <result property="lururq" column="lururq" jdbcType="VARCHAR"/> <!-- 录入日期 -->
        <result property="lurugy" column="lurugy" jdbcType="VARCHAR"/> <!-- 录入柜员 -->
        <result property="fuherq" column="fuherq" jdbcType="VARCHAR"/> <!-- 复核日期 -->
        <result property="fuhegy" column="fuhegy" jdbcType="VARCHAR"/> <!-- 复核柜员 -->
        <result property="weihrq" column="weihrq" jdbcType="VARCHAR"/> <!-- 维护日期 -->
        <result property="weihgy" column="weihgy" jdbcType="VARCHAR"/> <!-- 维护柜员 -->
        <result property="jioyrq" column="jioyrq" jdbcType="VARCHAR"/> <!-- 交易日期 -->
        <result property="weihjg" column="weihjg" jdbcType="VARCHAR"/> <!-- 维护机构 -->
        <result property="weihsj" column="weihsj" jdbcType="DECIMAL"/> <!-- 维护时间 -->
        <result property="shinch" column="shinch" jdbcType="DECIMAL"/> <!-- 时间戳 -->
        <result property="jiluzt" column="jiluzt" jdbcType="VARCHAR"/> <!-- 记录状态 -->
        <result property="dataDate" column="data_date" jdbcType="VARCHAR"/> <!-- 数据日期 -->
        <result property="yngyjg" column="yngyjg" jdbcType="VARCHAR"/> <!-- 营业机构 -->
        <result property="kuanxq" column="kuanxq" jdbcType="DECIMAL"/> <!-- 宽限期 -->
        <result property="txdqrq" column="txdqrq" jdbcType="VARCHAR"/> <!-- 贴现到期日 -->
        <result property="txqxrq" column="txqxrq" jdbcType="VARCHAR"/> <!-- 贴现起息日 -->
        <result property="huobdh" column="huobdh" jdbcType="VARCHAR"/> <!-- 货币代号 -->
        <result property="syrzjg" column="syrzjg" jdbcType="VARCHAR"/> <!-- 损益入帐机构 -->
        <result property="syzcjg" column="syzcjg" jdbcType="VARCHAR"/> <!-- 损益支出机构 -->
        <result property="ruzhjg" column="ruzhjg" jdbcType="VARCHAR"/> <!-- 入帐机构 -->
        <result property="zhngjg" column="zhngjg" jdbcType="VARCHAR"/> <!-- 帐务机构 -->
        <result property="lilvbh" column="lilvbh" jdbcType="VARCHAR"/> <!-- 利率编号 -->
        <result property="kehzwm" column="kehzwm" jdbcType="VARCHAR"/> <!-- 客户名 -->
        <result property="kehhao" column="kehhao" jdbcType="VARCHAR"/> <!-- 客户号 -->
        <result property="piojzh" column="piojzh" jdbcType="VARCHAR"/> <!-- 票据包号 -->
        <result property="txywzl" column="txywzl" jdbcType="VARCHAR"/> <!-- 贴现业务种类 -->
        <result property="txclzl" column="txclzl" jdbcType="VARCHAR"/> <!-- 贴现处理种类 -->
        <result property="tiexzh" column="tiexzh" jdbcType="VARCHAR"/> <!-- 贴现帐号 -->
        <result property="txnjjh" column="txnjjh" jdbcType="VARCHAR"/> <!-- 贴现借据号 -->
        <result property="faredm" column="faredm" jdbcType="VARCHAR"/> <!-- 法人代码 -->
        <result property="nyuell" column="nyuell" jdbcType="VARCHAR"/> <!-- 年月利率 -->
        <result property="tiexll" column="tiexll" jdbcType="DECIMAL"/> <!-- 贴现利率 -->
        <result property="txztye" column="txztye" jdbcType="DECIMAL"/> <!-- 贴现余额 -->
        <result property="shfuje" column="shfuje" jdbcType="DECIMAL"/> <!-- 实付金额 -->
        <result property="sxtxlx" column="sxtxlx" jdbcType="DECIMAL"/> <!-- 实收贴现利息 -->
        <result property="ljlxsr" column="ljlxsr" jdbcType="DECIMAL"/> <!-- 累计利息收入 -->
        <result property="txrzzq" column="txrzzq" jdbcType="VARCHAR"/> <!-- 利息摊销周期 -->
        <result property="dtsrye" column="dtsrye" jdbcType="DECIMAL"/> <!-- 待摊销收入余额 -->
        <result property="sctsrq" column="sctsrq" jdbcType="VARCHAR"/> <!-- 上次摊销收入日 -->
        <result property="xctsro" column="xctsro" jdbcType="VARCHAR"/> <!-- 下次摊销收入日 -->
        <result property="shshje" column="shshje" jdbcType="DECIMAL"/> <!-- 实收金额 -->
        <result property="sftxlx" column="sftxlx" jdbcType="DECIMAL"/> <!-- 实付贴现利息 -->
        <result property="ljlxzc" column="ljlxzc" jdbcType="DECIMAL"/> <!-- 累计利息支出 -->
        <result property="dtzcye" column="dtzcye" jdbcType="DECIMAL"/> <!-- 待摊销支出余额 -->
        <result property="sctzrq" column="sctzrq" jdbcType="VARCHAR"/> <!-- 上次摊销支出日 -->
    </resultMap>
    <sql id="Base_Column_List">
        dzywbh
        , duifhm
                , duifhh
                , jigulb
                , zhdkje
                , jiejuh
                , dnknbz
                , xinx01
                , fxcdje
                , bcmdll
                , txfxbz
                , mffxzh
                , mffxbl
                , txfxfs
                , cxcfbh
                , sfxthc
                , jieszh
                , xctzrq
                , bcmdlx
                , zhungt
                , mxxhao
                , lururq
                , lurugy
                , fuherq
                , fuhegy
                , weihrq
                , weihgy
                , jioyrq
                , weihjg
                , weihsj
                , shinch
                , jiluzt
                , data_date
                , yngyjg
                , kuanxq
                , txdqrq
                , txqxrq
                , huobdh
                , syrzjg
                , syzcjg
                , ruzhjg
                , zhngjg
                , lilvbh
                , kehzwm
                , kehhao
                , piojzh
                , txywzl
                , txclzl
                , tiexzh
                , txnjjh
                , faredm
                , nyuell
                , tiexll
                , txztye
                , shfuje
                , sxtxlx
                , ljlxsr
                , txrzzq
                , dtsrye
                , sctsrq
                , xctsro
                , shshje
                , sftxlx
                , ljlxzc
                , dtzcye
                , sctzrq
    </sql>
    <!-- 请在现有的LbHCoreAtxzhMapper.xml文件中添加以下SQL配置： -->

    <!-- 批量插入贴现账户主文件信息 -->
    <insert id="insertList" parameterType="java.util.List">
        INSERT INTO lb_h_core_atxzh (
            FAREDM, TXNJJH, TIEXZH, TXCLZL, TXYWZL, PIOJZH, KEHHAO, KEHZWM, YNGYJG, ZHNGJG,
            RUZHJG, SYZCJG, SYRZJG, HUOBDH, TXQXRQ, TXDQRQ, KUANXQ, LILVBH, NYUELL, TIEXLL,
            TXZTYE, SHFUJE, SXTXLX, LJLXSR, TXRZZQ, DTSRYE, SCTSRQ, XCTSRO, SHSHJE, SFTXLX,
            LJLXZC, DTZCYE, SCTZRQ, XCTZRQ, JIESZH, SFXTHC, CXCFBH, TXFXFS, MFFXBL, MFFXZH,
            TXFXBZ, DZYWBH, FXCDJE, XINX01, DNKNBZ, JIEJUH, ZHDKJE, JIGULB, DUIFHH, DUIFHM,
            BCMDLL, BCMDLX, ZHUNGT, MXXHAO, LURURQ, LURUGY, FUHERQ, FUHEGY, WEIHRQ, WEIHGY,
            JIOYRQ, WEIHJG, WEIHSJ, SHINCH, JILUZT, DATA_DATE
        )
        <foreach collection="list" item="item" separator=" UNION ALL ">
            SELECT
                #{item.faredm, jdbcType=VARCHAR},
                #{item.txnjjh, jdbcType=VARCHAR},
                #{item.tiexzh, jdbcType=VARCHAR},
                #{item.txclzl, jdbcType=VARCHAR},
                #{item.txywzl, jdbcType=VARCHAR},
                #{item.piojzh, jdbcType=VARCHAR},
                #{item.kehhao, jdbcType=VARCHAR},
                #{item.kehzwm, jdbcType=VARCHAR},
                #{item.yngyjg, jdbcType=VARCHAR},
                #{item.zhngjg, jdbcType=VARCHAR},
                #{item.ruzhjg, jdbcType=VARCHAR},
                #{item.syzcjg, jdbcType=VARCHAR},
                #{item.syrzjg, jdbcType=VARCHAR},
                #{item.huobdh, jdbcType=VARCHAR},
                #{item.txqxrq, jdbcType=VARCHAR},
                #{item.txdqrq, jdbcType=VARCHAR},
                #{item.kuanxq, jdbcType=DECIMAL},
                #{item.lilvbh, jdbcType=VARCHAR},
                #{item.nyuell, jdbcType=VARCHAR},
                #{item.tiexll, jdbcType=DECIMAL},
                #{item.txztye, jdbcType=DECIMAL},
                #{item.shfuje, jdbcType=DECIMAL},
                #{item.sxtxlx, jdbcType=DECIMAL},
                #{item.ljlxsr, jdbcType=DECIMAL},
                #{item.txrzzq, jdbcType=VARCHAR},
                #{item.dtsrye, jdbcType=DECIMAL},
                #{item.sctsrq, jdbcType=VARCHAR},
                #{item.xctsro, jdbcType=VARCHAR},
                #{item.shshje, jdbcType=DECIMAL},
                #{item.sftxlx, jdbcType=DECIMAL},
                #{item.ljlxzc, jdbcType=DECIMAL},
                #{item.dtzcye, jdbcType=DECIMAL},
                #{item.sctzrq, jdbcType=VARCHAR},
                #{item.xctzrq, jdbcType=VARCHAR},
                #{item.jieszh, jdbcType=VARCHAR},
                #{item.sfxthc, jdbcType=VARCHAR},
                #{item.cxcfbh, jdbcType=VARCHAR},
                #{item.txfxfs, jdbcType=VARCHAR},
                #{item.mffxbl, jdbcType=DECIMAL},
                #{item.mffxzh, jdbcType=VARCHAR},
                #{item.txfxbz, jdbcType=VARCHAR},
                #{item.dzywbh, jdbcType=VARCHAR},
                #{item.fxcdje, jdbcType=DECIMAL},
                #{item.xinx01, jdbcType=VARCHAR},
                #{item.dnknbz, jdbcType=VARCHAR},
                #{item.jiejuh, jdbcType=VARCHAR},
                #{item.zhdkje, jdbcType=DECIMAL},
                #{item.jigulb, jdbcType=VARCHAR},
                #{item.duifhh, jdbcType=VARCHAR},
                #{item.duifhm, jdbcType=VARCHAR},
                #{item.bcmdll, jdbcType=DECIMAL},
                #{item.bcmdlx, jdbcType=DECIMAL},
                #{item.zhungt, jdbcType=VARCHAR},
                #{item.mxxhao, jdbcType=DECIMAL},
                #{item.lururq, jdbcType=VARCHAR},
                #{item.lurugy, jdbcType=VARCHAR},
                #{item.fuherq, jdbcType=VARCHAR},
                #{item.fuhegy, jdbcType=VARCHAR},
                #{item.weihrq, jdbcType=VARCHAR},
                #{item.weihgy, jdbcType=VARCHAR},
                #{item.jioyrq, jdbcType=VARCHAR},
                #{item.weihjg, jdbcType=VARCHAR},
                #{item.weihsj, jdbcType=DECIMAL},
                #{item.shinch, jdbcType=DECIMAL},
                #{item.jiluzt, jdbcType=VARCHAR},
                #{item.dataDate, jdbcType=VARCHAR}
            FROM DUAL
        </foreach>

    </insert>

    <!-- 清空贴现账户主文件表所有数据 -->
    <delete id="deleteAll" parameterType="java.lang.String">
        TRUNCATE TABLE lb_h_core_atxzh
    </delete>
</mapper>