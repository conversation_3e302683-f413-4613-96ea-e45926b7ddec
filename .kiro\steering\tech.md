# Technology Stack

## Build System
- **Maven**: Multi-module Maven project with parent POM structure
- **Java**: Enterprise Java application
- **Spring Framework**: Dependency injection and application context

## Key Dependencies
- **MyBatis**: Database ORM for data access layer
- **MapStruct**: Bean mapping framework (version 1.4.2.Final)
- **Lombok**: Code generation for boilerplate reduction
- **Spring Boot**: Based on hsjry-loan-starter-parent (4.1.0-HNNS-SNAPSHOT)

## Common Commands

### Build & Compile
```bash
# Clean and compile all modules
mvn clean compile

# Build entire project
mvn clean install

# Build specific module
mvn clean install -pl hsjry-limit-batch-biz

# Skip tests during build
mvn clean install -DskipTests
```

### Testing
```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=SCorePprodFileSyncImplTest

# Run specific test method
mvn test -Dtest=SCorePprodFileSyncImplTest#testFileDataParsing

# Run tests in offline mode (if dependency issues)
mvn -o test
```

### Development
```bash
# Generate flattened POMs
mvn flatten:flatten

# Dependency analysis
mvn dependency:tree

# Clean target directories
mvn clean
```

## Architecture Patterns
- **Layered Architecture**: Clear separation between facade, business, core, and data access layers
- **Dependency Injection**: Spring-based IoC container
- **Template Method Pattern**: Abstract base classes for file processing
- **Sharding Pattern**: Data processing split across multiple shards for scalability