package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 网贷系统-中间表-对公合同信息Do
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
@Table(name = "lb_t_ol_corp_ctr_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbTOlCorpCtrInfoDo extends LbTOlCorpCtrInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1950902007284039682L;
    /** 总低风险额度 */
    @Column(name = "low_risk_amount")
    private java.math.BigDecimal lowRiskAmount;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 实体编号 */
    @Column(name = "entity_id")
    private String entityId;
    /** 虚拟合同标识;EnumBool(Y-是，N-否) */
    @Column(name = "virtual_contract_flag")
    private String virtualContractFlag;
    /** 关联编号 */
    @Column(name = "relation_id")
    private String relationId;
    /** 实占低风险 */
    @Column(name = "real_occupy_low_risk_amt")
    private java.math.BigDecimal realOccupyLowRiskAmt;
    /** 实占额度 */
    @Column(name = "real_occupy_amount")
    private java.math.BigDecimal realOccupyAmount;
    /** 总额度 */
    @Column(name = "total_amount")
    private java.math.BigDecimal totalAmount;
    /** 额度状态 */
    @Column(name = "limit_status")
    private String limitStatus;
    /** 证件号码 */
    @Column(name = "cert_no")
    private String certNo;
    /** 证件类型 */
    @Column(name = "cert_typ")
    private String certTyp;
    /** 客户名称 */
    @Column(name = "cust_nm")
    private String custNm;
    /** 客户类型 */
    @Column(name = "cust_typ")
    private String custTyp;
}
