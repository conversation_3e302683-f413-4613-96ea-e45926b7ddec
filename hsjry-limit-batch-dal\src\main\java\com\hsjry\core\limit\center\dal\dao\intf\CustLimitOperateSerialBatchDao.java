package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.query.CustLimitOperateSerialBatchQuery;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitOperateSerialQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitOperateSerialDo;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/11/7 19:59
 */
public interface CustLimitOperateSerialBatchDao {
    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitOperateSerialDo> selectShardList(CustLimitOperateSerialQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitOperateSerialDo selectFirstOne(CustLimitOperateSerialQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(CustLimitOperateSerialQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitOperateSerialDo> selectShardList(CustLimitOperateSerialBatchQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitOperateSerialDo selectFirstOne(CustLimitOperateSerialBatchQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(CustLimitOperateSerialBatchQuery query);
}
