package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 资金系统-中间表-对公客户产品层额度信息Do
 *
 * <AUTHOR>
 * @date 2025-07-30 02:03:23
 */
@Table(name = "lb_t_cptl_corp_prod_lmt_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbTCptlCorpProdLmtInfoDo extends LbTCptlCorpProdLmtInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1950376645714182144L;
    /** 产品编号 */
    @Column(name = "product_id")
    private String productId;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 核心机构号 */
    @Column(name = "core_inst_no")
    private String coreInstNo;
    /** 额度实例金额信息中实占额度 */
    @Column(name = "real_occupy_amount")
    private java.math.BigDecimal realOccupyAmount;
    /** 产品名称 */
    @Column(name = "product_name")
    private String productName;
    /** 额度状态 */
    @Column(name = "limit_status")
    private String limitStatus;
    /** 已用额度 */
    @Column(name = "used_amount")
    private java.math.BigDecimal usedAmount;
    /** 证件号码 */
    @Column(name = "cert_no")
    private String certNo;
    /** 证件类型 */
    @Column(name = "cert_typ")
    private String certTyp;
    /** 客户名称 */
    @Column(name = "cust_nm")
    private String custNm;
    /** 客户类型 */
    @Column(name = "cust_typ")
    private String custTyp;
}
