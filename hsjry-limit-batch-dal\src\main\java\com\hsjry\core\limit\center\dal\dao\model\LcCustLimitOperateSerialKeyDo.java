package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度操作流水主键
 *
 * <AUTHOR>
 * @date 2023-10-23 11:54:47
 */
@Table(name = "lc_cust_limit_operate_serial")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcCustLimitOperateSerialKeyDo implements Serializable {

    private static final long serialVersionUID = 1716422915035168768L;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    /** 操作流水编号 */
    @Id
    @Column(name = "clos_serial_no")
    private String closSerialNo;
}