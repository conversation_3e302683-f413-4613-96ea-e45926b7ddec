/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.model.enums.limit.EnumAmtLimitRecordStatus;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.intf.AmtLimitRecordInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.AmtLimitRecordInfoBatchMapper;
import com.hsjry.core.limit.center.dal.dao.query.AmtLimitRecordInfoBatchQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordInfoExample;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/6 15:33
 */
@Repository
@Slf4j
public class AmtLimitRecordInfoBatchDaoImpl
    extends AbstractBaseDaoImpl<LcAmtLimitRecordInfoDo, AmtLimitRecordInfoBatchMapper>
    implements AmtLimitRecordInfoBatchDao {
    @Override
    public PageInfo<LcAmtLimitRecordInfoDo> queryNeedValidByPage(Date date, PageParam pageParam) {
        LcAmtLimitRecordInfoExample example = new LcAmtLimitRecordInfoExample();

        LcAmtLimitRecordInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        criteria.andEffectiveEndTimeLessThanOrEqualTo(DateUtil.getDayEnd(date));
        criteria.andRecordStatusEqualTo(EnumAmtLimitRecordStatus.EFFECTIVE.getCode());
        return PageHelper.<LcAmtLimitRecordInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    @Override
    public List<LcAmtLimitRecordInfoDo> selectShardList(AmtLimitRecordInfoBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectShardList(query);
    }

    @Override
    public LcAmtLimitRecordInfoDo selectFirstOne(AmtLimitRecordInfoBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectFirstOne(query);
    }

    @Override
    public Integer selectCountByCurrentGroup(AmtLimitRecordInfoBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectCountByCurrentGroup(query);
    }
}
