package com.hsjry.core.limit.center.core.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.center.core.bo.CustLimitRelationBo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationDo;

@Mapper(componentModel = "spring")
public interface LcCustLimitRelationDoCnvs {
    LcCustLimitRelationDoCnvs INSTANCE = Mappers.getMapper(LcCustLimitRelationDoCnvs.class);

    /** 将[额度实例关联Do]转换为[额度实例关联Bo] */
    @Mappings({@Mapping(target = "parentInstId", source = "parentNodeLimitId"),//
        @Mapping(target = "currentInstId", source = "currentNodeLimitId")})
    CustLimitRelationBo cnvsDoToBo(LcCustLimitRelationDo dataObject);

    /** 将[额度实例关联Do列表]转换为[额度实例关联Bo列表] */
    List<CustLimitRelationBo> cnvsDoListToBoList(List<LcCustLimitRelationDo> doList);

    /** 将[额度实例关联Bo]转换为[额度实例关联Do] */
    @Mappings({@Mapping(target = "parentNodeLimitId", source = "parentInstId"),//
        @Mapping(target = "currentNodeLimitId", source = "currentInstId"),//
        @Mapping(target = "tenantId", expression = "java(com.hsjry.base.common.utils.AppParamUtil.getTenantId())"),//
        @Mapping(target = "createTime", expression = "java(com.hsjry.lang.business.date.BusinessDateUtil.getDate())"),//
        @Mapping(target = "updateTime", expression = "java(com.hsjry.lang.business.date.BusinessDateUtil.getDate())")})
    LcCustLimitRelationDo cnvsBoToDo(CustLimitRelationBo bo);
}
