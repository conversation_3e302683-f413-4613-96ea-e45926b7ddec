/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.core.limit.batch.biz.convert.file.LbHCoreBcdhpConverter;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.biz.entity.LbHCoreBcdhpData;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.AbstractFileBaseShardingPrepareBizImpl;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.constants.CharsetConstants;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.dal.dao.intf.LbHCoreBcdhpDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreBcdhpDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.sequence.SequenceTool;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
/**
 * 银承汇票历史表文件同步实现类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/22 10:30
 */
@Slf4j
@Service("lbHCoreBcdhpFileSyncImpl")
@RequiredArgsConstructor
public class LbHCoreBcdhpFileSyncImpl extends AbstractFileBaseShardingPrepareBizImpl<LbHCoreBcdhpData> {

    /** 法人代码列数 */
    private static final int FAREDM_NUM = 1;
    /** 银承协议编号列数 */
    private static final int CDXYBH_NUM = 2;
    /** 本次出票批次列数 */
    private static final int BCCPPC_NUM = 3;
    /** 本次出票编号列数 */
    private static final int CHUPBH_NUM = 4;
    /** 签发机构号列数 */
    private static final int YNGYJG_NUM = 5;
    /** 申请机构号列数 */
    private static final int ZHNGJG_NUM = 6;
    /** 银承合同编号列数 */
    private static final int HTNGBH_NUM = 7;
    /** 协议总票面金额列数 */
    private static final int ZONGJE_NUM = 8;
    /** 凭证种类列数 */
    private static final int PNGZZL_NUM = 9;
    /** 票据轮冠字列数 */
    private static final int PJLUGZ_NUM = 10;
    /** 票据号码列数 */
    private static final int PIOJHM_NUM = 11;
    /** 币种列数 */
    private static final int HUOBDH_NUM = 12;
    /** 票面金额列数 */
    private static final int PIOMJE_NUM = 13;
    /** 汇票金额列数 */
    private static final int HUIPJE_NUM = 14;
    /** 备款金额列数 */
    private static final int BEIKJE_NUM = 15;
    /** 签发行联行行号列数 */
    private static final int QFHLHH_NUM = 16;
    /** 签发行联行行名列数 */
    private static final int QFHLHM_NUM = 17;
    /** 出票人客户号列数 */
    private static final int KEHHAO_NUM = 18;
    /** 出票人帐号列数 */
    private static final int CHPRZH_NUM = 19;
    /** 出票人全称列数 */
    private static final int CHPRQC_NUM = 20;
    /** 收款人客户号列数 */
    private static final int SKRKHH_NUM = 21;
    /** 收款人帐号列数 */
    private static final int SHKRZH_NUM = 22;
    /** 收款人户名列数 */
    private static final int SHKRXM_NUM = 23;
    /** 收款行行号列数 */
    private static final int SKHHAO_NUM = 24;
    /** 收款行行名列数 */
    private static final int SHKHHM_NUM = 25;
    /** 是否全额保证金列数 */
    private static final int SHFOBZ_NUM = 26;
    /** 额度层次列数 */
    private static final int EDCENG_NUM = 27;
    /** 银承汇票签发方式列数 */
    private static final int YCQFFS_NUM = 28;
    /** 交易日期列数 */
    private static final int JIOYRQ_NUM = 29;
    /** 到期日期列数 */
    private static final int DAOQRQ_NUM = 30;
    /** 签发日期列数 */
    private static final int QNFARQ_NUM = 31;
    /** 承兑日期列数 */
    private static final int CDUIRQ_NUM = 32;
    /** 备款日期列数 */
    private static final int RUZHRQ_NUM = 33;
    /** 垫款标志列数 */
    private static final int DNKNBZ_NUM = 34;
    /** 垫款借据编号列数 */
    private static final int JIEJUH_NUM = 35;
    /** 垫款金额列数 */
    private static final int ZHDKJE_NUM = 36;
    /** 资金去向列数 */
    private static final int ZIJNQX_NUM = 37;
    /** 资金转入账号列数 */
    private static final int DCZRZH_NUM = 38;
    /** 持票人账号列数 */
    private static final int SKZHAO_NUM = 39;
    /** 持票人名称列数 */
    private static final int CHIPMC_NUM = 40;
    /** 持票人开户行行号列数 */
    private static final int KAIHHH_NUM = 41;
    /** 持票人开户行行名列数 */
    private static final int KAIHHM_NUM = 42;
    /** 未用退回日期列数 */
    private static final int SXIORQ_NUM = 43;
    /** 五级分类日期列数 */
    private static final int WJFLRQ_NUM = 44;
    /** 五级贷款分类列数 */
    private static final int WJDKFL_NUM = 45;
    /** 承兑汇票状态列数 */
    private static final int YCHPZT_NUM = 46;
    /** 票据状态列数 */
    private static final int PIOJZT_NUM = 47;
    /** 是否电子票据列数 */
    private static final int SFDZPJ_NUM = 48;
    /** 明细序号列数 */
    private static final int MXXHAO_NUM = 49;
    /** 挂失日期列数 */
    private static final int GSRIQI_NUM = 50;
    /** 解挂日期列数 */
    private static final int JGRIQI_NUM = 51;
    /** 备注列数 */
    private static final int REMARK_NUM = 52;
    /** 签发行联行地址列数 */
    private static final int BEIZXX_NUM = 53;
    /** 开户机构列数 */
    private static final int KAIHJG_NUM = 54;
    /** 开户柜员列数 */
    private static final int KAIHGY_NUM = 55;
    /** 维护柜员列数 */
    private static final int WEIHGY_NUM = 56;
    /** 维护日期列数 */
    private static final int WEIHRQ_NUM = 57;
    /** 销户柜员列数 */
    private static final int XIOHGY_NUM = 58;
    /** 销户日期列数 */
    private static final int XIOHRQ_NUM = 59;
    /** 维护机构列数 */
    private static final int WEIHJG_NUM = 60;
    /** 维护时间列数 */
    private static final int WEIHSJ_NUM = 61;
    /** 记录状态列数 */
    private static final int SHJNCH_NUM = 62;
    /** 记录状态列数 */
    private static final int JILUZT_NUM = 63;
    /** 最小字段数量 */
    private static final int MIN_FIELD_COUNT = 63;
    /** 分隔符 */
    private static final String FIELD_SEPARATOR = "\\|\\+\\|";
    /** 批处理大小 */
    private static final int BATCH_SIZE = 1000;
    private final String SEQUENCE_NO = "SEQUENCE_NO";

    private final LbHCoreBcdhpDao lbHCoreBcdhpDao;
    @Value("${project.core.bcdhp.filename:CBS_BCDHP_[DATE].csv}")
    private String fileName;
    @Value("${limit.batch.remoteFilePath:/app/bftfiles/cfs/recv/ebbs/}")
    private String remoteFilePathDefine;

    @Override
    public ShardingResult<LbHCoreBcdhpData> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始文件数据分片查询[{}]", GsonUtil.objToStrForLog(jobShared));

        ShardingResult<LbHCoreBcdhpData> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        try {
            // 读取文件分片数据
            FileLineData fileLineData = GsonUtil.json2Obj(jobShared.getExtParam(), FileLineData.class);
            log.info(prefixLog + "开始读取文件数据分片[{}]", GsonUtil.objToStrForLog(fileLineData));
            List<String> originData = FileShardingUtils.readFileSharedData(jobShared, skipFirst(), CharsetConstants.GBK);

            // 使用并行流处理数据，提升性能
            List<LbHCoreBcdhpData> fileDataList = processOriginDataParallel(originData, prefixLog);

            log.info(prefixLog + "读取文件数据分片总量[{}]", fileDataList.size());
            shardingResult.setShardingResultList(fileDataList);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        jobShared.setBatchSerialNo(inParam.getString(SEQUENCE_NO));
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbHCoreBcdhpData> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
        List<LbHCoreBcdhpData> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "银承汇票历史数据文件处理:文件分片数据为空,执行中断。");
            return;
        }

        log.info(prefixLog + "银承汇票历史数据文件处理:开始执行分片数据操作,数据量:[{}]", dataList.size());
        String dataDateStr = String.valueOf(businessDate);
        dataList.forEach(data -> data.setDataDate(dataDateStr));
        // 检查是否是第一个分片，如果是则清空表
        if (sliceBatchSerialDo.getBatchNum() == 1) {
            log.info(prefixLog + "第一个分片,清空目标表 lb_h_core_bcdhp");
            lbHCoreBcdhpDao.deleteByDataDate(dataDateStr);
        }

        // 确保分片流水对象的状态字段不为null
        if (sliceBatchSerialDo.getSharedStatus() == null) {
            log.debug(prefixLog + "分片流水状态为null,设置为处理中状态");
            sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
        }


        // 使用并行流进行数据转换和验证
        List<LbHCoreBcdhpDo> insertList = dataList.parallelStream().map(LbHCoreBcdhpConverter::data2Do)//
            .filter(this::validateData).collect(Collectors.toCollection(ArrayList::new));

        if (CollectionUtil.isNotEmpty(insertList)) {
            // 批量插入数据
            processBatchInsert(insertList, prefixLog);
            log.info(prefixLog + "插入[{}]条银承汇票历史数据", insertList.size());
        }

        // 更新分片流水成功
        normalUpdateSliceSerial(dataList.size(), sliceBatchSerialDo);
        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataList.size());
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.H_CORE_BCDHP_FILE_SYNC;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        JSONObject param = JSON.parseObject(jobInitDto.getInPara());
        //设置流水ID,解决多次从redis获取唯一值的性能问题
        param.put(SEQUENCE_NO, SequenceTool.nextId());
        //更新jobInitDto的inpara参数
        jobInitDto.setInPara(param.toJSONString());

        // 确保fixNum被正确设置
        if (jobInitDto.getFixNum() == null) {
            log.warn(prefixLog + "fixNum为null,设置默认值1000");
            jobInitDto.setFixNum(1000);
        }

        List<JobShared> sharedList = Lists.newArrayList();
        log.info(prefixLog + "[{}]文件处理", jobTradeDesc);

        // 从本地读取文件，使用默认的remoteFilePathDefine路径
        String localFilePath = remoteFilePathDefine + FileShardingUtils.ACCT_DATE_CODE_MARK + File.separator;
        Integer acctDate = jobInitDto.getBusinessDate();
        String localFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK, String.valueOf(acctDate));
        String actualLocalFilePath = localFilePath.replace(FileShardingUtils.ACCT_DATE_CODE_MARK, String.valueOf(acctDate));

        log.info(prefixLog + "转换后的本地文件路径: [{}]", actualLocalFilePath);

        try {
            String fileAttr = FIELD_SEPARATOR;
            String filePath = actualLocalFilePath + localFileName;
            log.info("实际查找的文件路径: [{}]", filePath);

            // 检查目录是否存在
            File directory = new File(actualLocalFilePath);
            if (!directory.exists()) {
                log.info(prefixLog + "目录[{}]不存在,尝试创建", actualLocalFilePath);
                directory.mkdirs();
            } else {
                log.info(prefixLog + "目录[{}]已存在", actualLocalFilePath);
                // 列出目录中的文件
                File[] files = directory.listFiles();
                if (files != null && files.length > 0) {
                    log.info(prefixLog + "目录[{}]中的文件列表:", actualLocalFilePath);
                    for (File f : files) {
                        log.info(prefixLog + " - {}", f.getName());
                    }
                } else {
                    log.info(prefixLog + "目录[{}]为空", actualLocalFilePath);
                }
            }

            File localFile = new File(filePath);
            log.info(prefixLog + "检查文件[{}]是否存在: {}", filePath, localFile.exists());

            //判断本地文件是否存在，不存在则直接报错
            if(!localFile.exists()){
                log.error(prefixLog + "本地文件[{}]不存在,请确认文件路径是否正确", filePath);
                String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
                String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
                throw new HsjryBizException(errorCode, errorMsg);
            }

            // 文件存在，直接进行分片处理
            log.info(prefixLog + "[{}]开始[{}]文件数据分片处理", jobTradeDesc, filePath);
            log.info(prefixLog + "分片参数: fixNum=[{}], fileAttr=[{}], skipFirst=[{}]",
                jobInitDto.getFixNum(), fileAttr, skipFirst());

            List<JobShared> jobShareds = FileShardingUtils.getFileSharedData(jobInitDto, localFile, fileAttr,
                skipFirst());
            log.info(prefixLog + "[{}]结束[{}]文件数据分片当前分片数[{}]", jobTradeDesc, filePath,
                jobShareds.size());
            sharedList.addAll(jobShareds);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }
        return sharedList;
    }

    private boolean skipFirst() {
        return true; // 跳过标题行
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 并行处理原始数据
     * 使用并行流提升数据处理性能
     *
     * @param originData 原始数据列表
     * @param prefixLog 日志前缀
     * @return 处理后的数据列表
     */
    private List<LbHCoreBcdhpData> processOriginDataParallel(List<String> originData, String prefixLog) {
        if (CollectionUtil.isEmpty(originData)) {
            return Lists.newArrayList();
        }

        AtomicInteger parseErrorCount = new AtomicInteger(0);
        List<LbHCoreBcdhpData> fileDataList = originData.parallelStream()//
            .map(line -> parseLineData(line, parseErrorCount))//
            .filter(Objects::nonNull)//
            .collect(Collectors.toCollection(ArrayList::new));

        if (parseErrorCount.get() > 0) {
            log.warn(prefixLog + "数据解析过程中发现[{}]条异常数据", parseErrorCount.get());
        }

        return fileDataList;
    }

    /**
     * 解析单行数据
     * 将CSV格式的单行数据解析为LbHCoreBcdhpData对象
     *
     * @param line 单行数据
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的数据对象，解析失败返回null
     */
    private LbHCoreBcdhpData parseLineData(String line, AtomicInteger parseErrorCount) {
        if (StringUtil.isBlank(line)) {
            return null;
        }

        try {
            String[] split = line.split(FIELD_SEPARATOR);
            if (split.length < MIN_FIELD_COUNT) {
                log.warn("数据字段数量不足,期望[{}]个字段,实际[{}]个字段,数据:[{}]", MIN_FIELD_COUNT, split.length, line);
                parseErrorCount.incrementAndGet();
                return null;
            }

            LbHCoreBcdhpData fileData = new LbHCoreBcdhpData();
            // 设置主键字段
            fileData.setFaredm(split[FAREDM_NUM - 1]);
            fileData.setCdxybh(split[CDXYBH_NUM - 1]);
            fileData.setBccppc(parseBigDecimalSafely(split[BCCPPC_NUM - 1], parseErrorCount));
            fileData.setChupbh(split[CHUPBH_NUM - 1]);

            // 设置业务字段
            fileData.setYngyjg(split[YNGYJG_NUM - 1]);
            fileData.setZhngjg(split[ZHNGJG_NUM - 1]);
            fileData.setHtngbh(split[HTNGBH_NUM - 1]);
            fileData.setZongje(parseBigDecimalSafely(split[ZONGJE_NUM - 1], parseErrorCount));
            fileData.setPngzzl(split[PNGZZL_NUM - 1]);
            fileData.setPjlugz(split[PJLUGZ_NUM - 1]);
            fileData.setPiojhm(split[PIOJHM_NUM - 1]);
            fileData.setHuobdh(split[HUOBDH_NUM - 1]);
            fileData.setPiomje(parseBigDecimalSafely(split[PIOMJE_NUM - 1], parseErrorCount));
            fileData.setHuipje(parseBigDecimalSafely(split[HUIPJE_NUM - 1], parseErrorCount));
            fileData.setBeikje(parseBigDecimalSafely(split[BEIKJE_NUM - 1], parseErrorCount));
            fileData.setQfhlhh(split[QFHLHH_NUM - 1]);
            fileData.setQfhlhm(split[QFHLHM_NUM - 1]);
            fileData.setKehhao(split[KEHHAO_NUM - 1]);
            fileData.setChprzh(split[CHPRZH_NUM - 1]);
            fileData.setChprqc(split[CHPRQC_NUM - 1]);
            fileData.setSkrkhh(split[SKRKHH_NUM - 1]);
            fileData.setShkrzh(split[SHKRZH_NUM - 1]);
            fileData.setShkrxm(split[SHKRXM_NUM - 1]);
            fileData.setSkhhao(split[SKHHAO_NUM - 1]);
            fileData.setShkhhm(split[SHKHHM_NUM - 1]);
            fileData.setShfobz(split[SHFOBZ_NUM - 1]);
            fileData.setEdceng(split[EDCENG_NUM - 1]);
            fileData.setYcqffs(split[YCQFFS_NUM - 1]);
            fileData.setJioyrq(split[JIOYRQ_NUM - 1]);
            fileData.setDaoqrq(split[DAOQRQ_NUM - 1]);
            fileData.setQnfarq(split[QNFARQ_NUM - 1]);
            fileData.setCduirq(split[CDUIRQ_NUM - 1]);
            fileData.setRuzhrq(split[RUZHRQ_NUM - 1]);
            fileData.setDnknbz(split[DNKNBZ_NUM - 1]);
            fileData.setJiejuh(split[JIEJUH_NUM - 1]);
            fileData.setZhdkje(parseBigDecimalSafely(split[ZHDKJE_NUM - 1], parseErrorCount));
            fileData.setZijnqx(split[ZIJNQX_NUM - 1]);
            fileData.setDczrzh(split[DCZRZH_NUM - 1]);
            fileData.setSkzhao(split[SKZHAO_NUM - 1]);
            fileData.setChipmc(split[CHIPMC_NUM - 1]);
            fileData.setKaihhh(split[KAIHHH_NUM - 1]);
            fileData.setKaihhm(split[KAIHHM_NUM - 1]);
            fileData.setSxiorq(split[SXIORQ_NUM - 1]);
            fileData.setWjflrq(split[WJFLRQ_NUM - 1]);
            fileData.setWjdkfl(split[WJDKFL_NUM - 1]);
            fileData.setYchpzt(split[YCHPZT_NUM - 1]);
            fileData.setPiojzt(split[PIOJZT_NUM - 1]);
            fileData.setSfdzpj(split[SFDZPJ_NUM - 1]);
            fileData.setMxxhao(parseBigDecimalSafely(split[MXXHAO_NUM - 1], parseErrorCount));
            fileData.setGsriqi(split[GSRIQI_NUM - 1]);
            fileData.setJgriqi(split[JGRIQI_NUM - 1]);
            fileData.setRemark(split[REMARK_NUM - 1]);
            fileData.setBeizxx(split[BEIZXX_NUM - 1]);
            fileData.setKaihjg(split[KAIHJG_NUM - 1]);
            fileData.setKaihgy(split[KAIHGY_NUM - 1]);
            fileData.setWeihgy(split[WEIHGY_NUM - 1]);
            fileData.setWeihrq(split[WEIHRQ_NUM - 1]);
            fileData.setXiohgy(split[XIOHGY_NUM - 1]);
            fileData.setXiohrq(split[XIOHRQ_NUM - 1]);
            fileData.setWeihjg(split[WEIHJG_NUM - 1]);
            fileData.setWeihsj(split[WEIHSJ_NUM - 1]);
            fileData.setShjnch(split[SHJNCH_NUM - 1]);
            fileData.setJiluzt(split[JILUZT_NUM - 1]);
            // fileData.setDataDate(split[DATA_DATE_NUM - 1]);

            return fileData;
        } catch (Exception e) {
            log.error("解析数据行异常,数据:[{}]", line, e);
            parseErrorCount.incrementAndGet();
            return null;
        }
    }

    /**
     * 安全解析BigDecimal
     * 处理空值和异常情况
     *
     * @param value 待解析的字符串值
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的BigDecimal值，解析失败返回null
     */
    private BigDecimal parseBigDecimalSafely(String value, AtomicInteger parseErrorCount) {
        if (StringUtil.isBlank(value)) {
            return null;
        }

        try {
            return new BigDecimal(value.trim());
        } catch (NumberFormatException e) {
            log.warn("数值解析失败,值:[{}]", value);
            parseErrorCount.incrementAndGet();
            return null;
        }
    }

    /**
     * 数据验证
     * 验证关键业务字段的有效性
     *
     * @param data 待验证的数据对象
     * @return 验证结果，true表示验证通过
     */
    private boolean validateData(LbHCoreBcdhpDo data) {
        if (Objects.isNull(data)) {
            return false;
        }

        // 验证主键字段
        if (StringUtil.isBlank(data.getFaredm())) {
            log.warn("法人代码为空,数据无效");
            return false;
        }

        if (StringUtil.isBlank(data.getCdxybh())) {
            log.warn("银承协议编号为空,法人代码:[{}]", data.getFaredm());
            return false;
        }

        if (Objects.isNull(data.getBccppc())) {
            log.warn("本次出票批次为空,法人代码:[{}],银承协议编号:[{}]", data.getFaredm(), data.getCdxybh());
            return false;
        }

        if (StringUtil.isBlank(data.getChupbh())) {
            log.warn("本次出票编号为空,法人代码:[{}],银承协议编号:[{}]", data.getFaredm(), data.getCdxybh());
            return false;
        }

        if (StringUtil.isBlank(data.getDataDate())) {
            log.warn("数据日期为空,法人代码:[{}],银承协议编号:[{}]", data.getFaredm(), data.getCdxybh());
            return false;
        }

        // 额外的业务验证逻辑
        if (Objects.nonNull(data.getFaredm()) && data.getFaredm().length() > 20) {
            log.warn("法人代码长度超限,银承协议编号:[{}]", data.getCdxybh());
            return false;
        }

        return true;
    }

    /**
     * 批量插入处理
     * 分批处理大量数据，避免内存溢出和数据库连接超时
     *
     * @param insertList 待插入数据列表
     * @param prefixLog 日志前缀
     */
    private void processBatchInsert(List<LbHCoreBcdhpDo> insertList, String prefixLog) {
        if (CollectionUtil.isEmpty(insertList)) {
            return;
        }

        int totalSize = insertList.size();
        int batchCount = (totalSize + BATCH_SIZE - 1) / BATCH_SIZE;

        log.info(prefixLog + "开始批量插入数据,总数据量:[{}],分批数量:[{}],每批大小:[{}]", totalSize, batchCount,
            BATCH_SIZE);

        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * BATCH_SIZE;
            int toIndex = Math.min(fromIndex + BATCH_SIZE, totalSize);
            List<LbHCoreBcdhpDo> batchList = insertList.subList(fromIndex, toIndex);

            try {
                lbHCoreBcdhpDao.insertList(batchList);
                log.debug(prefixLog + "批次[{}]插入完成,数据量:[{}]", i + 1, batchList.size());
            } catch (Exception e) {
                log.error(prefixLog + "批次[{}]插入失败,数据量:[{}]", i + 1, batchList.size(), e);
                throw e;
            }
        }
    }

}
