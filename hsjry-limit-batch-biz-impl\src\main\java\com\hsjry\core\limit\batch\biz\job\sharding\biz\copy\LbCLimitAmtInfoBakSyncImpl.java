package com.hsjry.core.limit.batch.biz.job.sharding.biz.copy;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.biz.convert.copy.LbCLimitAmtInfoConverter;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.dal.dao.intf.LbCLimitAmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitAmtInfoDo;
import com.hsjry.core.limit.center.dal.dao.intf.CustLimitAmtInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitAmtInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitAmtInfoQuery;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
/**
 * 额度实例金额信息数据同步到[核心系统-落地表-额度实例金额信息表]
 * 从源表lc_cust_limit_amt_info同步数据到目标表lb_c_limit_amt_info
 *
 * <AUTHOR>
 * @date 2025/7/9
 */
@Slf4j
@Service("lbCLimitAmtInfoBakSyncImpl")
@RequiredArgsConstructor
public class LbCLimitAmtInfoBakSyncImpl extends AbstractShardingPrepareBiz<CustLimitAmtInfoQuery>
    implements JobCoreBusiness<LcCustLimitAmtInfoDo> {

    private final CustLimitAmtInfoBatchDao custLimitAmtInfoBatchDao;
    private final LbCLimitAmtInfoDao lbCLimitAmtInfoDao;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.C_LIMIT_AMT_INFO_BAK_SYNC;
    }

    @Override
    public Integer selectCountByCurrentGroupFromDb(CustLimitAmtInfoQuery query) {
        Integer count = custLimitAmtInfoBatchDao.selectCountByCurrentGroup(query);
        return count != null ? count : 0;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "====================== 分片逻辑 start ================================================");

        List<JobShared> jobSharedList = new ArrayList<>();
        // SQL批处理数量，暂定为分片数量
        Integer batchFixNum = jobInitDto.getFixNum();
        // 当前分组的最大值，为下次批处理的最小值
        LcCustLimitAmtInfoDo maxLimitInfoDo = new LcCustLimitAmtInfoDo();

        // 构造查询条件，查询当前分批处理的排序最大对象
        CustLimitAmtInfoQuery query = CustLimitAmtInfoQuery.builder().tenantId(AppParamUtil.getTenantId()).offset(
            batchFixNum - 1).limit(1).build();

        // 分片流水
        int batchNum = 0;
        while (maxLimitInfoDo != null) {
            log.info("当前maxLimitInfoDo非空，进入循环");
            query.setCustLimitId(maxLimitInfoDo.getCustLimitId());
            maxLimitInfoDo = custLimitAmtInfoBatchDao.selectFirstOne(query);
            log.info("查询后maxLimitInfoDo: " + maxLimitInfoDo);
            // 统计分片数量
            batchNum = countBatchNum(batchFixNum, query, maxLimitInfoDo, batchNum, jobInitDto, jobSharedList,
                query.getCustLimitId(), false);
        }

        log.info(prefixLog + "====================== 分片逻辑 end ================================================");
        log.info(prefixLog + "额度实例信息同步分片任务生成完成,共{}个分片", jobSharedList.size());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LcCustLimitAmtInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始查询分片数据,分片号:{}", jobShared.getBatchNum());

        ShardingResult<LcCustLimitAmtInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        // 创建查询条件
        CustLimitAmtInfoQuery query = CustLimitAmtInfoQuery.builder().tenantId(AppParamUtil.getTenantId())//
            .offset(jobShared.getOffset()).limit(jobShared.getLimit()).build();

        log.info(prefixLog + "=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");

        // 查询分片数据
        List<LcCustLimitAmtInfoDo> dataList = custLimitAmtInfoBatchDao.selectShardList(query);
        shardingResult.setShardingResultList(dataList);

        log.info("分片数据查询完成,分片号:{},数据量:{}", jobShared.getBatchNum(),
            CollectionUtil.isEmpty(dataList) ? 0 : dataList.size());

        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcCustLimitAmtInfoDo> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        Integer dataSize = shardingResult.getShardingResultList().size();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        List<LcCustLimitAmtInfoDo> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "=========分片执行结束:[{}]数量为空===========", batchNum);
            return;
        }

        log.info(prefixLog + "=========分片执行开始:[{}]数量为[{}]===========", batchNum, dataSize);

        try {
            LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
            // 只在第一个分片中清空目标表
            if (sliceBatchSerialDo.getBatchNum() == 1) {
                log.info(prefixLog + "第一个分片,开始清空目标表lb_c_limit_amt_info");
                lbCLimitAmtInfoDao.deleteAll();
                log.info(prefixLog + "目标表lb_c_limit_amt_info清空完成");
            }

            // 数据转换和插入
            List<LbCLimitAmtInfoDo> targetDataList = convertSourceToTarget(dataList);
            if (CollectionUtil.isNotEmpty(targetDataList)) {
                lbCLimitAmtInfoDao.insertList(targetDataList);
                log.info(prefixLog + "成功插入{}条数据到目标表", targetDataList.size());
            }

            // 更新分片流水成功
            normalUpdateSliceSerial(dataSize, sliceBatchSerialDo);

        } catch (Exception e) {
            log.error(prefixLog + "分片执行异常:[{}]", batchNum, e);
            throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                EnumBatchJobError.SYSTEM_ERR.getDescription());
        }

        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataSize);
    }

    /**
     * 数据转换：从LcCustLimitInfoDo转换为LbCLimitInfoDo
     */
    private List<LbCLimitAmtInfoDo> convertSourceToTarget(List<LcCustLimitAmtInfoDo> sourceList) {
        if (CollectionUtil.isEmpty(sourceList)) {
            return Lists.newArrayList();
        }

        return LbCLimitAmtInfoConverter.doList2CopyList(sourceList);
    }
}
