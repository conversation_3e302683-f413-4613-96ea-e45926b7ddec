package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitRelationQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度实例关联数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 03:09:49
 */
public interface LcCustLimitRelationBatchDao extends IBaseDao<LcCustLimitRelationDo> {
    /**
     * 分页查询额度实例关联
     *
     * @param lcCustLimitRelationQuery 条件
     * @return PageInfo<LcCustLimitRelationDo>
     */
    PageInfo<LcCustLimitRelationDo> selectPage(LcCustLimitRelationQuery lcCustLimitRelationQuery, PageParam pageParam);

    /**
     * 根据key查询额度实例关联
     *
     * @param limitRelationId
     * @return
     */
    LcCustLimitRelationDo selectByKey(String limitRelationId);

    /**
     * 根据key删除额度实例关联
     *
     * @param limitRelationId
     * @return
     */
    int deleteByKey(String limitRelationId);

    /**
     * 查询额度实例关联信息
     *
     * @param lcCustLimitRelationQuery 条件
     * @return List<LcCustLimitRelationDo>
     */
    List<LcCustLimitRelationDo> selectByExample(LcCustLimitRelationQuery lcCustLimitRelationQuery);

    /**
     * 新增额度实例关联信息
     *
     * @param lcCustLimitRelation 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitRelationDo lcCustLimitRelation);

    /**
     * 修改额度实例关联信息
     *
     * @param lcCustLimitRelation
     * @return
     */
    int updateBySelective(LcCustLimitRelationDo lcCustLimitRelation);

    /**
     * 修改额度实例关联信息
     *
     * @param lcCustLimitRelation
     * @param lcCustLimitRelationQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitRelationDo lcCustLimitRelation,
        LcCustLimitRelationQuery lcCustLimitRelationQuery);

    /**
     * 查询额度实例关联信息
     *
     * @param lcCustLimitRelationQuery 条件
     * @return List<LcCustLimitRelationDo>
     */
    int deleteByExample(LcCustLimitRelationQuery lcCustLimitRelationQuery);
}

