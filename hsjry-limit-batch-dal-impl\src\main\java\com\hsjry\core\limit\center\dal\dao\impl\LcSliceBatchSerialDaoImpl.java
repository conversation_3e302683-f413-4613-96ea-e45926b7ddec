package com.hsjry.core.limit.center.dal.dao.impl;

import com.hsjry.core.limit.center.dal.dao.mapper.LcSliceBatchSerialMapper;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialExample;

import org.springframework.stereotype.Repository;

import java.util.List;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.query.LcSliceBatchSerialQuery;
import com.hsjry.core.limit.center.dal.dao.intf.LcSliceBatchSerialDao;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialKeyDo;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.business.date.BusinessDateUtil;

/**
 * 额度分片流水数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2023-03-13 12:30:49
 */
@Repository
public class LcSliceBatchSerialDaoImpl extends AbstractBaseDaoImpl<LcSliceBatchSerialDo, LcSliceBatchSerialMapper>
    implements LcSliceBatchSerialDao {
    /**
     * 分页查询
     *
     * @param lcSliceBatchSerial 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LcSliceBatchSerialDo> selectPage(LcSliceBatchSerialQuery lcSliceBatchSerial, PageParam pageParam) {
        LcSliceBatchSerialExample example = buildExample(lcSliceBatchSerial);
        return PageHelper.<LcSliceBatchSerialDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度分片流水
     *
     * @param batchSerialNo
     * @param batchNum
     * @return
     */
    @Override
    public LcSliceBatchSerialDo selectByKey(String batchSerialNo, Integer batchNum) {
        LcSliceBatchSerialKeyDo lcSliceBatchSerialKeyDo = new LcSliceBatchSerialKeyDo();
        lcSliceBatchSerialKeyDo.setBatchSerialNo(batchSerialNo);
        lcSliceBatchSerialKeyDo.setBatchNum(batchNum);
        lcSliceBatchSerialKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lcSliceBatchSerialKeyDo);
    }

    /**
     * 根据key删除额度分片流水
     *
     * @param batchSerialNo
     * @param batchNum
     * @return
     */
    @Override
    public int deleteByKey(String batchSerialNo, Integer batchNum) {
        LcSliceBatchSerialKeyDo lcSliceBatchSerialKeyDo = new LcSliceBatchSerialKeyDo();
        lcSliceBatchSerialKeyDo.setBatchSerialNo(batchSerialNo);
        lcSliceBatchSerialKeyDo.setBatchNum(batchNum);
        lcSliceBatchSerialKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lcSliceBatchSerialKeyDo);
    }

    /**
     * 查询额度分片流水信息
     *
     * @param lcSliceBatchSerial 条件
     * @return List<LcSliceBatchSerialDo>
     */
    @Override
    public List<LcSliceBatchSerialDo> selectByExample(LcSliceBatchSerialQuery lcSliceBatchSerial) {
        return getMapper().selectByExample(buildExample(lcSliceBatchSerial));
    }

    /**
     * 新增额度分片流水信息
     *
     * @param lcSliceBatchSerial 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LcSliceBatchSerialDo lcSliceBatchSerial) {
        if (lcSliceBatchSerial == null) {
            return -1;
        }
        lcSliceBatchSerial.setGlobalSerialNo(AppParamUtil.getSerialNo());
        lcSliceBatchSerial.setInboundSerialNo(AppParamUtil.getInboundSerialNo());
        lcSliceBatchSerial.setBizDatetime(AppParamUtil.getTransDatetime());
        lcSliceBatchSerial.setChannelNo(AppParamUtil.getChannelNo());
        lcSliceBatchSerial.setSerialNo(AppParamUtil.getTransSerialNo());

        lcSliceBatchSerial.setCreateTime(BusinessDateUtil.getDate());
        lcSliceBatchSerial.setUpdateTime(BusinessDateUtil.getDate());
        lcSliceBatchSerial.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lcSliceBatchSerial);
    }

    /**
     * 修改额度分片流水信息
     *
     * @param lcSliceBatchSerial
     * @return
     */
    @Override
    public int updateBySelective(LcSliceBatchSerialDo lcSliceBatchSerial) {
        if (lcSliceBatchSerial == null) {
            return -1;
        }
        lcSliceBatchSerial.setUpdateTime(BusinessDateUtil.getDate());
        lcSliceBatchSerial.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lcSliceBatchSerial);
    }

    @Override
    public int updateBySelectiveByExample(LcSliceBatchSerialDo lcSliceBatchSerial,
        LcSliceBatchSerialQuery lcSliceBatchSerialQuery) {
        lcSliceBatchSerial.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lcSliceBatchSerial, buildExample(lcSliceBatchSerialQuery));
    }

    /**
     * 构建额度分片流水Example信息
     *
     * @param lcSliceBatchSerial
     * @return
     */
    public LcSliceBatchSerialExample buildExample(LcSliceBatchSerialQuery lcSliceBatchSerial) {
        LcSliceBatchSerialExample example = new LcSliceBatchSerialExample();
        LcSliceBatchSerialExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lcSliceBatchSerial != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lcSliceBatchSerial.getTradeCode())) {
                criteria.andTradeCodeEqualTo(lcSliceBatchSerial.getTradeCode());
            }
            if (null != lcSliceBatchSerial.getFailureTimes()) {
                criteria.andFailureTimesEqualTo(lcSliceBatchSerial.getFailureTimes());
            }
            if (StringUtil.isNotEmpty(lcSliceBatchSerial.getFinishFlag())) {
                criteria.andFinishFlagEqualTo(lcSliceBatchSerial.getFinishFlag());
            }
            if (StringUtil.isNotEmpty(lcSliceBatchSerial.getSharedNote())) {
                criteria.andSharedNoteEqualTo(lcSliceBatchSerial.getSharedNote());
            }
            if (StringUtil.isNotEmpty(lcSliceBatchSerial.getSharedStatistics())) {
                criteria.andSharedStatisticsEqualTo(lcSliceBatchSerial.getSharedStatistics());
            }
            if (null != lcSliceBatchSerial.getSharedPassCount()) {
                criteria.andSharedPassCountEqualTo(lcSliceBatchSerial.getSharedPassCount());
            }
            if (StringUtil.isNotEmpty(lcSliceBatchSerial.getSharedDetail())) {
                criteria.andSharedDetailEqualTo(lcSliceBatchSerial.getSharedDetail());
            }
            if (StringUtil.isNotEmpty(lcSliceBatchSerial.getExecIp())) {
                criteria.andExecIpEqualTo(lcSliceBatchSerial.getExecIp());
            }
            if (null != lcSliceBatchSerial.getExecDate()) {
                criteria.andExecDateEqualTo(lcSliceBatchSerial.getExecDate());
            }
            if (StringUtil.isNotEmpty(lcSliceBatchSerial.getExtSerialId())) {
                criteria.andExtSerialIdEqualTo(lcSliceBatchSerial.getExtSerialId());
            }
            if (StringUtil.isNotEmpty(lcSliceBatchSerial.getFileStatus())) {
                criteria.andFileStatusEqualTo(lcSliceBatchSerial.getFileStatus());
            }
            if (StringUtil.isNotEmpty(lcSliceBatchSerial.getSharedStatus())) {
                criteria.andSharedStatusEqualTo(lcSliceBatchSerial.getSharedStatus());
            }
            if (StringUtil.isNotEmpty(lcSliceBatchSerial.getGlobalSerialNo())) {
                criteria.andGlobalSerialNoEqualTo(lcSliceBatchSerial.getGlobalSerialNo());
            }
            if (null != lcSliceBatchSerial.getBatchNum()) {
                criteria.andBatchNumEqualTo(lcSliceBatchSerial.getBatchNum());
            }
            if (StringUtil.isNotEmpty(lcSliceBatchSerial.getBatchSerialNo())) {
                criteria.andBatchSerialNoEqualTo(lcSliceBatchSerial.getBatchSerialNo());
            }
            if (null != lcSliceBatchSerial.getInboundSerialDatetimeBegin()) {
                criteria.andInboundSerialDatetimeGreaterThanOrEqualTo(
                    lcSliceBatchSerial.getInboundSerialDatetimeBegin());
            }
            if (null != lcSliceBatchSerial.getInboundSerialDatetimeEnd()) {
                criteria.andInboundSerialDatetimeLessThanOrEqualTo(lcSliceBatchSerial.getInboundSerialDatetimeEnd());
            }
            if (StringUtil.isNotEmpty(lcSliceBatchSerial.getInboundSerialNo())) {
                criteria.andInboundSerialNoEqualTo(lcSliceBatchSerial.getInboundSerialNo());
            }
            if (null != lcSliceBatchSerial.getBizDatetimeBegin()) {
                criteria.andBizDatetimeGreaterThanOrEqualTo(lcSliceBatchSerial.getBizDatetimeBegin());
            }
            if (null != lcSliceBatchSerial.getBizDatetimeEnd()) {
                criteria.andBizDatetimeLessThanOrEqualTo(lcSliceBatchSerial.getBizDatetimeEnd());
            }
            if (StringUtil.isNotEmpty(lcSliceBatchSerial.getChannelNo())) {
                criteria.andChannelNoEqualTo(lcSliceBatchSerial.getChannelNo());
            }
            if (StringUtil.isNotEmpty(lcSliceBatchSerial.getSerialNo())) {
                criteria.andSerialNoEqualTo(lcSliceBatchSerial.getSerialNo());
            }
        }
        buildExampleExt(lcSliceBatchSerial, criteria);
        return example;
    }

    /**
     * 构建额度分片流水ExampleExt方法
     *
     * @param lcSliceBatchSerial
     * @return
     */
    public void buildExampleExt(LcSliceBatchSerialQuery lcSliceBatchSerial,
        LcSliceBatchSerialExample.Criteria criteria) {

        //自定义实现
    }

}
