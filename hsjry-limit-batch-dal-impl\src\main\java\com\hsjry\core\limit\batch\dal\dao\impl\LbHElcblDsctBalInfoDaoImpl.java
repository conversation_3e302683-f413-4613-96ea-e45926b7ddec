package com.hsjry.core.limit.batch.dal.dao.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbHElcblDsctBalInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbHElcblDsctBalInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblDsctBalInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblDsctBalInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblDsctBalInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHElcblDsctBalInfoQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-历史表-贴现余额信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Repository
public class LbHElcblDsctBalInfoDaoImpl extends AbstractBaseDaoImpl<LbHElcblDsctBalInfoDo, LbHElcblDsctBalInfoMapper>
    implements LbHElcblDsctBalInfoDao {
    /**
     * 分页查询
     *
     * @param lbHElcblDsctBalInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbHElcblDsctBalInfoDo> selectPage(LbHElcblDsctBalInfoQuery lbHElcblDsctBalInfo,
        PageParam pageParam) {
        LbHElcblDsctBalInfoExample example = buildExample(lbHElcblDsctBalInfo);
        return PageHelper.<LbHElcblDsctBalInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询电票系统-历史表-贴现余额信息
     *
     * @param dicCno
     * @param billNo
     * @param userId
     * @param dataDate
     * @return
     */
    @Override
    public LbHElcblDsctBalInfoDo selectByKey(String dicCno, String billNo, String userId, String dataDate) {
        LbHElcblDsctBalInfoKeyDo lbHElcblDsctBalInfoKeyDo = new LbHElcblDsctBalInfoKeyDo();
        lbHElcblDsctBalInfoKeyDo.setDicCno(dicCno);
        lbHElcblDsctBalInfoKeyDo.setBillNo(billNo);
        lbHElcblDsctBalInfoKeyDo.setUserId(userId);
        lbHElcblDsctBalInfoKeyDo.setDataDate(dataDate);
        return getMapper().selectByPrimaryKey(lbHElcblDsctBalInfoKeyDo);
    }

    /**
     * 根据key删除电票系统-历史表-贴现余额信息
     *
     * @param dicCno
     * @param billNo
     * @param userId
     * @param dataDate
     * @return
     */
    @Override
    public int deleteByKey(String dicCno, String billNo, String userId, String dataDate) {
        LbHElcblDsctBalInfoKeyDo lbHElcblDsctBalInfoKeyDo = new LbHElcblDsctBalInfoKeyDo();
        lbHElcblDsctBalInfoKeyDo.setDicCno(dicCno);
        lbHElcblDsctBalInfoKeyDo.setBillNo(billNo);
        lbHElcblDsctBalInfoKeyDo.setUserId(userId);
        lbHElcblDsctBalInfoKeyDo.setDataDate(dataDate);
        return getMapper().deleteByPrimaryKey(lbHElcblDsctBalInfoKeyDo);
    }

    /**
     * 查询电票系统-历史表-贴现余额信息信息
     *
     * @param lbHElcblDsctBalInfo 条件
     * @return List<LbHElcblDsctBalInfoDo>
     */
    @Override
    public List<LbHElcblDsctBalInfoDo> selectByExample(LbHElcblDsctBalInfoQuery lbHElcblDsctBalInfo) {
        return getMapper().selectByExample(buildExample(lbHElcblDsctBalInfo));
    }

    /**
     * 新增电票系统-历史表-贴现余额信息信息
     *
     * @param lbHElcblDsctBalInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbHElcblDsctBalInfoDo lbHElcblDsctBalInfo) {
        if (lbHElcblDsctBalInfo == null) {
            return -1;
        }
        // 获取日期时间对象
        LocalDateTime localDateTime = LocalDateTime.now();
        // 转换为字符串
        String dateTimeString = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        lbHElcblDsctBalInfo.setCreateTime(dateTimeString);
        lbHElcblDsctBalInfo.setUpdateTime(dateTimeString);
        return getMapper().insertSelective(lbHElcblDsctBalInfo);
    }

    /**
     * 修改电票系统-历史表-贴现余额信息信息
     *
     * @param lbHElcblDsctBalInfo
     * @return
     */
    @Override
    public int updateBySelective(LbHElcblDsctBalInfoDo lbHElcblDsctBalInfo) {
        if (lbHElcblDsctBalInfo == null) {
            return -1;
        }
        // 获取日期时间对象
        LocalDateTime localDateTime = LocalDateTime.now();
        // 转换为字符串
        String dateTimeString = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        lbHElcblDsctBalInfo.setUpdateTime(dateTimeString);
        return getMapper().updateByPrimaryKeySelective(lbHElcblDsctBalInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbHElcblDsctBalInfoDo lbHElcblDsctBalInfo,
        LbHElcblDsctBalInfoQuery lbHElcblDsctBalInfoQuery) {

        // 获取日期时间对象
        LocalDateTime localDateTime = LocalDateTime.now();
        // 转换为字符串
        String dateTimeString = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        lbHElcblDsctBalInfo.setUpdateTime(dateTimeString);
        return getMapper().updateByExampleSelective(lbHElcblDsctBalInfo, buildExample(lbHElcblDsctBalInfoQuery));
    }

    /**
     * 构建电票系统-历史表-贴现余额信息Example信息
     *
     * @param lbHElcblDsctBalInfo
     * @return
     */
    public LbHElcblDsctBalInfoExample buildExample(LbHElcblDsctBalInfoQuery lbHElcblDsctBalInfo) {
        LbHElcblDsctBalInfoExample example = new LbHElcblDsctBalInfoExample();
        LbHElcblDsctBalInfoExample.Criteria criteria = example.createCriteria();
        if (lbHElcblDsctBalInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbHElcblDsctBalInfo.getOrgNo())) {
                criteria.andOrgNoEqualTo(lbHElcblDsctBalInfo.getOrgNo());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctBalInfo.getDicCno())) {
                criteria.andDicCnoEqualTo(lbHElcblDsctBalInfo.getDicCno());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctBalInfo.getBillNo())) {
                criteria.andBillNoEqualTo(lbHElcblDsctBalInfo.getBillNo());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctBalInfo.getBillRangeStart())) {
                criteria.andBillRangeStartEqualTo(lbHElcblDsctBalInfo.getBillRangeStart());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctBalInfo.getBillRangeEnd())) {
                criteria.andBillRangeEndEqualTo(lbHElcblDsctBalInfo.getBillRangeEnd());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctBalInfo.getUserId())) {
                criteria.andUserIdEqualTo(lbHElcblDsctBalInfo.getUserId());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctBalInfo.getUserName())) {
                criteria.andUserNameEqualTo(lbHElcblDsctBalInfo.getUserName());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctBalInfo.getCurrency())) {
                criteria.andCurrencyEqualTo(lbHElcblDsctBalInfo.getCurrency());
            }
            if (null != lbHElcblDsctBalInfo.getDiscountAmt()) {
                criteria.andDiscountAmtEqualTo(lbHElcblDsctBalInfo.getDiscountAmt());
            }
            if (null != lbHElcblDsctBalInfo.getDiscountBal()) {
                criteria.andDiscountBalEqualTo(lbHElcblDsctBalInfo.getDiscountBal());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctBalInfo.getStartDate())) {
                criteria.andStartDateEqualTo(lbHElcblDsctBalInfo.getStartDate());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctBalInfo.getEndDate())) {
                criteria.andEndDateEqualTo(lbHElcblDsctBalInfo.getEndDate());
            }
            if (StringUtil.isNotEmpty(lbHElcblDsctBalInfo.getDataDate())) {
                criteria.andDataDateEqualTo(lbHElcblDsctBalInfo.getDataDate());
            }
        }
        buildExampleExt(lbHElcblDsctBalInfo, criteria);
        return example;
    }

    /**
     * 构建电票系统-历史表-贴现余额信息ExampleExt方法
     *
     * @param lbHElcblDsctBalInfo
     * @return
     */
    public void buildExampleExt(LbHElcblDsctBalInfoQuery lbHElcblDsctBalInfo,
        LbHElcblDsctBalInfoExample.Criteria criteria) {

        //自定义实现
    }
    // 以下方法需要添加到 LbHElcblDsctBalInfoDaoImpl 实现类中

    /**
     * 批量插入电票系统-历史表-贴现余额信息
     *
     * @param lbHElcblDsctBalInfoList 批量数据
     * @return int
     */
    @Override
    public int insertList(List<LbHElcblDsctBalInfoDo> lbHElcblDsctBalInfoList) {
        if (lbHElcblDsctBalInfoList.isEmpty()) {
            return 0;
        }
        return getMapper().insertList(lbHElcblDsctBalInfoList);
    }

    /**
     * 清空电票系统-历史表-贴现余额信息所有数据
     *
     * @return int
     */
    @Override
    public int deleteAll() {
        return getMapper().deleteAll();
    }
    @Override
    public int deleteByDataDate(String dataDate) {
        return getMapper().deleteByDataDate(dataDate);
    }
}
