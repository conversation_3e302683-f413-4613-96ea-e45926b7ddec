package com.hsjry.core.limit.center.dal.dao.intf;

import com.hsjry.core.limit.center.dal.dao.model.LcSingleLimitTemplateDo;
import com.hsjry.core.limit.center.dal.dao.query.LcSingleLimitTemplateQuery;


import java.util.List;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 单一限额模板数据库操作接口
 * 
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
public interface LcSingleLimitTemplateDao extends IBaseDao<LcSingleLimitTemplateDo> {
    /**
     * 分页查询单一限额模板
     *
     * @param lcSingleLimitTemplateQuery 条件
     * @return PageInfo<LcSingleLimitTemplateDo>
     */
    PageInfo<LcSingleLimitTemplateDo> selectPage(LcSingleLimitTemplateQuery lcSingleLimitTemplateQuery,PageParam pageParam);
	  /**
     * 根据key查询单一限额模板
     *
     	 	 * @param singleLimitTemplateId
	 	 	 	      * @return
     */
	LcSingleLimitTemplateDo selectByKey(String singleLimitTemplateId);
    /**
     * 根据key删除单一限额模板
     *
               * @param singleLimitTemplateId
                      * @return
     */
    int deleteByKey(String singleLimitTemplateId);

    /**
     * 查询单一限额模板信息
     *
     * @param lcSingleLimitTemplateQuery 条件
     * @return List<LcSingleLimitTemplateDo>
     */
    List<LcSingleLimitTemplateDo> selectByExample(LcSingleLimitTemplateQuery lcSingleLimitTemplateQuery);

    /**
     * 新增单一限额模板信息
     *
     * @param lcSingleLimitTemplate 条件
     * @return int>
     */
    int insertBySelective(LcSingleLimitTemplateDo lcSingleLimitTemplate);

    /**
     * 修改单一限额模板信息
     *
     * @param lcSingleLimitTemplate
     * @return
     */
    int updateBySelective(LcSingleLimitTemplateDo lcSingleLimitTemplate);
    /**
     * 修改单一限额模板信息
     *
     * @param lcSingleLimitTemplate
     * @param lcSingleLimitTemplateQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcSingleLimitTemplateDo lcSingleLimitTemplate,
    LcSingleLimitTemplateQuery lcSingleLimitTemplateQuery);
}
