package com.hsjry.core.limit.center.core.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.center.core.bo.CustLimitAmtInfoBo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitAmtInfoDo;

@Mapper(componentModel = "spring")
public interface LcCustLimitAmtInfoDoCnvs {
    LcCustLimitAmtInfoDoCnvs INSTANCE = Mappers.getMapper(LcCustLimitAmtInfoDoCnvs.class);

    /**  [额度实例金额信息Do] 和 [额度实例金额信息Bo]转换 开始 *****/
    /** 将[额度实例金额信息Do]转换为[额度实例金额信息Bo] */
    @Mappings(
        {@Mapping(target = "tenantId", expression = "java(com.hsjry.base.common.utils.AppParamUtil.getTenantId())"),
            @Mapping(target = "createTime",
                expression = "java(com.hsjry.lang.business.date.BusinessDateUtil.getDate())"),
            @Mapping(target = "updateTime",
                expression = "java(com.hsjry.lang.business.date.BusinessDateUtil.getDate())")})
    LcCustLimitAmtInfoDo cnvsBoToDo(CustLimitAmtInfoBo bo);

    /** 将[额度实例金额信息Do列表]转换为[额度实例金额信息Bo列表] */
    List<LcCustLimitAmtInfoDo> cnvsBoListToDoList(List<CustLimitAmtInfoBo> boList);

    /** 将[额度实例金额信息Do]转换为[额度实例金额信息Bo] */
    CustLimitAmtInfoBo cnvsDoToBo(LcCustLimitAmtInfoDo dataObject);

    /** 将[额度实例金额信息Do列表]转换为[额度实例金额信息Bo列表] */
    List<CustLimitAmtInfoBo> cnvsDoListToBoList(List<LcCustLimitAmtInfoDo> doList);
    /**  [额度实例金额信息Do] 和 [额度实例金额信息Bo]转换 结束 *****/
}
