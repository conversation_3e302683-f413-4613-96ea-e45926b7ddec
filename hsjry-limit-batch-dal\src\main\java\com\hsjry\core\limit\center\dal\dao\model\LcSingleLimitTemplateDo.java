package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 单一限额模板Do
 *
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
@Table(name = "lc_single_limit_template")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcSingleLimitTemplateDo extends LcSingleLimitTemplateKeyDo implements Serializable {
    private static final long serialVersionUID = 1673314101088157699L;
    /** 校验策略 */
    @Column(name = "check_strategy")
    private String checkStrategy;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 模板名称;EnumSingleLimitTemplateStatus（010-草稿、020-上架、030-下架） */
    @Column(name = "template_name")
    private String templateName;
    /** 限额模板状态 */
    @Column(name = "template_status")
    private String templateStatus;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 限额使用阶段;EnumSingleLimitUseStage（001-授信阶段、002-用信阶段） */
    @Column(name = "use_stage")
    private String useStage;
}
