package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcEntityStatisticsInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcEntityStatisticsInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 实体统计信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcEntityStatisticsInfoDao extends IBaseDao<LcEntityStatisticsInfoDo> {
    /**
     * 分页查询实体统计信息
     *
     * @param lcEntityStatisticsInfoQuery 条件
     * @return PageInfo<LcEntityStatisticsInfoDo>
     */
    PageInfo<LcEntityStatisticsInfoDo> selectPage(LcEntityStatisticsInfoQuery lcEntityStatisticsInfoQuery,
        PageParam pageParam);

    /**
     * 根据key查询实体统计信息
     *
     * @param statisticsId
     * @return
     */
    LcEntityStatisticsInfoDo selectByKey(String statisticsId);

    /**
     * 根据key删除实体统计信息
     *
     * @param statisticsId
     * @return
     */
    int deleteByKey(String statisticsId);

    /**
     * 查询实体统计信息信息
     *
     * @param lcEntityStatisticsInfoQuery 条件
     * @return List<LcEntityStatisticsInfoDo>
     */
    List<LcEntityStatisticsInfoDo> selectByExample(LcEntityStatisticsInfoQuery lcEntityStatisticsInfoQuery);

    /**
     * 新增实体统计信息信息
     *
     * @param lcEntityStatisticsInfo 条件
     * @return int>
     */
    int insertBySelective(LcEntityStatisticsInfoDo lcEntityStatisticsInfo);

    /**
     * 修改实体统计信息信息
     *
     * @param lcEntityStatisticsInfo
     * @return
     */
    int updateBySelective(LcEntityStatisticsInfoDo lcEntityStatisticsInfo);

    /**
     * 修改实体统计信息信息
     *
     * @param lcEntityStatisticsInfo
     * @param lcEntityStatisticsInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcEntityStatisticsInfoDo lcEntityStatisticsInfo,
        LcEntityStatisticsInfoQuery lcEntityStatisticsInfoQuery);
}
