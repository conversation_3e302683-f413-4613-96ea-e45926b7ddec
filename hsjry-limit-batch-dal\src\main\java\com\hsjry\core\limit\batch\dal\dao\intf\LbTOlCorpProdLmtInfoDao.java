package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTOlCorpProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTOlCorpProdLmtInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-中间表-对公产品层额度信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
public interface LbTOlCorpProdLmtInfoDao extends IBaseDao<LbTOlCorpProdLmtInfoDo> {
    /**
     * 分页查询网贷系统-中间表-对公产品层额度信息
     *
     * @param lbTOlCorpProdLmtInfoQuery 条件
     * @return PageInfo<LbTOlCorpProdLmtInfoDo>
     */
    PageInfo<LbTOlCorpProdLmtInfoDo> selectPage(LbTOlCorpProdLmtInfoQuery lbTOlCorpProdLmtInfoQuery,
        PageParam pageParam);

    /**
     * 根据key查询网贷系统-中间表-对公产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTOlCorpProdLmtInfoDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除网贷系统-中间表-对公产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询网贷系统-中间表-对公产品层额度信息信息
     *
     * @param lbTOlCorpProdLmtInfoQuery 条件
     * @return List<LbTOlCorpProdLmtInfoDo>
     */
    List<LbTOlCorpProdLmtInfoDo> selectByExample(LbTOlCorpProdLmtInfoQuery lbTOlCorpProdLmtInfoQuery);

    /**
     * 新增网贷系统-中间表-对公产品层额度信息信息
     *
     * @param lbTOlCorpProdLmtInfo 条件
     * @return int>
     */
    int insertBySelective(LbTOlCorpProdLmtInfoDo lbTOlCorpProdLmtInfo);

    /**
     * 修改网贷系统-中间表-对公产品层额度信息信息
     *
     * @param lbTOlCorpProdLmtInfo
     * @return
     */
    int updateBySelective(LbTOlCorpProdLmtInfoDo lbTOlCorpProdLmtInfo);

    /**
     * 修改网贷系统-中间表-对公产品层额度信息信息
     *
     * @param lbTOlCorpProdLmtInfo
     * @param lbTOlCorpProdLmtInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTOlCorpProdLmtInfoDo lbTOlCorpProdLmtInfo,
        LbTOlCorpProdLmtInfoQuery lbTOlCorpProdLmtInfoQuery);

    /**
     * 将网贷对公客户额度同步数据插入到产品层额度信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_ol_corp_prod_lmt_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    int insertOlCorpProdLmtInfo(List<String> templateNodeIdList, List<String> custLimitIdList);

    /**
     * 更新额度实例金额信息表
     * 根据网贷系统中间表数据更新LC_CUST_LIMIT_AMT_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    int updateCustLimitAmtInfo(List<String> custLimitIdList);
}
