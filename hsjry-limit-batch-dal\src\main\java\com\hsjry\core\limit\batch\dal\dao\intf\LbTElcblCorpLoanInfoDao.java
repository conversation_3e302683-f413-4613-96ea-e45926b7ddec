package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpLoanInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTElcblCorpLoanInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-中间表-贴现对公借据信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-29 02:46:35
 */
public interface LbTElcblCorpLoanInfoDao extends IBaseDao<LbTElcblCorpLoanInfoDo> {
    /**
     * 分页查询电票系统-中间表-贴现对公借据信息
     *
     * @param lbTElcblCorpLoanInfoQuery 条件
     * @return PageInfo<LbTElcblCorpLoanInfoDo>
     */
    PageInfo<LbTElcblCorpLoanInfoDo> selectPage(LbTElcblCorpLoanInfoQuery lbTElcblCorpLoanInfoQuery,
        PageParam pageParam);

    /**
     * 根据key查询电票系统-中间表-贴现对公借据信息
     *
     * @param custNo
     * @param custLimitId
     * @param entityId
     * @return
     */
    LbTElcblCorpLoanInfoDo selectByKey(String custNo, String custLimitId, String entityId);

    /**
     * 根据key删除电票系统-中间表-贴现对公借据信息
     *
     * @param custNo
     * @param custLimitId
     * @param entityId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId, String entityId);

    /**
     * 查询电票系统-中间表-贴现对公借据信息信息
     *
     * @param lbTElcblCorpLoanInfoQuery 条件
     * @return List<LbTElcblCorpLoanInfoDo>
     */
    List<LbTElcblCorpLoanInfoDo> selectByExample(LbTElcblCorpLoanInfoQuery lbTElcblCorpLoanInfoQuery);

    /**
     * 新增电票系统-中间表-贴现对公借据信息信息
     *
     * @param lbTElcblCorpLoanInfo 条件
     * @return int>
     */
    int insertBySelective(LbTElcblCorpLoanInfoDo lbTElcblCorpLoanInfo);

    /**
     * 修改电票系统-中间表-贴现对公借据信息信息
     *
     * @param lbTElcblCorpLoanInfo
     * @return
     */
    int updateBySelective(LbTElcblCorpLoanInfoDo lbTElcblCorpLoanInfo);

    /**
     * 修改电票系统-中间表-贴现对公借据信息信息
     *
     * @param lbTElcblCorpLoanInfo
     * @param lbTElcblCorpLoanInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTElcblCorpLoanInfoDo lbTElcblCorpLoanInfo,
        LbTElcblCorpLoanInfoQuery lbTElcblCorpLoanInfoQuery);

    /**
     * 将电票对公客户额度同步数据插入到借据信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_elcbl_corp_loan_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    int insertElcblCorpLoanInfo(List<String> templateNodeIdList, List<String> custLimitIdList);

    /**
     * 更新实体信息表
     * 根据电票系统中间表数据更新LC_ENTITY_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    int updateEntityInfo(List<String> custLimitIdList);

    /**
     * 更新实体操作流水表
     * 根据电票系统中间表数据更新LC_ENTITY_OPERATE_SERIAL表
     *
     * @return int 更新的记录数
     */
    int updateEntityOperateSerial();
}
