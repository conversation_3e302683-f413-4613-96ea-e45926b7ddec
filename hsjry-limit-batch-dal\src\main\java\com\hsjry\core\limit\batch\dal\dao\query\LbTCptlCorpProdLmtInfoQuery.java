package com.hsjry.core.limit.batch.dal.dao.query;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * 资金系统-中间表-对公客户产品层额度信息查询条件
 *
 * <AUTHOR>
 * @date 2025-07-30 02:03:23
 */
@Data
@Builder
public class LbTCptlCorpProdLmtInfoQuery implements Serializable {

    /** serialVersionUID */
    private static final long serialVersionUID = 1950376645714182144L;

    /** 产品编号 */
    private String productId;
    /** 所属组织编号 */
    private String ownOrganId;
    /** 操作人编号 */
    private String operatorId;
    /** 核心机构号 */
    private String coreInstNo;
    /** 额度实例金额信息中实占额度 */
    private java.math.BigDecimal realOccupyAmount;
    /** 产品名称 */
    private String productName;
    /** 客户编号 */
    private String custNo;
    /** 额度状态 */
    private String limitStatus;
    /** 额度编号 */
    private String custLimitId;
    /** 已用额度 */
    private java.math.BigDecimal usedAmount;
    /** 证件号码 */
    private String certNo;
    /** 证件类型 */
    private String certTyp;
    /** 客户名称 */
    private String custNm;
    /** 客户类型 */
    private String custTyp;
}
