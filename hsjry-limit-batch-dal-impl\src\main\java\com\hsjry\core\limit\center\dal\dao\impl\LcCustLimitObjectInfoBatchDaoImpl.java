package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;

import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitObjectInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.LcCustLimitObjectInfoBatchMapper;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoExample;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoKeyDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度实例所属对象信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2022-12-13 03:09:49
 */
@Repository
public class LcCustLimitObjectInfoBatchDaoImpl
    extends AbstractBaseDaoImpl<LcCustLimitObjectInfoDo, LcCustLimitObjectInfoBatchMapper>
    implements LcCustLimitObjectInfoBatchDao {
    /**
     * 分页查询
     *
     * @param lcCustLimitObjectInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LcCustLimitObjectInfoDo> selectPage(LcCustLimitObjectInfoQuery lcCustLimitObjectInfo,
        PageParam pageParam) {
        LcCustLimitObjectInfoExample example = buildExample(lcCustLimitObjectInfo);
        return PageHelper.<LcCustLimitObjectInfoDo>startPage(pageParam.getPageNum(),
            pageParam.getPageSize()).doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度实例所属对象信息
     *
     * @param custLimitId
     * @return
     */
    @Override
    public LcCustLimitObjectInfoDo selectByKey(String custLimitId) {
        LcCustLimitObjectInfoKeyDo lcCustLimitObjectInfoKeyDo = new LcCustLimitObjectInfoKeyDo();
        lcCustLimitObjectInfoKeyDo.setCustLimitId(custLimitId);
        lcCustLimitObjectInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lcCustLimitObjectInfoKeyDo);
    }

    /**
     * 根据key删除额度实例所属对象信息
     *
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custLimitId) {
        LcCustLimitObjectInfoKeyDo lcCustLimitObjectInfoKeyDo = new LcCustLimitObjectInfoKeyDo();
        lcCustLimitObjectInfoKeyDo.setCustLimitId(custLimitId);
        lcCustLimitObjectInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lcCustLimitObjectInfoKeyDo);
    }

    /**
     * 查询额度实例所属对象信息信息
     *
     * @param lcCustLimitObjectInfo 条件
     * @return List<LcCustLimitObjectInfoDo>
     */
    @Override
    public List<LcCustLimitObjectInfoDo> selectByExample(LcCustLimitObjectInfoQuery lcCustLimitObjectInfo) {
        return getMapper().selectByExample(buildExample(lcCustLimitObjectInfo));
    }

    /**
     * 新增额度实例所属对象信息信息
     *
     * @param lcCustLimitObjectInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LcCustLimitObjectInfoDo lcCustLimitObjectInfo) {
        if (lcCustLimitObjectInfo == null) {
            return -1;
        }

        lcCustLimitObjectInfo.setCreateTime(BusinessDateUtil.getDate());
        lcCustLimitObjectInfo.setUpdateTime(BusinessDateUtil.getDate());
        lcCustLimitObjectInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lcCustLimitObjectInfo);
    }

    /**
     * 修改额度实例所属对象信息信息
     *
     * @param lcCustLimitObjectInfo
     * @return
     */
    @Override
    public int updateBySelective(LcCustLimitObjectInfoDo lcCustLimitObjectInfo) {
        if (lcCustLimitObjectInfo == null) {
            return -1;
        }
        lcCustLimitObjectInfo.setUpdateTime(BusinessDateUtil.getDate());
        lcCustLimitObjectInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lcCustLimitObjectInfo);
    }

    @Override
    public int updateBySelectiveByExample(LcCustLimitObjectInfoDo lcCustLimitObjectInfo,
        LcCustLimitObjectInfoQuery lcCustLimitObjectInfoQuery) {
        lcCustLimitObjectInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lcCustLimitObjectInfo, buildExample(lcCustLimitObjectInfoQuery));
    }

    @Override
    public PageInfo<LcCustLimitObjectInfoDo> selectPageUserDistinct(
        LcCustLimitObjectInfoQuery lcCustLimitObjectInfoQuery, PageParam pageParam) {
        return PageHelper.<LcCustLimitObjectInfoDo>startPage(pageParam.getPageNum(),
            pageParam.getPageSize()).doSelectPageInfo(
            () -> getMapper().selectPageUserDistinct(lcCustLimitObjectInfoQuery));
    }

    @Override
    public PageInfo<LcCustLimitObjectInfoDo> selectPageProductDistinct(
        LcCustLimitObjectInfoQuery lcCustLimitObjectInfoQuery, PageParam pageParam) {
        return PageHelper.<LcCustLimitObjectInfoDo>startPage(pageParam.getPageNum(),
            pageParam.getPageSize()).doSelectPageInfo(
            () -> getMapper().selectPageProductDistinct(lcCustLimitObjectInfoQuery));
    }


    /**
     * 构建额度实例所属对象信息Example信息
     *
     * @param lcCustLimitObjectInfo
     * @return
     */
    public LcCustLimitObjectInfoExample buildExample(LcCustLimitObjectInfoQuery lcCustLimitObjectInfo) {
        LcCustLimitObjectInfoExample example = new LcCustLimitObjectInfoExample();
        LcCustLimitObjectInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lcCustLimitObjectInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lcCustLimitObjectInfo.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lcCustLimitObjectInfo.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lcCustLimitObjectInfo.getIbFinancialId())) {
                criteria.andIbFinancialIdEqualTo(lcCustLimitObjectInfo.getIbFinancialId());
            }
            if (StringUtil.isNotEmpty(lcCustLimitObjectInfo.getIbFinancialProdCoreId())) {
                criteria.andIbFinancialProdCoreIdEqualTo(lcCustLimitObjectInfo.getIbFinancialProdCoreId());
            }
            if (StringUtil.isNotEmpty(lcCustLimitObjectInfo.getIbFinancialProdId())) {
                criteria.andIbFinancialProdIdEqualTo(lcCustLimitObjectInfo.getIbFinancialProdId());
            }
            if (StringUtil.isNotEmpty(lcCustLimitObjectInfo.getIbFinancialProdName())) {
                criteria.andIbFinancialProdNameEqualTo(lcCustLimitObjectInfo.getIbFinancialProdName());
            }
            if (StringUtil.isNotEmpty(lcCustLimitObjectInfo.getIbFinancialProdType())) {
                criteria.andIbFinancialProdTypeEqualTo(lcCustLimitObjectInfo.getIbFinancialProdType());
            }
            if (StringUtil.isNotEmpty(lcCustLimitObjectInfo.getIbFinancialType())) {
                criteria.andIbFinancialTypeEqualTo(lcCustLimitObjectInfo.getIbFinancialType());
            }
            if (StringUtil.isNotEmpty(lcCustLimitObjectInfo.getOutUserId())) {
                criteria.andOutUserIdEqualTo(lcCustLimitObjectInfo.getOutUserId());
            }
            if (StringUtil.isNotEmpty(lcCustLimitObjectInfo.getUserCertificateKind())) {
                criteria.andUserCertificateKindEqualTo(lcCustLimitObjectInfo.getUserCertificateKind());
            }
            if (StringUtil.isNotEmpty(lcCustLimitObjectInfo.getUserCertificateNo())) {
                criteria.andUserCertificateNoEqualTo(lcCustLimitObjectInfo.getUserCertificateNo());
            }
            if (StringUtil.isNotEmpty(lcCustLimitObjectInfo.getUserId())) {
                criteria.andUserIdEqualTo(lcCustLimitObjectInfo.getUserId());
            }
            if (StringUtil.isNotEmpty(lcCustLimitObjectInfo.getUserName())) {
                criteria.andUserNameEqualTo(lcCustLimitObjectInfo.getUserName());
            }
            if (StringUtil.isNotEmpty(lcCustLimitObjectInfo.getUserType())) {
                criteria.andUserTypeEqualTo(lcCustLimitObjectInfo.getUserType());
            }
        }
        buildExampleExt(lcCustLimitObjectInfo, criteria);
        return example;
    }

    /**
     * 构建额度实例所属对象信息ExampleExt方法
     *
     * @param lcCustLimitObjectInfo
     * @return
     */
    public void buildExampleExt(LcCustLimitObjectInfoQuery lcCustLimitObjectInfo,
        LcCustLimitObjectInfoExample.Criteria criteria) {
        if (StringUtil.isNotBlank(lcCustLimitObjectInfo.getTenantId())) {
            criteria.andTenantIdEqualTo(lcCustLimitObjectInfo.getTenantId());
        }
        if (CollectionUtil.isNotEmpty(lcCustLimitObjectInfo.getCustLimitIdList())) {
            criteria.andCustLimitIdIn(lcCustLimitObjectInfo.getCustLimitIdList());
        }
        if (CollectionUtil.isNotEmpty(lcCustLimitObjectInfo.getUserIdList())) {
            criteria.andUserIdIn(lcCustLimitObjectInfo.getUserIdList());
        }
        if (CollectionUtil.isNotEmpty(lcCustLimitObjectInfo.getIbFinancialProdIdList())) {
            criteria.andIbFinancialProdIdIn(lcCustLimitObjectInfo.getIbFinancialProdIdList());
        }
        if (StringUtil.isNotBlank(lcCustLimitObjectInfo.getUserNameLike())) {
            criteria.andUserNameLike("%" + lcCustLimitObjectInfo.getUserNameLike() + "%");
        }
        //自定义实现
    }

    @Override
    public LcCustLimitObjectInfoDo selectFirstOne(LcCustLimitObjectInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectFirstOne(query);
    }

    @Override
    public List<String> selectShardList(LcCustLimitObjectInfoQuery query){
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectShardList(query);
    }

    @Override
    public Integer selectCountByCurrentGroup(LcCustLimitObjectInfoQuery query){
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectCountByCurrentGroup(query);
    }

}
