package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.query.LcSliceBatchSerialQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度分片流水数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-03-13 12:30:49
 */
public interface LcSliceBatchSerialDao extends IBaseDao<LcSliceBatchSerialDo> {
    /**
     * 分页查询额度分片流水
     *
     * @param lcSliceBatchSerialQuery 条件
     * @return PageInfo<LcSliceBatchSerialDo>
     */
    PageInfo<LcSliceBatchSerialDo> selectPage(LcSliceBatchSerialQuery lcSliceBatchSerialQuery, PageParam pageParam);

    /**
     * 根据key查询额度分片流水
     *
     * @param batchSerialNo
     * @param batchNum
     * @return
     */
    LcSliceBatchSerialDo selectByKey(String batchSerialNo, Integer batchNum);

    /**
     * 根据key删除额度分片流水
     *
     * @param batchSerialNo
     * @param batchNum
     * @return
     */
    int deleteByKey(String batchSerialNo, Integer batchNum);

    /**
     * 查询额度分片流水信息
     *
     * @param lcSliceBatchSerialQuery 条件
     * @return List<LcSliceBatchSerialDo>
     */
    List<LcSliceBatchSerialDo> selectByExample(LcSliceBatchSerialQuery lcSliceBatchSerialQuery);

    /**
     * 新增额度分片流水信息
     *
     * @param lcSliceBatchSerial 条件
     * @return int>
     */
    int insertBySelective(LcSliceBatchSerialDo lcSliceBatchSerial);

    /**
     * 修改额度分片流水信息
     *
     * @param lcSliceBatchSerial
     * @return
     */
    int updateBySelective(LcSliceBatchSerialDo lcSliceBatchSerial);

    /**
     * 修改额度分片流水信息
     *
     * @param lcSliceBatchSerial
     * @param lcSliceBatchSerialQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcSliceBatchSerialDo lcSliceBatchSerial,
        LcSliceBatchSerialQuery lcSliceBatchSerialQuery);
}
