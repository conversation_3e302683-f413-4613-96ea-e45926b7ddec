/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.core.limit.batch.biz.convert.file.LbHCoreAtxzhConverter;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.biz.entity.LbHCoreAtxzhData;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.AbstractFileBaseShardingPrepareBizImpl;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.constants.CharsetConstants;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.dal.dao.intf.LbHCoreAtxzhDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAtxzhDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.sequence.SequenceTool;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 核心贴现账户主文件同步实现类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/11/7 10:27
 */
@Slf4j
@Service("lbHCoreAtxzhFileSyncImpl")
@RequiredArgsConstructor
public class LbHCoreAtxzhFileSyncImpl extends AbstractFileBaseShardingPrepareBizImpl<LbHCoreAtxzhData> {

    /** 法人代码列数 */
    private static final int FAREDM_NUM = 1;
    /** 贴现借据号列数 */
    private static final int TXNJJH_NUM = 2;
    /** 贴现帐号列数 */
    private static final int TIEXZH_NUM = 3;
    /** 贴现处理种类列数 */
    private static final int TXCLZL_NUM = 4;
    /** 贴现业务种类列数 */
    private static final int TXYWZL_NUM = 5;
    /** 票据包号列数 */
    private static final int PIOJZH_NUM = 6;
    /** 客户号列数 */
    private static final int KEHHAO_NUM = 7;
    /** 客户名列数 */
    private static final int KEHZWM_NUM = 8;
    /** 营业机构列数 */
    private static final int YNGYJG_NUM = 9;
    /** 帐务机构列数 */
    private static final int ZHNGJG_NUM = 10;
    /** 入帐机构列数 */
    private static final int RUZHJG_NUM = 11;
    /** 损益支出机构列数 */
    private static final int SYZCJG_NUM = 12;
    /** 损益入帐机构列数 */
    private static final int SYRZJG_NUM = 13;
    /** 货币代号列数 */
    private static final int HUOBDH_NUM = 14;
    /** 贴现起息日列数 */
    private static final int TXQXRQ_NUM = 15;
    /** 贴现到期日列数 */
    private static final int TXDQRQ_NUM = 16;
    /** 宽限期列数 */
    private static final int KUANXQ_NUM = 17;
    /** 利率编号列数 */
    private static final int LILVBH_NUM = 18;
    /** 年月利率列数 */
    private static final int NYUELL_NUM = 19;
    /** 贴现利率列数 */
    private static final int TIEXLL_NUM = 20;
    /** 贴现余额列数 */
    private static final int TXZTYE_NUM = 21;
    /** 实付金额列数 */
    private static final int SHFUJE_NUM = 22;
    /** 实收贴现利息列数 */
    private static final int SXTXLX_NUM = 23;
    /** 累计利息收入列数 */
    private static final int LJLXSR_NUM = 24;
    /** 利息摊销周期列数 */
    private static final int TXRZZQ_NUM = 25;
    /** 待摊销收入余额列数 */
    private static final int DTSRYE_NUM = 26;
    /** 上次摊销收入日列数 */
    private static final int SCTSRQ_NUM = 27;
    /** 下次摊销收入日列数 */
    private static final int XCTSRO_NUM = 28;
    /** 实收金额列数 */
    private static final int SHSHJE_NUM = 29;
    /** 实付贴现利息列数 */
    private static final int SFTXLX_NUM = 30;
    /** 累计利息支出列数 */
    private static final int LJLXZC_NUM = 31;
    /** 待摊销支出余额列数 */
    private static final int DTZCYE_NUM = 32;
    /** 上次摊销支出日列数 */
    private static final int SCTZRQ_NUM = 33;
    /** 下次摊销支出日列数 */
    private static final int XCTZRQ_NUM = 34;
    /** 客户结算帐号列数 */
    private static final int JIESZH_NUM = 35;
    /** 是否先贴后查列数 */
    private static final int SFXTHC_NUM = 36;
    /** 查询查复编号列数 */
    private static final int CXCFBH_NUM = 37;
    /** 付息方式列数 */
    private static final int TXFXFS_NUM = 38;
    /** 买方付息比例列数 */
    private static final int MFFXBL_NUM = 39;
    /** 买方付息帐号列数 */
    private static final int MFFXZH_NUM = 40;
    /** 贴现风险标志列数 */
    private static final int TXFXBZ_NUM = 41;
    /** 抵质押物编号列数 */
    private static final int DZYWBH_NUM = 42;
    /** 本次买卖利息金额列数 */
    private static final int FXCDJE_NUM = 43;
    /** 逆回购转入时原组号列数 */
    private static final int XINX01_NUM = 44;
    /** 垫款标志列数 */
    private static final int DNKNBZ_NUM = 45;
    /** 垫款借据编号列数 */
    private static final int JIEJUH_NUM = 46;
    /** 转垫款金额列数 */
    private static final int ZHDKJE_NUM = 47;
    /** 对手行类别列数 */
    private static final int JIGULB_NUM = 48;
    /** 对手行行号列数 */
    private static final int DUIFHH_NUM = 49;
    /** 对手行行名列数 */
    private static final int DUIFHM_NUM = 50;
    /** 内部转贴现本次卖断利率列数 */
    private static final int BCMDLL_NUM = 51;
    /** 内部转贴现本次卖断利息列数 */
    private static final int BCMDLX_NUM = 52;
    /** 贴现状态列数 */
    private static final int ZHUNGT_NUM = 53;
    /** 明细序号列数 */
    private static final int MXXHAO_NUM = 54;
    /** 录入日期列数 */
    private static final int LURURQ_NUM = 55;
    /** 录入柜员列数 */
    private static final int LURUGY_NUM = 56;
    /** 复核日期列数 */
    private static final int FUHERQ_NUM = 57;
    /** 复核柜员列数 */
    private static final int FUHEGY_NUM = 58;
    /** 维护日期列数 */
    private static final int WEIHRQ_NUM = 59;
    /** 维护柜员列数 */
    private static final int WEIHGY_NUM = 60;
    /** 交易日期列数 */
    private static final int JIOYRQ_NUM = 61;
    /** 维护机构列数 */
    private static final int WEIHJG_NUM = 62;
    /** 维护时间列数 */
    private static final int WEIHSJ_NUM = 63;
    /** 时间戳列数 */
    private static final int SHINCH_NUM = 64;
    /** 记录状态列数 */
    private static final int JILUZT_NUM = 65;
    /** 数据日期列数 */
    private static final int DATA_DATE_NUM = 66;
    /** 最小字段数量 */
    private static final int MIN_FIELD_COUNT = 66;
    /** 分隔符 */
    private static final String FIELD_SEPARATOR = "\\|\\+\\|";
    /** 批处理大小 */
    private static final int BATCH_SIZE = 1000;
    private final String SEQUENCE_NO = "SEQUENCE_NO";

    private final LbHCoreAtxzhDao lbHCoreAtxzhDao;
    @Value("${project.core.atxzh.filename:CBS_ATXZH_[DATE].dat}")
    private String fileName;
    @Value("${limit.batch.remoteFilePath:/app/bftfiles/cfs/recv/ebbs/}")
    private String remoteFilePathDefine;

    @Override
    public ShardingResult<LbHCoreAtxzhData> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始文件数据分片查询[{}]", GsonUtil.objToStrForLog(jobShared));

        ShardingResult<LbHCoreAtxzhData> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        try {
            // 读取文件分片数据
            FileLineData fileLineData = GsonUtil.json2Obj(jobShared.getExtParam(), FileLineData.class);
            log.info(prefixLog + "开始读取文件数据分片[{}]", GsonUtil.objToStrForLog(fileLineData));
            List<String> originData = FileShardingUtils.readFileSharedData(jobShared, skipFirst(), CharsetConstants.GBK);

            // 使用并行流处理数据，提升性能
            List<LbHCoreAtxzhData> fileDataList = processOriginDataParallel(originData, prefixLog);

            log.info(prefixLog + "读取文件数据分片总量[{}]", fileDataList.size());
            shardingResult.setShardingResultList(fileDataList);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        jobShared.setBatchSerialNo(inParam.getString(SEQUENCE_NO));
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbHCoreAtxzhData> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
        List<LbHCoreAtxzhData> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "核心贴现账户主数据文件处理:文件分片数据为空,执行中断。");
            return;
        }

        log.info(prefixLog + "核心贴现账户主数据文件处理:开始执行分片数据操作,数据量:[{}]", dataList.size());

        // 设置业务日期
        String dataDateStr = String.valueOf(businessDate);
        dataList.forEach(data -> data.setDataDate(dataDateStr));
        // 检查是否是第一个分片，如果是则删除当前业务日期的数据
        if (sliceBatchSerialDo.getBatchNum() == 1) {
            log.info(prefixLog + "第一个分片,删除目标表 lb_h_core_atxzh 中业务日期[{}]的数据", dataDateStr);
            lbHCoreAtxzhDao.deleteByDataDate(dataDateStr);
        }


        // 确保分片流水对象的状态字段不为null
        if (sliceBatchSerialDo.getSharedStatus() == null) {
            log.debug(prefixLog + "分片流水状态为null,设置为处理中状态");
            sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
        }
        // 使用并行流进行数据转换和验证
        List<LbHCoreAtxzhDo> insertList = dataList.parallelStream().map(LbHCoreAtxzhConverter::data2Do)//
            .filter(this::validateData).collect(Collectors.toCollection(ArrayList::new));

        if (CollectionUtil.isNotEmpty(insertList)) {
            // 批量插入数据
            processBatchInsert(insertList, prefixLog);
            log.info(prefixLog + "插入[{}]条核心贴现账户主数据", insertList.size());
        }

        // 更新分片流水成功
        normalUpdateSliceSerial(dataList.size(), sliceBatchSerialDo);
        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataList.size());
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.H_CORE_ATXZH_FILE_SYNC;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        Map<String, Object> jobParameters = jobInitDto.getJobParameters();
        JSONObject param = JSON.parseObject(jobInitDto.getInPara());
        //设置流水ID,解决多次从redis获取唯一值的性能问题
        param.put(SEQUENCE_NO, SequenceTool.nextId());
        //更新jobInitDto的inpara参数
        jobInitDto.setInPara(param.toJSONString());

        // 确保fixNum被正确设置
        if (jobInitDto.getFixNum() == null) {
            log.warn(prefixLog + "fixNum为null,设置默认值1000");
            jobInitDto.setFixNum(1000);
        }

        List<JobShared> sharedList = Lists.newArrayList();
        log.info(prefixLog + "[{}]文件处理", jobTradeDesc);
        String localFilePath = FileShardingUtils.getLocalFilePath(jobInitDto.getBusinessDate(),
            remoteFilePathDefine + FileShardingUtils.ACCT_DATE_CODE_MARK + File.separator);
        Integer acctDate = jobInitDto.getBusinessDate();
        String localFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK, String.valueOf(acctDate));
        log.info(prefixLog + "转换后的本地文件路径: [{}]", localFilePath);
        try {
            String fileAttr = FIELD_SEPARATOR;
            String filePath = localFilePath + localFileName;
            log.info("实际查找的文件路径: [{}]", filePath);

            // 检查目录是否存在
            File directory = new File(localFilePath);
            if (!directory.exists()) {
                log.info(prefixLog + "目录[{}]不存在,尝试创建", localFilePath);
                directory.mkdirs();
            } else {
                log.info(prefixLog + "目录[{}]已存在", localFilePath);
                // 列出目录中的文件
                File[] files = directory.listFiles();
                if (files != null && files.length > 0) {
                    log.info(prefixLog + "目录[{}]中的文件列表:", localFilePath);
                    for (File f : files) {
                        log.info(prefixLog + " - {}", f.getName());
                    }
                } else {
                    log.info(prefixLog + "目录[{}]为空", localFilePath);
                }
            }

            File localFile = new File(filePath);
            log.info(prefixLog + "检查文件[{}]是否存在: {}", filePath, localFile.exists());

            //判断本地文件是否存在，不存在则直接报错
            if(!localFile.exists()){
                log.error(prefixLog + "本地文件[{}]不存在,请确认文件路径是否正确", filePath);
                String errorCode = EnumBatchJobError.FILE_PATH_NOT_EXIST.getCode();
                String errorMsg = EnumBatchJobError.FILE_PATH_NOT_EXIST.getDescription();
                throw new HsjryBizException(errorCode, errorMsg);
            }

            // 文件存在，直接进行分片处理
            log.info(prefixLog + "[{}]开始[{}]文件数据分片处理", jobTradeDesc, filePath);
            log.info(prefixLog + "分片参数: fixNum=[{}], fileAttr=[{}], skipFirst=[{}]",
                jobInitDto.getFixNum(), fileAttr, skipFirst());

            List<JobShared> jobShareds = FileShardingUtils.getFileSharedData(jobInitDto, localFile, fileAttr,
                skipFirst());
            log.info(prefixLog + "[{}]结束[{}]文件数据分片当前分片数[{}]", jobTradeDesc, filePath,
                jobShareds.size());
            sharedList.addAll(jobShareds);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }
        return sharedList;
    }

    private boolean skipFirst() {
        return true; // 跳过标题行
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 并行处理原始数据
     * 使用并行流提升数据处理性能，同时保证线程安全
     *
     * @param originData 原始数据列表
     * @param prefixLog 日志前缀
     * @return 处理后的数据列表
     */
    private List<LbHCoreAtxzhData> processOriginDataParallel(List<String> originData, String prefixLog) {
        if (CollectionUtil.isEmpty(originData)) {
            return new ArrayList<>();
        }

        // 使用线程安全的计数器
        AtomicInteger invalidCount = new AtomicInteger(0);
        AtomicInteger parseErrorCount = new AtomicInteger(0);

        List<LbHCoreAtxzhData> fileDataList = originData.parallelStream().filter(Objects::nonNull).filter(
            item -> !StringUtil.isBlank(item)).map(
            item -> parseLineData(item, prefixLog, invalidCount, parseErrorCount)).filter(Objects::nonNull).collect(
            Collectors.toCollection(ArrayList::new));

        // 记录统计信息
        if (invalidCount.get() > 0) {
            log.warn(prefixLog + "数据格式不正确记录数量:[{}]", invalidCount.get());
        }
        if (parseErrorCount.get() > 0) {
            log.warn(prefixLog + "数字字段解析失败记录数量:[{}]", parseErrorCount.get());
        }

        return fileDataList;
    }

    /**
     * 解析单行数据
     * 提取单行数据解析逻辑，提高代码复用性和可维护性
     *
     * @param item 单行数据
     * @param prefixLog 日志前缀
     * @param invalidCount 无效数据计数器
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的数据对象
     */
    private LbHCoreAtxzhData parseLineData(String item, String prefixLog, AtomicInteger invalidCount,
        AtomicInteger parseErrorCount) {
        String[] split = item.split(FIELD_SEPARATOR);
        if (split.length < MIN_FIELD_COUNT) {
            invalidCount.incrementAndGet();
            return null;
        }

        LbHCoreAtxzhData fileData = new LbHCoreAtxzhData();
        // 设置字符串字段
        fileData.setFaredm(split[FAREDM_NUM - 1]);
        fileData.setTxnjjh(split[TXNJJH_NUM - 1]);
        fileData.setTiexzh(split[TIEXZH_NUM - 1]);
        fileData.setTxclzl(split[TXCLZL_NUM - 1]);
        fileData.setTxywzl(split[TXYWZL_NUM - 1]);
        fileData.setPiojzh(split[PIOJZH_NUM - 1]);
        fileData.setKehhao(split[KEHHAO_NUM - 1]);
        fileData.setKehzwm(split[KEHZWM_NUM - 1]);
        fileData.setYngyjg(split[YNGYJG_NUM - 1]);
        fileData.setZhngjg(split[ZHNGJG_NUM - 1]);
        fileData.setRuzhjg(split[RUZHJG_NUM - 1]);
        fileData.setSyzcjg(split[SYZCJG_NUM - 1]);
        fileData.setSyrzjg(split[SYRZJG_NUM - 1]);
        fileData.setHuobdh(split[HUOBDH_NUM - 1]);
        fileData.setTxqxrq(split[TXQXRQ_NUM - 1]);
        fileData.setTxdqrq(split[TXDQRQ_NUM - 1]);
        fileData.setLilvbh(split[LILVBH_NUM - 1]);
        fileData.setNyuell(split[NYUELL_NUM - 1]);
        fileData.setTxrzzq(split[TXRZZQ_NUM - 1]);
        fileData.setSctsrq(split[SCTSRQ_NUM - 1]);
        fileData.setXctsro(split[XCTSRO_NUM - 1]);
        fileData.setSctzrq(split[SCTZRQ_NUM - 1]);
        fileData.setXctzrq(split[XCTZRQ_NUM - 1]);
        fileData.setJieszh(split[JIESZH_NUM - 1]);
        fileData.setSfxthc(split[SFXTHC_NUM - 1]);
        fileData.setCxcfbh(split[CXCFBH_NUM - 1]);
        fileData.setTxfxfs(split[TXFXFS_NUM - 1]);
        fileData.setMffxzh(split[MFFXZH_NUM - 1]);
        fileData.setTxfxbz(split[TXFXBZ_NUM - 1]);
        fileData.setDzywbh(split[DZYWBH_NUM - 1]);
        fileData.setXinx01(split[XINX01_NUM - 1]);
        fileData.setDnknbz(split[DNKNBZ_NUM - 1]);
        fileData.setJiejuh(split[JIEJUH_NUM - 1]);
        fileData.setJigulb(split[JIGULB_NUM - 1]);
        fileData.setDuifhh(split[DUIFHH_NUM - 1]);
        fileData.setDuifhm(split[DUIFHM_NUM - 1]);
        fileData.setZhungt(split[ZHUNGT_NUM - 1]);
        fileData.setLururq(split[LURURQ_NUM - 1]);
        fileData.setLurugy(split[LURUGY_NUM - 1]);
        fileData.setFuherq(split[FUHERQ_NUM - 1]);
        fileData.setFuhegy(split[FUHEGY_NUM - 1]);
        fileData.setWeihrq(split[WEIHRQ_NUM - 1]);
        fileData.setWeihgy(split[WEIHGY_NUM - 1]);
        fileData.setJioyrq(split[JIOYRQ_NUM - 1]);
        fileData.setWeihjg(split[WEIHJG_NUM - 1]);
        fileData.setJiluzt(split[JILUZT_NUM - 1]);
        String dataDate = BusinessDateUtil.getCurrentDateStr();
        fileData.setDataDate(dataDate);

        // 安全解析数字字段
        fileData.setKuanxq(parseBigDecimalSafely(split[KUANXQ_NUM - 1], parseErrorCount));
        fileData.setTiexll(parseBigDecimalSafely(split[TIEXLL_NUM - 1], parseErrorCount));
        fileData.setTxztye(parseBigDecimalSafely(split[TXZTYE_NUM - 1], parseErrorCount));
        fileData.setShfuje(parseBigDecimalSafely(split[SHFUJE_NUM - 1], parseErrorCount));
        fileData.setSxtxlx(parseBigDecimalSafely(split[SXTXLX_NUM - 1], parseErrorCount));
        fileData.setLjlxsr(parseBigDecimalSafely(split[LJLXSR_NUM - 1], parseErrorCount));
        fileData.setDtsrye(parseBigDecimalSafely(split[DTSRYE_NUM - 1], parseErrorCount));
        fileData.setShshje(parseBigDecimalSafely(split[SHSHJE_NUM - 1], parseErrorCount));
        fileData.setSftxlx(parseBigDecimalSafely(split[SFTXLX_NUM - 1], parseErrorCount));
        fileData.setLjlxzc(parseBigDecimalSafely(split[LJLXZC_NUM - 1], parseErrorCount));
        fileData.setDtzcye(parseBigDecimalSafely(split[DTZCYE_NUM - 1], parseErrorCount));
        fileData.setMffxbl(parseBigDecimalSafely(split[MFFXBL_NUM - 1], parseErrorCount));
        fileData.setFxcdje(parseBigDecimalSafely(split[FXCDJE_NUM - 1], parseErrorCount));
        fileData.setZhdkje(parseBigDecimalSafely(split[ZHDKJE_NUM - 1], parseErrorCount));
        fileData.setBcmdll(parseBigDecimalSafely(split[BCMDLL_NUM - 1], parseErrorCount));
        fileData.setBcmdlx(parseBigDecimalSafely(split[BCMDLX_NUM - 1], parseErrorCount));
        fileData.setMxxhao(parseBigDecimalSafely(split[MXXHAO_NUM - 1], parseErrorCount));
        fileData.setWeihsj(parseBigDecimalSafely(split[WEIHSJ_NUM - 1], parseErrorCount));
        fileData.setShinch(parseBigDecimalSafely(split[SHINCH_NUM - 1], parseErrorCount));

        return fileData;
    }

    /**
     * 安全解析BigDecimal
     * 统一的数字字段解析逻辑，避免代码重复
     *
     * @param value 待解析的字符串值
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的BigDecimal值
     */
    private BigDecimal parseBigDecimalSafely(String value, AtomicInteger parseErrorCount) {
        try {
            return StringUtil.isBlank(value) ? BigDecimal.ZERO : new BigDecimal(value);
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return BigDecimal.ZERO;
        }
    }

    /**
     * 增强的数据验证方法
     * 使用Objects工具类和优化的验证逻辑
     *
     * @param data 待验证的数据
     * @return 是否有效
     */
    private boolean validateData(LbHCoreAtxzhDo data) {
        if (Objects.isNull(data)) {
            return false;
        }

        if (StringUtil.isBlank(data.getFaredm())) {
            log.warn("法人代码为空,数据无效");
            return false;
        }

        if (StringUtil.isBlank(data.getTxnjjh())) {
            log.warn("贴现借据号为空,数据无效");
            return false;
        }

        if (StringUtil.isBlank(data.getDataDate())) {
            log.warn("数据日期为空,法人代码:[{}],贴现借据号:[{}]", data.getFaredm(), data.getTxnjjh());
            return false;
        }

        // 额外的业务验证逻辑
        if (Objects.nonNull(data.getFaredm()) && data.getFaredm().length() > 4) {
            log.warn("法人代码长度超限,贴现借据号:[{}]", data.getTxnjjh());
            return false;
        }

        return true;
    }

    /**
     * 批量插入处理
     * 分批处理大量数据，避免内存溢出和数据库连接超时
     *
     * @param insertList 待插入数据列表
     * @param prefixLog 日志前缀
     */
    private void processBatchInsert(List<LbHCoreAtxzhDo> insertList, String prefixLog) {
        if (CollectionUtil.isEmpty(insertList)) {
            return;
        }

        int totalSize = insertList.size();
        int batchCount = (totalSize + BATCH_SIZE - 1) / BATCH_SIZE;

        log.info(prefixLog + "开始批量插入数据,总数据量:[{}],分批数量:[{}],每批大小:[{}]", totalSize, batchCount,
            BATCH_SIZE);

        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * BATCH_SIZE;
            int toIndex = Math.min(fromIndex + BATCH_SIZE, totalSize);
            List<LbHCoreAtxzhDo> batchList = insertList.subList(fromIndex, toIndex);

            try {
                lbHCoreAtxzhDao.insertList(batchList);
                log.debug(prefixLog + "批次[{}]插入完成,数据量:[{}]", i + 1, batchList.size());
            } catch (Exception e) {
                log.error(prefixLog + "批次[{}]插入失败,数据量:[{}]", i + 1, batchList.size(), e);
                throw e;
            }
        }
    }

}
