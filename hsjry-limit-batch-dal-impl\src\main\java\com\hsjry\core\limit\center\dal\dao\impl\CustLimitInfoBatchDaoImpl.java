/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.intf.CustLimitInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.CustLimitInfoBatchMapper;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitInfoQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/17 9:57
 */
@Repository
public class CustLimitInfoBatchDaoImpl extends AbstractBaseDaoImpl<LcCustLimitInfoDo, CustLimitInfoBatchMapper>
    implements CustLimitInfoBatchDao {
    @Override
    public List<LcCustLimitInfoDo> selectShardList(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectShardList(query);
    }

    @Override
    public LcCustLimitInfoDo selectFirstOne(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectFirstOne(query);
    }

    @Override
    public Integer selectCountByCurrentGroup(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitInfoDo> selectObjectShardList(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectObjectShardList(query);
    }

    @Override
    public List<LcCustLimitInfoDo> selectExpireShardList(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectExpireShardList(query);
    }

    @Override
    public LcCustLimitInfoDo selectObjectFirstOne(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectObjectFirstOne(query);
    }

    @Override
    public LcCustLimitInfoDo selectExpireFirstOne(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectExpireFirstOne(query);
    }

    @Override
    public Integer selectObjectCountByCurrentGroup(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectObjectCountByCurrentGroup(query);
    }
    @Override
    public Integer selectExpireCountByCurrentGroup(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectExpireCountByCurrentGroup(query);
    }
    @Override
    public List<LcCustLimitInfoDo> selectNotUsedShardList(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNotUsedShardList(query);
    }

    @Override
    public LcCustLimitInfoDo selectNotUsedFirstOne(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNotUsedFirstOne(query);
    }

    @Override
    public Integer selectNotUsedCountByCurrentGroup(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectNotUsedCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitInfoDo> selectNodeShardList(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNodeShardList(query);
    }

    @Override
    public LcCustLimitInfoDo selectNodeFirstOne(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNodeFirstOne(query);
    }

    @Override
    public Integer selectNodeCountByCurrentGroup(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectNodeCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitInfoDo> selectExpireLimitInfoByObjectId(CustLimitInfoQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectExpireLimitInfoByObjectId(query);
    }
}
