/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationDo;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitRelationQuery;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/17 9:58
 */
public interface CustLimitRelationBatchMapper extends CommonMapper<LcCustLimitRelationDo> {
    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitRelationDo> selectShardList(@Param("query") CustLimitRelationQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitRelationDo selectFirstOne(@Param("query") CustLimitRelationQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(@Param("query") CustLimitRelationQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitRelationDo> selectObjectShardList(@Param("query") CustLimitRelationQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitRelationDo selectObjectFirstOne(@Param("query") CustLimitRelationQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitRelationDo> selectExpireShardList(@Param("query") CustLimitRelationQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitRelationDo selectExpireFirstOne(@Param("query") CustLimitRelationQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectObjectCountByCurrentGroup(@Param("query") CustLimitRelationQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectExpireCountByCurrentGroup(@Param("query") CustLimitRelationQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitRelationDo> selectNotUsedShardList(@Param("query") CustLimitRelationQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitRelationDo selectNotUsedFirstOne(@Param("query") CustLimitRelationQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNotUsedCountByCurrentGroup(@Param("query") CustLimitRelationQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitRelationDo> selectNodeShardList(@Param("query") CustLimitRelationQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitRelationDo selectNodeFirstOne(@Param("query") CustLimitRelationQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNodeCountByCurrentGroup(@Param("query") CustLimitRelationQuery query);

    List<LcCustLimitRelationDo> selectExpireLimitInfoByObjectId(@Param("query") CustLimitRelationQuery query);
}
