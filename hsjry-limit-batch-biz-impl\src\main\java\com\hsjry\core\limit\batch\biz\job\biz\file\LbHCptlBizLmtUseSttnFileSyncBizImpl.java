/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.biz.file;

import java.util.Objects;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
import com.hsjry.base.common.job.biz.BaseShardingPrepareBiz;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.entity.LbHCptlBizLmtUseSttnData;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.core.limit.batch.core.slice.SliceBatchSerialCore;

import lombok.extern.slf4j.Slf4j;

/**
 * 资金系统历史表日终业务额度使用情况同步业务实现类
 * 负责调度和管理资金系统历史表日终业务额度使用情况的同步处理任务
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/15 10:00
 */
@Slf4j
@Service("lbHCptlBizLmtUseSttnFileSyncBizImpl")
public class LbHCptlBizLmtUseSttnFileSyncBizImpl implements BaseOrdinaryBiz {

    @Autowired
    @Qualifier("lbHCptlBizLmtUseSttnFileSyncImpl")
    private BaseShardingPrepareBiz baseShardingPrepareBiz;

    @Autowired
    @Qualifier("lbHCptlBizLmtUseSttnFileSyncImpl")
    private JobCoreBusiness<LbHCptlBizLmtUseSttnData> jobCoreBusiness;

    @Autowired
    private SliceBatchSerialCore sliceBatchSerialCore;
    
    public LbHCptlBizLmtUseSttnFileSyncBizImpl() {
        log.info("LbHCptlBizLmtUseSttnFileSyncBizImpl Bean 正在创建...");
    }

    /**
     * 获取任务交易码
     *
     * @return 资金系统历史表日终业务额度使用情况同步的任务交易码
     */
    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.H_CPTL_BIZ_LMT_USE_STTN;
    }

    /**
     * 执行资金系统历史表日终业务额度使用情况同步的基础任务
     * 此方法是任务的入口点，负责协调整个文件同步流程
     *
     * @param jobInitDto 任务初始化参数，包含业务日期、批次流水号等信息
     */
    @Override
    public void execBaseJob(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();

        String prefixLog = String.format("营运日期:[%s],批量流水号:[%s],任务交易码:[%s-%s]", businessDate,
            batchSerialNo, jobTradeCode, jobTradeDesc);

        log.info("{}========开始执行资金系统历史表日终业务额度使用情况同步任务========", prefixLog);

        // 确保JobInitDto的关键字段被正确初始化
        if (jobInitDto.getFixNum() == null) {
            log.warn("{}fixNum未设置，使用默认值", prefixLog);
            jobInitDto.setFixNum(5);
        }
        try {
            // 执行前置处理
            log.info("{}开始执行前置处理", prefixLog);
            jobCoreBusiness.preHandle(jobInitDto);
            log.info("{}前置处理完成", prefixLog);

            // 生成分片任务
            log.info("{}开始生成分片任务", prefixLog);
            List<JobShared> jobSharedList = baseShardingPrepareBiz.generateJobSharding(jobInitDto);
            if (CollectionUtil.isEmpty(jobSharedList)) {
                log.info("{}未生成分片任务，任务结束", prefixLog);
                return;
            }
            log.info("{}分片任务生成完成，共生成{}个分片", prefixLog, jobSharedList.size());

            // 执行分片任务
            log.info("{}开始执行分片任务处理", prefixLog);
            int successCount = 0;
            int totalCount = jobSharedList.size();

            for (int i = 0; i < jobSharedList.size(); i++) {
                JobShared jobShared = jobSharedList.get(i);
                Integer batchNum = jobShared.getBatchNum();

                try {
                    log.info("{}开始处理第[{}/{}]个分片，分片编号:[{}]", prefixLog, i + 1, totalCount, batchNum);

                    // 创建分片流水对象
                    LcSliceBatchSerialDo sliceBatchSerialDo = new LcSliceBatchSerialDo();
                    sliceBatchSerialDo.setBatchNum(batchNum);
                    sliceBatchSerialDo.setBatchSerialNo(batchSerialNo);
                    sliceBatchSerialDo.setTradeCode(jobTradeCode);

                    // 查询分片结果
                    ShardingResult<LbHCptlBizLmtUseSttnData> shardingResult = jobCoreBusiness.queryShardingResult(
                        sliceBatchSerialDo, jobInitDto, jobShared);

                    if (shardingResult == null || CollectionUtil.isEmpty(shardingResult.getShardingResultList())) {
                        log.info("{}第[{}/{}]个分片数据为空，跳过处理", prefixLog, i + 1, totalCount);
                        continue;
                    }

                    // 执行核心业务逻辑
                    jobCoreBusiness.execJobCoreBusiness(shardingResult);

                    // 更新分片流水状态为成功
                    sliceBatchSerialDo.setSharedPassCount(shardingResult.getShardingResultList().size());
                    sliceBatchSerialCore.updateSuccessSliceBatchSerial(sliceBatchSerialDo);

                    successCount++;
                    log.info("{}第[{}/{}]个分片处理完成，分片编号:[{}]，数据量:[{}]",
                        prefixLog, i + 1, totalCount, batchNum,
                        shardingResult.getShardingResultList().size());

                } catch (Exception e) {
                    log.error("{}第[{}/{}]个分片处理失败，分片编号:[{}]，异常信息:[{}]",
                        prefixLog, i + 1, totalCount, batchNum, e.getMessage(), e);
                    
                    // 更新分片流水状态为失败
                    try {
                        LcSliceBatchSerialDo sliceBatchSerialDo = new LcSliceBatchSerialDo();
                        sliceBatchSerialDo.setBatchNum(batchNum);
                        sliceBatchSerialDo.setBatchSerialNo(batchSerialNo);
                        sliceBatchSerialDo.setTradeCode(jobTradeCode);
                        sliceBatchSerialCore.updateFailSliceBatchSerial(sliceBatchSerialDo);
                    } catch (Exception updateException) {
                        log.error("{}更新分片流水状态失败，分片编号:[{}]，异常信息:[{}]",
                            prefixLog, batchNum, updateException.getMessage(), updateException);
                    }
                    
                    // 分片处理失败不中断整个流程，继续处理下一个分片
                }
            }

            log.info("{}分片任务处理完成，总分片数:[{}]，成功处理:[{}]，失败:[{}]",
                prefixLog, totalCount, successCount, totalCount - successCount);

            if (successCount == 0) {
                throw new RuntimeException("所有分片处理均失败");
            }

            log.info("{}资金系统历史表日终业务额度使用情况同步任务执行完成", prefixLog);

        } catch (Exception e) {
            log.error("{}资金系统历史表日终业务额度使用情况同步任务执行失败,异常信息:[{}]", prefixLog, e.getMessage(), e);
            throw new RuntimeException(String.format("资金系统历史表日终业务额度使用情况同步任务执行失败: %s", e.getMessage()), e);
        } finally {
            try {
                // 执行后置处理
                log.info("{}开始执行后置处理", prefixLog);
                jobCoreBusiness.afterHandle(jobInitDto);
                log.info("{}后置处理完成", prefixLog);
            } catch (Exception e) {
                log.error("{}后置处理执行失败", prefixLog, e);
                // 后置处理失败不影响主流程
            }
        }

        log.info("{}========资金系统历史表日终业务额度使用情况同步任务结束========", prefixLog);
    }
} 