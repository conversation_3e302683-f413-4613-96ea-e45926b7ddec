package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTElcblCorpProdLmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTElcblCorpProdLmtInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpProdLmtInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpProdLmtInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTElcblCorpProdLmtInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

import lombok.extern.slf4j.Slf4j;

/**
 * 电票系统-中间表-贴现对公产品层额度信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-29 02:46:35
 */
@Slf4j
@Repository
public class LbTElcblCorpProdLmtInfoDaoImpl
    extends AbstractBaseDaoImpl<LbTElcblCorpProdLmtInfoDo, LbTElcblCorpProdLmtInfoMapper>
    implements LbTElcblCorpProdLmtInfoDao {
    /**
     * 分页查询
     *
     * @param lbTElcblCorpProdLmtInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTElcblCorpProdLmtInfoDo> selectPage(LbTElcblCorpProdLmtInfoQuery lbTElcblCorpProdLmtInfo,
        PageParam pageParam) {
        LbTElcblCorpProdLmtInfoExample example = buildExample(lbTElcblCorpProdLmtInfo);
        return PageHelper.<LbTElcblCorpProdLmtInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询电票系统-中间表-贴现对公产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTElcblCorpProdLmtInfoDo selectByKey(String custNo, String custLimitId) {
        LbTElcblCorpProdLmtInfoKeyDo lbTElcblCorpProdLmtInfoKeyDo = new LbTElcblCorpProdLmtInfoKeyDo();
        lbTElcblCorpProdLmtInfoKeyDo.setCustNo(custNo);
        lbTElcblCorpProdLmtInfoKeyDo.setCustLimitId(custLimitId);
        lbTElcblCorpProdLmtInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTElcblCorpProdLmtInfoKeyDo);
    }

    /**
     * 根据key删除电票系统-中间表-贴现对公产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTElcblCorpProdLmtInfoKeyDo lbTElcblCorpProdLmtInfoKeyDo = new LbTElcblCorpProdLmtInfoKeyDo();
        lbTElcblCorpProdLmtInfoKeyDo.setCustNo(custNo);
        lbTElcblCorpProdLmtInfoKeyDo.setCustLimitId(custLimitId);
        lbTElcblCorpProdLmtInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTElcblCorpProdLmtInfoKeyDo);
    }

    /**
     * 查询电票系统-中间表-贴现对公产品层额度信息信息
     *
     * @param lbTElcblCorpProdLmtInfo 条件
     * @return List<LbTElcblCorpProdLmtInfoDo>
     */
    @Override
    public List<LbTElcblCorpProdLmtInfoDo> selectByExample(LbTElcblCorpProdLmtInfoQuery lbTElcblCorpProdLmtInfo) {
        return getMapper().selectByExample(buildExample(lbTElcblCorpProdLmtInfo));
    }

    /**
     * 新增电票系统-中间表-贴现对公产品层额度信息信息
     *
     * @param lbTElcblCorpProdLmtInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTElcblCorpProdLmtInfoDo lbTElcblCorpProdLmtInfo) {
        if (lbTElcblCorpProdLmtInfo == null) {
            return -1;
        }

        lbTElcblCorpProdLmtInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTElcblCorpProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTElcblCorpProdLmtInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTElcblCorpProdLmtInfo);
    }

    /**
     * 修改电票系统-中间表-贴现对公产品层额度信息信息
     *
     * @param lbTElcblCorpProdLmtInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTElcblCorpProdLmtInfoDo lbTElcblCorpProdLmtInfo) {
        if (lbTElcblCorpProdLmtInfo == null) {
            return -1;
        }
        lbTElcblCorpProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTElcblCorpProdLmtInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTElcblCorpProdLmtInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTElcblCorpProdLmtInfoDo lbTElcblCorpProdLmtInfo,
        LbTElcblCorpProdLmtInfoQuery lbTElcblCorpProdLmtInfoQuery) {
        lbTElcblCorpProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTElcblCorpProdLmtInfo,
            buildExample(lbTElcblCorpProdLmtInfoQuery));
    }

    /**
     * 构建电票系统-中间表-贴现对公产品层额度信息Example信息
     *
     * @param lbTElcblCorpProdLmtInfo
     * @return
     */
    public LbTElcblCorpProdLmtInfoExample buildExample(LbTElcblCorpProdLmtInfoQuery lbTElcblCorpProdLmtInfo) {
        LbTElcblCorpProdLmtInfoExample example = new LbTElcblCorpProdLmtInfoExample();
        LbTElcblCorpProdLmtInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTElcblCorpProdLmtInfo != null) {
            //添加查询条件
            if (null != lbTElcblCorpProdLmtInfo.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTElcblCorpProdLmtInfo.getLowRiskAmount());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpProdLmtInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTElcblCorpProdLmtInfo.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpProdLmtInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTElcblCorpProdLmtInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpProdLmtInfo.getEntityId())) {
                criteria.andEntityIdEqualTo(lbTElcblCorpProdLmtInfo.getEntityId());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpProdLmtInfo.getVirtualContractFlag())) {
                criteria.andVirtualContractFlagEqualTo(lbTElcblCorpProdLmtInfo.getVirtualContractFlag());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpProdLmtInfo.getRelationId())) {
                criteria.andRelationIdEqualTo(lbTElcblCorpProdLmtInfo.getRelationId());
            }
            if (null != lbTElcblCorpProdLmtInfo.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTElcblCorpProdLmtInfo.getRealOccupyLowRiskAmt());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpProdLmtInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTElcblCorpProdLmtInfo.getCustNo());
            }
            if (null != lbTElcblCorpProdLmtInfo.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTElcblCorpProdLmtInfo.getRealOccupyAmount());
            }
            if (null != lbTElcblCorpProdLmtInfo.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTElcblCorpProdLmtInfo.getTotalAmount());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpProdLmtInfo.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTElcblCorpProdLmtInfo.getLimitStatus());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpProdLmtInfo.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTElcblCorpProdLmtInfo.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpProdLmtInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTElcblCorpProdLmtInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpProdLmtInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTElcblCorpProdLmtInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpProdLmtInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTElcblCorpProdLmtInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpProdLmtInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTElcblCorpProdLmtInfo.getCustTyp());
            }
        }
        buildExampleExt(lbTElcblCorpProdLmtInfo, criteria);
        return example;
    }

    /**
     * 构建电票系统-中间表-贴现对公产品层额度信息ExampleExt方法
     *
     * @param lbTElcblCorpProdLmtInfo
     * @return
     */
    public void buildExampleExt(LbTElcblCorpProdLmtInfoQuery lbTElcblCorpProdLmtInfo,
        LbTElcblCorpProdLmtInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 将电票对公客户额度同步数据插入到产品层额度信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_elcbl_corp_prod_lmt_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    @Override
    public int insertElcblCorpProdLmtInfo(List<String> templateNodeIdList, List<String> custLimitIdList) {
        if (CollectionUtils.isEmpty(templateNodeIdList) || CollectionUtils.isEmpty(custLimitIdList)) {
            log.warn("模板节点ID列表或客户额度ID列表为空，跳过插入操作");
            return 0;
        }
        return getMapper().insertElcblCorpProdLmtInfo(templateNodeIdList, custLimitIdList);
    }

    /**
     * 更新额度实例金额信息表
     * 根据电票系统中间表数据更新LC_CUST_LIMIT_AMT_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    @Override
    public int updateCustLimitAmtInfo(List<String> custLimitIdList) {
        if (CollectionUtils.isEmpty(custLimitIdList)) {
            log.warn("客户额度ID列表为空，跳过更新额度实例金额信息操作");
            return 0;
        }
        return getMapper().updateCustLimitAmtInfo(custLimitIdList);
    }

}
