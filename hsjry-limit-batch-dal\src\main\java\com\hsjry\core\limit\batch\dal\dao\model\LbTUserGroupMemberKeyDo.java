package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度中心-中间表-集团客户成员主键
 *
 * <AUTHOR>
 * @date 2025-07-15 03:50:12
 */
@Table(name = "lb_t_user_group_member")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbTUserGroupMemberKeyDo implements Serializable {

    private static final long serialVersionUID = 1944967709649469440L;
    /** 资源项id */
    @Id
    @Column(name = "resource_id")
    private String resourceId;

    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}