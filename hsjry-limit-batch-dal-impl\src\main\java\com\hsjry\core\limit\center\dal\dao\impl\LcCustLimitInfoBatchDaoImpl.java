package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;

import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.LcCustLimitInfoBatchMapper;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoExample;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoKeyDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度实例信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2023-06-12 09:20:39
 */
@Repository
public class LcCustLimitInfoBatchDaoImpl extends AbstractBaseDaoImpl<LcCustLimitInfoDo, LcCustLimitInfoBatchMapper>
    implements LcCustLimitInfoBatchDao {
    /**
     * 分页查询
     *
     * @param lcCustLimitInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LcCustLimitInfoDo> selectPage(LcCustLimitInfoQuery lcCustLimitInfo, PageParam pageParam) {
        LcCustLimitInfoExample example = buildExample(lcCustLimitInfo);
        return PageHelper.<LcCustLimitInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度实例信息
     *
     * @param custLimitId
     * @return
     */
    @Override
    public LcCustLimitInfoDo selectByKey(String custLimitId) {
        LcCustLimitInfoKeyDo lcCustLimitInfoKeyDo = new LcCustLimitInfoKeyDo();
        lcCustLimitInfoKeyDo.setCustLimitId(custLimitId);
        lcCustLimitInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lcCustLimitInfoKeyDo);
    }

    /**
     * 根据key删除额度实例信息
     *
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custLimitId) {
        LcCustLimitInfoKeyDo lcCustLimitInfoKeyDo = new LcCustLimitInfoKeyDo();
        lcCustLimitInfoKeyDo.setCustLimitId(custLimitId);
        lcCustLimitInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lcCustLimitInfoKeyDo);
    }

    /**
     * 查询额度实例信息信息
     *
     * @param lcCustLimitInfo 条件
     * @return List<LcCustLimitInfoDo>
     */
    @Override
    public List<LcCustLimitInfoDo> selectByExample(LcCustLimitInfoQuery lcCustLimitInfo) {
        return getMapper().selectByExample(buildExample(lcCustLimitInfo));
    }

    @Override
    public List<LcCustLimitInfoDo> selectByExampleOrderByLastTime(LcCustLimitInfoQuery lcCustLimitInfo) {
        LcCustLimitInfoExample example = buildExample(lcCustLimitInfo);
        example.setOrderByClause("limit_last_time desc");
        return getMapper().selectByExample(example);
    }

    /**
     * 新增额度实例信息信息
     *
     * @param lcCustLimitInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LcCustLimitInfoDo lcCustLimitInfo) {
        if (lcCustLimitInfo == null) {
            return -1;
        }

        lcCustLimitInfo.setCreateTime(BusinessDateUtil.getDate());
        lcCustLimitInfo.setUpdateTime(BusinessDateUtil.getDate());
        lcCustLimitInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lcCustLimitInfo);
    }

    /**
     * 修改额度实例信息信息
     *
     * @param lcCustLimitInfo
     * @return
     */
    @Override
    public int updateBySelective(LcCustLimitInfoDo lcCustLimitInfo) {
        if (lcCustLimitInfo == null) {
            return -1;
        }
        lcCustLimitInfo.setUpdateTime(BusinessDateUtil.getDate());
        lcCustLimitInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lcCustLimitInfo);
    }

    @Override
    public int updateBySelectiveByExample(LcCustLimitInfoDo lcCustLimitInfo,
        LcCustLimitInfoQuery lcCustLimitInfoQuery) {
        lcCustLimitInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lcCustLimitInfo, buildExample(lcCustLimitInfoQuery));
    }

    @Override
    public Integer selectCountByCurrentGroup(LcCustLimitInfoQuery query) {
        return getMapper().selectCountByCurrentGroup(query);
    }

    @Override
    public LcCustLimitInfoDo selectFirstOne(LcCustLimitInfoQuery query) {
        return getMapper().selectFirstOne(query);
    }

    @Override
    public List<LcCustLimitInfoDo> selectShardList(LcCustLimitInfoQuery query) {
        return getMapper().selectShardList(query);
    }

    /**
     * 构建额度实例信息Example信息
     *
     * @param lcCustLimitInfo
     * @return
     */
    public LcCustLimitInfoExample buildExample(LcCustLimitInfoQuery lcCustLimitInfo) {
        LcCustLimitInfoExample example = new LcCustLimitInfoExample();
        LcCustLimitInfoExample.Criteria criteria = example.createCriteria();
        example.setOrderByClause("create_time desc");
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lcCustLimitInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lcCustLimitInfo.getContractLimitFlag())) {
                criteria.andContractLimitFlagEqualTo(lcCustLimitInfo.getContractLimitFlag());
            }
            if (StringUtil.isNotEmpty(lcCustLimitInfo.getLimitLevel())) {
                criteria.andLimitLevelEqualTo(lcCustLimitInfo.getLimitLevel());
            }
            if (StringUtil.isNotEmpty(lcCustLimitInfo.getLimitObjectId())) {
                criteria.andLimitObjectIdEqualTo(lcCustLimitInfo.getLimitObjectId());
            }
            if (StringUtil.isNotEmpty(lcCustLimitInfo.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lcCustLimitInfo.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lcCustLimitInfo.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lcCustLimitInfo.getLimitStatus());
            }

            if (null != lcCustLimitInfo.getLimitEnableTerm()) {
                criteria.andLimitEnableTermEqualTo(lcCustLimitInfo.getLimitEnableTerm());
            }
            if (StringUtil.isNotEmpty(lcCustLimitInfo.getLimitEnableTermUnit())) {
                criteria.andLimitEnableTermUnitEqualTo(lcCustLimitInfo.getLimitEnableTermUnit());
            }
            if (StringUtil.isNotEmpty(lcCustLimitInfo.getOutCustLimitId())) {
                criteria.andOutCustLimitIdEqualTo(lcCustLimitInfo.getOutCustLimitId());
            }
            if (StringUtil.isNotEmpty(lcCustLimitInfo.getCustLimitTypeId())) {
                criteria.andCustLimitTypeIdEqualTo(lcCustLimitInfo.getCustLimitTypeId());
            }
            if (StringUtil.isNotEmpty(lcCustLimitInfo.getTemplateNodeId())) {
                criteria.andTemplateNodeIdEqualTo(lcCustLimitInfo.getTemplateNodeId());
            }
            if (StringUtil.isNotEmpty(lcCustLimitInfo.getLimitTemplateId())) {
                criteria.andLimitTemplateIdEqualTo(lcCustLimitInfo.getLimitTemplateId());
            }
        }
        buildExampleExt(lcCustLimitInfo, criteria);
        return example;
    }

    /**
     * 构建额度实例信息ExampleExt方法
     *
     * @param lcCustLimitInfo
     * @return
     */
    public void buildExampleExt(LcCustLimitInfoQuery lcCustLimitInfo, LcCustLimitInfoExample.Criteria criteria) {
        //自定义实现
    }





}
