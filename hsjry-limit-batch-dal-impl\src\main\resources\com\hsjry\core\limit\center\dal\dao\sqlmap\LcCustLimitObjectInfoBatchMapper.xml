<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.center.dal.dao.mapper.LcCustLimitObjectInfoBatchMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo">
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
        <result property="custLimitId" column="cust_limit_id" jdbcType="VARCHAR"/> <!-- 额度编号 -->
        <result property="ibFinancialId" column="ib_financial_id" jdbcType="VARCHAR"/> <!-- 同业金融编号 -->
        <result property="ibFinancialProdCoreId" column="ib_financial_prod_core_id"
                jdbcType="VARCHAR"/> <!-- 核心同业金融产品编号 -->
        <result property="ibFinancialProdId" column="ib_financial_prod_id" jdbcType="VARCHAR"/> <!-- 同业金融产品编号 -->
        <result property="ibFinancialProdName" column="ib_financial_prod_name" jdbcType="VARCHAR"/> <!-- 同业金融产品名称 -->
        <result property="ibFinancialProdType" column="ib_financial_prod_type" jdbcType="CHAR"/> <!-- 同业金融产品类型 -->
        <result property="ibFinancialType" column="ib_financial_type" jdbcType="CHAR"/> <!-- 同业金融类型 -->
        <result property="outUserId" column="out_user_id" jdbcType="VARCHAR"/> <!-- 核心客户编号 -->
        <result property="tenantId" column="tenant_id" jdbcType="CHAR"/> <!-- 租户号 -->
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
        <result property="userCertificateKind" column="user_certificate_kind" jdbcType="VARCHAR"/> <!-- 客户证件类型 -->
        <result property="userCertificateNo" column="user_certificate_no" jdbcType="VARCHAR"/> <!-- 客户证件编号 -->
        <result property="userId" column="user_id" jdbcType="VARCHAR"/> <!-- 客户编号 -->
        <result property="userName" column="user_name" jdbcType="VARCHAR"/> <!-- 客户名称 -->
        <result property="userType" column="user_type" jdbcType="CHAR"/> <!-- 客户类型 -->
    </resultMap>
<!--    <resultMap id="InterBankBaseResultMap" type="com.hsjry.core.limit.center.dal.dao.model.LcCustLimitBaseInfoDo">-->
<!--        <result property="outUserId" column="out_user_id" jdbcType="VARCHAR"/> &lt;!&ndash; 核心客户编号 &ndash;&gt;-->
<!--        <result property="userCertificateKind" column="user_certificate_kind" jdbcType="VARCHAR"/> &lt;!&ndash; 客户证件类型 &ndash;&gt;-->
<!--        <result property="userCertificateNo" column="user_certificate_no" jdbcType="VARCHAR"/> &lt;!&ndash; 客户证件编号 &ndash;&gt;-->
<!--        <result property="userId" column="user_id" jdbcType="VARCHAR"/> &lt;!&ndash; 客户编号 &ndash;&gt;-->
<!--        <result property="userName" column="user_name" jdbcType="VARCHAR"/> &lt;!&ndash; 客户名称 &ndash;&gt;-->
<!--        <result property="userType" column="user_type" jdbcType="CHAR"/> &lt;!&ndash; 客户类型 &ndash;&gt;-->
<!--        <result property="parentUserId" column="parent_user_id" jdbcType="VARCHAR"/> &lt;!&ndash; 客户所属集团编号 &ndash;&gt;-->
<!--        <result property="parentUserName" column="parent_user_name" jdbcType="VARCHAR"/> &lt;!&ndash; 客户所属集团名称 &ndash;&gt;-->
<!--        <result property="operatorId" column="operator_id" jdbcType="VARCHAR"/> &lt;!&ndash; 客户编号 &ndash;&gt;-->
<!--        <result property="organId" column="own_organ_id" jdbcType="VARCHAR"/> &lt;!&ndash; 客户名称 &ndash;&gt;-->
<!--    </resultMap>-->
    <sql id="Base_Column_List">
        create_time
        , cust_limit_id
                , ib_financial_id
                , ib_financial_prod_core_id
                , ib_financial_prod_id
                , ib_financial_prod_name
                , ib_financial_prod_type
                , ib_financial_type
                , out_user_id
                , tenant_id
                , update_time
                , user_certificate_kind
                , user_certificate_no
                , user_id
                , user_name
                , user_type
    </sql>
    <select id="selectPageUserDistinct"
            resultMap="BaseResultMap"
            parameterType="com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery">
        select *
        from (select row_number() over (partition by o1.USER_ID, o1.TENANT_ID order by o1.CREATE_TIME desc) RNO,
        o1.USER_ID,
        o1.TENANT_ID,
        o1.cust_limit_id,
        o1.CREATE_TIME
        from lc_cust_limit_object_info o1
        <if test="query.parentUserNameLike != null and query.parentUserNameLike != '' ">
            left join lc_cust_limit_relation r on o1.cust_limit_id = r.current_node_limit_id
            left join lc_cust_limit_object_info o2 on o2.cust_limit_id = r.parent_node_limit_id
        </if>
        <if test="query.organIdList != null and query.organIdList.size() > 0 || query.operatorIdList != null and query.operatorIdList.size() > 0 ">
            left join lc_cust_limit_info i on o1.cust_limit_id = i.cust_limit_id
        </if>
        where o1.user_id is not null
        <if test="query.outUserId != null and query.outUserId != '' ">
            and o1.OUT_USER_ID = #{query.outUserId}
        </if>
        <if test="query.userId != null and query.userId != '' ">
            and o1.USER_ID = #{query.userId}
        </if>
        <if test="query.userName != null and query.userName != '' ">
            and o1.USER_NAME = #{query.userName}
        </if>
        <if test="query.userNameLike != null and query.userNameLike != '' ">
            and o1.USER_NAME like #{query.userNameLike}
        </if>
        <if test="query.userCertificateKind != null and query.userCertificateKind != '' ">
            and o1.USER_CERTIFICATE_KIND = #{query.userCertificateKind}
        </if>
        <if test="query.userCertificateNo != null and query.userCertificateNo != '' ">
            and o1.USER_CERTIFICATE_NO = #{query.userCertificateNo}
        </if>
        <if test="query.tenantId != null and query.tenantId != '' ">
            and o1.TENANT_ID = #{query.tenantId}
        </if>
        <if test="query.userType != null and query.userType != '' ">
            and o1.USER_TYPE = #{query.userType}
        </if>
        <if test="query.parentUserNameLike != null and query.parentUserNameLike != '' ">
            and o2.USER_NAME like #{query.parentUserNameLike}
        </if>
        <if test="query.limitRelationType != null and query.limitRelationType != '' ">
            and r.LIMIT_RELATION_TYPE = #{query.limitRelationType}
        </if>
        <if test="query.organIdList != null and query.organIdList.size() > 0 ">
            and i.own_organ_id in
            <foreach collection="query.organIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="query.operatorIdList != null and query.operatorIdList.size() > 0 ">
            and i.operator_id in
            <foreach collection="query.operatorIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        )
        where RNO = 1
        order by CREATE_TIME desc
    </select>


    <select id="selectPageProductDistinct"
            resultMap="BaseResultMap"
            parameterType="com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery">
        select distinct IB_FINANCIAL_PROD_ID,IB_FINANCIAL_PROD_TYPE,TENANT_ID
        from lc_cust_limit_object_info
        where
        1 = 1
        <if test="query.tenantId != null">
            and TENANT_ID = #{query.tenantId}
        </if>
        <if test="query.ibFinancialProdName != null">
            and IB_FINANCIAL_PROD_NAME = #{query.ibFinancialProdName}
        </if>
        <if test="query.ibFinancialProdType != null">
            and IB_FINANCIAL_PROD_TYPE = #{query.ibFinancialProdType}
        </if>
        <if test="query.ibFinancialProdId != null">
            and IB_FINANCIAL_PROD_ID = #{query.ibFinancialProdId}
        </if>
        <if test="query.ibFinancialProdCoreId != null">
            and IB_FINANCIAL_PROD_CORE_ID = #{query.ibFinancialProdCoreId}
        </if>
        <if test="query.ibFinancialProdIdNotNull != null and query.ibFinancialProdIdNotNull eq 'Y'.toString() ">
            and IB_FINANCIAL_PROD_ID is not null
        </if>
    </select>


    <select id="selectCountByCurrentGroup"
            parameterType="com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery"
            resultType="java.lang.Integer">
        SELECT count(distinct user_id)
        FROM lc_cust_limit_object_info WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectFirstOne" parameterType="com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery"
            resultMap="BaseResultMap">
        SELECT
        distinct user_id
        FROM lc_cust_limit_object_info
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectShardList" parameterType="com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery"
            resultType="java.lang.String">
        SELECT
        distinct user_id
        FROM lc_cust_limit_object_info
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <sql id="fixQuerySql">
        <if test="query.userId != null and query.userId!=''">
            AND user_id <![CDATA[ > ]]> #{query.userId}
        </if>
        <if test="query.userType != null and query.userType!=''">
            AND user_type = #{query.userType}
        </if>
        <if test="query.userIdList !=null">
            AND user_id in
            <foreach collection="query.userIdList" index="index" item="item"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY user_id
    </sql>


</mapper>
