<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.center.dal.dao.mapper.AmtLimitRecordInfoBatchMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordInfoDo">
        <result property="amountWarnLevel" column="amount_warn_level" jdbcType="INTEGER"/> <!-- 限额预警等级 -->
        <result property="cocnRatioLevel" column="cocn_ratio_level" jdbcType="INTEGER"/> <!-- 集中度预警等级 -->
        <result property="collectFlag" column="collect_flag" jdbcType="CHAR"/> <!-- 汇总计算标记 -->
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
        <result property="effectiveEndTime" column="effective_end_time" jdbcType="TIMESTAMP"/> <!-- 生效结束时间 -->
        <result property="effectiveStartTime" column="effective_start_time" jdbcType="TIMESTAMP"/> <!-- 生效开始时间 -->
        <result property="preOccupyAmount" column="pre_occupy_amount" jdbcType="DECIMAL"/> <!-- 预占金额 -->
        <result property="realOccupyAmount" column="real_occupy_amount" jdbcType="DECIMAL"/> <!-- 实占金额 -->
        <result property="recordId" column="record_id" jdbcType="VARCHAR"/> <!-- 记录编号 -->
        <result property="recordStatus" column="record_status"
                jdbcType="CHAR"/> <!-- 记录状态;EnumAmtLimitRecordStatus :010-生效、020-失效 -->
        <result property="ruleId" column="rule_id" jdbcType="VARCHAR"/> <!-- 限额规则编号 -->
        <result property="tenantId" column="tenant_id" jdbcType="CHAR"/> <!-- 租户号 -->
        <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/> <!-- 总金额 -->
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
    </resultMap>
    <sql id="Base_Column_List">
        amount_warn_level
        , cocn_ratio_level
                , collect_flag
                , create_time
                , effective_end_time
                , effective_start_time
                , pre_occupy_amount
                , real_occupy_amount
                , record_id
                , record_status
                , rule_id
                , tenant_id
                , total_amount
                , update_time
    </sql>
    <select id="selectCountByCurrentGroup"
            parameterType="com.hsjry.core.limit.center.dal.dao.query.AmtLimitRecordInfoBatchQuery"
            resultType="java.lang.Integer">
        SELECT count(*)
        FROM lc_amt_limit_record_info WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectFirstOne" parameterType="com.hsjry.core.limit.center.dal.dao.query.AmtLimitRecordInfoBatchQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_amt_limit_record_info
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectShardList" parameterType="com.hsjry.core.limit.center.dal.dao.query.AmtLimitRecordInfoBatchQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_amt_limit_record_info
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <sql id="fixQuerySql">
        <if test="query.effectiveStartTimeBegin !=null">
            AND effective_start_time <![CDATA[ <= ]]> #{query.effectiveStartTimeBegin}
        </if>
        <if test="query.effectiveStartTimeEnd !=null ">
            AND effective_end_time <![CDATA[ >= ]]>#{query.effectiveStartTimeEnd}
        </if>
        <if test="query.recordId != null and query.recordId!=''">
            AND record_id > #{query.recordId}
        </if>
        <if test="query.recordStatus != null and query.recordStatus!=''">
            AND record_status = #{query.recordStatus}
        </if>
        ORDER BY record_id
    </sql>

</mapper>