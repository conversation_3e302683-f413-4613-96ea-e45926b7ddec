package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAtxzhDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 核心系统-历史表-贴现账户主文件mapper
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
public interface LbHCoreAtxzhMapper extends CommonMapper<LbHCoreAtxzhDo> {
    // 请在现有的LbHCoreAtxzhMapper接口中添加以下方法：

    /**
     * 批量插入贴现账户主文件信息
     *
     * @param lbHCoreAtxzhList 批量数据
     * @return int
     */
    @Override
    int insertList(@Param("list") List<LbHCoreAtxzhDo> lbHCoreAtxzhList);

    /**
     * 清空贴现账户主文件表所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 根据数据日期删除贴现账户主文件表数据
     *
     * @param dataDate 数据日期
     * @return int
     */
    int deleteByDataDate(@Param("dataDate") String dataDate);
}