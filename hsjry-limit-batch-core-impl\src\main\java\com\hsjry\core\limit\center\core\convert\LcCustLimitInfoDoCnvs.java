package com.hsjry.core.limit.center.core.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.center.core.bo.CustLimitInfoBo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;

@Mapper(componentModel = "spring")
public interface LcCustLimitInfoDoCnvs {
    LcCustLimitInfoDoCnvs INSTANCE = Mappers.getMapper(LcCustLimitInfoDoCnvs.class);

    /** 将[额度实例信息Do]转换为[额度实例信息Bo] */
    CustLimitInfoBo cnvsDoToBo(LcCustLimitInfoDo dataObject);

    /** 将[额度实例信息Do列表]转换为[额度实例信息Bo列表] */
    List<CustLimitInfoBo> cnvsDoListToBoList(List<LcCustLimitInfoDo> doList);

    /** 将[额度实例信息Bo]转换为[额度实例信息Do] */
    LcCustLimitInfoDo cnvsBoToDo(CustLimitInfoBo bo);

    /** 将[额度实例信息Bo列表]转换为[额度实例信息Do列表] */
    List<LcCustLimitInfoDo> cnvsBoListToDoList(List<CustLimitInfoBo> boList);
}
