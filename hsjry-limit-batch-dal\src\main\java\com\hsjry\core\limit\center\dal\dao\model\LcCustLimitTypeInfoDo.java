package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度类型信息Do
 *
 * <AUTHOR>
 * @date 2022-12-02 03:03:37
 */
@Table(name = "lc_cust_limit_type_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitTypeInfoDo extends LcCustLimitTypeInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1598513186523512832L;
    /** 是否合同额度;EnumBool(Y-是，N-否) */
    @Column(name = "contract_limit_flag")
    private String contractLimitFlag;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 额度类型名称 */
    @Column(name = "cust_limit_type_name")
    private String custLimitTypeName;
    /** 超额占用方式;EnumExcessOccupationType:001-压缩占用、002-超额占用 */
    @Column(name = "excess_occupation_type")
    private String excessOccupationType;
    /** 额度分类;数据字典:LIMIT_CLASSIFICATION */
    @Column(name = "limit_classification")
    private String limitClassification;
    /** 额度设立方式;使用“,”分隔.EnumGrantType 001-拆分、002-共享、003-汇总 */
    @Column(name = "limit_grant_type")
    private String limitGrantType;
    /** 额度层级;EnumLimitLevel:001-根节点、002-中间节点、003-末级节点 */
    @Column(name = "limit_level")
    private String limitLevel;
    /** 额度占用方式;EnumOccupationType(001-合同占用, 002-余额占用，003-总量占用), */
    @Column(name = "limit_occupation_type")
    private String limitOccupationType;
    /** 额度类型状态;EnumLimitTypeStatus 001-草稿，002-上架，003-下架 */
    @Column(name = "limit_type_status")
    private String limitTypeStatus;
    /** 额度使用方式;使用“,”分隔.EnumUsageType(001-非循环授信, 002-循环授信） */
    @Column(name = "limit_usage_type")
    private String limitUsageType;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 业务品种【产品】列表 */
    @Column(name = "product_id_arr")
    private String productIdArr;
    /** 单一限额编码 */
    @Column(name = "single_limit_code")
    private String singleLimitCode;
    /** 是否唯一;EnumBool(Y-是，N-否) */
    @Column(name = "sole_flag")
    private String soleFlag;
    /** 是否向上占用;EnumBool(Y-是，N-否) */
    @Column(name = "up_flag")
    private String upFlag;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
}
