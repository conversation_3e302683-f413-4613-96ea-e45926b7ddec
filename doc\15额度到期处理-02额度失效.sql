-- INEFFECTIVE("010", "未生效"),
--     VALID("020", "生效"),
--     FROZEN("030", "冻结"),
--     BREAK("040", "终止"),
--     EXPIRE("050", "到期"),
--     INVALID("060", "失效"),


select *
from LB_T_LMT_EXPY_AMT;
-- 查询额度状态为[010-未生效]且额度启动到期日小于[营业日期]的[额度操作流水]插入[额度中心-中间表-额度到期中实占金额相关]
insert into LB_T_LMT_EXPY_AMT(tenant_id, create_time, update_time, operator_id, own_organ_id, limit_object_id,
                              cust_limit_id, limit_status, limit_enable_end_time, relation_id, real_occupy_amount,
                              currency, low_risk_currency, operate_type, operate_amount, operate_low_risk_amount,
                              remark)
select lcli.TENANT_ID,
       lcli.CREATE_TIME,
       lcli.UPDATE_TIME,
       lcli.OPERATOR_ID,
       lcli.OWN_ORGAN_ID,
       lcli.LIMIT_OBJECT_ID,
       lcli.CUST_LIMIT_ID,
       lcli.LIMIT_STATUS,
       lcli.limit_enable_end_time,
       lcli.RELATION_ID,
       lclai.REAL_OCCUPY_AMOUNT,
       lclai.CURRENCY,
       lclai.LOW_RISK_CURRENCY,
       '013'                                                                                                as operate_type,
       0                                                                                                    as operate_amount,
       0                                                                                                    as operate_low_risk_amount,
       '额度失效处理中查询额度状态为[010-未生效]且额度启动到期日小于[营业日期]的时候插入[013-失效额度]流水' as remark
from LC_CUST_LIMIT_INFO lcli
         inner join LC_CUST_LIMIT_AMT_INFO lclai
                    on lcli.CUST_LIMIT_ID = lclai.CUST_LIMIT_ID and
                       lcli.LIMIT_OBJECT_ID = lclai.CUST_NO
where lcli.LIMIT_TEMPLATE_ID in ('HNNSDGKHEDTX', 'HNNSGRKHEDTX', 'HNNSTYKHEDTX', 'HNNSJTKHEDTX')
  and lcli.LIMIT_STATUS in ('010')
  and lcli.LIMIT_ENABLE_END_TIME < '营业日期';

-- 查询额度状态为[050-到期]和[040-终止]且实占额度等于0的[额度操作流水]插入[额度中心-中间表-额度到期中实占金额相关]
insert into LB_T_LMT_EXPY_AMT(tenant_id, create_time, update_time, operator_id, own_organ_id, limit_object_id,
                              cust_limit_id, limit_status, limit_enable_end_time, relation_id, real_occupy_amount,
                              currency, low_risk_currency, operate_type, operate_amount, operate_low_risk_amount,
                              remark)
select lcli.TENANT_ID,
       lcli.CREATE_TIME,
       lcli.UPDATE_TIME,
       lcli.OPERATOR_ID,
       lcli.OWN_ORGAN_ID,
       lcli.LIMIT_OBJECT_ID,
       lcli.CUST_LIMIT_ID,
       lcli.LIMIT_STATUS,
       lcli.limit_enable_end_time,
       lcli.RELATION_ID,
       lclai.REAL_OCCUPY_AMOUNT,
       lclai.CURRENCY,
       lclai.LOW_RISK_CURRENCY,
       '013'                                                                                           as operate_type,
       0                                                                                               as operate_amount,
       0                                                                                               as operate_low_risk_amount,
       '额度失效处理中查询额度状态为[050-到期]和[040-终止]且实占额度等于0的时候插入[013-失效额度]流水' as remark
from LC_CUST_LIMIT_INFO lcli
         inner join LC_CUST_LIMIT_AMT_INFO lclai
                    on lcli.CUST_LIMIT_ID = lclai.CUST_LIMIT_ID and
                       lcli.LIMIT_OBJECT_ID = lclai.CUST_NO
where lcli.LIMIT_TEMPLATE_ID in ('HNNSDGKHEDTX', 'HNNSGRKHEDTX', 'HNNSTYKHEDTX', 'HNNSJTKHEDTX')
  and lcli.LIMIT_STATUS in ('040', '040')
  and lclai.REAL_OCCUPY_AMOUNT = 0;

-- 将上述两种情况中的额度状态设置为[060-失效]
update LC_CUST_LIMIT_INFO t
set t.LIMIT_STATUS = '060'
where t.CUST_LIMIT_ID in (select distinct src.CUST_LIMIT_ID
                          from LB_T_LMT_EXPY_AMT src);

-- 插入操作类型为[013-失效额度]额度操作流水
INSERT INTO LC_CUST_LIMIT_OPERATE_SERIAL (GLOBAL_SERIAL_NO, SERIAL_NO, CHANNEL_NO, BIZ_DATETIME, INBOUND_SERIAL_NO,
                                          INBOUND_SERIAL_DATETIME, TENANT_ID, CREATE_TIME, UPDATE_TIME, CLOS_SERIAL_NO,
                                          STATUS, OPERATOR_ID, OWN_ORGAN_ID, OPERATE_TYPE, OPERATE_AMOUNT,
                                          OPERATE_AMOUNT_ID, OPERATE_AMOUNT_CURRENCY, OPERATE_LOW_RISK_AMOUNT,
                                          OPERATE_LOW_RISK_AMT_ID, OPERATE_LOW_RISK_CURRENCY, RELATION_ID,
                                          LAST_INBOUND_SERIAL_NO, CUST_LIMIT_ID, OPERATE_DIRECTION, FAIL_REASON, REMARK,
                                          ENTITY_ID, EXCHANGE_RATE_VERSION, CONTRACT_RECAL_FLAG, OPERATE_PATH, CUST_NO)
SELECT GENERATE_SERIAL_NO('LCLOS', 'GSN') AS GLOBAL_SERIAL_NO,
       GENERATE_SERIAL_NO('LCLOS', 'SN')  AS SERIAL_NO,
       NULL                               AS CHANNEL_NO,
       SYSDATE                            AS BIZ_DATETIME,
       GENERATE_SERIAL_NO('LCLOS', 'ISN') AS INBOUND_SERIAL_NO,
       SYSDATE                            AS INBOUND_SERIAL_DATETIME,
       src.TENANT_ID                      AS TENANT_ID,
       SYSDATE                            AS CREATE_TIME,
       SYSDATE                            AS UPDATE_TIME,
       GENERATE_SERIAL_NO('LCLOS', 'CSN') AS CLOS_SERIAL_NO,
       '020'                              AS STATUS,
       src.OPERATOR_ID                    AS OPERATOR_ID,
       src.OWN_ORGAN_ID                   AS OWN_ORGAN_ID,
       src.OPERATE_TYPE                   AS OPERATE_TYPE,
       0                                  AS OPERATE_AMOUNT,
       NULL                               AS OPERATE_AMOUNT_ID,
       src.CURRENCY                       AS OPERATE_AMOUNT_CURRENCY,
       src.OPERATE_LOW_RISK_AMOUNT        AS OPERATE_LOW_RISK_AMOUNT,
       NULL                               AS OPERATE_LOW_RISK_AMT_ID,
       src.LOW_RISK_CURRENCY              AS OPERATE_LOW_RISK_CURRENCY,
       src.RELATION_ID                    AS RELATION_ID,
       NULL                               AS LAST_INBOUND_SERIAL_NO,
       src.CUST_LIMIT_ID                  AS CUST_LIMIT_ID,
       NULL                               AS OPERATE_DIRECTION,
       NULL                               AS FAIL_REASON,
       src.REMARK                         AS REMARK,
       NULL                               AS ENTITY_ID,
       NULL                               AS EXCHANGE_RATE_VERSION,
       NULL                               AS CONTRACT_RECAL_FLAG,
       NULL                               AS OPERATE_PATH,
       src.LIMIT_OBJECT_ID                AS CUST_NO
FROM LB_T_LMT_EXPY_AMT src;