package com.hsjry.core.limit.batch.dal.dao.query;

import java.io.Serializable;

import lombok.Data;
import lombok.Builder;

/**
 * 网贷系统-中间表-个人产品层额度信息查询条件
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
@Data
@Builder
public class LbTOlIndvProdLmtInfoQuery implements Serializable {

    /** serialVersionUID */
    private static final long serialVersionUID = 1950902007284039681L;

    /** 总低风险额度 */
    private java.math.BigDecimal lowRiskAmount;
    /** 所属组织编号 */
    private String ownOrganId;
    /** 操作人编号 */
    private String operatorId;
    /** 实体编号 */
    private String entityId;
    /** 虚拟合同标识;EnumBool(Y-是，N-否) */
    private String virtualContractFlag;
    /** 关联编号 */
    private String relationId;
    /** 实占低风险 */
    private java.math.BigDecimal realOccupyLowRiskAmt;
    /** 客户编号 */
    private String custNo;
    /** 实占额度 */
    private java.math.BigDecimal realOccupyAmount;
    /** 总额度 */
    private java.math.BigDecimal totalAmount;
    /** 额度状态 */
    private String limitStatus;
    /** 额度编号 */
    private String custLimitId;
    /** 证件号码 */
    private String certNo;
    /** 证件类型 */
    private String certTyp;
    /** 客户名称 */
    private String custNm;
    /** 客户类型 */
    private String custTyp;
}
