package com.hsjry.core.limit.center.dal.dao.intf;

import com.hsjry.core.limit.center.dal.dao.model.LcSingleLimitSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcSingleLimitSerialQuery;


import java.util.List;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 单一限额操作流水数据库操作接口
 * 
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
public interface LcSingleLimitSerialDao extends IBaseDao<LcSingleLimitSerialDo> {
    /**
     * 分页查询单一限额操作流水
     *
     * @param lcSingleLimitSerialQuery 条件
     * @return PageInfo<LcSingleLimitSerialDo>
     */
    PageInfo<LcSingleLimitSerialDo> selectPage(LcSingleLimitSerialQuery lcSingleLimitSerialQuery,PageParam pageParam);
	  /**
     * 根据key查询单一限额操作流水
     *
     	 	 * @param lslsSerialNo
	 	 	 	      * @return
     */
	LcSingleLimitSerialDo selectByKey(String lslsSerialNo);
    /**
     * 根据key删除单一限额操作流水
     *
               * @param lslsSerialNo
                      * @return
     */
    int deleteByKey(String lslsSerialNo);

    /**
     * 查询单一限额操作流水信息
     *
     * @param lcSingleLimitSerialQuery 条件
     * @return List<LcSingleLimitSerialDo>
     */
    List<LcSingleLimitSerialDo> selectByExample(LcSingleLimitSerialQuery lcSingleLimitSerialQuery);

    /**
     * 新增单一限额操作流水信息
     *
     * @param lcSingleLimitSerial 条件
     * @return int>
     */
    int insertBySelective(LcSingleLimitSerialDo lcSingleLimitSerial);

    /**
     * 修改单一限额操作流水信息
     *
     * @param lcSingleLimitSerial
     * @return
     */
    int updateBySelective(LcSingleLimitSerialDo lcSingleLimitSerial);
    /**
     * 修改单一限额操作流水信息
     *
     * @param lcSingleLimitSerial
     * @param lcSingleLimitSerialQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcSingleLimitSerialDo lcSingleLimitSerial,
    LcSingleLimitSerialQuery lcSingleLimitSerialQuery);
}
