package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度前置流水主键
 *
 * <AUTHOR>
 * @date 2023-07-25 11:58:40
 */
@Table(name = "lc_inbound_serial")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcInboundSerialKeyDo implements Serializable {

    private static final long serialVersionUID = 1683808986002030592L;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    /** 请求流水编号 */
    @Id
    @Column(name = "cis_serial_no")
    private String cisSerialNo;
}