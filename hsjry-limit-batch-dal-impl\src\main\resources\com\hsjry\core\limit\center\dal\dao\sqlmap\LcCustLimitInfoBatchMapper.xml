<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.center.dal.dao.mapper.LcCustLimitInfoBatchMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo">
        <result property="contractLimitFlag" column="contract_limit_flag"
                jdbcType="CHAR"/> <!-- 是否合同额度;EnumBool(Y-是，N-否) -->
        <result property="limitCoreObjectId" column="limit_core_object_id" jdbcType="VARCHAR"/> <!-- 所属对象核心编号 -->
        <result property="limitObjectType" column="limit_object_type" jdbcType="CHAR"/> <!-- 所属对象类型 -->
        <result property="useOccupyTimes" column="use_occupy_times" jdbcType="INTEGER"/> <!-- 已占用次数 -->
        <result property="occupyTimesLimit" column="occupy_times_limit" jdbcType="INTEGER"/> <!-- 占用次数限制 -->
        <result property="productId" column="product_id" jdbcType="VARCHAR"/> <!-- 业务品种【产品】 -->
        <result property="limitLevel" column="limit_level"
                jdbcType="CHAR"/> <!-- 额度层级;EnumLimitLevel:001-根节点、002-中间节点、003-末级节点 -->
        <result property="limitClassification" column="limit_classification"
                jdbcType="VARCHAR"/> <!-- 额度分类;dictKey:LIMIT_CLASSIFICATION -->
        <result property="limitUsageType" column="limit_usage_type"
                jdbcType="CHAR"/> <!-- 额度使用方式;EnumUsageType(001-非循环授信, 002-循环授信） -->
        <result property="limitGrantType" column="limit_grant_type"
                jdbcType="CHAR"/> <!-- 额度设立方式;EnumLimitGrantType 001-拆分、002-共享、003-汇总 -->
        <result property="limitOccupationType" column="limit_occupation_type"
                jdbcType="CHAR"/> <!-- 额度占用方式;EnumOccupationType(001-合同占用, 002-余额占用,003-总量占用) -->
        <result property="upFlag" column="up_flag" jdbcType="CHAR"/> <!-- 是否向上占用;EnumBool(Y-是，N-否) -->
        <result property="limitObjectId" column="limit_object_id" jdbcType="VARCHAR"/> <!-- 所属对象编号 -->
        <result property="soleFlag" column="sole_flag" jdbcType="CHAR"/> <!-- 是否唯一;EnumBool(Y-是，N-否) -->
        <result property="exchangeRateVersion" column="exchange_rate_version" jdbcType="INTEGER"/> <!-- 汇率版本 -->
        <result property="contractRecalFlag" column="contract_recal_flag" jdbcType="CHAR"/> <!-- 合同额度汇率重算标记 -->
        <result property="seq" column="seq" jdbcType="INTEGER"/> <!-- 额度序号 -->
        <result property="excessOccupationType" column="excess_occupation_type"
                jdbcType="CHAR"/> <!-- 超额占用方式;EnumExcessOccupationType:001-压缩占用、002-超额占用 -->
        <result property="instId" column="inst_id" jdbcType="VARCHAR"/> <!-- 实例编号 -->
        <result property="relationId" column="relation_id" jdbcType="VARCHAR"/> <!-- 关联编号 -->
        <result property="bizLine" column="biz_line" jdbcType="CHAR"/> <!-- 业务条线 -->
        <result property="supportSharedFlag" column="support_shared_flag"
                jdbcType="CHAR"/> <!-- 是否允许被串用 EnumBool(Y-是，N-否) -->
        <result property="supportShareFlag" column="support_share_flag"
                jdbcType="CHAR"/> <!-- 是否允许串用 EnumBool(Y-是，N-否) -->
        <result property="limitGraceTerm" column="limit_grace_term" jdbcType="INTEGER"/> <!-- 宽限期限 -->
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
        <result property="custLimitId" column="cust_limit_id" jdbcType="VARCHAR"/> <!-- 额度编号 -->
        <result property="limitStatus" column="limit_status"
                jdbcType="CHAR"/> <!-- 额度状态;EnumCustLimitStatus(010-未生效，020-生效，030-冻结，040-终止-失效，050-到期-失效) -->
        <result property="operatorId" column="operator_id" jdbcType="VARCHAR"/> <!-- 操作人编号 -->
        <result property="ownOrganId" column="own_organ_id" jdbcType="VARCHAR"/> <!-- 所属组织编号 -->
        <result property="effectiveStartTime" column="effective_start_time" jdbcType="TIMESTAMP"/> <!-- 生效起始时间 -->
        <result property="effectiveEndTime" column="effective_end_time" jdbcType="TIMESTAMP"/> <!-- 生效结束时间 -->
        <result property="limitTerm" column="limit_term" jdbcType="INTEGER"/> <!-- 期限 -->
        <result property="limitTermUnit" column="limit_term_unit" jdbcType="CHAR"/> <!-- 期限单位;001-年、002-月、003-日 -->
        <result property="tenantId" column="tenant_id" jdbcType="CHAR"/> <!-- 租户号 -->
        <result property="limitGraceTermUnit" column="limit_grace_term_unit"
                jdbcType="CHAR"/> <!-- 宽限期限单位;001-年、002-月、003-日 -->
        <result property="limitLastTime" column="limit_last_time" jdbcType="TIMESTAMP"/> <!-- 额度最后到期日 -->
        <result property="limitApprovalDate" column="limit_approval_date" jdbcType="TIMESTAMP"/> <!-- 批复日期 -->
        <result property="limitEnableEndTime" column="limit_enable_end_time" jdbcType="TIMESTAMP"/> <!-- 额度启动到期日 -->
        <result property="limitEnableTerm" column="limit_enable_term" jdbcType="INTEGER"/> <!-- 启用期限 -->
        <result property="limitEnableTermUnit" column="limit_enable_term_unit" jdbcType="CHAR"/> <!-- 启用期限单位 -->
        <result property="outCustLimitId" column="out_cust_limit_id" jdbcType="VARCHAR"/> <!-- 三方额度编号 -->
        <result property="custLimitTypeId" column="cust_limit_type_id" jdbcType="VARCHAR"/> <!-- 额度类型编号 -->
        <result property="templateNodeId" column="template_node_id" jdbcType="VARCHAR"/> <!-- 模板节点编号 -->
        <result property="limitTemplateId" column="limit_template_id" jdbcType="VARCHAR"/> <!-- 额度模板编号 -->
        <result property="virtualContractFlag" column="virtual_contract_flag" jdbcType="CHAR"/> <!-- 是否虚拟合同标志 -->
    </resultMap>
    <sql id="Base_Column_List">
        contract_limit_flag
        , limit_core_object_id
        , limit_object_type
        , use_occupy_times
        , occupy_times_limit
        , product_id
        , limit_level
        , limit_classification
        , limit_usage_type
        , limit_grant_type
        , limit_occupation_type
        , up_flag
        , limit_object_id
        , sole_flag
        , exchange_rate_version
        , contract_recal_flag
        , seq
        , excess_occupation_type
        , inst_id
        , relation_id
        , biz_line
        , support_shared_flag
        , support_share_flag
        , limit_grace_term
        , create_time
        , update_time
        , cust_limit_id
        , limit_status
        , operator_id
        , own_organ_id
        , effective_start_time
        , effective_end_time
        , limit_term
        , limit_term_unit
        , tenant_id
        , limit_grace_term_unit
        , limit_last_time
        , limit_approval_date
        , limit_enable_end_time
        , limit_enable_term
        , limit_enable_term_unit
        , out_cust_limit_id
        , cust_limit_type_id
        , template_node_id
        , limit_template_id
        , virtual_contract_flag
    </sql>


    <select id="selectCountByCurrentGroup"
            parameterType="com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery"
            resultType="java.lang.Integer">
        SELECT count(*)
        FROM lc_cust_limit_info WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectFirstOne" parameterType="com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_cust_limit_info
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
        <include refid="fixQuerySqlLimit"/>
    </select>
    <select id="selectShardList" parameterType="com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_cust_limit_info
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
        <include refid="fixQuerySqlLimit"/>
    </select>
    <sql id="fixQuerySql">
        <if test="query.limitStatus != null and query.limitStatus!=''">
            AND limit_status = #{query.limitStatus}
        </if>
        <if test="query.contractLimitFlag != null and query.contractLimitFlag!=''">
            AND contract_limit_flag = #{query.contractLimitFlag}
        </if>
        <if test="query.limitLevel != null and query.limitLevel!=''">
            AND limit_level = #{query.limitLevel}
        </if>
        <if test="query.custLimitId != null and query.custLimitId!=''">
            AND cust_limit_id <![CDATA[ > ]]> #{query.custLimitId}
        </if>
        ORDER BY cust_limit_id
    </sql>
    <sql id="fixQuerySqlLimit">
        <if test="query.offset != null and query.limit!=null">
            limit ${query.offset},${query.limit}
        </if>
    </sql>

</mapper>