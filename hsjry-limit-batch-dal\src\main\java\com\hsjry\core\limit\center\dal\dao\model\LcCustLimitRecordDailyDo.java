package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度记录日报Do
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
@Table(name = "lc_cust_limit_record_daily")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitRecordDailyDo extends LcCustLimitRecordDailyKeyDo implements Serializable {
    private static final long serialVersionUID = 1602582710713516053L;
    /** 已用敞口额度 */
    @Column(name = "used_open_amount")
    private java.math.BigDecimal usedOpenAmount;
    /** 已用低风险额度 */
    @Column(name = "used_low_risk_amount")
    private java.math.BigDecimal usedLowRiskAmount;
    /** 已用额度 */
    @Column(name = "used_amount")
    private java.math.BigDecimal usedAmount;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 总敞口额度 */
    @Column(name = "total_open_amount")
    private java.math.BigDecimal totalOpenAmount;
    /** 总低风险额度 */
    @Column(name = "total_low_risk_amount")
    private java.math.BigDecimal totalLowRiskAmount;
    /** 总额度 */
    @Column(name = "total_amount")
    private java.math.BigDecimal totalAmount;
    /** 统计分类;EnumStatisticsType(001-统一授信额度，002-授信额度，003-他用额度，004-产品额度) */
    @Column(name = "statistics_type")
    private String statisticsType;
    /** 可用额度 */
    @Column(name = "available_amount")
    private java.math.BigDecimal availableAmount;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 额度状态;EnumCustLimitStatus(010-未生效，020-生效，030-冻结，040-终止-失效，050-到期-失效) */
    @Column(name = "limit_status")
    private String limitStatus;
    /** 额度编号 */
    @Column(name = "cust_limit_id")
    private String custLimitId;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 可用敞口额度 */
    @Column(name = "available_open_amount")
    private java.math.BigDecimal availableOpenAmount;
    /** 可用低风险额度 */
    @Column(name = "available_low_risk_amount")
    private java.math.BigDecimal availableLowRiskAmount;
}
