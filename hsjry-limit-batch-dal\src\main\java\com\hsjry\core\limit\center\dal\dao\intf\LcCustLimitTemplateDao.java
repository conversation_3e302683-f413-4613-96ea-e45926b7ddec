package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitTemplateDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitTemplateQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度体系模板数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-07 06:17:16
 */
public interface LcCustLimitTemplateDao extends IBaseDao<LcCustLimitTemplateDo> {
    /**
     * 分页查询额度体系模板
     *
     * @param lcCustLimitTemplateQuery 条件
     * @return PageInfo<LcCustLimitTemplateDo>
     */
    PageInfo<LcCustLimitTemplateDo> selectPage(LcCustLimitTemplateQuery lcCustLimitTemplateQuery, PageParam pageParam);

    /**
     * 根据key查询额度体系模板
     *
     * @param limitTemplateId
     * @return
     */
    LcCustLimitTemplateDo selectByKey(String limitTemplateId);

    /**
     * 根据key删除额度体系模板
     *
     * @param limitTemplateId
     * @return
     */
    int deleteByKey(String limitTemplateId);

    /**
     * 导出时查询额度体系模板信息
     *
     * @param lcCustLimitTemplateQuery 条件
     * @return List<LcCustLimitTemplateDo>
     */
    List<LcCustLimitTemplateDo> selectExcelByExample(LcCustLimitTemplateQuery lcCustLimitTemplateQuery);


    /**
     * 查询额度体系模板信息
     *
     * @param lcCustLimitTemplateQuery 条件
     * @return List<LcCustLimitTemplateDo>
     */
    List<LcCustLimitTemplateDo> selectByExample(LcCustLimitTemplateQuery lcCustLimitTemplateQuery);

    /**
     * 新增额度体系模板信息
     *
     * @param lcCustLimitTemplate 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitTemplateDo lcCustLimitTemplate);

    /**
     * 修改额度体系模板信息
     *
     * @param lcCustLimitTemplate
     * @return
     */
    int updateBySelective(LcCustLimitTemplateDo lcCustLimitTemplate);

    /**
     * 修改额度体系模板信息
     *
     * @param lcCustLimitTemplate
     * @param lcCustLimitTemplateQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitTemplateDo lcCustLimitTemplate,
        LcCustLimitTemplateQuery lcCustLimitTemplateQuery);

    /**
     * 删除额度体系模板信息
     *
     * @param lcCustLimitTemplateQuery 条件
     * @return List<LcCustLimitTemplateDo>
     */
    int deleteByExample(LcCustLimitTemplateQuery lcCustLimitTemplateQuery);
}
