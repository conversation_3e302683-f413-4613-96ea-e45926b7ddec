package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 网贷系统-落地表-借据信息（记录客户借款信息）Do
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Table(name = "lb_s_ol_loan_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbSOlLoanInfoDo extends LbSOlLoanInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1942415996337979398L;
    /** 所属组织id */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 渠道编号 */
    @Column(name = "channel_no")
    private String channelNo;
    /** 分期金额 */
    @Column(name = "installment_amount")
    private java.math.BigDecimal installmentAmount;
    /** 分期期数 */
    @Column(name = "installment_num")
    private Integer installmentNum;
    /** 借据状态(1-正常,2-逾期,3-结清) */
    @Column(name = "status")
    private String status;
    /** 业务标识 */
    @Column(name = "business_sign")
    private String businessSign;
    /** 贷款分类 */
    @Column(name = "classification")
    private String classification;
    /** 营销中心编号 */
    @Column(name = "market_center_id")
    private String marketCenterId;
    /** 结清日期 */
    @Column(name = "settle_date")
    private java.util.Date settleDate;
    /** 操作者编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 门店名称 */
    @Column(name = "store_name")
    private String storeName;
    /** 客户经理编号 */
    @Column(name = "cust_mgr_id")
    private String custMgrId;
    /** 客户经理机构编号 */
    @Column(name = "cust_mgr_organ_id")
    private String custMgrOrganId;
    /** 代扣协议编号 */
    @Column(name = "withhold_protocol_id")
    private String withholdProtocolId;
    /** 合同编号 */
    @Column(name = "contract_id")
    private String contractId;
    /** 授信申请编号 */
    @Column(name = "credit_apply_id")
    private String creditApplyId;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 借款到期时间 */
    @Column(name = "loan_end_time")
    private java.util.Date loanEndTime;
    /** 用户编号 */
    @Column(name = "user_id")
    private String userId;
    /** 用户姓名 */
    @Column(name = "user_name")
    private String userName;
    /** 客户手机号码 */
    @Column(name = "user_tel")
    private String userTel;
    /** 客户证件类型 */
    @Column(name = "certificate_type")
    private String certificateType;
    /** 客户证件号码 */
    @Column(name = "certificate_no")
    private String certificateNo;
    /** 借款金额 */
    @Column(name = "loan_amount")
    private java.math.BigDecimal loanAmount;
    /** 借款利率 */
    @Column(name = "rate")
    private java.math.BigDecimal rate;
    /** 借款起始时间 */
    @Column(name = "loan_start_time")
    private java.util.Date loanStartTime;
    /** 资产借据编号 */
    @Column(name = "loan_invoice_id")
    private String loanInvoiceId;
    /** 放款方式 */
    @Column(name = "loan_type")
    private String loanType;
    /** 还款日 */
    @Column(name = "repay_day")
    private Integer repayDay;
    /** 产品编号 */
    @Column(name = "product_id")
    private String productId;
    /** 产品名称 */
    @Column(name = "product_name")
    private String productName;
    /** 产品种类 */
    @Column(name = "product_catalog")
    private String productCatalog;
    /** 商户或者合作方编号 */
    @Column(name = "merchant_id")
    private String merchantId;
    /** 商户名称 */
    @Column(name = "merchant_name")
    private String merchantName;
    /** 门店id */
    @Column(name = "store_id")
    private String storeId;
    /** 放款支付时间 */
    @Column(name = "loan_pay_time")
    private java.util.Date loanPayTime;
}
