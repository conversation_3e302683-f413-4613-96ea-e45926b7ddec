package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationHisDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitRelationHisQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度关联历史数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-11-13 01:05:13
 */
public interface LcCustLimitRelationHisDao extends IBaseDao<LcCustLimitRelationHisDo> {
    /**
     * 分页查询额度关联历史
     *
     * @param lcCustLimitRelationHisQuery 条件
     * @return PageInfo<LcCustLimitRelationHisDo>
     */
    PageInfo<LcCustLimitRelationHisDo> selectPage(LcCustLimitRelationHisQuery lcCustLimitRelationHisQuery,
        PageParam pageParam);

    /**
     * 根据key查询额度关联历史
     *
     * @param hisId
     * @param limitRelationId
     * @return
     */
    LcCustLimitRelationHisDo selectByKey(String hisId, String limitRelationId);

    /**
     * 根据key删除额度关联历史
     *
     * @param hisId
     * @param limitRelationId
     * @return
     */
    int deleteByKey(String hisId, String limitRelationId);

    /**
     * 查询额度关联历史信息
     *
     * @param lcCustLimitRelationHisQuery 条件
     * @return List<LcCustLimitRelationHisDo>
     */
    List<LcCustLimitRelationHisDo> selectByExample(LcCustLimitRelationHisQuery lcCustLimitRelationHisQuery);

    /**
     * 新增额度关联历史信息
     *
     * @param lcCustLimitRelationHis 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitRelationHisDo lcCustLimitRelationHis);

    /**
     * 修改额度关联历史信息
     *
     * @param lcCustLimitRelationHis
     * @return
     */
    int updateBySelective(LcCustLimitRelationHisDo lcCustLimitRelationHis);

    /**
     * 修改额度关联历史信息
     *
     * @param lcCustLimitRelationHis
     * @param lcCustLimitRelationHisQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitRelationHisDo lcCustLimitRelationHis,
        LcCustLimitRelationHisQuery lcCustLimitRelationHisQuery);

    /**
     * 批量更新客户编号
     *
     * @return
     */
    int updateCustNo();
}
