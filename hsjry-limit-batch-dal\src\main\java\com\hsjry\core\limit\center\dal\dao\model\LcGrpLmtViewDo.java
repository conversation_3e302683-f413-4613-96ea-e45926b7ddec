package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 集团客户额度视图Do
 *
 * <AUTHOR>
 * @date 2025-07-07 12:58:26
 */
@Table(name = "lc_grp_lmt_view")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcGrpLmtViewDo extends LcGrpLmtViewKeyDo implements Serializable {
    private static final long serialVersionUID = 1942206575120941061L;
    /** 集团非授信额度(元) */
    @Column(name = "grp_no_crdt_lmt")
    private java.math.BigDecimal grpNoCrdtLmt;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 合作方可用额度(元) */
    @Column(name = "grp_co_prtn_avl_lmt")
    private java.math.BigDecimal grpCoPrtnAvlLmt;
    /** 集团总合作方额度(元) */
    @Column(name = "grp_co_prtn_lmt")
    private java.math.BigDecimal grpCoPrtnLmt;
    /** 集团总担保额度(元) */
    @Column(name = "grp_guar_lmt")
    private java.math.BigDecimal grpGuarLmt;
    /** 集团纯低风险可用额度(元) */
    @Column(name = "grp_whl_low_risk_avl_lmt")
    private java.math.BigDecimal grpWhlLowRiskAvlLmt;
    /** 集团一般授信可用额度(元) */
    @Column(name = "grp_com_crdt_avl_lmt")
    private java.math.BigDecimal grpComCrdtAvlLmt;
    /** 集团纯低风险额度(元) */
    @Column(name = "grp_whl_low_risk_lmt")
    private java.math.BigDecimal grpWhlLowRiskLmt;
    /** 集团一般授信额度(元) */
    @Column(name = "grp_com_crdt_lmt")
    private java.math.BigDecimal grpComCrdtLmt;
    /** 集团综合授信额度(元) */
    @Column(name = "grp_cprsv_crdt_lmt")
    private java.math.BigDecimal grpCprsvCrdtLmt;
    /** 客户编号 */
    @Column(name = "cust_no")
    private String custNo;
    /** 客户名称 */
    @Column(name = "cust_nm")
    private String custNm;
}
