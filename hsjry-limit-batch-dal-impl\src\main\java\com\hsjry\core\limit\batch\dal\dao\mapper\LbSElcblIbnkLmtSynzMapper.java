package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblIbnkLmtSynzDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblIbnkLmtSynzExample;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 电票系统-落地表-同业客户额度同步mapper
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
public interface LbSElcblIbnkLmtSynzMapper extends CommonMapper<LbSElcblIbnkLmtSynzDo> {
    /**
     * 批量插入电票系统-落地表-同业客户额度同步信息
     *
     * @param lbSElcblIbnkLmtSynzList 批量数据
     * @return int
     */
    @Override
    int insertList(@Param("list") List<LbSElcblIbnkLmtSynzDo> lbSElcblIbnkLmtSynzList);

    /**
     * 清空电票系统-落地表-同业客户额度同步表所有数据
     *
     * @return int
     */
    int deleteAll();
    
    /**
     * 根据条件查询数据量
     *
     * @param example 条件
     * @return long
     */
    long countByExample(LbSElcblIbnkLmtSynzExample example);
}
