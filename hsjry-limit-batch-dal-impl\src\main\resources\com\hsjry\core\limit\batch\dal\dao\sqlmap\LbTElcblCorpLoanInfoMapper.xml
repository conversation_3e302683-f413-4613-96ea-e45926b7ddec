<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbTElcblCorpLoanInfoMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpLoanInfoDo">
        <result property="entityRelationId" column="entity_relation_id" jdbcType="VARCHAR"/> <!-- 实体业务编号 -->
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/> <!-- 租户号 -->
        <result property="ownOrganId" column="own_organ_id" jdbcType="VARCHAR"/> <!-- 所属组织编号 -->
        <result property="operatorId" column="operator_id" jdbcType="VARCHAR"/> <!-- 操作人编号 -->
        <result property="leftLowRisk" column="left_low_risk" jdbcType="DECIMAL"/> <!-- 低风险余额 -->
        <result property="lowRisk" column="low_risk" jdbcType="DECIMAL"/> <!-- 发放低风险 -->
        <result property="leftAmount" column="left_amount" jdbcType="DECIMAL"/> <!-- 当前余额 -->
        <result property="amount" column="amount" jdbcType="DECIMAL"/> <!-- 发放金额 -->
        <result property="custNo" column="cust_no" jdbcType="VARCHAR"/> <!-- 客户编号 -->
        <result property="entityApplyId" column="entity_apply_id" jdbcType="VARCHAR"/> <!-- 实体申请编号 -->
        <result property="status" column="status" jdbcType="VARCHAR"/> <!-- 状态;EnumEntityStatus -->
        <result property="entityType" column="entity_type" jdbcType="VARCHAR"/> <!-- 实体类型;EnumLimitCenterEntityType -->
        <result property="entityId" column="entity_id" jdbcType="VARCHAR"/> <!-- 实体编号 -->
        <result property="custLimitId" column="cust_limit_id" jdbcType="VARCHAR"/> <!-- 额度编号 -->
        <result property="certNo" column="cert_no" jdbcType="VARCHAR"/> <!-- 证件号码 -->
        <result property="certTyp" column="cert_typ" jdbcType="VARCHAR"/> <!-- 证件类型 -->
        <result property="custNm" column="cust_nm" jdbcType="VARCHAR"/> <!-- 客户名称 -->
        <result property="custTyp" column="cust_typ" jdbcType="VARCHAR"/> <!-- 客户类型 -->
    </resultMap>
    <sql id="Base_Column_List">
        entity_relation_id
        , update_time
                , create_time
                , tenant_id
                , own_organ_id
                , operator_id
                , left_low_risk
                , low_risk
                , left_amount
                , amount
                , cust_no
                , entity_apply_id
                , status
                , entity_type
                , entity_id
                , cust_limit_id
                , cert_no
                , cert_typ
                , cust_nm
                , cust_typ
    </sql>

    <!-- 将电票对公客户额度同步数据插入到借据信息表 -->
    <insert id="insertElcblCorpLoanInfo">
        insert into lb_t_elcbl_corp_loan_info (
        cust_no, cust_typ, cust_nm, cert_typ, cert_no, cust_limit_id, entity_id,
        entity_type, status, entity_apply_id, entity_relation_id, amount, left_amount,
        low_risk, left_low_risk, operator_id, own_organ_id, tenant_id, create_time, update_time
        )
        select ltici.cust_no,
        ltici.cust_typ,
        ltici.cust_nm,
        ltici.cert_typ,
        ltici.cert_no,
        lcli.cust_limit_id,
        lei.entity_id,
        lei.entity_type,
        lei.status,
        lsedbi.bill_no || '_' || lsedbi.bill_range_start || '_' || lsedbi.bill_range_end as entity_apply_id,
        lsedbi.dic_cno as entity_relation_id,
        lsedbi.discount_amt as amount,
        lsedbi.discount_bal as left_amount,
        lsedbi.discount_amt as low_risk,
        lsedbi.discount_bal as left_low_risk,
        lcli.operator_id,
        lcli.own_organ_id,
        lcli.tenant_id,
        sysdate as create_time,
        sysdate as update_time
        from lb_s_elcbl_dsct_bal_info lsedbi
        inner join lb_t_corp_cust_info ltici on lsedbi.user_id = ltici.cust_no
        inner join lc_cust_limit_info lcli on lcli.limit_object_id = ltici.cust_no
        inner join lc_entity_info lei on lei.cust_limit_id = lcli.cust_limit_id
        where lcli.template_node_id in
        <if test="templateNodeIdList != null and templateNodeIdList.size() > 0">
            <foreach collection="templateNodeIdList" item="templateNodeId" open="(" separator="," close=")">
                #{templateNodeId}
            </foreach>
        </if>
        <if test="templateNodeIdList == null or templateNodeIdList.size() == 0">
            (null)
        </if>
        and lcli.cust_limit_id in
        <if test="custLimitIdList != null and custLimitIdList.size() > 0">
            <foreach collection="custLimitIdList" item="custLimitId" open="(" separator="," close=")">
                #{custLimitId}
            </foreach>
        </if>
        <if test="custLimitIdList == null or custLimitIdList.size() == 0">
            (null)
        </if>
    </insert>

    <!-- 更新实体信息表 -->
    <update id="updateEntityInfo">
        MERGE INTO LC_ENTITY_INFO lei
        USING lb_t_elcbl_corp_loan_info ltecli
        ON (ltecli.CUST_LIMIT_ID = lei.CUST_LIMIT_ID
        AND ltecli.ENTITY_APPLY_ID = lei.ENTITY_APPLY_ID
        AND ltecli.ENTITY_RELATION_ID = lei.ENTITY_RELATION_ID
        AND ltecli.cust_no = lei.LIMIT_OBJECT_ID)
        WHEN MATCHED THEN
        UPDATE
        SET lei.AMOUNT = ltecli.AMOUNT,
        lei.LEFT_AMOUNT = ltecli.LEFT_AMOUNT,
        lei.LOW_RISK = ltecli.LOW_RISK,
        lei.LEFT_LOW_RISK = ltecli.LEFT_LOW_RISK
        WHERE lei.CUST_LIMIT_ID IN
        <if test="custLimitIdList != null and custLimitIdList.size() > 0">
            <foreach collection="custLimitIdList" item="custLimitId" open="(" separator="," close=")">
                #{custLimitId}
            </foreach>
        </if>
        <if test="custLimitIdList == null or custLimitIdList.size() == 0">
            (null)
        </if>
    </update>

    <!-- 更新实体操作流水表 -->
    <update id="updateEntityOperateSerial">
        MERGE INTO LC_ENTITY_OPERATE_SERIAL leos
        USING lb_t_elcbl_corp_loan_info ltecli
        ON (
            ltecli.ENTITY_APPLY_ID = leos.ENTITY_APPLY_ID
                AND ltecli.ENTITY_RELATION_ID = leos.ENTITY_RELATION_ID
            )
        WHEN MATCHED THEN
        UPDATE
            SET leos.AMOUNT = ltecli.AMOUNT,
            leos.LOW_RISK_AMOUNT = ltecli.LOW_RISK
    </update>
</mapper>