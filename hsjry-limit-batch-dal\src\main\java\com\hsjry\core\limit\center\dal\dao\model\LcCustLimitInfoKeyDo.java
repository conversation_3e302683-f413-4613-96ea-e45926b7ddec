package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度实例信息主键
 *
 * <AUTHOR>
 * @date 2023-11-07 08:52:41
 */
@Table(name = "lc_cust_limit_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcCustLimitInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1721812908083707904L;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    /** 额度编号 */
    @Id
    @Column(name = "cust_limit_id")
    private String custLimitId;
}