package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTElcblCorpProdLmtInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-中间表-贴现对公产品层额度信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-29 02:46:35
 */
public interface LbTElcblCorpProdLmtInfoDao extends IBaseDao<LbTElcblCorpProdLmtInfoDo> {
    /**
     * 分页查询电票系统-中间表-贴现对公产品层额度信息
     *
     * @param lbTElcblCorpProdLmtInfoQuery 条件
     * @return PageInfo<LbTElcblCorpProdLmtInfoDo>
     */
    PageInfo<LbTElcblCorpProdLmtInfoDo> selectPage(LbTElcblCorpProdLmtInfoQuery lbTElcblCorpProdLmtInfoQuery,
        PageParam pageParam);

    /**
     * 根据key查询电票系统-中间表-贴现对公产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTElcblCorpProdLmtInfoDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除电票系统-中间表-贴现对公产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询电票系统-中间表-贴现对公产品层额度信息信息
     *
     * @param lbTElcblCorpProdLmtInfoQuery 条件
     * @return List<LbTElcblCorpProdLmtInfoDo>
     */
    List<LbTElcblCorpProdLmtInfoDo> selectByExample(LbTElcblCorpProdLmtInfoQuery lbTElcblCorpProdLmtInfoQuery);

    /**
     * 新增电票系统-中间表-贴现对公产品层额度信息信息
     *
     * @param lbTElcblCorpProdLmtInfo 条件
     * @return int>
     */
    int insertBySelective(LbTElcblCorpProdLmtInfoDo lbTElcblCorpProdLmtInfo);

    /**
     * 修改电票系统-中间表-贴现对公产品层额度信息信息
     *
     * @param lbTElcblCorpProdLmtInfo
     * @return
     */
    int updateBySelective(LbTElcblCorpProdLmtInfoDo lbTElcblCorpProdLmtInfo);

    /**
     * 修改电票系统-中间表-贴现对公产品层额度信息信息
     *
     * @param lbTElcblCorpProdLmtInfo
     * @param lbTElcblCorpProdLmtInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTElcblCorpProdLmtInfoDo lbTElcblCorpProdLmtInfo,
        LbTElcblCorpProdLmtInfoQuery lbTElcblCorpProdLmtInfoQuery);

    /**
     * 将电票对公客户额度同步数据插入到产品层额度信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_elcbl_corp_prod_lmt_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    int insertElcblCorpProdLmtInfo(List<String> templateNodeIdList, List<String> custLimitIdList);

    /**
     * 更新额度实例金额信息表
     * 根据电票系统中间表数据更新LC_CUST_LIMIT_AMT_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    int updateCustLimitAmtInfo(List<String> custLimitIdList);
}
