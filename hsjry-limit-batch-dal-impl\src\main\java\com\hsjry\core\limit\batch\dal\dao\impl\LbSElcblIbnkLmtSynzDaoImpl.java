package com.hsjry.core.limit.batch.dal.dao.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbSElcblIbnkLmtSynzDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbSElcblIbnkLmtSynzMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblIbnkLmtSynzDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblIbnkLmtSynzExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblIbnkLmtSynzKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSElcblIbnkLmtSynzQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-落地表-同业客户额度同步数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Repository
public class LbSElcblIbnkLmtSynzDaoImpl extends AbstractBaseDaoImpl<LbSElcblIbnkLmtSynzDo, LbSElcblIbnkLmtSynzMapper>
    implements LbSElcblIbnkLmtSynzDao {
    private static final Logger log = LoggerFactory.getLogger(LbSElcblIbnkLmtSynzDaoImpl.class);

    /**
     * 分页查询
     *
     * @param lbSElcblIbnkLmtSynz 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbSElcblIbnkLmtSynzDo> selectPage(LbSElcblIbnkLmtSynzQuery lbSElcblIbnkLmtSynz,
        PageParam pageParam) {
        LbSElcblIbnkLmtSynzExample example = buildExample(lbSElcblIbnkLmtSynz);
        return PageHelper.<LbSElcblIbnkLmtSynzDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询电票系统-落地表-同业客户额度同步
     *
     * @param id 主键ID
     * @return
     */
    @Override
    public LbSElcblIbnkLmtSynzDo selectByKey(String id) {
        LbSElcblIbnkLmtSynzKeyDo lbSElcblIbnkLmtSynzKeyDo = new LbSElcblIbnkLmtSynzKeyDo();
        lbSElcblIbnkLmtSynzKeyDo.setId(id);
        return getMapper().selectByPrimaryKey(lbSElcblIbnkLmtSynzKeyDo);
    }

    /**
     * 根据key删除电票系统-落地表-同业客户额度同步
     *
     * @param id 主键ID
     * @return
     */
    @Override
    public int deleteByKey(String id) {
        LbSElcblIbnkLmtSynzKeyDo lbSElcblIbnkLmtSynzKeyDo = new LbSElcblIbnkLmtSynzKeyDo();
        lbSElcblIbnkLmtSynzKeyDo.setId(id);
        return getMapper().deleteByPrimaryKey(lbSElcblIbnkLmtSynzKeyDo);
    }

    /**
     * 查询电票系统-落地表-同业客户额度同步信息
     *
     * @param lbSElcblIbnkLmtSynz 条件
     * @return List<LbSElcblIbnkLmtSynzDo>
     */
    @Override
    public List<LbSElcblIbnkLmtSynzDo> selectByExample(LbSElcblIbnkLmtSynzQuery lbSElcblIbnkLmtSynz) {
        return getMapper().selectByExample(buildExample(lbSElcblIbnkLmtSynz));
    }

    /**
     * 新增电票系统-落地表-同业客户额度同步信息
     *
     * @param lbSElcblIbnkLmtSynz 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbSElcblIbnkLmtSynzDo lbSElcblIbnkLmtSynz) {
        if (lbSElcblIbnkLmtSynz == null) {
            return -1;
        }

        // 生成UUID作为主键ID
        if (lbSElcblIbnkLmtSynz.getId() == null) {
            lbSElcblIbnkLmtSynz.setId(UUID.randomUUID().toString());
        }

        // 获取日期时间对象
        LocalDateTime localDateTime = LocalDateTime.now();
        // 转换为字符串
        String dateTimeString = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        lbSElcblIbnkLmtSynz.setCreateTime(dateTimeString);
        lbSElcblIbnkLmtSynz.setUpdateTime(dateTimeString);
        return getMapper().insertSelective(lbSElcblIbnkLmtSynz);
    }

    /**
     * 修改电票系统-落地表-同业客户额度同步信息
     *
     * @param lbSElcblIbnkLmtSynz
     * @return
     */
    @Override
    public int updateBySelective(LbSElcblIbnkLmtSynzDo lbSElcblIbnkLmtSynz) {
        if (lbSElcblIbnkLmtSynz == null) {
            return -1;
        }
        // 获取日期时间对象
        LocalDateTime localDateTime = LocalDateTime.now();
        // 转换为字符串
        String dateTimeString = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        lbSElcblIbnkLmtSynz.setUpdateTime(dateTimeString);
        return getMapper().updateByPrimaryKeySelective(lbSElcblIbnkLmtSynz);
    }

    @Override
    public int updateBySelectiveByExample(LbSElcblIbnkLmtSynzDo lbSElcblIbnkLmtSynz,
        LbSElcblIbnkLmtSynzQuery lbSElcblIbnkLmtSynzQuery) {
        // 获取日期时间对象
        LocalDateTime localDateTime = LocalDateTime.now();
        // 转换为字符串
        String dateTimeString = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        lbSElcblIbnkLmtSynz.setUpdateTime(dateTimeString);
        return getMapper().updateByExampleSelective(lbSElcblIbnkLmtSynz, buildExample(lbSElcblIbnkLmtSynzQuery));
    }

    /**
     * 构建电票系统-落地表-同业客户额度同步Example信息
     *
     * @param lbSElcblIbnkLmtSynz
     * @return
     */
    public LbSElcblIbnkLmtSynzExample buildExample(LbSElcblIbnkLmtSynzQuery lbSElcblIbnkLmtSynz) {
        LbSElcblIbnkLmtSynzExample example = new LbSElcblIbnkLmtSynzExample();
        LbSElcblIbnkLmtSynzExample.Criteria criteria = example.createCriteria();
        if (lbSElcblIbnkLmtSynz != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbSElcblIbnkLmtSynz.getIbnkUserId())) {
                criteria.andIbnkUserIdEqualTo(lbSElcblIbnkLmtSynz.getIbnkUserId());
            }
            if (StringUtil.isNotEmpty(lbSElcblIbnkLmtSynz.getIbnkUserCertificateKind())) {
                criteria.andIbnkUserCertificateKindEqualTo(lbSElcblIbnkLmtSynz.getIbnkUserCertificateKind());
            }
            if (StringUtil.isNotEmpty(lbSElcblIbnkLmtSynz.getIbnkUserCertificateNo())) {
                criteria.andIbnkUserCertificateNoEqualTo(lbSElcblIbnkLmtSynz.getIbnkUserCertificateNo());
            }
            if (null != lbSElcblIbnkLmtSynz.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbSElcblIbnkLmtSynz.getTotalAmount());
            }
            if (null != lbSElcblIbnkLmtSynz.getAvailableAmount()) {
                criteria.andAvailableAmountEqualTo(lbSElcblIbnkLmtSynz.getAvailableAmount());
            }
            if (null != lbSElcblIbnkLmtSynz.getUseOccupyAmount()) {
                criteria.andUseOccupyAmountEqualTo(lbSElcblIbnkLmtSynz.getUseOccupyAmount());
            }
            if (StringUtil.isNotEmpty(lbSElcblIbnkLmtSynz.getCoreInstNo())) {
                criteria.andCoreInstNoEqualTo(lbSElcblIbnkLmtSynz.getCoreInstNo());
            }
        }
        buildExampleExt(lbSElcblIbnkLmtSynz, criteria);
        return example;
    }

    /**
     * 构建电票系统-落地表-同业客户额度同步ExampleExt方法
     *
     * @param lbSElcblIbnkLmtSynz
     * @return
     */
    public void buildExampleExt(LbSElcblIbnkLmtSynzQuery lbSElcblIbnkLmtSynz,
        LbSElcblIbnkLmtSynzExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 批量插入电票系统-落地表-同业客户额度同步信息
     *
     * @param lbSElcblIbnkLmtSynzList 批量数据
     * @return int
     */
    @Override
    public int insertList(List<LbSElcblIbnkLmtSynzDo> lbSElcblIbnkLmtSynzList) {
        if (lbSElcblIbnkLmtSynzList.isEmpty()) {
            return 0;
        }
        
        // 为每条记录生成UUID作为主键ID
        for (LbSElcblIbnkLmtSynzDo item : lbSElcblIbnkLmtSynzList) {
            if (item.getId() == null) {
                item.setId(UUID.randomUUID().toString());
            }
        }
        
        return getMapper().insertList(lbSElcblIbnkLmtSynzList);
    }

    /**
     * 清空电票系统-落地表-同业客户额度同步所有数据
     *
     * @return int
     */
    @Override
    public int deleteAll() {
        return getMapper().deleteAll();
    }

    /**
     * 获取第一个对象，用于分片查询
     * 根据ibnkUserId主键排序，获取指定偏移量的第一条记录
     *
     * @param query 查询条件
     * @return 第一条记录，如果没有则返回null
     */
    @Override
    public LbSElcblIbnkLmtSynzDo selectFirstOne(LbSElcblIbnkLmtSynzQuery query) {
        LbSElcblIbnkLmtSynzExample example = buildExample(query);
        example.setOrderByClause("ibnk_user_id ASC");
        List<LbSElcblIbnkLmtSynzDo> list = getMapper().selectByExample(example);
        return list != null && !list.isEmpty() ? list.get(0) : null;
    }

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据ibnkUserId主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含ibnkUserId范围
     * @return 当前分片的数据量
     */
    @Override
    public Integer selectCountByCurrentGroup(LbSElcblIbnkLmtSynzQuery query) {
        LbSElcblIbnkLmtSynzExample example = buildExample(query);
        return Math.toIntExact(getMapper().countByExample(example));
    }

}
