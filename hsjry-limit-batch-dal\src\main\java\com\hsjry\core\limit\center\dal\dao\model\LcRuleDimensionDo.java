package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 规则维度信息Do
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
@Table(name = "lc_rule_dimension")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcRuleDimensionDo extends LcRuleDimensionKeyDo implements Serializable {
    private static final long serialVersionUID = 1602582710713516056L;
    /** 全维度值标记;EnumBool Y-是，N-否 */
    @Column(name = "all_dimension_value_flag")
    private String allDimensionValueFlag;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 维度编码 */
    @Column(name = "dimension_code")
    private String dimensionCode;
    /** 维度比对类型;EnumDimensionCompareType: 001-全匹配，002-范围【前后包含】 */
    @Column(name = "dimension_compare_type")
    private String dimensionCompareType;
    /** 维度最大值 */
    @Column(name = "dimension_max_value")
    private String dimensionMaxValue;
    /** 维度最小值 */
    @Column(name = "dimension_min_value")
    private String dimensionMinValue;
    /** 维度名称 */
    @Column(name = "dimension_name")
    private String dimensionName;
    /** 维度值 */
    @Column(name = "dimension_value")
    private String dimensionValue;
    /** 维度值描述 */
    @Column(name = "dimension_value_desc")
    private String dimensionValueDesc;
    /** 规则编号 */
    @Column(name = "rule_id")
    private String ruleId;
    /** 维度序号;不同维度序号不一致，用于排序 */
    @Column(name = "seq_no")
    private Integer seqNo;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
}
