<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbHElcblDsctBalInfoMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbHElcblDsctBalInfoDo">
        <result property="orgNo" column="org_no" jdbcType="VARCHAR"/> <!-- 法人行机构编号 -->
        <result property="dicCno" column="dic_cno" jdbcType="VARCHAR"/> <!-- 贴现编号(核心借据号) -->
        <result property="billNo" column="bill_no" jdbcType="VARCHAR"/> <!-- 票据编号 -->
        <result property="billRangeStart" column="bill_range_start" jdbcType="VARCHAR"/> <!-- 子票区间起始 -->
        <result property="billRangeEnd" column="bill_range_end" jdbcType="VARCHAR"/> <!-- 子票区间截止 -->
        <result property="userId" column="user_id" jdbcType="VARCHAR"/> <!-- 贴现客户编号(对公客户编号) -->
        <result property="userName" column="user_name" jdbcType="VARCHAR"/> <!-- 贴现客户名称(对公客户名称) -->
        <result property="currency" column="currency" jdbcType="VARCHAR"/> <!-- 票据币种 -->
        <result property="discountAmt" column="discount_amt" jdbcType="DECIMAL"/> <!-- 贴现金额(票面金额) -->
        <result property="discountBal" column="discount_bal" jdbcType="DECIMAL"/> <!-- 贴现余额 -->
        <result property="startDate" column="start_date" jdbcType="VARCHAR"/> <!-- 贴现起始日期 -->
        <result property="endDate" column="end_date" jdbcType="VARCHAR"/> <!-- 贴现到期日期 -->
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/> <!-- 更新时间 -->
        <result property="dataDate" column="data_date" jdbcType="VARCHAR"/> <!-- 数据日期 -->
    </resultMap>

    <sql id="Base_Column_List">
        org_no, dic_cno, bill_no, bill_range_start, bill_range_end, user_id, user_name, currency, discount_amt, discount_bal, start_date, end_date, create_time, update_time, data_date
    </sql>

    <!-- 批量插入历史表贴现余额信息 -->
    <insert id="insertList" parameterType="java.util.List">
        INSERT ALL
        <foreach collection="list" item="item" separator="">
            INTO lb_h_elcbl_dsct_bal_info (
            org_no, dic_cno, bill_no, bill_range_start, bill_range_end, user_id, user_name, currency, discount_amt,
            discount_bal, start_date, end_date, create_time, update_time, data_date
            ) VALUES (
            #{item.orgNo, jdbcType=VARCHAR},
            #{item.dicCno, jdbcType=VARCHAR},
            #{item.billNo, jdbcType=VARCHAR},
            #{item.billRangeStart, jdbcType=VARCHAR},
            #{item.billRangeEnd, jdbcType=VARCHAR},
            #{item.userId, jdbcType=VARCHAR},
            #{item.userName, jdbcType=VARCHAR},
            #{item.currency, jdbcType=VARCHAR},
            #{item.discountAmt, jdbcType=DECIMAL},
            #{item.discountBal, jdbcType=DECIMAL},
            #{item.startDate, jdbcType=VARCHAR},
            #{item.endDate, jdbcType=VARCHAR},
            #{item.createTime, jdbcType=VARCHAR},
            #{item.updateTime, jdbcType=VARCHAR},
            #{item.dataDate, jdbcType=VARCHAR}
            )
        </foreach>
        SELECT * FROM DUAL
    </insert>

    <!-- 清空历史表贴现余额信息所有数据 -->
    <delete id="deleteAll">
        TRUNCATE TABLE lb_h_elcbl_dsct_bal_info
    </delete>

    <!-- 根据数据日期删除历史表贴现余额信息记录 -->
    <delete id="deleteByDataDate" parameterType="java.lang.String">
        DELETE FROM lb_h_elcbl_dsct_bal_info WHERE data_date = #{dataDate}
    </delete>
</mapper>