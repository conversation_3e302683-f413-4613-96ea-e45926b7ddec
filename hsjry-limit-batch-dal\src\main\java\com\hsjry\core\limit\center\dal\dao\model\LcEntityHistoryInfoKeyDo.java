package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 实体历史信息主键
 *
 * <AUTHOR>
 * @date 2023-04-17 11:39:12
 */
@Table(name = "lc_entity_history_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcEntityHistoryInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1647927686536036352L;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    /** 实体编号 */
    @Id
    @Column(name = "entity_id")
    private String entityId;
}