--1.查询[额度中心-中间表-对公客户信息]
select ltcci.CUST_NO, ltcci.*
from LB_T_CORP_CUST_INFO ltcci
where 1 = 1;
select *
from lc_corp_lmt_view;
truncate table lc_corp_lmt_view;

--1.处理客户信息,使用MERGE语句实现存在则更新,不存在则插入
merge into lc_corp_lmt_view tgt
using (select ltcci.tenant_id,
              ltcci.cust_no,
              ltcci.cust_nm,
              ltcci.cert_typ,
              ltcci.cert_no
       from lb_t_corp_cust_info ltcci
       where 1 = 1) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.cust_nm = src.cust_nm,
    tgt.cert_typ = src.cert_typ,
    tgt.cert_no = src.cert_no
    when not matched then
insert (lc_corp_lmt_view_id, cust_nm, cust_no, cert_typ, cert_no,
    tenant_id)
    values
(generate_primary_key('LCLV'),
    src.cust_nm,
    src.cust_no,
    src.cert_typ,
    src.cert_no,
    src.tenant_id);

--2.更新总额度/敞口额度/敞口可用额度
select lcli.tenant_id                             as tenant_id,
       lcli.limit_object_id                       as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.total_amount                         as totl_crdt_lmt,
       lclai.total_amount - lclai.low_risk_amount as esr_lmt,
       (lclai.total_amount - lclai.low_risk_amount) - (lclai.pre_occupy_amount - lclai.pre_occupy_low_risk_amt) -
       (lclai.real_occupy_amount - lclai.real_occupy_low_risk_amt)
                                                  as esr_avl_lmt,
       lcli.operator_id                           as operator_id,
       lcli.own_organ_id                          as own_organ_id,
       lcli.create_time                           as create_time
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai
                    on lcli.cust_limit_id = lclai.cust_limit_id
                        and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSDGKHEDTX')
  and lcli.template_node_id in ('DGZED');

merge into lc_corp_lmt_view tgt
using (select lcli.tenant_id                             as tenant_id,
              lcli.limit_object_id                       as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.total_amount                         as totl_crdt_lmt,
              lclai.total_amount - lclai.low_risk_amount as esr_lmt,
              (lclai.total_amount - lclai.low_risk_amount) - (lclai.pre_occupy_amount - lclai.pre_occupy_low_risk_amt) -
              (lclai.real_occupy_amount - lclai.real_occupy_low_risk_amt)
                                                         as esr_avl_lmt,
              lcli.operator_id                           as operator_id,
              lcli.own_organ_id                          as own_organ_id,
              lcli.create_time                           as create_time
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai
                           on lcli.tenant_id = lclai.tenant_id
                               and lcli.cust_limit_id = lclai.cust_limit_id
                               and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSDGKHEDTX')
         and lcli.template_node_id in ('DGZED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.totl_crdt_lmt = src.totl_crdt_lmt,
    tgt.esr_lmt = src.esr_lmt,
    tgt.esr_avl_lmt = src.esr_avl_lmt,
    tgt.operator_id = src.operator_id,
    tgt.own_organ_id = src.own_organ_id,
    tgt.create_time = src.create_time,
    tgt.update_time = sysdate;
-- 更新时间
-- type='P'（可打印字符，包含字母、数字、常用符号）


--3.更新低风险分项额度/低风险分项可用额度
select lcli.tenant_id                 as tenant_id,
       lcli.limit_object_id           as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.low_risk_amount          as low_risk_sub_itm_lmt,
       lclai.low_risk_amount - lclai.pre_occupy_low_risk_amt -
       lclai.real_occupy_low_risk_amt as low_risk_sub_itm_avl_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai
                    on lcli.tenant_id = lclai.tenant_id
                        and lcli.cust_limit_id = lclai.cust_limit_id
                        and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSDGKHEDTX')
  and lcli.template_node_id in ('DGDFXFXED');

merge into lc_corp_lmt_view tgt
using (select lcli.tenant_id                 as tenant_id,
              lcli.limit_object_id           as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.low_risk_amount          as low_risk_sub_itm_lmt,
              lclai.low_risk_amount - lclai.pre_occupy_low_risk_amt -
              lclai.real_occupy_low_risk_amt as low_risk_sub_itm_avl_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai
                           on lcli.tenant_id = lclai.tenant_id
                               and lcli.cust_limit_id = lclai.cust_limit_id
                               and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSDGKHEDTX')
         and lcli.template_node_id in ('DGDFXFXED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.low_risk_sub_itm_lmt = src.low_risk_sub_itm_lmt,
    tgt.low_risk_sub_itm_avl_lmt = src.low_risk_sub_itm_avl_lmt,
    tgt.update_time = sysdate;

--4.更新一般授信额度期限/一般授信额度期限单位
select lcli.tenant_id       as tenant_id,
       lcli.limit_object_id as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lcli.limit_term      as com_crdt_term,
       lcli.limit_term_unit as com_crdt_term_unit
from lc_cust_limit_info lcli
where lcli.limit_template_id in ('HNNSDGKHEDTX')
  and lcli.template_node_id in ('DGYBSXED');

merge into lc_corp_lmt_view tgt
using (select lcli.tenant_id       as tenant_id,
              lcli.limit_object_id as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lcli.limit_term      as com_crdt_term,
              lcli.limit_term_unit as com_crdt_term_unit
       from lc_cust_limit_info lcli
       where lcli.limit_template_id in ('HNNSDGKHEDTX')
         and lcli.template_node_id in ('DGYBSXED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.com_crdt_term = src.com_crdt_term,
    tgt.com_crdt_term_unit = src.com_crdt_term_unit,
    tgt.update_time = sysdate;

--5.更新纯低风险额度/纯低风险可用额度/纯低风险额度期限/纯低风险额度期限单位
select lcli.tenant_id                 as tenant_id,
       lcli.limit_object_id           as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.low_risk_amount          as whl_low_risk_lmt,
       lclai.low_risk_amount - lclai.pre_occupy_low_risk_amt -
       lclai.real_occupy_low_risk_amt as whl_low_risk_avl_lmt,
       lcli.limit_term                as whl_low_risk_crdt_term,
       lcli.limit_term_unit           as whl_low_risk_crdt_term_unit
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai
                    on lcli.tenant_id = lclai.tenant_id
                        and lcli.cust_limit_id = lclai.cust_limit_id
                        and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSDGKHEDTX')
  and lcli.template_node_id in ('DGCDFXED');

merge into lc_corp_lmt_view tgt
using (select lcli.tenant_id                 as tenant_id,
              lcli.limit_object_id           as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.low_risk_amount          as whl_low_risk_lmt,
              lclai.low_risk_amount - lclai.pre_occupy_low_risk_amt -
              lclai.real_occupy_low_risk_amt as whl_low_risk_avl_lmt,
              lcli.limit_term                as whl_low_risk_crdt_term,
              lcli.limit_term_unit           as whl_low_risk_crdt_term_unit
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai
                           on lcli.tenant_id = lclai.tenant_id
                               and lcli.cust_limit_id = lclai.cust_limit_id
                               and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSDGKHEDTX')
         and lcli.template_node_id in ('DGCDFXED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.whl_low_risk_lmt = src.whl_low_risk_lmt,
    tgt.whl_low_risk_avl_lmt = src.whl_low_risk_avl_lmt,
    tgt.whl_low_risk_crdt_term = src.whl_low_risk_crdt_term,
    tgt.whl_low_risk_crdt_term_unit = src.whl_low_risk_crdt_term_unit,
    tgt.update_time = sysdate;

--6.更新非授信额度
select lcli.tenant_id       as tenant_id,
       lcli.limit_object_id as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.total_amount   as no_crdt_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai
                    on lcli.tenant_id = lclai.tenant_id
                        and lcli.cust_limit_id = lclai.cust_limit_id
                        and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSDGKHEDTX')
  and lcli.template_node_id in ('DGFSXED');

merge into lc_corp_lmt_view tgt
using (select lcli.tenant_id       as tenant_id,
              lcli.limit_object_id as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.total_amount   as no_crdt_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai
                           on lcli.tenant_id = lclai.tenant_id
                               and lcli.cust_limit_id = lclai.cust_limit_id
                               and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSDGKHEDTX')
         and lcli.template_node_id in ('DGFSXED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.no_crdt_lmt = src.no_crdt_lmt,
    tgt.update_time = sysdate;
--7.更新担保额度
select lcli.tenant_id       as tenant_id,
       lcli.limit_object_id as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.total_amount   as guar_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai
                    on lcli.tenant_id = lclai.tenant_id
                        and lcli.cust_limit_id = lclai.cust_limit_id
                        and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSDGKHEDTX')
  and lcli.template_node_id in ('DGPTDBED');

merge into lc_corp_lmt_view tgt
using (select lcli.tenant_id       as tenant_id,
              lcli.limit_object_id as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.total_amount   as guar_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai
                           on lcli.tenant_id = lclai.tenant_id
                               and lcli.cust_limit_id = lclai.cust_limit_id
                               and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSDGKHEDTX')
         and lcli.template_node_id in ('DGPTDBED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.guar_lmt = src.guar_lmt,
    tgt.update_time = sysdate;
--8.更新合作方额度/合作方可用额度
select lcli.tenant_id           as tenant_id,
       lcli.limit_object_id     as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.total_amount       as co_prtn_lmt,
       lclai.total_amount - lclai.pre_occupy_amount -
       lclai.real_occupy_amount as co_prtn_avl_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai
                    on lcli.tenant_id = lclai.tenant_id
                        and lcli.cust_limit_id = lclai.cust_limit_id
                        and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSDGKHEDTX')
  and lcli.template_node_id in ('DGHZFED');

merge into lc_corp_lmt_view tgt
using (select lcli.tenant_id           as tenant_id,
              lcli.limit_object_id     as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.total_amount       as co_prtn_lmt,
              lclai.total_amount - lclai.pre_occupy_amount -
              lclai.real_occupy_amount as co_prtn_avl_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai
                           on lcli.tenant_id = lclai.tenant_id
                               and lcli.cust_limit_id = lclai.cust_limit_id
                               and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSDGKHEDTX')
         and lcli.template_node_id in ('DGHZFED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.co_prtn_lmt = src.co_prtn_lmt,
    tgt.co_prtn_avl_lmt = src.co_prtn_avl_lmt,
    tgt.update_time = sysdate;

--9.更新客户风险标签
--如果limit_status中包含030-冻结,那么客户风险标签为[01-冻结],否则为[02-非冻结]
select lcli.tenant_id       as tenant_id,
       lcli.limit_object_id as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lcli.limit_status
from lc_cust_limit_info lcli
where lcli.limit_template_id in ('HNNSDGKHEDTX');

merge into lc_corp_lmt_view lclv
using (select distinct lcli.tenant_id,
                       lcli.limit_object_id as cust_no,
                       case
                           when max(case when instr(lcli.limit_status, '030') > 0 then 1 else 0 end) = 1
                               then '01'
                           else '02'
                           end              as new_risk_tag
       FROM lc_cust_limit_info lcli
       WHERE lcli.limit_template_id = 'HNNSDGKHEDTX'
       GROUP BY lcli.tenant_id, lcli.limit_object_id) src
on (lclv.tenant_id = src.tenant_id and lclv.cust_no = src.cust_no)
when matched then
update
    set lclv.cust_risk_tag = src.new_risk_tag,
    lclv.update_time = sysdate;

--10.更新客户中心的相关字段,包括所属行业类型,所属集团名称,所属集团编号,国民经济行业分类-小类,国民经济行业分类-大类,国民经济行业分类-中类
select *
from LC_CUST_LIMIT_OBJECT_INFO;
select ltcci.TENANT_ID, ltcci.CUST_NO, ltcci.BELG_INDS_TYP, ltcci.BELG_GRP_NO, ltcci.BELG_GRP_NM
from lb_t_corp_cust_info ltcci;

merge into lc_corp_lmt_view tgt
using (select ltcci.tenant_id, ltcci.cust_no, ltcci.belg_inds_typ, ltcci.belg_grp_no, ltcci.belg_grp_nm
       from lb_t_corp_cust_info ltcci) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.belg_inds_typ = src.belg_inds_typ,
    tgt.belg_grp_no = src.belg_grp_no,
    tgt.belg_grp_nm = src.belg_grp_nm,
    tgt.update_time = sysdate;
