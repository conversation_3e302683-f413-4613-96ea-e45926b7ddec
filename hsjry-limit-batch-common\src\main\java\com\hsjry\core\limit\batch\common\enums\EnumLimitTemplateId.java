package com.hsjry.core.limit.batch.common.enums;

import com.hsjry.lang.common.stereotype.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 额度模板编号
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/10/27 09:55
 */
@Getter
@AllArgsConstructor
public enum EnumLimitTemplateId implements IEnum {
    DGKHEDTX("HNNSDGKHEDTX", "对公客户额度体系"),
    GRKHEDTX("HNNSGRKHEDTX", "个人客户额度体系"),
    TYKHEDTX("HNNSTYKHEDTX", "同业客户额度体系"),
    JTKHEDTX("HNNSJTKHEDTX", "集团客户额度体系"),
    ;

    /** 状态码 */
    private final String code;

    /** 状态描述 */
    private final String description;

    /**
     * 根据编码查找枚举
     *
     * @param code 编码
     * @return {@link EnumLimitTemplateId } 实例
     **/
    public static EnumLimitTemplateId find(String code) {
        for (EnumLimitTemplateId instance : EnumLimitTemplateId.values()) {
            if (instance.getCode().equals(code)) {
                return instance;
            }
        }
        return null;
    }
}