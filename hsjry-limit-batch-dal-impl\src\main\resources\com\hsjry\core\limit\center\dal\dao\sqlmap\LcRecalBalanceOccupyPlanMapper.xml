<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.center.dal.dao.mapper.LcRecalBalanceOccupyPlanMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.center.dal.dao.model.LcRecalBalanceOccupyPlanDo">
                  <result property="lowRiskAmtBalance" column="low_risk_amt_balance" jdbcType="DECIMAL"/> <!-- 实体低风险额度余额 -->
                        <result property="caPreAmountShare" column="ca_pre_amount_share" jdbcType="DECIMAL"/> <!-- 撤销实体串用预发放余额 -->
                        <result property="caAmountShare" column="ca_amount_share" jdbcType="DECIMAL"/> <!-- 撤销实体串用余额 -->
                        <result property="preAmountShare" column="pre_amount_share" jdbcType="DECIMAL"/> <!-- 实体串用预发放余额 -->
                        <result property="amountShare" column="amount_share" jdbcType="DECIMAL"/> <!-- 实体串用余额 -->
                        <result property="exchangeRateVersion" column="exchange_rate_version" jdbcType="INTEGER"/> <!-- 汇率版本 -->
                        <result property="newExchangeRateVersion" column="new_exchange_rate_version" jdbcType="INTEGER"/> <!-- 新汇率版本 -->
                        <result property="caPreLowRiskAmtBalance" column="ca_pre_low_risk_amt_balance" jdbcType="DECIMAL"/> <!-- 撤销预发实体低风险额度余额 -->
                        <result property="caLowRiskAmtBalance" column="ca_low_risk_amt_balance" jdbcType="DECIMAL"/> <!-- 撤销实体低风险额度余额 -->
                        <result property="preLowRiskAmtBalance" column="pre_low_risk_amt_balance" jdbcType="DECIMAL"/> <!-- 预发实体低风险额度余额 -->
                        <result property="tenantId" column="tenant_id" jdbcType="CHAR"/> <!-- 租户号 -->
                        <result property="caAmountBalance" column="ca_amount_balance" jdbcType="DECIMAL"/> <!-- 撤销实体额度余额 -->
                        <result property="caPreAmountBalance" column="ca_pre_amount_balance" jdbcType="DECIMAL"/> <!-- 撤销预发实体额度余额 -->
                        <result property="preAmountBalance" column="pre_amount_balance" jdbcType="DECIMAL"/> <!-- 预发实体额度余额 -->
                        <result property="amountBalance" column="amount_balance" jdbcType="DECIMAL"/> <!-- 实体额度余额 -->
                        <result property="limitObjectId" column="limit_object_id" jdbcType="VARCHAR"/> <!-- 所属对象编号 -->
                        <result property="entityId" column="entity_id" jdbcType="VARCHAR"/> <!-- 实体编号 -->
                        <result property="custLimitId" column="cust_limit_id" jdbcType="VARCHAR"/> <!-- 额度编号 -->
                        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
                        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
              </resultMap>
  <sql id="Base_Column_List">
                low_risk_amt_balance
                , ca_pre_amount_share
                , ca_amount_share
                , pre_amount_share
                , amount_share
                , exchange_rate_version
                , new_exchange_rate_version
                , ca_pre_low_risk_amt_balance
                , ca_low_risk_amt_balance
                , pre_low_risk_amt_balance
                , tenant_id
                , ca_amount_balance
                , ca_pre_amount_balance
                , pre_amount_balance
                , amount_balance
                , limit_object_id
                , entity_id
                , cust_limit_id
                , update_time
                , create_time
          </sql>

    <select id="selectFirstOne" parameterType="com.hsjry.core.limit.center.dal.dao.query.LcRecalBalanceOccupyPlanQuery"
            resultType="string">
        SELECT
        distinct limit_object_id
        FROM lc_recal_balance_occupy_plan
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>

    <select id="selectCountByCurrentGroup"
            parameterType="com.hsjry.core.limit.center.dal.dao.query.LcRecalBalanceOccupyPlanQuery"
            resultType="java.lang.Integer">
        SELECT count(distinct limit_object_id)
        FROM lc_recal_balance_occupy_plan
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectShardList" parameterType="com.hsjry.core.limit.center.dal.dao.query.LcRecalBalanceOccupyPlanQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_recal_balance_occupy_plan
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>

    <sql id="fixQuerySql">
        <if test="query.limitObjectId != null and query.limitObjectId!=''">
            AND limit_object_id > #{query.limitObjectId}
        </if>
        <if test="query.currentMaxLimitObjectId != null and query.currentMaxLimitObjectId!=''">
            AND limit_object_id  <![CDATA[ <= ]]> #{query.currentMaxLimitObjectId}
        </if>
        ORDER BY limit_object_id
    </sql>

    <delete id="deleteAll" parameterType="map">
        delete from lc_recal_balance_occupy_plan
        where TENANT_ID = #{tenantId,jdbcType=VARCHAR}
    </delete>
</mapper>