package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度实例所属对象信息Do
 *
 * <AUTHOR>
 * @date 2022-12-13 03:09:49
 */
@Table(name = "lc_cust_limit_object_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitObjectInfoDo extends LcCustLimitObjectInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1602501015033282561L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 同业金融编号 */
    @Column(name = "ib_financial_id")
    private String ibFinancialId;
    /** 核心同业金融产品编号 */
    @Column(name = "ib_financial_prod_core_id")
    private String ibFinancialProdCoreId;
    /** 同业金融产品编号 */
    @Column(name = "ib_financial_prod_id")
    private String ibFinancialProdId;
    /** 同业金融产品名称 */
    @Column(name = "ib_financial_prod_name")
    private String ibFinancialProdName;
    /** 同业金融产品类型 */
    @Column(name = "ib_financial_prod_type")
    private String ibFinancialProdType;
    /** 同业金融类型 */
    @Column(name = "ib_financial_type")
    private String ibFinancialType;
    /** 核心客户编号 */
    @Column(name = "out_user_id")
    private String outUserId;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 客户证件类型 */
    @Column(name = "user_certificate_kind")
    private String userCertificateKind;
    /** 客户证件编号 */
    @Column(name = "user_certificate_no")
    private String userCertificateNo;
    /** 客户编号 */
    @Column(name = "user_id")
    private String userId;
    /** 客户名称 */
    @Column(name = "user_name")
    private String userName;
    /** 客户类型 */
    @Column(name = "user_type")
    private String userType;
    /** 国民经济行业分类-中类 */
    @Column(name = "vocation_level_three")
    private String vocationLevelThree;
    /** 国民经济行业分类-大类 */
    @Column(name = "vocation_level_two")
    private String vocationLevelTwo;
    /** 国民经济行业分类-小类 */
    @Column(name = "industry_category")
    private String industryCategory;
    /** 所属集团编号 */
    @Column(name = "belg_grp_no")
    private String belgGrpNo;
    /** 所属集团名称 */
    @Column(name = "belg_grp_nm")
    private String belgGrpNm;
    /** 所属行业类型 */
    @Column(name = "belg_inds_typ")
    private String belgIndsTyp;
}
