package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 单一限额操作流水Do
 *
 * <AUTHOR>
 * @date 2023-06-28 11:32:07
 */
@Table(name = "lc_single_limit_serial")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcSingleLimitSerialDo extends LcSingleLimitSerialKeyDo implements Serializable {
    private static final long serialVersionUID = 1674017831437664256L;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 状态 */
    @Column(name = "status")
    private String status;
    /** 业务流水号 */
    @Column(name = "serial_no")
    private String serialNo;
    /** 规则编号 */
    @Column(name = "rule_id")
    private String ruleId;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 单一限额操作类型 */
    @Column(name = "operate_type")
    private String operateType;
    /** 操作金额 */
    @Column(name = "operate_amount")
    private java.math.BigDecimal operateAmount;
    /** 业务时间 */
    @Column(name = "biz_datetime")
    private java.util.Date bizDatetime;
    /** 前置业务关联流水 */
    @Column(name = "last_inbound_serial_no")
    private String lastInboundSerialNo;
    /** 前置业务流水 */
    @Column(name = "inbound_serial_no")
    private String inboundSerialNo;
    /** 前置业务时间 */
    @Column(name = "inbound_serial_datetime")
    private java.util.Date inboundSerialDatetime;
    /** 全局流水号 */
    @Column(name = "global_serial_no")
    private String globalSerialNo;
    /** 关联编号 */
    @Column(name = "entity_id")
    private String entityId;
    /** 额度编号 */
    @Column(name = "cust_limit_id")
    private String custLimitId;
    /** 币种 */
    @Column(name = "currency")
    private String currency;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 渠道号 */
    @Column(name = "channel_no")
    private String channelNo;
}
