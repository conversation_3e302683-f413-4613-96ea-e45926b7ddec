/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.intf.CustLimitRelationBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.CustLimitRelationBatchMapper;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationDo;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitRelationQuery;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/17 9:57
 */
@Repository
public class CustLimitRelationBatchDaoImpl
    extends AbstractBaseDaoImpl<LcCustLimitRelationDo, CustLimitRelationBatchMapper>
    implements CustLimitRelationBatchDao {
    @Override
    public List<LcCustLimitRelationDo> selectShardList(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectShardList(query);
    }

    @Override
    public LcCustLimitRelationDo selectFirstOne(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectFirstOne(query);
    }

    @Override
    public Integer selectCountByCurrentGroup(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitRelationDo> selectObjectShardList(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectObjectShardList(query);
    }

    @Override
    public List<LcCustLimitRelationDo> selectExpireShardList(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectExpireShardList(query);
    }

    @Override
    public LcCustLimitRelationDo selectObjectFirstOne(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectObjectFirstOne(query);
    }

    @Override
    public LcCustLimitRelationDo selectExpireFirstOne(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectExpireFirstOne(query);
    }

    @Override
    public Integer selectObjectCountByCurrentGroup(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectObjectCountByCurrentGroup(query);
    }

    @Override
    public Integer selectExpireCountByCurrentGroup(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectExpireCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitRelationDo> selectNotUsedShardList(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNotUsedShardList(query);
    }

    @Override
    public LcCustLimitRelationDo selectNotUsedFirstOne(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNotUsedFirstOne(query);
    }

    @Override
    public Integer selectNotUsedCountByCurrentGroup(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectNotUsedCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitRelationDo> selectNodeShardList(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNodeShardList(query);
    }

    @Override
    public LcCustLimitRelationDo selectNodeFirstOne(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectNodeFirstOne(query);
    }

    @Override
    public Integer selectNodeCountByCurrentGroup(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectNodeCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitRelationDo> selectExpireLimitInfoByObjectId(CustLimitRelationQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectExpireLimitInfoByObjectId(query);
    }
}
