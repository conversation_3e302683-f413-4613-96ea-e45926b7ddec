/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.center.dal.dao.query.CustLimitInfoQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/17 9:58
 */
public interface CustLimitInfoBatchMapper extends CommonMapper<LcCustLimitInfoDo> {
    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitInfoDo> selectShardList(@Param("query") CustLimitInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitInfoDo selectFirstOne(@Param("query") CustLimitInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(@Param("query") CustLimitInfoQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitInfoDo> selectObjectShardList(@Param("query") CustLimitInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitInfoDo selectObjectFirstOne(@Param("query") CustLimitInfoQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitInfoDo> selectExpireShardList(@Param("query") CustLimitInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitInfoDo selectExpireFirstOne(@Param("query") CustLimitInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectObjectCountByCurrentGroup(@Param("query") CustLimitInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectExpireCountByCurrentGroup(@Param("query") CustLimitInfoQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitInfoDo> selectNotUsedShardList(@Param("query") CustLimitInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitInfoDo selectNotUsedFirstOne(@Param("query") CustLimitInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNotUsedCountByCurrentGroup(@Param("query") CustLimitInfoQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitInfoDo> selectNodeShardList(@Param("query") CustLimitInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitInfoDo selectNodeFirstOne(@Param("query") CustLimitInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNodeCountByCurrentGroup(@Param("query") CustLimitInfoQuery query);

    List<LcCustLimitInfoDo> selectExpireLimitInfoByObjectId(@Param("query") CustLimitInfoQuery query);
}
