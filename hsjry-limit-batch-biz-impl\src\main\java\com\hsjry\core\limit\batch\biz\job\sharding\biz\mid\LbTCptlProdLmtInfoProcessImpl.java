package com.hsjry.core.limit.batch.biz.job.sharding.biz.mid;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.customer.EnumUserType;
import com.hsjry.base.common.model.enums.limit.EnumCustLimitStatus;
import com.hsjry.base.common.model.enums.limit.EnumLmtTplNode;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.dal.dao.intf.LbSCptlBizLmtUseSttnDao;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTCptlCorpProdLmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTCptlIbnkProdLmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCptlBizLmtUseSttnDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSCptlBizLmtUseSttnQuery;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitAmtInfoDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitInfoDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitObjectInfoDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 资金系统日终产品层额度处理
 *
 * <AUTHOR>
 * @date 2025-07-10 12:31:05
 */
@Slf4j
@Service("lbTCptlProdLmtInfoProcessImpl")
@RequiredArgsConstructor
public class LbTCptlProdLmtInfoProcessImpl extends AbstractShardingPrepareBiz<LbSCptlBizLmtUseSttnQuery>
    implements JobCoreBusiness<LbSCptlBizLmtUseSttnDo> {
    private final LcCustLimitInfoDao custLimitInfoDao;
    private final LcCustLimitAmtInfoDao custLimitAmtInfoDao;
    private final LcCustLimitObjectInfoDao custLimitObjectInfoDao;
    private final LbSCptlBizLmtUseSttnDao lbSCptlBizLmtUseSttnDao;
    private final LbTCptlIbnkProdLmtInfoDao lbTCptlIbnkProdLmtInfoDao;
    private final LbTCptlCorpProdLmtInfoDao lbTCptlCorpProdLmtInfoDao;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.LB_T_CPTL_PROD_LMT_INFO_PROCESS;
    }

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据userId主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含userId范围
     * @return 当前分片的数据量
     */
    @Override
    public Integer selectCountByCurrentGroupFromDb(LbSCptlBizLmtUseSttnQuery query) {
        Integer count = lbSCptlBizLmtUseSttnDao.selectCountByCurrentGroup(query);
        return count != null ? count : 0;
    }

    /**
     * 生成分片任务列表
     * 采用userId主键分页方式，循环查找最大userId对象，动态生成分片任务
     * @param jobInitDto 任务初始化参数
     * @return 分片任务列表
     */
    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "====================== 分片逻辑 start ================================================");

        List<JobShared> jobSharedList = new ArrayList<>();
        // SQL批处理数量,暂定为分片数量
        Integer batchFixNum = jobInitDto.getFixNum();
        // 当前分组的最大值,为下次批处理的最小值
        LbSCptlBizLmtUseSttnDo maxDo = null;

        // 构造查询条件,查询当前分批处理的排序最大对象
        LbSCptlBizLmtUseSttnQuery query = LbSCptlBizLmtUseSttnQuery.builder().build();

        // 分片流水
        int batchNum = 0;
        while (true) {
            if (maxDo != null) {
                query.setUserId(maxDo.getUserId());
            }
            maxDo = lbSCptlBizLmtUseSttnDao.selectFirstOne(query);
            if (maxDo == null) {
                break;
            }
            // 统计分片数量
            batchNum = countBatchNum(batchFixNum, query, maxDo, batchNum, jobInitDto, jobSharedList, query.getUserId(),
                false);
        }

        log.info(prefixLog + "====================== 分片逻辑 end ================================================");
        log.info(prefixLog + "资金系统日终产品层额度处理分片任务生成完成,共[{}]个分片", jobSharedList.size());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LbSCptlBizLmtUseSttnDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {

        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始查询分片数据,分片号:{}", jobShared.getBatchNum());

        ShardingResult<LbSCptlBizLmtUseSttnDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);

        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        // 创建查询条件 - 查询所有信用卡账户数据
        LbSCptlBizLmtUseSttnQuery query = LbSCptlBizLmtUseSttnQuery.builder().build();

        log.info(prefixLog + "=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");

        List<LbSCptlBizLmtUseSttnDo> dataList = lbSCptlBizLmtUseSttnDao.selectByExample(query);
        shardingResult.setShardingResultList(dataList);

        log.info("分片数据查询完成,分片号:{},数据量:{}", jobShared.getBatchNum(),
            CollectionUtils.isEmpty(dataList) ? 0 : dataList.size());

        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbSCptlBizLmtUseSttnDo> shardingResult) {
        // 获取分片数据列表
        List<LbSCptlBizLmtUseSttnDo> shardingDataList = shardingResult.getShardingResultList();
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        Integer dataSize = shardingResult.getShardingResultList().size();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "=========分片执行开始:[{}]数量为[{}]===========", batchNum, dataSize);

        // 判空处理，若分片数据为空直接返回
        if (CollectionUtils.isEmpty(shardingDataList)) {
            log.info(prefixLog + "=========分片执行结束:分片号[{}]数量为空===========", batchNum);
            return;
        }
        try {
            log.info(prefixLog + "开始处理[{}]条同业客户额度同步数据", shardingDataList.size());
            LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
            //查询[资金系统-落地表-日终业务额度使用情况同步]中所有同业客户的[USER_ID]和[CORE_INST_NO]
            List<String> ibnkUserIdList = shardingDataList.stream().filter(Objects::nonNull)//
                .filter(item -> Objects.equals(EnumUserType.INTERBANK_CUSTOMER.getCode(), item.getUserType()))//
                .map(LbSCptlBizLmtUseSttnDo::getUserId)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            List<String> coreInstNoList = shardingDataList.stream().filter(Objects::nonNull)//
                .filter(item -> Objects.equals(EnumUserType.INTERBANK_CUSTOMER.getCode(), item.getUserType()))//
                .map(LbSCptlBizLmtUseSttnDo::getCoreInstNo)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            //查询同业客户的[额度实例所属对象信息]
            List<LcCustLimitObjectInfoDo> ibnkObjectInfoDoList = custLimitObjectInfoDao.selectByExample(
                LcCustLimitObjectInfoQuery.builder()//
                    .userType(EnumUserType.INTERBANK_CUSTOMER.getCode())//
                    .userIdList(ibnkUserIdList)//
                    .build());
            List<String> ibnkCustLimitIdList = ibnkObjectInfoDoList.stream().filter(Objects::nonNull)//
                .map(LcCustLimitObjectInfoDo::getCustLimitId)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            //查询同业客户的[额度实例信息]中TEMPLATE_NODE_ID=['TYTRZYWED']并且BLNG_LGLPSN_CORE_INS_NO=[CORE_INST_NO]的[CUST_LIMIT_ID]列表
            List<String> ibnkNodeIdList = Lists.newArrayList(EnumLmtTplNode.TYTRZYWED.getTemplateNodeId());
            List<LcCustLimitInfoDo> ibnkLimitInfoDoList = custLimitInfoDao.selectByExample(
                LcCustLimitInfoQuery.builder()
                    .custLimitIdList(ibnkCustLimitIdList)
                    .limitObjectIdList(ibnkUserIdList)
                    .templateNodeIdList(ibnkNodeIdList)
                    .blngLglpsnCoreInsNoList(coreInstNoList)
                    .statusList(EnumCustLimitStatus.getOperableCodeList())
                    .build());
            List<String> limitId4tytrzywedList = ibnkLimitInfoDoList.stream().filter(Objects::nonNull)//
                .map(LcCustLimitInfoDo::getCustLimitId)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            //查询[资金系统-落地表-日终业务额度使用情况同步]中所有对公客户的[USER_ID]
            List<String> corpUserIdList = shardingDataList.stream().filter(Objects::nonNull)//
                .filter(item -> Objects.equals(EnumUserType.INSTITUTIONAL_CUSTOMER.getCode(), item.getUserType()))//
                .map(LbSCptlBizLmtUseSttnDo::getUserId)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

            //查询对公客户的[额度实例所属对象信息]
            List<LcCustLimitObjectInfoDo> corpObjectInfoDoList = custLimitObjectInfoDao.selectByExample(
                LcCustLimitObjectInfoQuery.builder()//
                    .userType(EnumUserType.INSTITUTIONAL_CUSTOMER.getCode())//
                    .userIdList(corpUserIdList)//
                    .build());
            List<String> corpCustLimitIdList = corpObjectInfoDoList.stream().filter(Objects::nonNull)//
                .map(LcCustLimitObjectInfoDo::getCustLimitId)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            //查询对公客户的[额度实例信息]中TEMPLATE_NODE_ID=['DGZHSXDYCPED']的[CUST_LIMIT_ID]列表
            List<String> corpNodeIdList = Lists.newArrayList(EnumLmtTplNode.DGZHSXDYCPED.getTemplateNodeId());
            List<LcCustLimitInfoDo> corpLimitInfoDoList = custLimitInfoDao.selectByExample(
                LcCustLimitInfoQuery.builder()
                    .custLimitIdList(corpCustLimitIdList)
                    .limitObjectIdList(corpUserIdList)
                    .templateNodeIdList(corpNodeIdList)
                    .statusList(EnumCustLimitStatus.getOperableCodeList())
                    .build());
            List<String> limitId4dgzhsxdycpedList = corpLimitInfoDoList.stream().filter(Objects::nonNull)//
                .map(LcCustLimitInfoDo::getCustLimitId)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

            // 往[资金系统-中间表-同业客户产品层额度信息]插入数据
            int ibnkInsertResult = lbTCptlIbnkProdLmtInfoDao.insertCptlIbnkProdLmtInfo(ibnkNodeIdList,
                limitId4tytrzywedList);
            log.info(prefixLog + "往[资金系统-中间表-同业客户产品层额度信息]插入数据结果为[{}]", ibnkInsertResult);

            // 更新同业客户的[额度实例金额信息]中[实占额度]
            int ibnkUpdateResult = lbTCptlIbnkProdLmtInfoDao.updateRealOccupyAmount(limitId4tytrzywedList);
            log.info(prefixLog + "更新同业客户的[额度实例金额信息]中[实占额度]结果为[{}]", ibnkUpdateResult);

            // 往[资金系统-中间表-对公客户产品层额度信息]插入数据
            int corpInsertResult = lbTCptlCorpProdLmtInfoDao.insertCptlCorpProdLmtInfo(corpNodeIdList,
                limitId4dgzhsxdycpedList);
            log.info(prefixLog + "往[资金系统-中间表-对公客户产品层额度信息]插入数据结果为[{}]", corpInsertResult);

            // 更新对公客户的[额度实例金额信息]中[实占额度]
            int corpUpdateResult = lbTCptlCorpProdLmtInfoDao.updateRealOccupyAmount(limitId4dgzhsxdycpedList);
            log.info(prefixLog + "更新对公客户的[额度实例金额信息]中[实占额度]结果为[{}]", corpUpdateResult);

            // 更新分片流水成功
            normalUpdateSliceSerial(shardingDataList.size(), sliceBatchSerialDo);
        } catch (Exception e) {
            // 异常处理，记录详细日志并抛出运行时异常
            log.error(prefixLog + "分片执行异常:[{}]", batchNum, e);
            throw new RuntimeException(prefixLog + "处理异常", e);
        }
        log.info(prefixLog + "=========分片执行结束:[{}]===========", batchNum);
    }
}