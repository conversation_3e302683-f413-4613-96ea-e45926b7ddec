package com.hsjry.core.limit.center.dal.dao.intf;

import com.hsjry.core.limit.center.dal.dao.model.LcSingleLimitRelDo;
import com.hsjry.core.limit.center.dal.dao.query.LcSingleLimitRelQuery;


import java.util.List;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 单一限额关联数据库操作接口
 * 
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
public interface LcSingleLimitRelDao extends IBaseDao<LcSingleLimitRelDo> {
    /**
     * 分页查询单一限额关联
     *
     * @param lcSingleLimitRelQuery 条件
     * @return PageInfo<LcSingleLimitRelDo>
     */
    PageInfo<LcSingleLimitRelDo> selectPage(LcSingleLimitRelQuery lcSingleLimitRelQuery,PageParam pageParam);
	  /**
     * 根据key查询单一限额关联
     *
     	 	 * @param singleRelId
	 	 	 	      * @return
     */
	LcSingleLimitRelDo selectByKey(String singleRelId);
    /**
     * 根据key删除单一限额关联
     *
               * @param singleRelId
                      * @return
     */
    int deleteByKey(String singleRelId);

    /**
     * 查询单一限额关联信息
     *
     * @param lcSingleLimitRelQuery 条件
     * @return List<LcSingleLimitRelDo>
     */
    List<LcSingleLimitRelDo> selectByExample(LcSingleLimitRelQuery lcSingleLimitRelQuery);

    /**
     * 新增单一限额关联信息
     *
     * @param lcSingleLimitRel 条件
     * @return int>
     */
    int insertBySelective(LcSingleLimitRelDo lcSingleLimitRel);

    /**
     * 修改单一限额关联信息
     *
     * @param lcSingleLimitRel
     * @return
     */
    int updateBySelective(LcSingleLimitRelDo lcSingleLimitRel);
    /**
     * 修改单一限额关联信息
     *
     * @param lcSingleLimitRel
     * @param lcSingleLimitRelQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcSingleLimitRelDo lcSingleLimitRel,
    LcSingleLimitRelQuery lcSingleLimitRelQuery);
}
