/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.io.File;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.core.limit.batch.biz.convert.file.LbSOlCtrInfoConverter;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.biz.entity.LbSOlCtrInfoData;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.AbstractFileBaseShardingPrepareBizImpl;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.constants.CharsetConstants;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.dal.dao.intf.LbSOlCtrInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlCtrInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.sequence.SequenceTool;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
/**
 * 网贷系统合同信息文件同步实现类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2024/1/11 16:00
 */
@Slf4j
@Service("lbSOlCtrInfoFileSyncImpl")
@RequiredArgsConstructor
public class LbSOlCtrInfoFileSyncImpl extends AbstractFileBaseShardingPrepareBizImpl<LbSOlCtrInfoData> {

    /** 合同编号列数 */
    private static final int CONTRACT_ID_NUM = 1;
    /** 客户编号列数 */
    private static final int USER_ID_NUM = 2;
    /** 合同名称列数 */
    private static final int CONTRACT_NAME_NUM = 3;
    /** 合同类型列数 */
    private static final int CONTRACT_TYPE_NUM = 4;
    /** 合同下载地址列数 */
    private static final int CONTRACT_FILE_URL_NUM = 5;
    /** 关联ID列数 */
    private static final int RELATION_ID_NUM = 6;
    /** 关联ID类型列数 */
    private static final int RELATION_ID_TYPE_NUM = 7;
    /** 产品编号列数 */
    private static final int PRODUCT_ID_NUM = 8;
    /** 合同状态列数 */
    private static final int CONTRACT_STATUS_NUM = 9;
    /** 关联三方合同编号列数 */
    private static final int THIRDPART_CONTRACT_ID_NUM = 10;
    /** 客户姓名列数 */
    private static final int USER_NAME_NUM = 11;
    /** 客户手机列数 */
    private static final int USER_PHONE_NUM = 12;
    /** 客户证件类型列数 */
    private static final int CERTIFICATE_TYPE_NUM = 13;
    /** 客户证件号码列数 */
    private static final int CERTIFICATE_NO_NUM = 14;
    /** 签订时间列数 */
    private static final int SIGN_TIME_NUM = 15;
    /** 有效开始时间列数 */
    private static final int EFFECTIVE_START_TIME_NUM = 16;
    /** 有效截止时间列数 */
    private static final int EFFECTIVE_END_TIME_NUM = 17;
    /** 合作方编号列数 */
    private static final int THIRDPART_ID_NUM = 18;
    /** 线上标识列数 */
    private static final int ONLINE_FLAG_NUM = 19;
    /** 合同模式列数 */
    private static final int CONTRACT_MODE_NUM = 20;
    /** 创建时间列数 */
    private static final int CREATE_TIME_NUM = 21;
    /** 更新时间列数 */
    private static final int UPDATE_TIME_NUM = 22;
    /** 操作人编号列数 */
    private static final int OPERATOR_ID_NUM = 23;
    /** 所属组织id列数 */
    private static final int OWN_ORGAN_ID_NUM = 24;
    /** 租户号列数 */
    private static final int TENANT_ID_NUM = 25;
    /** 合同额度状态列数 */
    private static final int LIMIT_STATUS_NUM = 26;
    /** 支用借据编号列数 */
    private static final int LOAN_INVOICE_ID_NUM = 27;
    /** 支用申请编号列数 */
    private static final int LOAN_APPLY_ID_NUM = 28;
    /** 合同冻结类型列数 */
    private static final int LIMIT_MODE_NUM = 29;
    /** 合同金额列数 */
    private static final int CONTRACT_AMOUNT_NUM = 30;

    /** 最小字段数量 */
    private static final int MIN_FIELD_COUNT = 30;
    /** 分隔符 */
    private static final String FIELD_SEPARATOR = "\u0003";
    /** 批处理大小 */
    private static final int BATCH_SIZE = 1000;
    /** 日期格式 */
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private final String SEQUENCE_NO = "SEQUENCE_NO";

    private final LbSOlCtrInfoDao lbSOlCtrInfoDao;
    @Value("${project.ol.ctr.info.filename:ICM_CREDIT_CONTRACT_INFO_[DATE].txt}")
    private String fileName;
    @Value("${limit.batch.remoteFilePath:/app/bftfiles/cfs/recv/ebbs/}")
    private String remoteFilePathDefine;

    @Override
    public ShardingResult<LbSOlCtrInfoData> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始文件数据分片查询[{}]", GsonUtil.objToStrForLog(jobShared));

        ShardingResult<LbSOlCtrInfoData> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        try {
            // 读取文件分片数据
            FileLineData fileLineData = GsonUtil.json2Obj(jobShared.getExtParam(), FileLineData.class);
            log.info(prefixLog + "开始读取文件数据分片[{}]", GsonUtil.objToStrForLog(fileLineData));
            List<String> originData = FileShardingUtils.readFileSharedData(jobShared, skipFirst(), CharsetConstants.GBK);

            // 使用并行流处理数据，提升性能
            List<LbSOlCtrInfoData> fileDataList = processOriginDataParallel(originData, prefixLog);

            log.info(prefixLog + "读取文件数据分片总量[{}]", fileDataList.size());
            shardingResult.setShardingResultList(fileDataList);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        jobShared.setBatchSerialNo(inParam.getString(SEQUENCE_NO));
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbSOlCtrInfoData> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
        List<LbSOlCtrInfoData> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "网贷合同信息数据文件处理:文件分片数据为空,执行中断。");
            return;
        }

        log.info(prefixLog + "网贷合同信息数据文件处理:开始执行分片数据操作,数据量:[{}]", dataList.size());

        // 检查是否是第一个分片，如果是则清空表
        if (sliceBatchSerialDo.getBatchNum() == 1) {
            log.info(prefixLog + "第一个分片,清空目标表 lb_s_ol_ctr_info");
            lbSOlCtrInfoDao.deleteAll();
        }

        // 确保分片流水对象的状态字段不为null
        if (sliceBatchSerialDo.getSharedStatus() == null) {
            log.debug(prefixLog + "分片流水状态为null,设置为处理中状态");
            sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
        }

        // 使用并行流进行数据转换和验证
        List<LbSOlCtrInfoDo> insertList = dataList.parallelStream().map(LbSOlCtrInfoConverter::data2Do)//
            .filter(this::validateData).collect(Collectors.toCollection(ArrayList::new));

        if (CollectionUtil.isNotEmpty(insertList)) {
            // 批量插入数据
            processBatchInsert(insertList, prefixLog);
            log.info(prefixLog + "插入[{}]条网贷合同信息数据", insertList.size());
        }

        // 更新分片流水成功
        normalUpdateSliceSerial(dataList.size(), sliceBatchSerialDo);
        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataList.size());
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.S_OL_CTR_INFO;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        // 确保fixNum被正确设置
        if (jobInitDto.getFixNum() == null) {
            log.warn(prefixLog + "fixNum为null,设置默认值1000");
            jobInitDto.setFixNum(1000);
        }

        JSONObject param = JSON.parseObject(jobInitDto.getInPara());
        //设置流水ID,解决多次从redis获取唯一值的性能问题
        param.put(SEQUENCE_NO, SequenceTool.nextId());
        //更新jobInitDto的inpara参数
        jobInitDto.setInPara(param.toJSONString());

        List<JobShared> sharedList = Lists.newArrayList();
        log.info(prefixLog + "[{}]文件处理", jobTradeDesc);
        String localFilePath = FileShardingUtils.getLocalFilePath(jobInitDto.getBusinessDate(),
            remoteFilePathDefine + FileShardingUtils.ACCT_DATE_CODE_MARK + File.separator);
        Integer acctDate = jobInitDto.getBusinessDate();
        String localFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK, String.valueOf(acctDate));

        try {
            String fileAttr = FIELD_SEPARATOR;
            String filePath = localFilePath + localFileName;
            log.info("实际查找的文件路径: [{}]", filePath);

            // 检查目录是否存在
            File directory = new File(localFilePath);
            if (!directory.exists()) {
                log.info(prefixLog + "目录[{}]不存在,尝试创建", localFilePath);
                directory.mkdirs();
            } else {
                log.info(prefixLog + "目录[{}]已存在", localFilePath);
                // 列出目录中的文件
                File[] files = directory.listFiles();
                if (files != null && files.length > 0) {
                    log.info(prefixLog + "目录[{}]中的文件列表:", localFilePath);
                    for (File f : files) {
                        log.info(prefixLog + " - {}", f.getName());
                    }
                } else {
                    log.info(prefixLog + "目录[{}]为空", localFilePath);
                }
            }

            File localFile = new File(filePath);
            log.info(prefixLog + "检查文件[{}]是否存在: {}", filePath, localFile.exists());

            //判断本地文件是否存在，不存在则直接报错
            if(!localFile.exists()){
                log.error(prefixLog + "本地文件[{}]不存在,请确认文件路径是否正确", filePath);
                String errorCode = EnumBatchJobError.FILE_PATH_NOT_EXIST.getCode();
                String errorMsg = EnumBatchJobError.FILE_PATH_NOT_EXIST.getDescription();
                throw new HsjryBizException(errorCode, errorMsg);
            }

            // 文件存在，直接进行分片处理
            log.info(prefixLog + "[{}]开始[{}]文件数据分片处理", jobTradeDesc, filePath);
            log.info(prefixLog + "分片参数: fixNum=[{}], fileAttr=[{}], skipFirst=[{}]",
                jobInitDto.getFixNum(), fileAttr, skipFirst());

            List<JobShared> jobShareds = FileShardingUtils.getFileSharedData(jobInitDto, localFile, fileAttr,
                skipFirst());
            log.info(prefixLog + "[{}]结束[{}]文件数据分片当前分片数[{}]", jobTradeDesc, filePath,
                jobShareds.size());
            sharedList.addAll(jobShareds);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }
        return sharedList;
    }

    private boolean skipFirst() {
        return true; // 跳过标题行
    }

    /**
     * 并行处理原始数据
     */
    private List<LbSOlCtrInfoData> processOriginDataParallel(List<String> originData, String prefixLog) {
        if (CollectionUtil.isEmpty(originData)) {
            return new ArrayList<>();
        }

        // 使用线程安全的计数器
        AtomicInteger invalidCount = new AtomicInteger(0);
        AtomicInteger parseErrorCount = new AtomicInteger(0);

        List<LbSOlCtrInfoData> fileDataList = originData.parallelStream().filter(Objects::nonNull).filter(
            item -> !StringUtil.isBlank(item)).map(
            item -> parseLineData(item, prefixLog, invalidCount, parseErrorCount)).filter(Objects::nonNull).collect(
            Collectors.toCollection(ArrayList::new));

        // 记录统计信息
        if (invalidCount.get() > 0) {
            log.warn(prefixLog + "数据格式不正确记录数量:[{}]", invalidCount.get());
        }
        if (parseErrorCount.get() > 0) {
            log.warn(prefixLog + "数字字段解析失败记录数量:[{}]", parseErrorCount.get());
        }

        return fileDataList;
    }

    /**
     * 解析单行数据
     */
    private LbSOlCtrInfoData parseLineData(String item, String prefixLog, AtomicInteger invalidCount,
        AtomicInteger parseErrorCount) {
        String[] split = item.split(FIELD_SEPARATOR);
        if (split.length < MIN_FIELD_COUNT) {
            invalidCount.incrementAndGet();
            return null;
        }

        LbSOlCtrInfoData fileData = new LbSOlCtrInfoData();

        // 设置字符串字段
        fileData.setContractId(split[CONTRACT_ID_NUM - 1]);
        fileData.setUserId(split[USER_ID_NUM - 1]);
        fileData.setContractName(split[CONTRACT_NAME_NUM - 1]);
        fileData.setContractType(split[CONTRACT_TYPE_NUM - 1]);
        fileData.setContractFileUrl(split[CONTRACT_FILE_URL_NUM - 1]);
        fileData.setRelationId(split[RELATION_ID_NUM - 1]);
        fileData.setRelationIdType(split[RELATION_ID_TYPE_NUM - 1]);
        fileData.setProductId(split[PRODUCT_ID_NUM - 1]);
        fileData.setContractStatus(split[CONTRACT_STATUS_NUM - 1]);
        fileData.setThirdpartContractId(split[THIRDPART_CONTRACT_ID_NUM - 1]);
        fileData.setUserName(split[USER_NAME_NUM - 1]);
        fileData.setUserPhone(split[USER_PHONE_NUM - 1]);
        fileData.setCertificateType(split[CERTIFICATE_TYPE_NUM - 1]);
        fileData.setCertificateNo(split[CERTIFICATE_NO_NUM - 1]);
        fileData.setThirdpartId(split[THIRDPART_ID_NUM - 1]);
        fileData.setOnlineFlag(split[ONLINE_FLAG_NUM - 1]);
        fileData.setContractMode(split[CONTRACT_MODE_NUM - 1]);
        fileData.setOperatorId(split[OPERATOR_ID_NUM - 1]);
        fileData.setOwnOrganId(split[OWN_ORGAN_ID_NUM - 1]);
        fileData.setTenantId(split[TENANT_ID_NUM - 1]);
        fileData.setLimitStatus(split[LIMIT_STATUS_NUM - 1]);
        fileData.setLoanInvoiceId(split[LOAN_INVOICE_ID_NUM - 1]);
        fileData.setLoanApplyId(split[LOAN_APPLY_ID_NUM - 1]);
        fileData.setLimitMode(split[LIMIT_MODE_NUM - 1]);

        // 安全解析日期字段
        fileData.setSignTime(parseDateSafely(split[SIGN_TIME_NUM - 1], parseErrorCount));
        fileData.setEffectiveStartTime(parseDateSafely(split[EFFECTIVE_START_TIME_NUM - 1], parseErrorCount));
        fileData.setEffectiveEndTime(parseDateSafely(split[EFFECTIVE_END_TIME_NUM - 1], parseErrorCount));
        fileData.setCreateTime(parseDateSafely(split[CREATE_TIME_NUM - 1], parseErrorCount));
        fileData.setUpdateTime(parseDateSafely(split[UPDATE_TIME_NUM - 1], parseErrorCount));

        // 安全解析数字字段
        fileData.setContractAmount(parseBigDecimalSafely(split[CONTRACT_AMOUNT_NUM - 1], parseErrorCount));

        return fileData;
    }

    /**
     * 安全解析BigDecimal
     */
    private BigDecimal parseBigDecimalSafely(String value, AtomicInteger parseErrorCount) {
        try {
            return StringUtil.isBlank(value) ? BigDecimal.ZERO : new BigDecimal(value);
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return BigDecimal.ZERO;
        }
    }

    /**
     * 安全解析日期
     */
    private Date parseDateSafely(String value, AtomicInteger parseErrorCount) {
        if (StringUtil.isBlank(value)) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
            return sdf.parse(value);
        } catch (ParseException e) {
            parseErrorCount.incrementAndGet();
            return null;
        }
    }

    /**
     * 数据验证方法
     */
    private boolean validateData(LbSOlCtrInfoDo data) {
        if (Objects.isNull(data)) {
            return false;
        }

        if (StringUtil.isBlank(data.getContractId())) {
            log.warn("合同编号为空,数据无效");
            return false;
        }

        if (StringUtil.isBlank(data.getTenantId())) {
            log.warn("租户号为空,合同编号:[{}]", data.getContractId());
            return false;
        }

        if (StringUtil.isBlank(data.getUserId())) {
            log.warn("客户编号为空,合同编号:[{}]", data.getContractId());
            return false;
        }

        return true;
    }

    /**
     * 批量插入处理
     */
    private void processBatchInsert(List<LbSOlCtrInfoDo> insertList, String prefixLog) {
        try {
            // 分批处理，避免单次插入数据量过大
            int totalSize = insertList.size();
            for (int i = 0; i < totalSize; i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, totalSize);
                List<LbSOlCtrInfoDo> batchList = insertList.subList(i, endIndex);

                int insertCount = lbSOlCtrInfoDao.insertList(batchList);
                log.debug(prefixLog + "批量插入网贷合同信息数据,当前批次:[{}-{}],插入量:[{}]",
                    i + 1, endIndex, insertCount);
            }
        } catch (Exception e) {
            log.error(prefixLog + "批量插入网贷合同信息数据失败", e);
            throw new HsjryBizException(EnumLimitBatchErrorCode.FILE_DATA_ERROR.getCode(),
                "网贷合同信息数据插入失败: " + e.getMessage());
        }
    }
}
