<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.center.dal.dao.mapper.LcCustLimitRelationBatchMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationDo">
                  <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
                        <result property="currentNodeLimitId" column="current_node_limit_id" jdbcType="VARCHAR"/> <!-- 当前节点额度编号 -->
                        <result property="limitRelationId" column="limit_relation_id" jdbcType="VARCHAR"/> <!-- 额度关系编号 -->
                        <result property="limitRelationType" column="limit_relation_type" jdbcType="CHAR"/> <!-- 关系类型;关联关系不校验体系，EnumLimitRelationType:001-默认、002-关联 -->
                        <result property="parentNodeLimitId" column="parent_node_limit_id" jdbcType="VARCHAR"/> <!-- 父节点额度编号 -->
                        <result property="tenantId" column="tenant_id" jdbcType="CHAR"/> <!-- 租户号 -->
                        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
              </resultMap>
  <sql id="Base_Column_List">
                create_time
                , current_node_limit_id
                , limit_relation_id
                , limit_relation_type
                , parent_node_limit_id
                , tenant_id
                , update_time
          </sql>
</mapper>