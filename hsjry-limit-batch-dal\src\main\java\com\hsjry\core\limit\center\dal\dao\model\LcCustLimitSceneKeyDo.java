package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度场景主键
 *
 * <AUTHOR>
 * @date 2023-02-02 06:51:40
 */
@Table(name = "lc_cust_limit_scene")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcCustLimitSceneKeyDo implements Serializable {

    private static final long serialVersionUID = 1621038625741537280L;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    /** 场景码 */
    @Id
    @Column(name = "scene_code")
    private String sceneCode;
    /** 体系模板节点编号 */
    @Id
    @Column(name = "template_node_id")
    private String templateNodeId;
}