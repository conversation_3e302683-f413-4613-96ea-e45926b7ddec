package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcEntityDimensionDo;
import com.hsjry.core.limit.center.dal.dao.query.LcEntityDimensionQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 实体维度信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcEntityDimensionDao extends IBaseDao<LcEntityDimensionDo> {
    /**
     * 分页查询实体维度信息
     *
     * @param lcEntityDimensionQuery 条件
     * @return PageInfo<LcEntityDimensionDo>
     */
    PageInfo<LcEntityDimensionDo> selectPage(LcEntityDimensionQuery lcEntityDimensionQuery, PageParam pageParam);

    /**
     * 根据key查询实体维度信息
     *
     * @param ruleDimensionId
     * @return
     */
    LcEntityDimensionDo selectByKey(String ruleDimensionId);

    /**
     * 根据key删除实体维度信息
     *
     * @param ruleDimensionId
     * @return
     */
    int deleteByKey(String ruleDimensionId);

    /**
     * 查询实体维度信息信息
     *
     * @param lcEntityDimensionQuery 条件
     * @return List<LcEntityDimensionDo>
     */
    List<LcEntityDimensionDo> selectByExample(LcEntityDimensionQuery lcEntityDimensionQuery);

    /**
     * 新增实体维度信息信息
     *
     * @param lcEntityDimension 条件
     * @return int>
     */
    int insertBySelective(LcEntityDimensionDo lcEntityDimension);

    /**
     * 修改实体维度信息信息
     *
     * @param lcEntityDimension
     * @return
     */
    int updateBySelective(LcEntityDimensionDo lcEntityDimension);

    /**
     * 修改实体维度信息信息
     *
     * @param lcEntityDimension
     * @param lcEntityDimensionQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcEntityDimensionDo lcEntityDimension,
        LcEntityDimensionQuery lcEntityDimensionQuery);

    /**
     * 删除实体维度信息信息
     *
     * @param lcEntityDimensionQuery 条件
     * @return List<LcEntityDimensionDo>
     */
    int deleteByExample(LcEntityDimensionQuery lcEntityDimensionQuery);
}
