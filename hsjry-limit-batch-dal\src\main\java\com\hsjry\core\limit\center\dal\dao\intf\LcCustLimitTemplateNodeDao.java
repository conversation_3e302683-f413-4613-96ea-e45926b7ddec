package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitTemplateNodeDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitTemplateNodeQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度体系模板节点数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-02-02 05:57:30
 */
public interface LcCustLimitTemplateNodeDao extends IBaseDao<LcCustLimitTemplateNodeDo> {
    /**
     * 分页查询额度体系模板节点
     *
     * @param lcCustLimitTemplateNodeQuery 条件
     * @return PageInfo<LcCustLimitTemplateNodeDo>
     */
    PageInfo<LcCustLimitTemplateNodeDo> selectPage(LcCustLimitTemplateNodeQuery lcCustLimitTemplateNodeQuery,
        PageParam pageParam);

    /**
     * 根据key查询额度体系模板节点
     *
     * @param templateNodeId
     * @return
     */
    LcCustLimitTemplateNodeDo selectByKey(String templateNodeId);

    /**
     * 根据key删除额度体系模板节点
     *
     * @param templateNodeId
     * @return
     */
    int deleteByKey(String templateNodeId);

    /**
     * 查询额度体系模板节点信息
     *
     * @param lcCustLimitTemplateNodeQuery 条件
     * @return List<LcCustLimitTemplateNodeDo>
     */
    List<LcCustLimitTemplateNodeDo> selectByExample(LcCustLimitTemplateNodeQuery lcCustLimitTemplateNodeQuery);

    /**
     * 新增额度体系模板节点信息
     *
     * @param lcCustLimitTemplateNode 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitTemplateNodeDo lcCustLimitTemplateNode);

    /**
     * 修改额度体系模板节点信息
     *
     * @param lcCustLimitTemplateNode
     * @return
     */
    int updateBySelective(LcCustLimitTemplateNodeDo lcCustLimitTemplateNode);

    /**
     * 修改额度体系模板节点信息
     *
     * @param lcCustLimitTemplateNode
     * @param lcCustLimitTemplateNodeQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitTemplateNodeDo lcCustLimitTemplateNode,
        LcCustLimitTemplateNodeQuery lcCustLimitTemplateNodeQuery);

    /**
     * 删除额度体系模板节点信息
     *
     * @param lcCustLimitTemplateNodeQuery 条件
     * @return
     */
    int deleteByExample(LcCustLimitTemplateNodeQuery lcCustLimitTemplateNodeQuery);
}
