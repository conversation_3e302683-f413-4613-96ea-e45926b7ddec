package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 合同占用重算计划(准备)主键
 *
 * <AUTHOR>
 * @date 2023-11-08 02:51:47
 */
@Table(name = "lc_recal_contract_occupy_plan")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcRecalContractOccupyPlanKeyDo implements Serializable {

    private static final long serialVersionUID = 1722084472624709632L;
        /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
        /** 关联编号 */
    @Id
    @Column(name = "relation_id")
    private String relationId;
        /** 额度编号 */
    @Id
    @Column(name = "cust_limit_id")
    private String custLimitId;
    }