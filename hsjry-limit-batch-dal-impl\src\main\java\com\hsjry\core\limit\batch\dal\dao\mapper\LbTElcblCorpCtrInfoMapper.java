package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpCtrInfoDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 电票系统-中间表-贴现对公合同信息mapper
 *
 * <AUTHOR>
 * @date 2025-07-29 02:46:35
 */
public interface LbTElcblCorpCtrInfoMapper extends CommonMapper<LbTElcblCorpCtrInfoDo> {

    /**
     * 将电票对公客户额度同步数据插入到合同信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_elcbl_corp_ctr_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    int insertElcblCorpCtrInfo(@Param("templateNodeIdList") List<String> templateNodeIdList,
        @Param("custLimitIdList") List<String> custLimitIdList);

    /**
     * 更新额度实例金额信息表
     * 根据电票系统中间表数据更新LC_CUST_LIMIT_AMT_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    int updateCustLimitAmtInfo(@Param("custLimitIdList") List<String> custLimitIdList);
}