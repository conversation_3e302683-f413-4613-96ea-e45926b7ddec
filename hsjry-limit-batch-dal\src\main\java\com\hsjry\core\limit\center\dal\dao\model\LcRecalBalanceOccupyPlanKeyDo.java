package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 余额占用重算计划(准备)主键
 *
 * <AUTHOR>
 * @date 2023-06-12 05:41:04
 */
@Table(name = "lc_recal_balance_occupy_plan")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcRecalBalanceOccupyPlanKeyDo implements Serializable {

    private static final long serialVersionUID = 1668131282032459776L;
        /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
        /** 额度编号 */
    @Id
    @Column(name = "cust_limit_id")
    private String custLimitId;
        /** 实体编号 */
    @Id
    @Column(name = "entity_id")
    private String entityId;
    }