package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTOlIndvCtrInfoDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 网贷系统-中间表-个人合同信息mapper
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
public interface LbTOlIndvCtrInfoMapper extends CommonMapper<LbTOlIndvCtrInfoDo> {

    /**
     * 将网贷个人客户额度同步数据插入到合同信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_ol_indv_ctr_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    int insertOlIndvCtrInfo(List<String> templateNodeIdList, List<String> custLimitIdList);

    /**
     * 更新额度实例金额信息表
     * 根据网贷系统中间表数据更新LC_CUST_LIMIT_AMT_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    int updateCustLimitAmtInfo(List<String> custLimitIdList);
}