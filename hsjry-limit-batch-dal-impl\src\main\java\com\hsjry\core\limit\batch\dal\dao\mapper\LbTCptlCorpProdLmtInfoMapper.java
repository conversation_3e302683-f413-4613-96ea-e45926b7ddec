package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbTCptlCorpProdLmtInfoDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 资金系统-中间表-对公客户产品层额度信息mapper
 *
 * <AUTHOR>
 * @date 2025-07-30 02:03:23
 */
public interface LbTCptlCorpProdLmtInfoMapper extends CommonMapper<LbTCptlCorpProdLmtInfoDo> {

    /**
     * 往[资金系统-中间表-对公客户产品层额度信息]插入数据
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param limitIdList 额度ID列表
     * @return 插入条数
     */
    int insertCptlCorpProdLmtInfo(@Param("templateNodeIdList") List<String> templateNodeIdList,
        @Param("limitIdList") List<String> limitIdList);

    /**
     * 更新[额度实例金额信息]中[实占额度]
     *
     * @param limitIdList 额度ID列表
     * @return 更新条数
     */
    int updateRealOccupyAmount(@Param("limitIdList") List<String> limitIdList);
}