package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitAmtBaseInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitAmtInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitAmtInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度实例金额信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-15 07:51:56
 */
public interface LcCustLimitAmtInfoDao extends IBaseDao<LcCustLimitAmtInfoDo> {
    /**
     * 分页查询额度实例金额信息
     *
     * @param lcCustLimitAmtInfoQuery 条件
     * @return PageInfo<LcCustLimitAmtInfoDo>
     */
    PageInfo<LcCustLimitAmtInfoDo> selectPage(LcCustLimitAmtInfoQuery lcCustLimitAmtInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度实例金额信息
     *
     * @param custLimitId
     * @return
     */
    LcCustLimitAmtInfoDo selectByKey(String custLimitId);

    /**
     * 根据key删除额度实例金额信息
     *
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custLimitId);

    /**
     * 查询额度实例金额信息信息
     *
     * @param lcCustLimitAmtInfoQuery 条件
     * @return List<LcCustLimitAmtInfoDo>
     */
    List<LcCustLimitAmtInfoDo> selectByExample(LcCustLimitAmtInfoQuery lcCustLimitAmtInfoQuery);

    /**
     * 查询额度实例金额信息信息
     *
     * @param lcCustLimitAmtInfoQuery 条件
     * @return List<LcCustLimitAmtInfoDo>
     */
    List<LcCustLimitAmtInfoDo> selectExampleWithoutAop(LcCustLimitAmtInfoQuery lcCustLimitAmtInfoQuery);

    /**
     * 查询额度实例金额信息信息
     * 
     * @param userIds
     * @param templateNodeIdList
     * @return
     */
    List<LcCustLimitAmtBaseInfoDo> selectCustLimitAmtBase(List<String> userIds, List<String> templateNodeIdList,
        String limitLevel);

    /**
     * 新增额度实例金额信息信息
     *
     * @param lcCustLimitAmtInfo 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitAmtInfoDo lcCustLimitAmtInfo);

    /**
     * 修改额度实例金额信息信息
     *
     * @param lcCustLimitAmtInfo
     * @return
     */
    int updateBySelective(LcCustLimitAmtInfoDo lcCustLimitAmtInfo);

    /**
     * 修改额度实例金额信息信息
     *
     * @param lcCustLimitAmtInfo
     * @param lcCustLimitAmtInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitAmtInfoDo lcCustLimitAmtInfo,
        LcCustLimitAmtInfoQuery lcCustLimitAmtInfoQuery);

    /**
     * 修改额度实例金额为空
     *
     * @param custLimitId
     * @param lowRiskAmount
     * @param preOccupyLowRiskAmt
     * @param realOccupyLowRiskAmt
     * @param usedLowRiskAmt
     */
    void updateAmtNull(String custLimitId, boolean lowRiskAmount, boolean preOccupyLowRiskAmt,
        boolean realOccupyLowRiskAmt, boolean usedLowRiskAmt);

    /**
     * 从对象信息表同步客户编号到金额信息表
     *
     * @return int
     */
    int updateCustNo();
}
