package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 导入流水Do
 *
 * <AUTHOR>
 * @date 2023-04-18 08:29:40
 */
@Table(name = "lc_import_serial")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcImportSerialDo extends LcImportSerialKeyDo implements Serializable {
    private static final long serialVersionUID = 1648242377338585088L;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 错误信息 */
    @Column(name = "error_message")
    private String errorMessage;
    /** 空白行数量 */
    @Column(name = "empty_count")
    private Integer emptyCount;
    /** 处理失败的数据量 */
    @Column(name = "fail_count")
    private Integer failCount;
    /** 处理通过的数据量 */
    @Column(name = "success_count")
    private Integer successCount;
    /** 数据总量 */
    @Column(name = "total_count")
    private Integer totalCount;
    /** 附件摘要 */
    @Column(name = "attach_abstract")
    private String attachAbstract;
    /** 附件编号 */
    @Column(name = "attachment_id")
    private String attachmentId;
    /** 导入业务类型 */
    @Column(name = "import_biz_type")
    private String importBizType;
    /** 处理状态 */
    @Column(name = "status")
    private String status;
    /** 经办人 */
    @Column(name = "operator_no")
    private String operatorNo;
    /** 全局流水号 */
    @Column(name = "global_serial_no")
    private String globalSerialNo;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 前置业务时间 */
    @Column(name = "inbound_serial_datetime")
    private java.util.Date inboundSerialDatetime;
    /** 前置业务流水 */
    @Column(name = "inbound_serial_no")
    private String inboundSerialNo;
    /** 业务时间 */
    @Column(name = "biz_datetime")
    private java.util.Date bizDatetime;
    /** 渠道号 */
    @Column(name = "channel_no")
    private String channelNo;
    /** 业务流水号 */
    @Column(name = "serial_no")
    private String serialNo;
}
