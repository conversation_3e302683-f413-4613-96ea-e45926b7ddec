package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcAmtDetailDo;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtDetailQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 金额明细数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcAmtDetailDao extends IBaseDao<LcAmtDetailDo> {
    /**
     * 分页查询金额明细
     *
     * @param lcAmtDetailQuery 条件
     * @return PageInfo<LcAmtDetailDo>
     */
    PageInfo<LcAmtDetailDo> selectPage(LcAmtDetailQuery lcAmtDetailQuery, PageParam pageParam);

    /**
     * 根据key查询金额明细
     *
     * @param amountId
     * @param seq
     * @return
     */
    LcAmtDetailDo selectByKey(String amountId, Integer seq);

    /**
     * 根据key删除金额明细
     *
     * @param amountId
     * @param seq
     * @return
     */
    int deleteByKey(String amountId, Integer seq);

    /**
     * 查询金额明细信息
     *
     * @param lcAmtDetailQuery 条件
     * @return List<LcAmtDetailDo>
     */
    List<LcAmtDetailDo> selectByExample(LcAmtDetailQuery lcAmtDetailQuery);

    /**
     * 新增金额明细信息
     *
     * @param lcAmtDetail 条件
     * @return int>
     */
    int insertBySelective(LcAmtDetailDo lcAmtDetail);

    /**
     * 修改金额明细信息
     *
     * @param lcAmtDetail
     * @return
     */
    int updateBySelective(LcAmtDetailDo lcAmtDetail);

    /**
     * 修改金额明细信息
     *
     * @param lcAmtDetail
     * @param lcAmtDetailQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcAmtDetailDo lcAmtDetail, LcAmtDetailQuery lcAmtDetailQuery);
}
