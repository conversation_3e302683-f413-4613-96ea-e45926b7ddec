package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitSceneDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitSceneQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度场景数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-02-02 06:51:40
 */
public interface LcCustLimitSceneDao extends IBaseDao<LcCustLimitSceneDo> {
    /**
     * 分页查询额度场景
     *
     * @param lcCustLimitSceneQuery 条件
     * @return PageInfo<LcCustLimitSceneDo>
     */
    PageInfo<LcCustLimitSceneDo> selectPage(LcCustLimitSceneQuery lcCustLimitSceneQuery, PageParam pageParam);

    /**
     * 根据key查询额度场景
     *
     * @param sceneCode
     * @param templateNodeId
     * @return
     */
    LcCustLimitSceneDo selectByKey(String sceneCode, String templateNodeId);

    /**
     * 根据key删除额度场景
     *
     * @param sceneCode
     * @param templateNodeId
     * @return
     */
    int deleteByKey(String sceneCode, String templateNodeId);

    /**
     * 查询额度场景信息
     *
     * @param lcCustLimitSceneQuery 条件
     * @return List<LcCustLimitSceneDo>
     */
    List<LcCustLimitSceneDo> selectByExample(LcCustLimitSceneQuery lcCustLimitSceneQuery);

    /**
     * 新增额度场景信息
     *
     * @param lcCustLimitScene 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitSceneDo lcCustLimitScene);

    /**
     * 修改额度场景信息
     *
     * @param lcCustLimitScene
     * @return
     */
    int updateBySelective(LcCustLimitSceneDo lcCustLimitScene);

    /**
     * 修改额度场景信息
     *
     * @param lcCustLimitScene
     * @param lcCustLimitSceneQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitSceneDo lcCustLimitScene, LcCustLimitSceneQuery lcCustLimitSceneQuery);

    /**
     * 删除额度场景信息
     *
     * @param lcCustLimitSceneQuery 条件
     * @return
     */
    int deleteByExample(LcCustLimitSceneQuery lcCustLimitSceneQuery);
}
