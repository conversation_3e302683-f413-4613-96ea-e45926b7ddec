# Design Document

## Overview

本设计文档描述了为hsjry-limit-batch项目中copy目录下的bizImpl类完成分片执行方法的执行逻辑实现。该设计基于现有的分片处理架构，通过JobCoreBusinessFactory工厂模式获取对应的sharding impl类，实现高效的数据备份同步处理。

系统采用分层架构设计，将业务调度层(BizImpl)与分片执行层(ShardingImpl)分离，通过工厂模式实现松耦合，确保系统的可扩展性和可维护性。

## Architecture

### 整体架构图

```mermaid
graph TB
    A[JobInitDto] --> B[BizImpl Classes]
    B --> C[JobCoreBusinessFactory]
    C --> D[ShardingImpl Classes]
    D --> E[AbstractShardingPrepareBiz]
    E --> F[Database Operations]
    
    subgraph "Business Layer"
        B1[LbCEntityInfoBakSyncBizImpl]
        B2[LbCLimitInfoBakSyncBizImpl]
        B3[LbCEntityOperateSerialBakSyncBizImpl]
        B4[LbCLimitAmtInfoBakSyncBizImpl]
        B5[LbCLimitObjectInfoBakSyncBizImpl]
        B6[LbCLimitOperateSerialBakSyncBizImpl]
        B7[LbCLimitRelationBakSyncBizImpl]
    end
    
    subgraph "Sharding Layer"
        D1[LbCEntityInfoBakSyncImpl]
        D2[LbCLimitInfoBakSyncImpl]
        D3[LbCEntityOperateSerialBakSyncImpl]
        D4[LbCLimitAmtInfoBakSyncImpl]
        D5[LbCLimitObjectInfoBakSyncImpl]
        D6[LbCLimitOperateSerialFileSyncImpl]
        D7[LbCLimitRelationFileSyncImpl]
    end
    
    B1 --> D1
    B2 --> D2
    B3 --> D3
    B4 --> D4
    B5 --> D5
    B6 --> D6
    B7 --> D7
```

### 分片处理流程图

```mermaid
sequenceDiagram
    participant Client as 调度系统
    participant BizImpl as BizImpl类
    participant Factory as JobCoreBusinessFactory
    participant ShardingImpl as ShardingImpl类
    participant DB as 数据库
    
    Client->>BizImpl: execBaseJob(jobInitDto)
    BizImpl->>Factory: getJobCoreBusiness(jobTradeCode)
    Factory->>ShardingImpl: 返回对应的实现类
    BizImpl->>ShardingImpl: preHandle(jobInitDto)
    ShardingImpl->>ShardingImpl: generateJobSharding(jobInitDto)
    
    loop 分片处理
        ShardingImpl->>ShardingImpl: queryShardingResult(...)
        ShardingImpl->>DB: 查询分片数据
        DB->>ShardingImpl: 返回数据
        ShardingImpl->>ShardingImpl: execJobCoreBusiness(...)
        ShardingImpl->>DB: 清空目标表(仅第一个分片)
        ShardingImpl->>ShardingImpl: 数据转换
        ShardingImpl->>DB: 批量插入数据
        ShardingImpl->>ShardingImpl: 更新分片流水
    end
    
    BizImpl->>ShardingImpl: afterHandle(jobInitDto)
    BizImpl->>Client: 返回执行结果
```

## Components and Interfaces

### 核心组件

#### 1. BizImpl业务实现类
- **职责**: 作为任务入口点，负责调度和管理整个数据备份同步流程
- **接口**: 实现`BaseOrdinaryBiz`接口
- **关键方法**:
  - `getJobTrade()`: 返回任务交易码
  - `execBaseJob(JobInitDto)`: 执行基础任务逻辑

#### 2. ShardingImpl分片实现类
- **职责**: 实现具体的分片处理逻辑和数据操作
- **接口**: 继承`AbstractShardingPrepareBiz`，实现`JobCoreBusiness`接口
- **关键方法**:
  - `generateJobSharding()`: 生成分片任务
  - `queryShardingResult()`: 查询分片数据
  - `execJobCoreBusiness()`: 执行分片业务逻辑

#### 3. JobCoreBusinessFactory工厂类
- **职责**: 管理和提供JobCoreBusiness实例
- **模式**: 工厂模式 + Spring ApplicationContextAware
- **功能**: 根据jobTradeCode获取对应的实现类

### 接口设计

#### BaseOrdinaryBiz接口
```java
public interface BaseOrdinaryBiz {
    IEnumTrade getJobTrade();
    void execBaseJob(JobInitDto jobInitDto);
}
```

#### JobCoreBusiness接口
```java
public interface JobCoreBusiness<T> {
    IEnumTrade getJobTrade();
    void preHandle(JobInitDto jobInitDto);
    void afterHandle(JobInitDto jobInitDto);
    List<JobShared> generateJobSharding(JobInitDto jobInitDto);
    ShardingResult<T> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo, 
                                         JobInitDto jobInitDto, JobShared jobShared);
    void execJobCoreBusiness(ShardingResult<T> shardingResult);
}
```

## Data Models

### 核心数据模型

#### JobInitDto - 任务初始化参数
```java
public class JobInitDto {
    private Integer businessDate;    // 业务日期
    private String batchSerialNo;   // 批次流水号
    private Integer fixNum;         // 分片大小
    // 其他字段...
}
```

#### JobShared - 分片任务参数
```java
public class JobShared {
    private Integer businessDate;    // 业务日期
    private String batchSerialNo;   // 批次流水号
    private Integer batchNum;       // 分片号
    private Integer offset;         // 偏移量
    private Integer limit;          // 限制数量
    private String extParam;        // 扩展参数
    // 其他字段...
}
```

#### ShardingResult - 分片结果
```java
public class ShardingResult<T> {
    private LcSliceBatchSerialDo lcSliceBatchSerialDo;  // 分片流水
    private JobShared jobShared;                        // 分片参数
    private List<T> shardingResultList;                 // 分片数据
    // 其他字段...
}
```

### 数据转换模型

每个copy任务都有对应的数据转换：
- `LcEntityInfoDo` → `LbCEntityInfoDo`
- `LcCustLimitInfoDo` → `LbCLimitInfoDo`
- `LcEntityOperateSerialDo` → `LbCEntityOperateSerialDo`
- `LcCustLimitAmtInfoDo` → `LbCLimitAmtInfoDo`
- 等等...

## Error Handling

### 异常处理策略

#### 1. 业务异常处理
- **异常类型**: `HsjryBizException`
- **错误码**: `EnumBatchJobError.SYSTEM_ERR`
- **处理方式**: 记录详细错误日志，抛出标准业务异常

#### 2. 系统异常处理
- **异常类型**: `RuntimeException`
- **处理方式**: 包装原始异常信息，提供有意义的错误描述

#### 3. 数据异常处理
- **空数据**: 快速返回，避免不必要的处理
- **数据转换异常**: 记录错误并继续处理其他数据
- **数据库异常**: 回滚事务，记录详细错误信息

### 异常处理流程

```mermaid
graph TD
    A[异常发生] --> B{异常类型判断}
    B -->|业务异常| C[记录业务日志]
    B -->|系统异常| D[记录系统日志]
    B -->|数据异常| E[记录数据日志]
    
    C --> F[抛出HsjryBizException]
    D --> G[抛出RuntimeException]
    E --> H[继续处理或跳过]
    
    F --> I[上层捕获处理]
    G --> I
    H --> J[记录处理结果]
```

## Testing Strategy

### 测试层次

#### 1. 单元测试
- **测试范围**: 各个方法的独立功能
- **测试工具**: JUnit + Mockito
- **覆盖率要求**: 核心业务逻辑 > 80%

#### 2. 集成测试
- **测试范围**: 组件间的交互和数据流
- **测试环境**: Spring Boot Test环境
- **数据准备**: 使用测试数据库和模拟数据

#### 3. 性能测试
- **测试指标**: 处理速度、内存使用、数据库连接
- **测试场景**: 大数据量分片处理
- **性能基准**: 每分钟处理数据量 > 10万条

### 测试用例设计

#### BizImpl类测试用例
```java
@Test
public void testExecBaseJob_Success() {
    // 测试正常执行流程
}

@Test
public void testExecBaseJob_JobCoreBusinessNotFound() {
    // 测试工厂返回null的情况
}

@Test
public void testExecBaseJob_PreHandleException() {
    // 测试前置处理异常
}

@Test
public void testExecBaseJob_AfterHandleException() {
    // 测试后置处理异常
}
```

#### ShardingImpl类测试用例
```java
@Test
public void testGenerateJobSharding_Success() {
    // 测试分片生成
}

@Test
public void testQueryShardingResult_Success() {
    // 测试分片数据查询
}

@Test
public void testExecJobCoreBusiness_Success() {
    // 测试分片业务执行
}

@Test
public void testExecJobCoreBusiness_EmptyData() {
    // 测试空数据处理
}

@Test
public void testExecJobCoreBusiness_FirstShardClearTable() {
    // 测试第一个分片清空表
}
```

### 测试数据管理

#### 测试数据准备
- **源数据**: 在lc_*表中准备测试数据
- **目标数据**: 验证lb_c_*表中的数据正确性
- **分片数据**: 准备不同大小的数据集测试分片逻辑

#### 测试环境隔离
- **数据库隔离**: 使用独立的测试数据库
- **事务隔离**: 每个测试用例独立事务
- **数据清理**: 测试完成后自动清理数据

## Implementation Guidelines

### 代码规范

#### 1. 类命名规范
- BizImpl类: `Lb{业务名}BakSyncBizImpl`
- ShardingImpl类: `Lb{业务名}BakSyncImpl`
- 服务注册名: 使用小驼峰命名

#### 2. 日志规范
```java
String prefixLog = String.format("营运日期:[%s],批量流水号:[%s],任务交易码:[%s-%s]", 
    businessDate, batchSerialNo, jobTradeCode, jobTradeDesc);
```

#### 3. 异常处理规范
```java
try {
    // 业务逻辑
} catch (Exception e) {
    log.error(prefixLog + "执行失败", e);
    throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
        EnumBatchJobError.SYSTEM_ERR.getDescription());
}
```

### 性能优化指导

#### 1. 批量处理优化
- 使用合适的批处理大小(建议1000-5000条)
- 避免在循环中进行数据库操作
- 使用批量插入而非单条插入

#### 2. 内存管理优化
- 及时释放大对象引用
- 使用流式处理避免一次性加载大量数据
- 合理设置JVM堆内存参数

#### 3. 数据库优化
- 使用索引优化查询性能
- 合理设置数据库连接池参数
- 避免长时间持有数据库连接

### 监控和运维

#### 1. 日志监控
- 记录关键节点的执行时间
- 记录数据处理量和处理速度
- 记录异常和错误信息

#### 2. 性能监控
- 监控内存使用情况
- 监控数据库连接数
- 监控任务执行时间

#### 3. 告警机制
- 任务执行失败告警
- 性能指标异常告警
- 数据质量问题告警