package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTUserManageInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTUserManageInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-客户经营数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-15 03:50:12
 */
public interface LbTUserManageInfoDao extends IBaseDao<LbTUserManageInfoDo> {
    /**
     * 分页查询额度中心-中间表-客户经营
     *
     * @param lbTUserManageInfoQuery 条件
     * @return PageInfo<LbTUserManageInfoDo>
     */
    PageInfo<LbTUserManageInfoDo> selectPage(LbTUserManageInfoQuery lbTUserManageInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-客户经营
     *
     * @param resourceId
     * @return
     */
    LbTUserManageInfoDo selectByKey(String resourceId);

    /**
     * 根据key删除额度中心-中间表-客户经营
     *
     * @param resourceId
     * @return
     */
    int deleteByKey(String resourceId);

    /**
     * 查询额度中心-中间表-客户经营信息
     *
     * @param lbTUserManageInfoQuery 条件
     * @return List<LbTUserManageInfoDo>
     */
    List<LbTUserManageInfoDo> selectByExample(LbTUserManageInfoQuery lbTUserManageInfoQuery);

    /**
     * 新增额度中心-中间表-客户经营信息
     *
     * @param lbTUserManageInfo 条件
     * @return int>
     */
    int insertBySelective(LbTUserManageInfoDo lbTUserManageInfo);

    /**
     * 修改额度中心-中间表-客户经营信息
     *
     * @param lbTUserManageInfo
     * @return
     */
    int updateBySelective(LbTUserManageInfoDo lbTUserManageInfo);

    /**
     * 修改额度中心-中间表-客户经营信息
     *
     * @param lbTUserManageInfo
     * @param lbTUserManageInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTUserManageInfoDo lbTUserManageInfo,
        LbTUserManageInfoQuery lbTUserManageInfoQuery);
}
