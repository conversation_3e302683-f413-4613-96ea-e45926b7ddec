package com.hsjry.core.limit.center.dal.dao.intf;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoHisDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoHisQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度实例历史数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-11-13 01:05:13
 */
public interface LcCustLimitInfoHisDao extends IBaseDao<LcCustLimitInfoHisDo> {
    /**
     * 分页查询额度实例历史
     *
     * @param lcCustLimitInfoHisQuery 条件
     * @return PageInfo<LcCustLimitInfoHisDo>
     */
    PageInfo<LcCustLimitInfoHisDo> selectPage(LcCustLimitInfoHisQuery lcCustLimitInfoHisQuery, PageParam pageParam);

    /**
     * 根据key查询额度实例历史
     *
     * @param hisId
     * @param custLimitId
     * @return
     */
    LcCustLimitInfoHisDo selectByKey(String hisId, String custLimitId);

    /**
     * 根据key删除额度实例历史
     *
     * @param hisId
     * @param custLimitId
     * @return
     */
    int deleteByKey(String hisId, String custLimitId);

    /**
     * 查询额度实例历史信息
     *
     * @param lcCustLimitInfoHisQuery 条件
     * @return List<LcCustLimitInfoHisDo>
     */
    List<LcCustLimitInfoHisDo> selectByExample(LcCustLimitInfoHisQuery lcCustLimitInfoHisQuery);

    /**
     * 新增额度实例历史信息
     *
     * @param lcCustLimitInfoHis 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitInfoHisDo lcCustLimitInfoHis);

    /**
     * 修改额度实例历史信息
     *
     * @param lcCustLimitInfoHis
     * @return
     */
    int updateBySelective(LcCustLimitInfoHisDo lcCustLimitInfoHis);

    /**
     * 修改额度实例历史信息
     *
     * @param lcCustLimitInfoHis
     * @param lcCustLimitInfoHisQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitInfoHisDo lcCustLimitInfoHis,
        LcCustLimitInfoHisQuery lcCustLimitInfoHisQuery);
}
