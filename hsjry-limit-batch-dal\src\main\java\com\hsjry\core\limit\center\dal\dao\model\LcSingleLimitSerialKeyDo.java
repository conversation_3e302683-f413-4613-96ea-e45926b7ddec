package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 单一限额操作流水主键
 *
 * <AUTHOR>
 * @date 2023-06-28 11:32:07
 */
@Table(name = "lc_single_limit_serial")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcSingleLimitSerialKeyDo implements Serializable {

    private static final long serialVersionUID = 1674017831437664256L;
    /** 单一限额操作流水编号 */
    @Id
    @Column(name = "lsls_serial_no")
    private String lslsSerialNo;
        /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    }