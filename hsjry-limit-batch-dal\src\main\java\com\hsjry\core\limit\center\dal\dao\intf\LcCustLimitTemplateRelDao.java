package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitTemplateRelDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitTemplateRelQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度体系模板关联数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-06 03:12:50
 */
public interface LcCustLimitTemplateRelDao extends IBaseDao<LcCustLimitTemplateRelDo> {
    /**
     * 分页查询额度体系模板关联
     *
     * @param lcCustLimitTemplateRelQuery 条件
     * @return PageInfo<LcCustLimitTemplateRelDo>
     */
    PageInfo<LcCustLimitTemplateRelDo> selectPage(LcCustLimitTemplateRelQuery lcCustLimitTemplateRelQuery,
        PageParam pageParam);

    /**
     * 根据key查询额度体系模板关联
     *
     * @param templateRelationId
     * @return
     */
    LcCustLimitTemplateRelDo selectByKey(String templateRelationId);

    /**
     * 根据key删除额度体系模板关联
     *
     * @param templateRelationId
     * @return
     */
    int deleteByKey(String templateRelationId);

    /**
     * 查询额度体系模板关联信息
     *
     * @param lcCustLimitTemplateRelQuery 条件
     * @return List<LcCustLimitTemplateRelDo>
     */
    List<LcCustLimitTemplateRelDo> selectByExample(LcCustLimitTemplateRelQuery lcCustLimitTemplateRelQuery);

    /**
     * 新增额度体系模板关联信息
     *
     * @param lcCustLimitTemplateRel 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitTemplateRelDo lcCustLimitTemplateRel);

    /**
     * 修改额度体系模板关联信息
     *
     * @param lcCustLimitTemplateRel
     * @return
     */
    int updateBySelective(LcCustLimitTemplateRelDo lcCustLimitTemplateRel);

    /**
     * 修改额度体系模板关联信息
     *
     * @param lcCustLimitTemplateRel
     * @param lcCustLimitTemplateRelQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitTemplateRelDo lcCustLimitTemplateRel,
        LcCustLimitTemplateRelQuery lcCustLimitTemplateRelQuery);

    /**
     * 删除额度体系模板关联信息
     *
     * @param lcCustLimitTemplateRelQuery 条件
     * @return
     */
    int deleteByExample(LcCustLimitTemplateRelQuery lcCustLimitTemplateRelQuery);
}
