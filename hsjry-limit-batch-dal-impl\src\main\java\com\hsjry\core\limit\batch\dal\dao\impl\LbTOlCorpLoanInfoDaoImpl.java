package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTOlCorpLoanInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTOlCorpLoanInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlCorpLoanInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlCorpLoanInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlCorpLoanInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTOlCorpLoanInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

import lombok.extern.slf4j.Slf4j;

/**
 * 网贷系统-中间表-对公借据信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
@Slf4j
@Repository
public class LbTOlCorpLoanInfoDaoImpl extends AbstractBaseDaoImpl<LbTOlCorpLoanInfoDo, LbTOlCorpLoanInfoMapper>
    implements LbTOlCorpLoanInfoDao {
    /**
     * 分页查询
     *
     * @param lbTOlCorpLoanInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTOlCorpLoanInfoDo> selectPage(LbTOlCorpLoanInfoQuery lbTOlCorpLoanInfo, PageParam pageParam) {
        LbTOlCorpLoanInfoExample example = buildExample(lbTOlCorpLoanInfo);
        return PageHelper.<LbTOlCorpLoanInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询网贷系统-中间表-对公借据信息
     *
     * @param custNo
     * @param custLimitId
     * @param entityId
     * @return
     */
    @Override
    public LbTOlCorpLoanInfoDo selectByKey(String custNo, String custLimitId, String entityId) {
        LbTOlCorpLoanInfoKeyDo lbTOlCorpLoanInfoKeyDo = new LbTOlCorpLoanInfoKeyDo();
        lbTOlCorpLoanInfoKeyDo.setCustNo(custNo);
        lbTOlCorpLoanInfoKeyDo.setCustLimitId(custLimitId);
        lbTOlCorpLoanInfoKeyDo.setEntityId(entityId);
        lbTOlCorpLoanInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTOlCorpLoanInfoKeyDo);
    }

    /**
     * 根据key删除网贷系统-中间表-对公借据信息
     *
     * @param custNo
     * @param custLimitId
     * @param entityId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId, String entityId) {
        LbTOlCorpLoanInfoKeyDo lbTOlCorpLoanInfoKeyDo = new LbTOlCorpLoanInfoKeyDo();
        lbTOlCorpLoanInfoKeyDo.setCustNo(custNo);
        lbTOlCorpLoanInfoKeyDo.setCustLimitId(custLimitId);
        lbTOlCorpLoanInfoKeyDo.setEntityId(entityId);
        lbTOlCorpLoanInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTOlCorpLoanInfoKeyDo);
    }

    /**
     * 查询网贷系统-中间表-对公借据信息信息
     *
     * @param lbTOlCorpLoanInfo 条件
     * @return List<LbTOlCorpLoanInfoDo>
     */
    @Override
    public List<LbTOlCorpLoanInfoDo> selectByExample(LbTOlCorpLoanInfoQuery lbTOlCorpLoanInfo) {
        return getMapper().selectByExample(buildExample(lbTOlCorpLoanInfo));
    }

    /**
     * 新增网贷系统-中间表-对公借据信息信息
     *
     * @param lbTOlCorpLoanInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTOlCorpLoanInfoDo lbTOlCorpLoanInfo) {
        if (lbTOlCorpLoanInfo == null) {
            return -1;
        }

        lbTOlCorpLoanInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTOlCorpLoanInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTOlCorpLoanInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTOlCorpLoanInfo);
    }

    /**
     * 修改网贷系统-中间表-对公借据信息信息
     *
     * @param lbTOlCorpLoanInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTOlCorpLoanInfoDo lbTOlCorpLoanInfo) {
        if (lbTOlCorpLoanInfo == null) {
            return -1;
        }
        lbTOlCorpLoanInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTOlCorpLoanInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTOlCorpLoanInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTOlCorpLoanInfoDo lbTOlCorpLoanInfo,
        LbTOlCorpLoanInfoQuery lbTOlCorpLoanInfoQuery) {
        lbTOlCorpLoanInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTOlCorpLoanInfo, buildExample(lbTOlCorpLoanInfoQuery));
    }

    /**
     * 构建网贷系统-中间表-对公借据信息Example信息
     *
     * @param lbTOlCorpLoanInfo
     * @return
     */
    public LbTOlCorpLoanInfoExample buildExample(LbTOlCorpLoanInfoQuery lbTOlCorpLoanInfo) {
        LbTOlCorpLoanInfoExample example = new LbTOlCorpLoanInfoExample();
        LbTOlCorpLoanInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTOlCorpLoanInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTOlCorpLoanInfo.getEntityRelationId())) {
                criteria.andEntityRelationIdEqualTo(lbTOlCorpLoanInfo.getEntityRelationId());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpLoanInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTOlCorpLoanInfo.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpLoanInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTOlCorpLoanInfo.getOperatorId());
            }
            if (null != lbTOlCorpLoanInfo.getLeftLowRisk()) {
                criteria.andLeftLowRiskEqualTo(lbTOlCorpLoanInfo.getLeftLowRisk());
            }
            if (null != lbTOlCorpLoanInfo.getLowRisk()) {
                criteria.andLowRiskEqualTo(lbTOlCorpLoanInfo.getLowRisk());
            }
            if (null != lbTOlCorpLoanInfo.getLeftAmount()) {
                criteria.andLeftAmountEqualTo(lbTOlCorpLoanInfo.getLeftAmount());
            }
            if (null != lbTOlCorpLoanInfo.getAmount()) {
                criteria.andAmountEqualTo(lbTOlCorpLoanInfo.getAmount());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpLoanInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTOlCorpLoanInfo.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpLoanInfo.getEntityApplyId())) {
                criteria.andEntityApplyIdEqualTo(lbTOlCorpLoanInfo.getEntityApplyId());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpLoanInfo.getStatus())) {
                criteria.andStatusEqualTo(lbTOlCorpLoanInfo.getStatus());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpLoanInfo.getEntityType())) {
                criteria.andEntityTypeEqualTo(lbTOlCorpLoanInfo.getEntityType());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpLoanInfo.getEntityId())) {
                criteria.andEntityIdEqualTo(lbTOlCorpLoanInfo.getEntityId());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpLoanInfo.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTOlCorpLoanInfo.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpLoanInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTOlCorpLoanInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpLoanInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTOlCorpLoanInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpLoanInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTOlCorpLoanInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTOlCorpLoanInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTOlCorpLoanInfo.getCustTyp());
            }
        }
        buildExampleExt(lbTOlCorpLoanInfo, criteria);
        return example;
    }

    /**
     * 构建网贷系统-中间表-对公借据信息ExampleExt方法
     *
     * @param lbTOlCorpLoanInfo
     * @return
     */
    public void buildExampleExt(LbTOlCorpLoanInfoQuery lbTOlCorpLoanInfo, LbTOlCorpLoanInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 将网贷对公客户额度同步数据插入到借据信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_ol_corp_loan_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    @Override
    public int insertOlCorpLoanInfo(List<String> templateNodeIdList, List<String> custLimitIdList) {
        if (CollectionUtils.isEmpty(templateNodeIdList) || CollectionUtils.isEmpty(custLimitIdList)) {
            log.warn("模板节点ID列表或客户额度ID列表为空，跳过插入操作");
            return 0;
        }
        return getMapper().insertOlCorpLoanInfo(templateNodeIdList, custLimitIdList);
    }

    /**
     * 更新实体信息表
     * 根据网贷系统中间表数据更新LC_ENTITY_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    @Override
    public int updateEntityInfo(List<String> custLimitIdList) {
        if (CollectionUtils.isEmpty(custLimitIdList)) {
            log.warn("客户额度ID列表为空，跳过更新实体信息操作");
            return 0;
        }
        return getMapper().updateEntityInfo(custLimitIdList);
    }

    /**
     * 更新实体操作流水表
     * 根据网贷系统中间表数据更新LC_ENTITY_OPERATE_SERIAL表
     *
     * @return int 更新的记录数
     */
    @Override
    public int updateEntityOperateSerial() {
        return getMapper().updateEntityOperateSerial();
    }

}
