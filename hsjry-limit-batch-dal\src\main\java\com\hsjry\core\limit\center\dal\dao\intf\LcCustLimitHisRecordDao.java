package com.hsjry.core.limit.center.dal.dao.intf;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitHisRecordDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitHisRecordQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度历史记录数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-11-13 01:05:13
 */
public interface LcCustLimitHisRecordDao extends IBaseDao<LcCustLimitHisRecordDo> {
    /**
     * 分页查询额度历史记录
     *
     * @param lcCustLimitHisRecordQuery 条件
     * @return PageInfo<LcCustLimitHisRecordDo>
     */
    PageInfo<LcCustLimitHisRecordDo> selectPage(LcCustLimitHisRecordQuery lcCustLimitHisRecordQuery,
        PageParam pageParam);

    /**
     * 根据key查询额度历史记录
     *
     * @param recordId
     * @return
     */
    LcCustLimitHisRecordDo selectByKey(String recordId);

    /**
     * 根据key删除额度历史记录
     *
     * @param recordId
     * @return
     */
    int deleteByKey(String recordId);

    /**
     * 查询额度历史记录信息
     *
     * @param lcCustLimitHisRecordQuery 条件
     * @return List<LcCustLimitHisRecordDo>
     */
    List<LcCustLimitHisRecordDo> selectByExample(LcCustLimitHisRecordQuery lcCustLimitHisRecordQuery);

    /**
     * 新增额度历史记录信息
     *
     * @param lcCustLimitHisRecord 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitHisRecordDo lcCustLimitHisRecord);

    /**
     * 修改额度历史记录信息
     *
     * @param lcCustLimitHisRecord
     * @return
     */
    int updateBySelective(LcCustLimitHisRecordDo lcCustLimitHisRecord);

    /**
     * 修改额度历史记录信息
     *
     * @param lcCustLimitHisRecord
     * @param lcCustLimitHisRecordQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitHisRecordDo lcCustLimitHisRecord,
        LcCustLimitHisRecordQuery lcCustLimitHisRecordQuery);
}
