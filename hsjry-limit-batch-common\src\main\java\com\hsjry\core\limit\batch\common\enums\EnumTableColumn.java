package com.hsjry.core.limit.batch.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
@AllArgsConstructor
public enum EnumTableColumn {
    /** 额度编号 **/
    LCLI_CLI("LC_CUST_LIMIT_INFO", "CUST_LIMIT_ID"),
    /** 实例编号 **/
    LCLI_II("LC_CUST_LIMIT_INFO", "inst_id"),
    /** 额度关系编号 **/
    LCLR_LRI("LC_CUST_LIMIT_RELATION", "limit_relation_id"),
    /** 全局流水号 **/
    LCLOS_GSN("LC_CUST_LIMIT_OPERATE_SERIAL", "global_serial_no"),
    /** 业务流水号 **/
    LCLOS_SN("LC_CUST_LIMIT_OPERATE_SERIAL", "serial_no"),
    /** 前置业务流水 **/
    LCLOS_ISN("LC_CUST_LIMIT_OPERATE_SERIAL", "inbound_serial_no"),
    /** 操作流水编号 **/
    LCLOS_CSN("LC_CUST_LIMIT_OPERATE_SERIAL", "clos_serial_no"),
    ;
    private final String tableName;

    private final String columnName;
}
