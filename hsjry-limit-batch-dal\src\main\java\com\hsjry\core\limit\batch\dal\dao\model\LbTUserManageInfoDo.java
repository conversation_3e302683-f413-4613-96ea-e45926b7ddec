package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度中心-中间表-客户经营Do
 *
 * <AUTHOR>
 * @date 2025-07-15 03:50:12
 */
@Table(name = "lb_t_user_manage_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbTUserManageInfoDo extends LbTUserManageInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1944967709649469441L;
    /** 客户id */
    @Column(name = "user_id")
    private String userId;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 修改日期 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建人 */
    @Column(name = "creator")
    private String creator;
    /** 修改人 */
    @Column(name = "modifier")
    private String modifier;
    /** 证件类型 */
    @Column(name = "certificate_kind")
    private String certificateKind;
    /** 证件号码 */
    @Column(name = "certificate_no")
    private String certificateNo;
    /** 国民经济行业分类-小类 */
    @Column(name = "industry_category")
    private String industryCategory;
    /** 国民经济行业分类-大类 */
    @Column(name = "vocation_level_two")
    private String vocationLevelTwo;
    /** 国民经济行业分类-中类 */
    @Column(name = "vocation_level_three")
    private String vocationLevelThree;
    /** 国民经济行业分类-门类 */
    @Column(name = "industry_type")
    private String industryType;
}
