/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.intf.EntityInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.EntityInfoBatchMapper;
import com.hsjry.core.limit.center.dal.dao.query.EntityInfoBatchQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityInfoDo;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/14 10:28
 */
@Repository
public class EntityInfoBatchDaoImpl extends AbstractBaseDaoImpl<LcEntityInfoDo, EntityInfoBatchMapper>
    implements EntityInfoBatchDao {
    @Override
    public List<LcEntityInfoDo> selectShardList(EntityInfoBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectShardList(query);
    }

    @Override
    public LcEntityInfoDo selectFirstOne(EntityInfoBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectFirstOne(query);
    }

    @Override
    public Integer selectCountByCurrentGroup(EntityInfoBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectCountByCurrentGroup(query);
    }

    @Override
    public int updateExchangeRateVersion(List<String> entityIdList, Date updateTime, String targetCustFlag,
        String targetAmtFlag, String sourceCustFlag, String sourceAmtFlag) {
        return getMapper().updateExchangeRateVersion(AppParamUtil.getTenantId(), entityIdList, updateTime,
            targetCustFlag, targetAmtFlag, sourceCustFlag, sourceAmtFlag);
    }
}
