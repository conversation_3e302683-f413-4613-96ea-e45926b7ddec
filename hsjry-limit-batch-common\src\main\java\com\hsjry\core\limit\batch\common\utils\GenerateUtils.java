package com.hsjry.core.limit.batch.common.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;

import com.hsjry.base.common.model.enums.limit.EnumLmtTplNode;
import com.hsjry.core.limit.batch.common.enums.EnumTableColumn;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.sequence.SequenceTool;

public class GenerateUtils {
    // 原子计数器，用于确保同一毫秒内的唯一性
    private static final AtomicInteger counter = new AtomicInteger(0);

    // 时间格式化器
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");

    /**
     * 生成唯一额度编号
     */
    public static String custLimitId(EnumLmtTplNode enumLmtTplNode) {
        // 额度编号生成规则调整,长度调整为16位
        // return GenerateUtils.getByNodeTableColumn(enumLmtTplNode, EnumTableColumn.LCLI_CLI.getTableName(),
        //     EnumTableColumn.LCLI_CLI.getColumnName());
        String DATE_FORMAT_yyMMdd = "yyMMdd";
        String fullPrefix = DateUtil.getDate(BusinessDateUtil.getDate(), DATE_FORMAT_yyMMdd);
        String sequence = SequenceTool.nextId();
        return fullPrefix + sequence;
    }

    /**
     * 生成唯一实例编号
     */
    public static String instId(EnumLmtTplNode enumLmtTplNode) {
        return GenerateUtils.getByNodeTableColumn(enumLmtTplNode, EnumTableColumn.LCLI_II.getTableName(),
            EnumTableColumn.LCLI_II.getColumnName());
    }

    /**
     * 生成关联编号
     */
    public static String relationId(EnumLmtTplNode enumLmtTplNode) {
        return GenerateUtils.getByNodeTableColumn(enumLmtTplNode, EnumTableColumn.LCLR_LRI.getTableName(),
            EnumTableColumn.LCLR_LRI.getColumnName());
    }

    /**
     * 生成全局流水号
     */
    public static String globalSerialNo(EnumLmtTplNode enumLmtTplNode) {
        return GenerateUtils.getByNodeTableColumn(enumLmtTplNode, EnumTableColumn.LCLOS_GSN.getTableName(),
            EnumTableColumn.LCLOS_GSN.getColumnName());
    }

    /**
     * 生成业务流水号
     */
    public static String serialNo(EnumLmtTplNode enumLmtTplNode) {
        return GenerateUtils.getByNodeTableColumn(enumLmtTplNode, EnumTableColumn.LCLOS_SN.getTableName(),
            EnumTableColumn.LCLOS_SN.getColumnName());
    }

    /**
     * 生成前置业务流水
     */
    public static String inboundSerialNo(EnumLmtTplNode enumLmtTplNode) {
        return GenerateUtils.getByNodeTableColumn(enumLmtTplNode, EnumTableColumn.LCLOS_ISN.getTableName(),
            EnumTableColumn.LCLOS_ISN.getColumnName());
    }

    /**
     * 生成操作流水编号
     */
    public static String closSerialNo(EnumLmtTplNode enumLmtTplNode) {
        return GenerateUtils.getByNodeTableColumn(enumLmtTplNode, EnumTableColumn.LCLOS_CSN.getTableName(),
            EnumTableColumn.LCLOS_CSN.getColumnName());
    }

    public static String getByNodeTableColumn(EnumLmtTplNode enumLmtTplNode, String tableName, String columnName) {
        String templateNodeId = enumLmtTplNode.getTemplateNodeId();

        // 获取表名的首字母并大写
        String tableFirstLetter = Character.toUpperCase(tableName.charAt(0)) + "";

        // 获取表字段名的首字母并大写
        String columnFirstLetter = Character.toUpperCase(columnName.charAt(0)) + "";

        // 获取当前时间,精确到毫秒
        String currentTime = LocalDateTime.now().format(FORMATTER);

        // 生成序号，确保同一毫秒内的唯一性（支持每秒1万笔）
        int current = counter.incrementAndGet();
        if (current > 9999) {
            counter.compareAndSet(current, 1);
            current = 1;
        }

        // 生成6位随机数
        String randomSuffix = String.format("%06d", ThreadLocalRandom.current().nextInt(1000000));

        // 将上述都拼接起来
        return String.format("%s%s%s%s%04d%s", templateNodeId, tableFirstLetter, columnFirstLetter, currentTime,
            current, randomSuffix);
    }
}
