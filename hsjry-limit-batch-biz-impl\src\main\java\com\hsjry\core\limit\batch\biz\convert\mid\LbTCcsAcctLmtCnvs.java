package com.hsjry.core.limit.batch.biz.convert.mid;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.batch.common.dto.file.LbTCcsAcctLmtDto;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCcsAcctDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTCcsAcctLmtDo;

/**
 * 信用卡额度信息转换器
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Mapper(componentModel = "spring")
public interface LbTCcsAcctLmtCnvs {

    LbTCcsAcctLmtCnvs INSTANCE = Mappers.getMapper(LbTCcsAcctLmtCnvs.class);

    /**
     * 信用卡账户信息转换为信用卡额度信息，包含复杂的可用余额计算逻辑
     *
     * @param model 信用卡账户信息
     * @return 信用卡额度信息
     */
    @Mappings({@Mapping(target = "xaccount", source = "xaccount"),//
        @Mapping(target = "bank", source = "bank"),//
        @Mapping(target = "credLimit", source = "credLimit"), //
        @Mapping(target = "balCmpint", source = "balCmpint"),//
        @Mapping(target = "balFree", source = "balFree"),//
        @Mapping(target = "balInt", source = "balInt"),//
        @Mapping(target = "balNoint", source = "balNoint"), //
        @Mapping(target = "balOrint", source = "balOrint"),//
        @Mapping(target = "mpRemPpl", source = "mpRemPpl"), //
        @Mapping(target = "stmBalfre", source = "stmBalfre"),//
        @Mapping(target = "stmBalint", source = "stmBalint"), //
        @Mapping(target = "stmBalori", source = "stmBalori"),//
        @Mapping(target = "stmNoint", source = "stmNoint"), //
        @Mapping(target = "balMp", source = "balMp"),//
        @Mapping(target = "stmBalmp", source = "stmBalmp"), //
        @Mapping(target = "accName", source = "accName1"),//
        @Mapping(target = "custrNbr", source = "custrNbr"), //
        @Mapping(target = "custNo", ignore = true), // 需要通过关联查询获取
        @Mapping(target = "avlBal", ignore = true)   // 需要通过复杂计算获取
    })
    LbTCcsAcctLmtDo ccsAcctDo2LmtDo(LbSCcsAcctDo model);

    LbTCcsAcctLmtDo dtoToDo(LbTCcsAcctLmtDto dto);

    LbTCcsAcctLmtDto do2Dto(LbTCcsAcctLmtDo model);
} 