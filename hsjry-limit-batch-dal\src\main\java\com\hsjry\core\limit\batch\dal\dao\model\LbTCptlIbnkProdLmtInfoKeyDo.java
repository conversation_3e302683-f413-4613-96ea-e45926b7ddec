package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 资金系统-中间表-同业客户产品层额度信息主键
 *
 * <AUTHOR>
 * @date 2025-07-30 02:03:23
 */
@Table(name = "lb_t_cptl_ibnk_prod_lmt_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbTCptlIbnkProdLmtInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1950376645714182145L;
    /** 客户编号 */
    @Id
    @Column(name = "cust_no")
    private String custNo;
    /** 额度编号 */
    @Id
    @Column(name = "cust_limit_id")
    private String custLimitId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}