package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreAtxzhDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 核心系统-落地表-贴现账户主文件mapper
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
public interface LbSCoreAtxzhMapper extends CommonMapper<LbSCoreAtxzhDo> {
    // 在 LbSCoreAtxzhMapper 接口中添加以下方法：

    /**
     * 批量插入贴现账户主文件信息
     *
     * @param lbSCoreAtxzhList 批量数据
     * @return int
     */
    int insertList(@Param("list") List<LbSCoreAtxzhDo> lbSCoreAtxzhList);

    /**
     * 清空贴现账户主文件表所有数据
     *
     * @return int
     */
    int deleteAll();
}