package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcGrpLmtViewDo;
import com.hsjry.core.limit.center.dal.dao.query.LcGrpLmtViewQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 集团客户额度视图数据库操作接口
 * 
 * <AUTHOR>
 * @date 2025-07-07 12:58:26
 */
public interface LcGrpLmtViewDao extends IBaseDao<LcGrpLmtViewDo> {
    /**
     * 分页查询集团客户额度视图
     *
     * @param lcGrpLmtViewQuery 条件
     * @return PageInfo<LcGrpLmtViewDo>
     */
    PageInfo<LcGrpLmtViewDo> selectPage(LcGrpLmtViewQuery lcGrpLmtViewQuery, PageParam pageParam);

    /**
     * 根据key查询集团客户额度视图
     *
     * @param grpLmtViewId
     * @return
     */
    LcGrpLmtViewDo selectByKey(String grpLmtViewId);
    /**
     * 根据key删除集团客户额度视图
     *
     * @param grpLmtViewId
     * @return
     */
    int deleteByKey(String grpLmtViewId);

    /**
     * 查询集团客户额度视图信息
     *
     * @param lcGrpLmtViewQuery 条件
     * @return List<LcGrpLmtViewDo>
     */
    List<LcGrpLmtViewDo> selectByExample(LcGrpLmtViewQuery lcGrpLmtViewQuery);

    /**
     * 新增集团客户额度视图信息
     *
     * @param lcGrpLmtView 条件
     * @return int>
     */
    int insertBySelective(LcGrpLmtViewDo lcGrpLmtView);

    /**
     * 修改集团客户额度视图信息
     *
     * @param lcGrpLmtView
     * @return
     */
    int updateBySelective(LcGrpLmtViewDo lcGrpLmtView);
    /**
     * 修改集团客户额度视图信息
     *
     * @param lcGrpLmtView
     * @param lcGrpLmtViewQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcGrpLmtViewDo lcGrpLmtView, LcGrpLmtViewQuery lcGrpLmtViewQuery);

    /**
     * 1.处理客户信息,使用MERGE语句实现存在则更新,不存在则插入
     */
    int mergeCustomerInfo();

    /**
     * 2.更新集团综合授信额度
     */
    int mergeGroupComprehensiveCreditLimit();

    /**
     * 3.更新集团一般授信额度/集团一般授信可用额度
     */
    int mergeGroupCommonCreditLimit();

    /**
     * 4.更新集团纯低风险额度/集团纯低风险可用额度
     */
    int mergeGroupWholeLowRiskLimit();

    /**
     * 5.更新集团非授信额度
     */
    int mergeGroupNoCreditLimit();

    /**
     * 6.更新集团总担保额度
     */
    int mergeGroupGuaranteeLimit();

    /**
     * 7.更新集团总合作方额度/合作方可用额度
     */
    int mergeGroupCoPartnerLimit();
}
