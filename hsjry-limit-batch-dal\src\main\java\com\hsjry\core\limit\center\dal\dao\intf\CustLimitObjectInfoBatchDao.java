/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.query.CustLimitObjectInfoQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/17 9:54
 */
public interface CustLimitObjectInfoBatchDao {
    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitObjectInfoDo> selectShardList(CustLimitObjectInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitObjectInfoDo selectFirstOne(CustLimitObjectInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(CustLimitObjectInfoQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitObjectInfoDo> selectObjectShardList(CustLimitObjectInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitObjectInfoDo selectObjectFirstOne(CustLimitObjectInfoQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitObjectInfoDo> selectExpireShardList(CustLimitObjectInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitObjectInfoDo selectExpireFirstOne(CustLimitObjectInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectObjectCountByCurrentGroup(CustLimitObjectInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectExpireCountByCurrentGroup(CustLimitObjectInfoQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitObjectInfoDo> selectNotUsedShardList(CustLimitObjectInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitObjectInfoDo selectNotUsedFirstOne(CustLimitObjectInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNotUsedCountByCurrentGroup(CustLimitObjectInfoQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcCustLimitObjectInfoDo> selectNodeShardList(CustLimitObjectInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcCustLimitObjectInfoDo selectNodeFirstOne(CustLimitObjectInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNodeCountByCurrentGroup(CustLimitObjectInfoQuery query);

    List<LcCustLimitObjectInfoDo> selectExpireLimitInfoByObjectId(CustLimitObjectInfoQuery query);
}
