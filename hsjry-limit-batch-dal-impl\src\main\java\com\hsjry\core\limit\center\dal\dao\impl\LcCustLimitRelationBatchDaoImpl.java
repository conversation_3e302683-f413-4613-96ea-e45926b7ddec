package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;

import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitRelationBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.LcCustLimitRelationBatchMapper;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationExample;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationKeyDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitRelationQuery;

import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度实例关联数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2022-12-13 03:09:49
 */
@Repository
public class LcCustLimitRelationBatchDaoImpl extends AbstractBaseDaoImpl<LcCustLimitRelationDo, LcCustLimitRelationBatchMapper>
    implements LcCustLimitRelationBatchDao {
    /**
     * 分页查询
     *
     * @param lcCustLimitRelation 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LcCustLimitRelationDo> selectPage(LcCustLimitRelationQuery lcCustLimitRelation,
        PageParam pageParam) {
        LcCustLimitRelationExample example = buildExample(lcCustLimitRelation);
        return PageHelper.<LcCustLimitRelationDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度实例关联
     *
     * @param limitRelationId
     * @return
     */
    @Override
    public LcCustLimitRelationDo selectByKey(String limitRelationId) {
        LcCustLimitRelationKeyDo lcCustLimitRelationKeyDo = new LcCustLimitRelationKeyDo();
        lcCustLimitRelationKeyDo.setLimitRelationId(limitRelationId);
        lcCustLimitRelationKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lcCustLimitRelationKeyDo);
    }

    /**
     * 根据key删除额度实例关联
     *
     * @param limitRelationId
     * @return
     */
    @Override
    public int deleteByKey(String limitRelationId) {
        LcCustLimitRelationKeyDo lcCustLimitRelationKeyDo = new LcCustLimitRelationKeyDo();
        lcCustLimitRelationKeyDo.setLimitRelationId(limitRelationId);
        lcCustLimitRelationKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lcCustLimitRelationKeyDo);
    }

    /**
     * 查询额度实例关联信息
     *
     * @param lcCustLimitRelation 条件
     * @return List<LcCustLimitRelationDo>
     */
    @Override
    public List<LcCustLimitRelationDo> selectByExample(LcCustLimitRelationQuery lcCustLimitRelation) {
        return getMapper().selectByExample(buildExample(lcCustLimitRelation));
    }

    /**
     * 新增额度实例关联信息
     *
     * @param lcCustLimitRelation 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LcCustLimitRelationDo lcCustLimitRelation) {
        if (lcCustLimitRelation == null) {
            return -1;
        }

        lcCustLimitRelation.setCreateTime(BusinessDateUtil.getDate());
        lcCustLimitRelation.setUpdateTime(BusinessDateUtil.getDate());
        lcCustLimitRelation.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lcCustLimitRelation);
    }

    /**
     * 修改额度实例关联信息
     *
     * @param lcCustLimitRelation
     * @return
     */
    @Override
    public int updateBySelective(LcCustLimitRelationDo lcCustLimitRelation) {
        if (lcCustLimitRelation == null) {
            return -1;
        }
        lcCustLimitRelation.setUpdateTime(BusinessDateUtil.getDate());
        lcCustLimitRelation.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lcCustLimitRelation);
    }

    @Override
    public int updateBySelectiveByExample(LcCustLimitRelationDo lcCustLimitRelation,
        LcCustLimitRelationQuery lcCustLimitRelationQuery) {
        lcCustLimitRelation.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lcCustLimitRelation, buildExample(lcCustLimitRelationQuery));
    }

    @Override
    public int deleteByExample(LcCustLimitRelationQuery lcCustLimitRelationQuery) {
        return getMapper().deleteByExample(buildExample(lcCustLimitRelationQuery));
    }

    /**
     * 构建额度实例关联Example信息
     *
     * @param lcCustLimitRelation
     * @return
     */
    public LcCustLimitRelationExample buildExample(LcCustLimitRelationQuery lcCustLimitRelation) {
        LcCustLimitRelationExample example = new LcCustLimitRelationExample();
        LcCustLimitRelationExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lcCustLimitRelation != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lcCustLimitRelation.getCurrentNodeLimitId())) {
                criteria.andCurrentNodeLimitIdEqualTo(lcCustLimitRelation.getCurrentNodeLimitId());
            }
            if (StringUtil.isNotEmpty(lcCustLimitRelation.getLimitRelationId())) {
                criteria.andLimitRelationIdEqualTo(lcCustLimitRelation.getLimitRelationId());
            }
            if (StringUtil.isNotEmpty(lcCustLimitRelation.getLimitRelationType())) {
                criteria.andLimitRelationTypeEqualTo(lcCustLimitRelation.getLimitRelationType());
            }
            if (StringUtil.isNotEmpty(lcCustLimitRelation.getParentNodeLimitId())) {
                criteria.andParentNodeLimitIdEqualTo(lcCustLimitRelation.getParentNodeLimitId());
            }
        }
        buildExampleExt(lcCustLimitRelation, criteria);
        return example;
    }

    /**
     * 构建额度实例关联ExampleExt方法
     *
     * @param lcCustLimitRelation
     * @return
     */
    public void buildExampleExt(LcCustLimitRelationQuery lcCustLimitRelation,
        LcCustLimitRelationExample.Criteria criteria) {
        if (CollectionUtil.isNotEmpty(lcCustLimitRelation.getParentNodeLimitIdList())) {
            criteria.andParentNodeLimitIdIn(lcCustLimitRelation.getParentNodeLimitIdList());
        }
        if (CollectionUtil.isNotEmpty(lcCustLimitRelation.getCurrentNodeLimitIdList())) {
            criteria.andCurrentNodeLimitIdIn(lcCustLimitRelation.getCurrentNodeLimitIdList());
        }
        //自定义实现
    }

}
