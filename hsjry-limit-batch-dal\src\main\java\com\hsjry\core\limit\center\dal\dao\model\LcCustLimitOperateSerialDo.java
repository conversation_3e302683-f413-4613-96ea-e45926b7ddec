package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度操作流水Do
 *
 * <AUTHOR>
 * @date 2023-10-23 11:54:47
 */
@Table(name = "lc_cust_limit_operate_serial")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitOperateSerialDo extends LcCustLimitOperateSerialKeyDo implements Serializable {
    private static final long serialVersionUID = 1716422915035168768L;
    /** 操作金额编号 */
    @Column(name = "operate_amount_id")
    private String operateAmountId;
    /** 操作路径 */
    @Column(name = "operate_path")
    private String operatePath;
    /** 合同额度汇率重算标记 */
    @Column(name = "contract_recal_flag")
    private String contractRecalFlag;
    /** 汇率版本 */
    @Column(name = "exchange_rate_version")
    private Integer exchangeRateVersion;
    /** 实体编号 */
    @Column(name = "entity_id")
    private String entityId;
    /** 备注 */
    @Column(name = "remark")
    private String remark;
    /** 失败原因 */
    @Column(name = "fail_reason")
    private String failReason;
    /** 操作方向 */
    @Column(name = "operate_direction")
    private String operateDirection;
    /** 额度编号 */
    @Column(name = "cust_limit_id")
    private String custLimitId;
    /** 前置业务关联流水 */
    @Column(name = "last_inbound_serial_no")
    private String lastInboundSerialNo;
    /** 关联编号 */
    @Column(name = "relation_id")
    private String relationId;
    /** 操作低风险币种 */
    @Column(name = "operate_low_risk_currency")
    private String operateLowRiskCurrency;
    /** 操作低风险金额编号 */
    @Column(name = "operate_low_risk_amt_id")
    private String operateLowRiskAmtId;
    /** 操作低风险金额 */
    @Column(name = "operate_low_risk_amount")
    private java.math.BigDecimal operateLowRiskAmount;
    /** 操作金额币种 */
    @Column(name = "operate_amount_currency")
    private String operateAmountCurrency;
    /** 全局流水号 */
    @Column(name = "global_serial_no")
    private String globalSerialNo;
    /** 操作金额 */
    @Column(name = "operate_amount")
    private java.math.BigDecimal operateAmount;
    /** 操作类型;EnumCustLimitOperateType */
    @Column(name = "operate_type")
    private String operateType;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 状态 */
    @Column(name = "status")
    private String status;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 前置业务时间 */
    @Column(name = "inbound_serial_datetime")
    private java.util.Date inboundSerialDatetime;
    /** 前置业务流水 */
    @Column(name = "inbound_serial_no")
    private String inboundSerialNo;
    /** 业务时间 */
    @Column(name = "biz_datetime")
    private java.util.Date bizDatetime;
    /** 渠道号 */
    @Column(name = "channel_no")
    private String channelNo;
    /** 业务流水号 */
    @Column(name = "serial_no")
    private String serialNo;

    /** 客户编号 */
    @Column(name = "cust_no")
    private String custNo;
}
