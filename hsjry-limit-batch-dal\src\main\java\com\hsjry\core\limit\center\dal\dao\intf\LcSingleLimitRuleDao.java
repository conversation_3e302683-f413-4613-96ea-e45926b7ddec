package com.hsjry.core.limit.center.dal.dao.intf;

import com.hsjry.core.limit.center.dal.dao.model.LcSingleLimitRuleDo;
import com.hsjry.core.limit.center.dal.dao.query.LcSingleLimitRuleQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 单一限额规则数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
public interface LcSingleLimitRuleDao extends IBaseDao<LcSingleLimitRuleDo> {
    /**
     * 分页查询单一限额规则
     *
     * @param lcSingleLimitRuleQuery 条件
     * @return PageInfo<LcSingleLimitRuleDo>
     */
    PageInfo<LcSingleLimitRuleDo> selectPage(LcSingleLimitRuleQuery lcSingleLimitRuleQuery, PageParam pageParam);

    /**
     * 根据key查询单一限额规则
     *
     * @param ruleId
     * @return
     */
    LcSingleLimitRuleDo selectByKey(String ruleId);

    /**
     * 根据key删除单一限额规则
     *
     * @param ruleId
     * @return
     */
    int deleteByKey(String ruleId);

    /**
     * 查询单一限额规则信息
     *
     * @param lcSingleLimitRuleQuery 条件
     * @return List<LcSingleLimitRuleDo>
     */
    List<LcSingleLimitRuleDo> selectByExample(LcSingleLimitRuleQuery lcSingleLimitRuleQuery);

    /**
     * 新增单一限额规则信息
     *
     * @param lcSingleLimitRule 条件
     * @return int>
     */
    int insertBySelective(LcSingleLimitRuleDo lcSingleLimitRule);

    /**
     * 修改单一限额规则信息
     *
     * @param lcSingleLimitRule
     * @return
     */
    int updateBySelective(LcSingleLimitRuleDo lcSingleLimitRule);

    /**
     * 修改单一限额规则信息
     *
     * @param lcSingleLimitRule
     * @param lcSingleLimitRuleQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcSingleLimitRuleDo lcSingleLimitRule,
        LcSingleLimitRuleQuery lcSingleLimitRuleQuery);
}
