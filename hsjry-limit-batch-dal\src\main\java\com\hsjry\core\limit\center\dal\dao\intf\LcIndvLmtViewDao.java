package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcIndvLmtViewDo;
import com.hsjry.core.limit.center.dal.dao.query.LcIndvLmtViewQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 个人客户额度视图数据库操作接口
 * 
 * <AUTHOR>
 * @date 2025-07-07 12:58:26
 */
public interface LcIndvLmtViewDao extends IBaseDao<LcIndvLmtViewDo> {
    /**
     * 分页查询个人客户额度视图
     *
     * @param lcIndvLmtViewQuery 条件
     * @return PageInfo<LcIndvLmtViewDo>
     */
    PageInfo<LcIndvLmtViewDo> selectPage(LcIndvLmtViewQuery lcIndvLmtViewQuery, PageParam pageParam);

    /**
     * 根据key查询个人客户额度视图
     *
     * @param indvLmtViewId
     * @return
     */
    LcIndvLmtViewDo selectByKey(String indvLmtViewId);
    /**
     * 根据key删除个人客户额度视图
     *
     * @param indvLmtViewId
     * @return
     */
    int deleteByKey(String indvLmtViewId);

    /**
     * 查询个人客户额度视图信息
     *
     * @param lcIndvLmtViewQuery 条件
     * @return List<LcIndvLmtViewDo>
     */
    List<LcIndvLmtViewDo> selectByExample(LcIndvLmtViewQuery lcIndvLmtViewQuery);

    /**
     * 新增个人客户额度视图信息
     *
     * @param lcIndvLmtView 条件
     * @return int>
     */
    int insertBySelective(LcIndvLmtViewDo lcIndvLmtView);

    /**
     * 修改个人客户额度视图信息
     *
     * @param lcIndvLmtView
     * @return
     */
    int updateBySelective(LcIndvLmtViewDo lcIndvLmtView);
    /**
     * 修改个人客户额度视图信息
     *
     * @param lcIndvLmtView
     * @param lcIndvLmtViewQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcIndvLmtViewDo lcIndvLmtView, LcIndvLmtViewQuery lcIndvLmtViewQuery);

    /**
     * 1.处理客户信息,使用MERGE语句实现存在则更新,不存在则插入
     */
    int mergeCustomerInfo();

    /**
     * 2.更新经营贷额度/经营贷可用额度
     */
    int mergeOpLoanLimit();

    /**
     * 3.更新经营贷产品总额度/经营贷产品可用额度
     */
    int mergeOpLoanProdLimit();

    /**
     * 4.更新经营按揭产品总额度/经营按揭产品可用额度
     */
    int mergeOpMrtgProdLimit();

    /**
     * 5.更新消费贷额度/消费贷可用额度
     */
    int mergeConsmLoanLimit();

    /**
     * 6.更新消费贷产品总额度/消费贷产品可用额度
     */
    int mergeConsmLoanProdLimit();

    /**
     * 7.更新消费按揭产品总额度/消费按揭产品可用额度
     */
    int mergeConsmMrtgProdLimit();

    /**
     * 8.更新信用卡额度/信用卡可用额度
     */
    int mergeCrdCdLimit();
}
