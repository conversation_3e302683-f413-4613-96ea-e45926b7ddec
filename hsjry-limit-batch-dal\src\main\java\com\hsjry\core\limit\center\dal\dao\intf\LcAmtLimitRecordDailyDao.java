package com.hsjry.core.limit.center.dal.dao.intf;

import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordDailyDo;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitRecordDailyQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 限额记录日报数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-03-29 11:30:16
 */
public interface LcAmtLimitRecordDailyDao extends IBaseDao<LcAmtLimitRecordDailyDo> {
    /**
     * 分页查询限额记录日报
     *
     * @param lcAmtLimitRecordDailyQuery 条件
     * @return PageInfo<LcAmtLimitRecordDailyDo>
     */
    PageInfo<LcAmtLimitRecordDailyDo> selectPage(LcAmtLimitRecordDailyQuery lcAmtLimitRecordDailyQuery,
        PageParam pageParam);

    /**
     * 根据key查询限额记录日报
     *
     * @param recordDate
     * @param recordId
     * @return
     */
    LcAmtLimitRecordDailyDo selectByKey(Integer recordDate, String recordId);

    /**
     * 根据key删除限额记录日报
     *
     * @param recordDate
     * @param recordId
     * @return
     */
    int deleteByKey(Integer recordDate, String recordId);

    /**
     * 查询限额记录日报信息
     *
     * @param lcAmtLimitRecordDailyQuery 条件
     * @return List<LcAmtLimitRecordDailyDo>
     */
    List<LcAmtLimitRecordDailyDo> selectByExample(LcAmtLimitRecordDailyQuery lcAmtLimitRecordDailyQuery);

    /**
     * 新增限额记录日报信息
     *
     * @param lcAmtLimitRecordDaily 条件
     * @return int>
     */
    int insertBySelective(LcAmtLimitRecordDailyDo lcAmtLimitRecordDaily);

    /**
     * 修改限额记录日报信息
     *
     * @param lcAmtLimitRecordDaily
     * @return
     */
    int updateBySelective(LcAmtLimitRecordDailyDo lcAmtLimitRecordDaily);

    /**
     * 修改限额记录日报信息
     *
     * @param lcAmtLimitRecordDaily
     * @param lcAmtLimitRecordDailyQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcAmtLimitRecordDailyDo lcAmtLimitRecordDaily,
        LcAmtLimitRecordDailyQuery lcAmtLimitRecordDailyQuery);
}
