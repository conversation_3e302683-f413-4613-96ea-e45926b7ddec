package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度体系模板主键
 *
 * <AUTHOR>
 * @date 2022-12-07 06:17:16
 */
@Table(name = "lc_cust_limit_template")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcCustLimitTemplateKeyDo implements Serializable {

    private static final long serialVersionUID = 1600373860908335104L;
    /** 额度体系模板编号 */
    @Id
    @Column(name = "limit_template_id")
    private String limitTemplateId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}