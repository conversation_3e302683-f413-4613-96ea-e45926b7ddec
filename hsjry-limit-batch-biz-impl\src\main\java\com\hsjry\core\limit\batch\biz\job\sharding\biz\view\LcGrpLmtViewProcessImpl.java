package com.hsjry.core.limit.batch.biz.job.sharding.biz.view;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.center.dal.dao.intf.LcGrpLmtViewDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitObjectInfoQuery;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 额度中心-集团客户额度视图处理
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Slf4j
@Service("lcGrpLmtViewProcessImpl")
@RequiredArgsConstructor
public class LcGrpLmtViewProcessImpl extends AbstractShardingPrepareBiz<CustLimitObjectInfoQuery>
    implements JobCoreBusiness<LcCustLimitObjectInfoDo> {

    private final LcGrpLmtViewDao lcGrpLmtViewDao;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.LC_GRP_LMT_VIEW_PROCESS;
    }

    @Override
    public Integer selectCountByCurrentGroupFromDb(CustLimitObjectInfoQuery query) {
        return 0;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "====================== 分片逻辑 start ================================================");
        List<JobShared> jobSharedList = new ArrayList<>();
        log.info(prefixLog + "====================== 分片逻辑 end ================================================");
        log.info(prefixLog + "集团客户信息处理分片任务生成完成,共{}个分片",
            CollectionUtil.isEmpty(jobSharedList) ? 0 : jobSharedList.size());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LcCustLimitObjectInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {

        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始查询分片数据,分片号:{}", jobShared.getBatchNum());

        ShardingResult<LcCustLimitObjectInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }
        // 创建查询条件
        CustLimitObjectInfoQuery query = CustLimitObjectInfoQuery.builder().tenantId(AppParamUtil.getTenantId())//
            .offset(jobShared.getOffset()).limit(jobShared.getLimit()).build();
        log.info(prefixLog + "=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");

        // 查询分片数据
        List<LcCustLimitObjectInfoDo> dataList = Lists.newArrayList();
        shardingResult.setShardingResultList(dataList);

        log.info("分片数据查询完成,分片号:{},数据量:{}", jobShared.getBatchNum(),
            CollectionUtil.isEmpty(dataList) ? 0 : dataList.size());

        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcCustLimitObjectInfoDo> shardingResult) {
        List<LcCustLimitObjectInfoDo> shardingDataList = shardingResult.getShardingResultList();
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        Integer dataSize = shardingResult.getShardingResultList().size();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "=========分片执行开始:[{}]数量为[{}]===========", batchNum, dataSize);

        try {
            LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();

            // 1.处理客户信息,使用MERGE语句实现存在则更新,不存在则插入
            log.info(prefixLog + "步骤1:开始处理客户信息");
            int customerInfoCount = lcGrpLmtViewDao.mergeCustomerInfo();
            log.info(prefixLog + "步骤1:客户信息处理完成,影响记录数:[{}]", customerInfoCount);

            // 2.更新集团综合授信额度
            log.info(prefixLog + "步骤2:开始更新集团综合授信额度");
            int comprehensiveCreditLimitCount = lcGrpLmtViewDao.mergeGroupComprehensiveCreditLimit();
            log.info(prefixLog + "步骤2:集团综合授信额度更新完成,影响记录数:[{}]", comprehensiveCreditLimitCount);

            // 3.更新集团一般授信额度/集团一般授信可用额度
            log.info(prefixLog + "步骤3:开始更新集团一般授信额度/集团一般授信可用额度");
            int commonCreditLimitCount = lcGrpLmtViewDao.mergeGroupCommonCreditLimit();
            log.info(prefixLog + "步骤3:集团一般授信额度更新完成,影响记录数:[{}]", commonCreditLimitCount);

            // 4.更新集团纯低风险额度/集团纯低风险可用额度
            log.info(prefixLog + "步骤4:开始更新集团纯低风险额度/集团纯低风险可用额度");
            int wholeLowRiskLimitCount = lcGrpLmtViewDao.mergeGroupWholeLowRiskLimit();
            log.info(prefixLog + "步骤4:集团纯低风险额度更新完成,影响记录数:[{}]", wholeLowRiskLimitCount);

            // 5.更新集团非授信额度
            log.info(prefixLog + "步骤5:开始更新集团非授信额度");
            int noCreditLimitCount = lcGrpLmtViewDao.mergeGroupNoCreditLimit();
            log.info(prefixLog + "步骤5:集团非授信额度更新完成,影响记录数:[{}]", noCreditLimitCount);

            // 6.更新集团总担保额度
            log.info(prefixLog + "步骤6:开始更新集团总担保额度");
            int guaranteeLimitCount = lcGrpLmtViewDao.mergeGroupGuaranteeLimit();
            log.info(prefixLog + "步骤6:集团总担保额度更新完成,影响记录数:[{}]", guaranteeLimitCount);

            // 7.更新集团总合作方额度/合作方可用额度
            log.info(prefixLog + "步骤7:开始更新集团总合作方额度/合作方可用额度");
            int coPartnerLimitCount = lcGrpLmtViewDao.mergeGroupCoPartnerLimit();
            log.info(prefixLog + "步骤7:集团总合作方额度更新完成,影响记录数:[{}]", coPartnerLimitCount);

            // 更新分片流水成功
            normalUpdateSliceSerial(0, sliceBatchSerialDo);

        } catch (Exception e) {
            log.error(prefixLog + "分片执行异常:[{}]", batchNum, e);
            throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                EnumBatchJobError.SYSTEM_ERR.getDescription());
        }

        log.info(prefixLog + "=========分片执行结束:[{}]===========", batchNum);
    }
} 