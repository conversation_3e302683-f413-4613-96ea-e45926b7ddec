package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblIbnkProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTElcblIbnkProdLmtInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-中间表-同业客户产品层额度信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-28 11:16:05
 */
public interface LbTElcblIbnkProdLmtInfoDao extends IBaseDao<LbTElcblIbnkProdLmtInfoDo> {
    /**
     * 分页查询电票系统-中间表-同业客户产品层额度信息
     *
     * @param lbTElcblIbnkProdLmtInfoQuery 条件
     * @return PageInfo<LbTElcblIbnkProdLmtInfoDo>
     */
    PageInfo<LbTElcblIbnkProdLmtInfoDo> selectPage(LbTElcblIbnkProdLmtInfoQuery lbTElcblIbnkProdLmtInfoQuery,
        PageParam pageParam);

    /**
     * 根据key查询电票系统-中间表-同业客户产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTElcblIbnkProdLmtInfoDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除电票系统-中间表-同业客户产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询电票系统-中间表-同业客户产品层额度信息信息
     *
     * @param lbTElcblIbnkProdLmtInfoQuery 条件
     * @return List<LbTElcblIbnkProdLmtInfoDo>
     */
    List<LbTElcblIbnkProdLmtInfoDo> selectByExample(LbTElcblIbnkProdLmtInfoQuery lbTElcblIbnkProdLmtInfoQuery);

    /**
     * 新增电票系统-中间表-同业客户产品层额度信息信息
     *
     * @param lbTElcblIbnkProdLmtInfo 条件
     * @return int>
     */
    int insertBySelective(LbTElcblIbnkProdLmtInfoDo lbTElcblIbnkProdLmtInfo);

    /**
     * 修改电票系统-中间表-同业客户产品层额度信息信息
     *
     * @param lbTElcblIbnkProdLmtInfo
     * @return
     */
    int updateBySelective(LbTElcblIbnkProdLmtInfoDo lbTElcblIbnkProdLmtInfo);

    /**
     * 修改电票系统-中间表-同业客户产品层额度信息信息
     *
     * @param lbTElcblIbnkProdLmtInfo
     * @param lbTElcblIbnkProdLmtInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTElcblIbnkProdLmtInfoDo lbTElcblIbnkProdLmtInfo,
        LbTElcblIbnkProdLmtInfoQuery lbTElcblIbnkProdLmtInfoQuery);

    /**
     * 将电票同业客户额度同步数据插入到产品层额度信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_elcbl_ibnk_prod_lmt_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    int insertElcblIbnkProdLmtInfo(List<String> templateNodeIdList, List<String> custLimitIdList);

    /**
     * 更新额度实例金额信息中的实占额度
     * 将电票系统-中间表-同业客户产品层额度信息中的实占额度同步到额度实例金额信息表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    int updateRealOccupyAmount(List<String> custLimitIdList);
}
