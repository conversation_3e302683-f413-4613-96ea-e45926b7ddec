<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.center.dal.dao.mapper.EntityInfoBatchMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.center.dal.dao.model.LcEntityInfoDo">
        <result property="productId" column="product_id" jdbcType="VARCHAR"/> <!-- 产品编号 -->
        <result property="preGrantLowRisk" column="pre_grant_low_risk" jdbcType="DECIMAL"/> <!-- 预发放低风险 -->
        <result property="preGrantLowRiskAmtId" column="pre_grant_low_risk_amt_id" jdbcType="VARCHAR"/> <!-- 预发放低风险金额编号 -->
        <result property="lowRisk" column="low_risk" jdbcType="DECIMAL"/> <!-- 发放低风险 -->
        <result property="lowRiskAmountId" column="low_risk_amount_id" jdbcType="VARCHAR"/> <!-- 发放低风险金额编号 -->
        <result property="leftLowRisk" column="left_low_risk" jdbcType="DECIMAL"/> <!-- 低风险余额 -->
        <result property="leftLowRiskAmountId" column="left_low_risk_amount_id" jdbcType="VARCHAR"/> <!-- 低风险余额金额编号 -->
        <result property="lowRiskCurrency" column="low_risk_currency" jdbcType="CHAR"/> <!-- 低风险币种 -->
        <result property="amtLimitFlag" column="amt_limit_flag" jdbcType="CHAR"/> <!-- 是否限额管控;EnumBool   Y-是、N-否 -->
        <result property="amountCurrency" column="amount_currency" jdbcType="CHAR"/> <!-- 金额币种 -->
        <result property="custLimitId" column="cust_limit_id" jdbcType="VARCHAR"/> <!-- 额度编号 -->
        <result property="limitObjectId" column="limit_object_id" jdbcType="VARCHAR"/> <!-- 所属对象编号 -->
        <result property="uniqueIdentifier" column="unique_identifier" jdbcType="VARCHAR"/> <!-- 实体唯一标识 -->
        <result property="nextExchangeRateVersion" column="next_exchange_rate_version" jdbcType="INTEGER"/> <!-- 更新汇率版本 -->
        <result property="shortSellingFlag" column="short_selling_flag" jdbcType="CHAR"/> <!-- 卖空标识;EnumBool   Y-是、N-否 -->
        <result property="ibFinancialProdRelId" column="ib_financial_prod_rel_id" jdbcType="VARCHAR"/> <!-- 关联同业金融产品编号 -->
        <result property="limitObjectType" column="limit_object_type" jdbcType="CHAR"/> <!-- 所属对象类型 EnumLimitObjectType:001-客户、002-金融产品 -->
        <result property="tenantId" column="tenant_id" jdbcType="CHAR"/> <!-- 租户号 -->
        <result property="leftAmount" column="left_amount" jdbcType="DECIMAL"/> <!-- 当前余额 -->
        <result property="amount" column="amount" jdbcType="DECIMAL"/> <!-- 发放金额 -->
        <result property="preGrantAmount" column="pre_grant_amount" jdbcType="DECIMAL"/> <!-- 预发放金额 -->
        <result property="grantDateTime" column="grant_date_time" jdbcType="TIMESTAMP"/> <!-- 发放时间 -->
        <result property="amtLimitExRateRecalFlag" column="amt_limit_ex_rate_recal_flag" jdbcType="CHAR"/> <!-- 限额汇率重算标记;EnumBool   Y-是、N-否 -->
        <result property="custLimitExRateRecalFlag" column="cust_limit_ex_rate_recal_flag" jdbcType="CHAR"/> <!-- 额度汇率重选标记;EnumBool   Y-是、N-否 -->
        <result property="exchangeRateVersion" column="exchange_rate_version" jdbcType="INTEGER"/> <!-- 汇率版本 -->
        <result property="systemSign" column="system_sign" jdbcType="CHAR"/> <!-- 实体所属系统 -->
        <result property="entityRelationId" column="entity_relation_id" jdbcType="VARCHAR"/> <!-- 实体业务编号 -->
        <result property="entityApplyId" column="entity_apply_id" jdbcType="VARCHAR"/> <!-- 实体申请编号 -->
        <result property="status" column="status" jdbcType="CHAR"/> <!-- 状态;EnumEntityStatus(010-未结清,020-结清,030-已冲账,040-已撤销) -->
        <result property="entityType" column="entity_type"
                jdbcType="CHAR"/> <!-- 实体类型;EnumLimitEntityType:001-借据、002-信用证、003-保函、004-垫款、005-贴现、006-贷款承诺 -->
        <result property="entityId" column="entity_id" jdbcType="VARCHAR"/> <!-- 实体编号 -->
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
    </resultMap>
    <sql id="Base_Column_List">
        product_id
        , pre_grant_low_risk
                , pre_grant_low_risk_amt_id
                , low_risk
                , low_risk_amount_id
                , left_low_risk
                , left_low_risk_amount_id
                , low_risk_currency
                , amt_limit_flag
                , amount_currency
                , cust_limit_id
                , limit_object_id
                , unique_identifier
                , next_exchange_rate_version
                , short_selling_flag
                , ib_financial_prod_rel_id
                , limit_object_type
                , tenant_id
                , left_amount
                , amount
                , pre_grant_amount
                , grant_date_time
                , amt_limit_ex_rate_recal_flag
                , cust_limit_ex_rate_recal_flag
                , exchange_rate_version
                , system_sign
                , entity_relation_id
                , entity_apply_id
                , status
                , entity_type
                , entity_id
                , update_time
                , create_time
    </sql>
    <select id="selectCountByCurrentGroup"
            parameterType="com.hsjry.core.limit.center.dal.dao.query.EntityInfoBatchQuery"
            resultType="java.lang.Integer">
        SELECT count(*)
        FROM lc_entity_info WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectFirstOne" parameterType="com.hsjry.core.limit.center.dal.dao.query.EntityInfoBatchQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_entity_info
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectShardList" parameterType="com.hsjry.core.limit.center.dal.dao.query.EntityInfoBatchQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_entity_info
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <sql id="fixQuerySql">
        <if test="query.exchangeRateVersion !=null and query.exchangeRateVersion!=''">
            AND exchange_rate_version <![CDATA[ < ]]> #{query.exchangeRateVersion}
        </if>
        <if test="(query.custLimitExRateRecalFlag !=null and query.custLimitExRateRecalFlag!='') or (query.amtLimitExRateRecalFlag !=null and query.amtLimitExRateRecalFlag!='')">
            AND (
            <if test="query.custLimitExRateRecalFlag !=null and query.custLimitExRateRecalFlag!=''">
                 cust_limit_ex_rate_recal_flag = #{query.custLimitExRateRecalFlag}
            </if>
            <if test="(query.custLimitExRateRecalFlag !=null and query.custLimitExRateRecalFlag!='') and (query.amtLimitExRateRecalFlag !=null and query.amtLimitExRateRecalFlag!='')">
                OR
            </if>
            <if test="query.amtLimitExRateRecalFlag !=null and query.amtLimitExRateRecalFlag!=''">
                 amt_limit_ex_rate_recal_flag = #{query.amtLimitExRateRecalFlag}
            </if>
            )
        </if>
        <if test="query.entityId != null and query.entityId!=''">
            AND entity_id > #{query.entityId}
        </if>
        <if test="query.status != null and query.status!=''">
            AND status = #{query.status}
        </if>
        <if test="query.statusList != null  ">
            AND status in
            <foreach collection="query.statusList" index="index" item="item"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY entity_id
    </sql>


    <update id="updateExchangeRateVersion" parameterType="map">
        update lc_entity_info
        set exchange_rate_version = next_exchange_rate_version,
        next_exchange_rate_version = null,
        <if test="targetCustFlag !=null and targetCustFlag!=''">
        cust_limit_ex_rate_recal_flag = #{targetCustFlag},
        </if>
        <if test="targetAmtFlag !=null and targetAmtFlag!=''">
        amt_limit_ex_rate_recal_flag =  #{targetAmtFlag},
        </if>
        update_time =#{updateTime}
        where TENANT_ID=#{tenantId,jdbcType=VARCHAR}
        <if test="sourceCustFlag !=null and sourceCustFlag!=''">
        and cust_limit_ex_rate_recal_flag = #{sourceCustFlag}
        </if>
        <if test="sourceAmtFlag !=null and sourceAmtFlag!=''">
        and amt_limit_ex_rate_recal_flag = #{sourceAmtFlag}
        </if>
        <if test="entityIdList != null  ">
            AND entity_id in
            <foreach collection="entityIdList" index="index" item="item"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>
</mapper>