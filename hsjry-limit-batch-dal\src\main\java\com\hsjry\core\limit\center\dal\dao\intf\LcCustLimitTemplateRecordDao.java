package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitTemplateRecordDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitTemplateRecordQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度体系模板记录数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-04-11 06:33:53
 */
public interface LcCustLimitTemplateRecordDao extends IBaseDao<LcCustLimitTemplateRecordDo> {
    /**
     * 分页查询额度体系模板记录
     *
     * @param lcCustLimitTemplateRecordQuery 条件
     * @return PageInfo<LcCustLimitTemplateRecordDo>
     */
    PageInfo<LcCustLimitTemplateRecordDo> selectPage(LcCustLimitTemplateRecordQuery lcCustLimitTemplateRecordQuery,
        PageParam pageParam);

    /**
     * 根据key查询额度体系模板记录
     *
     * @param limitTemplateRecordId
     * @return
     */
    LcCustLimitTemplateRecordDo selectByKey(String limitTemplateRecordId);

    /**
     * 根据key删除额度体系模板记录
     *
     * @param limitTemplateRecordId
     * @return
     */
    int deleteByKey(String limitTemplateRecordId);

    /**
     * 查询额度体系模板记录信息
     *
     * @param lcCustLimitTemplateRecordQuery 条件
     * @return List<LcCustLimitTemplateRecordDo>
     */
    List<LcCustLimitTemplateRecordDo> selectByExample(LcCustLimitTemplateRecordQuery lcCustLimitTemplateRecordQuery);

    /**
     * 新增额度体系模板记录信息
     *
     * @param lcCustLimitTemplateRecord 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitTemplateRecordDo lcCustLimitTemplateRecord);

    /**
     * 修改额度体系模板记录信息
     *
     * @param lcCustLimitTemplateRecord
     * @return
     */
    int updateBySelective(LcCustLimitTemplateRecordDo lcCustLimitTemplateRecord);

    /**
     * 修改额度体系模板记录信息
     *
     * @param lcCustLimitTemplateRecord
     * @param lcCustLimitTemplateRecordQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitTemplateRecordDo lcCustLimitTemplateRecord,
        LcCustLimitTemplateRecordQuery lcCustLimitTemplateRecordQuery);
}
