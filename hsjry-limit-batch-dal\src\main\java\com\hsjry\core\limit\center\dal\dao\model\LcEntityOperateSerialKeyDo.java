package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 实体操作流水主键
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
@Table(name = "lc_entity_operate_serial")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcEntityOperateSerialKeyDo implements Serializable {

    private static final long serialVersionUID = 1602582710713516048L;
    /** 实体操作流水 */
    @Id
    @Column(name = "leos_serial_no")
    private String leosSerialNo;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}