<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.center.dal.dao.mapper.LcSliceBatchSerialMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo">
                  <result property="tradeCode" column="trade_code" jdbcType="CHAR"/> <!-- 交易码;EnumJobTrade -->
                        <result property="failureTimes" column="failure_times" jdbcType="INTEGER"/> <!-- 数据分片处理失败次数 -->
                        <result property="finishFlag" column="finish_flag" jdbcType="CHAR"/> <!-- 结束标记 EnumBool(N-否, Y-是) -->
                        <result property="sharedNote" column="shared_note" jdbcType="VARCHAR"/> <!-- 数据分片备注，记录失败原因或其他 -->
                        <result property="sharedStatistics" column="shared_statistics" jdbcType="VARCHAR"/> <!-- 数据分片统计内容 -->
                        <result property="sharedPassCount" column="shared_pass_count" jdbcType="INTEGER"/> <!-- 数据分片实际成功处理的条数 -->
                        <result property="sharedDetail" column="shared_detail" jdbcType="VARCHAR"/> <!-- 数据分片详情 -->
                        <result property="execIp" column="exec_ip" jdbcType="VARCHAR"/> <!-- 执行ip -->
                        <result property="execDate" column="exec_date" jdbcType="INTEGER"/> <!-- 任务执行日期,8位日期标识 -->
                        <result property="extSerialId" column="ext_serial_id" jdbcType="VARCHAR"/> <!-- 调度平台分片信息id，外部流水 -->
                        <result property="fileStatus" column="file_status" jdbcType="CHAR"/> <!-- 文件处理状态 -->
                        <result property="sharedStatus" column="shared_status" jdbcType="CHAR"/> <!-- 分片数据处理状态 -->
                        <result property="globalSerialNo" column="global_serial_no" jdbcType="VARCHAR"/> <!-- 全局流水号 -->
                        <result property="batchNum" column="batch_num" jdbcType="INTEGER"/> <!-- 批次号 -->
                        <result property="batchSerialNo" column="batch_serial_no" jdbcType="VARCHAR"/> <!-- 批次总流水 -->
                        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
                        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
                        <result property="tenantId" column="tenant_id" jdbcType="CHAR"/> <!-- 租户号 -->
                        <result property="inboundSerialDatetime" column="inbound_serial_datetime" jdbcType="TIMESTAMP"/> <!-- 前置业务时间 -->
                        <result property="inboundSerialNo" column="inbound_serial_no" jdbcType="VARCHAR"/> <!-- 前置业务流水 -->
                        <result property="bizDatetime" column="biz_datetime" jdbcType="TIMESTAMP"/> <!-- 业务时间 -->
                        <result property="channelNo" column="channel_no" jdbcType="CHAR"/> <!-- 渠道号 -->
                        <result property="serialNo" column="serial_no" jdbcType="VARCHAR"/> <!-- 业务流水号 -->
              </resultMap>
  <sql id="Base_Column_List">
                trade_code
                , failure_times
                , finish_flag
                , shared_note
                , shared_statistics
                , shared_pass_count
                , shared_detail
                , exec_ip
                , exec_date
                , ext_serial_id
                , file_status
                , shared_status
                , global_serial_no
                , batch_num
                , batch_serial_no
                , update_time
                , create_time
                , tenant_id
                , inbound_serial_datetime
                , inbound_serial_no
                , biz_datetime
                , channel_no
                , serial_no
          </sql>
</mapper>