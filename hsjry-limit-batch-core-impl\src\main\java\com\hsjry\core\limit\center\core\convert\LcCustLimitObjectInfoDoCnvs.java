package com.hsjry.core.limit.center.core.convert;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.center.core.bo.CustLimitObjectInfoBo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;

@Mapper(componentModel = "spring")
public interface LcCustLimitObjectInfoDoCnvs {
    LcCustLimitObjectInfoDoCnvs INSTANCE = Mappers.getMapper(LcCustLimitObjectInfoDoCnvs.class);

    /** 将[额度实例所属对象信息Bo]转换为[额度实例所属对象信息Do] */
    LcCustLimitObjectInfoDo cnvsBoToDo(CustLimitObjectInfoBo bo);

    /** 将[额度实例所属对象信息Bo列表]转换为[额度实例所属对象信息Do列表] */
    List<LcCustLimitObjectInfoDo> cnvsBoListToDoList(List<CustLimitObjectInfoBo> boList);

    /** 将[额度实例所属对象信息Do]转换为[额度实例所属对象信息Bo] */
    CustLimitObjectInfoBo cnvsDoToBo(LcCustLimitObjectInfoDo dataObject);

    /** 将[额度实例所属对象信息Do列表]转换为[额度实例所属对象信息Bo列表] */
    List<CustLimitObjectInfoBo> cnvsDoListToBoList(List<LcCustLimitObjectInfoDo> doList);
}
