package com.hsjry.core.limit.batch.biz.job.job.mid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 额度中心-中间表-网贷系统个人合同信息处理任务
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/16
 */
@Slf4j
@Service("lbTOlIndvCtrInfoProcessJob")
public class LbTOlIndvCtrInfoProcessJob extends AbstractBaseBatchJob {
    public LbTOlIndvCtrInfoProcessJob() {
        log.info("LbTOlIndvCtrInfoProcessJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbTOlIndvCtrInfoProcessBizImpl")
    private BaseOrdinaryBiz baseOrdinaryBiz;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return baseOrdinaryBiz;
    }
} 