package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbHCoreAtxzhDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbHCoreAtxzhMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAtxzhDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAtxzhExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAtxzhKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHCoreAtxzhQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 核心系统-历史表-贴现账户主文件数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Repository
public class LbHCoreAtxzhDaoImpl extends AbstractBaseDaoImpl<LbHCoreAtxzhDo, LbHCoreAtxzhMapper>
    implements LbHCoreAtxzhDao {
    /**
     * 分页查询
     *
     * @param lbHCoreAtxzh 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbHCoreAtxzhDo> selectPage(LbHCoreAtxzhQuery lbHCoreAtxzh, PageParam pageParam) {
        LbHCoreAtxzhExample example = buildExample(lbHCoreAtxzh);
        return PageHelper.<LbHCoreAtxzhDo>startPage(pageParam.getPageNum(), pageParam.getPageSize()).doSelectPageInfo(
            () -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询核心系统-历史表-贴现账户主文件
     *
     * @param faredm
     * @param txnjjh
     * @param dataDate
     * @return
     */
    @Override
    public LbHCoreAtxzhDo selectByKey(String faredm, String txnjjh, String dataDate) {
        LbHCoreAtxzhKeyDo lbHCoreAtxzhKeyDo = new LbHCoreAtxzhKeyDo();
        lbHCoreAtxzhKeyDo.setFaredm(faredm);
        lbHCoreAtxzhKeyDo.setTxnjjh(txnjjh);
        lbHCoreAtxzhKeyDo.setDataDate(dataDate);
        return getMapper().selectByPrimaryKey(lbHCoreAtxzhKeyDo);
    }

    /**
     * 根据key删除核心系统-历史表-贴现账户主文件
     *
     * @param faredm
     * @param txnjjh
     * @param dataDate
     * @return
     */
    @Override
    public int deleteByKey(String faredm, String txnjjh, String dataDate) {
        LbHCoreAtxzhKeyDo lbHCoreAtxzhKeyDo = new LbHCoreAtxzhKeyDo();
        lbHCoreAtxzhKeyDo.setFaredm(faredm);
        lbHCoreAtxzhKeyDo.setTxnjjh(txnjjh);
        lbHCoreAtxzhKeyDo.setDataDate(dataDate);
        return getMapper().deleteByPrimaryKey(lbHCoreAtxzhKeyDo);
    }

    /**
     * 查询核心系统-历史表-贴现账户主文件信息
     *
     * @param lbHCoreAtxzh 条件
     * @return List<LbHCoreAtxzhDo>
     */
    @Override
    public List<LbHCoreAtxzhDo> selectByExample(LbHCoreAtxzhQuery lbHCoreAtxzh) {
        return getMapper().selectByExample(buildExample(lbHCoreAtxzh));
    }

    /**
     * 新增核心系统-历史表-贴现账户主文件信息
     *
     * @param lbHCoreAtxzh 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbHCoreAtxzhDo lbHCoreAtxzh) {
        if (lbHCoreAtxzh == null) {
            return -1;
        }

        return getMapper().insertSelective(lbHCoreAtxzh);
    }

    /**
     * 修改核心系统-历史表-贴现账户主文件信息
     *
     * @param lbHCoreAtxzh
     * @return
     */
    @Override
    public int updateBySelective(LbHCoreAtxzhDo lbHCoreAtxzh) {
        if (lbHCoreAtxzh == null) {
            return -1;
        }

        return getMapper().updateByPrimaryKeySelective(lbHCoreAtxzh);
    }

    @Override
    public int updateBySelectiveByExample(LbHCoreAtxzhDo lbHCoreAtxzh, LbHCoreAtxzhQuery lbHCoreAtxzhQuery) {
        return getMapper().updateByExampleSelective(lbHCoreAtxzh, buildExample(lbHCoreAtxzhQuery));
    }

    /**
     * 构建核心系统-历史表-贴现账户主文件Example信息
     *
     * @param lbHCoreAtxzh
     * @return
     */
    public LbHCoreAtxzhExample buildExample(LbHCoreAtxzhQuery lbHCoreAtxzh) {
        LbHCoreAtxzhExample example = new LbHCoreAtxzhExample();
        LbHCoreAtxzhExample.Criteria criteria = example.createCriteria();
        if (lbHCoreAtxzh != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getDzywbh())) {
                criteria.andDzywbhEqualTo(lbHCoreAtxzh.getDzywbh());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getDuifhm())) {
                criteria.andDuifhmEqualTo(lbHCoreAtxzh.getDuifhm());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getDuifhh())) {
                criteria.andDuifhhEqualTo(lbHCoreAtxzh.getDuifhh());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getJigulb())) {
                criteria.andJigulbEqualTo(lbHCoreAtxzh.getJigulb());
            }
            if (null != lbHCoreAtxzh.getZhdkje()) {
                criteria.andZhdkjeEqualTo(lbHCoreAtxzh.getZhdkje());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getJiejuh())) {
                criteria.andJiejuhEqualTo(lbHCoreAtxzh.getJiejuh());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getDnknbz())) {
                criteria.andDnknbzEqualTo(lbHCoreAtxzh.getDnknbz());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getXinx01())) {
                criteria.andXinx01EqualTo(lbHCoreAtxzh.getXinx01());
            }
            if (null != lbHCoreAtxzh.getFxcdje()) {
                criteria.andFxcdjeEqualTo(lbHCoreAtxzh.getFxcdje());
            }
            if (null != lbHCoreAtxzh.getBcmdll()) {
                criteria.andBcmdllEqualTo(lbHCoreAtxzh.getBcmdll());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getTxfxbz())) {
                criteria.andTxfxbzEqualTo(lbHCoreAtxzh.getTxfxbz());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getMffxzh())) {
                criteria.andMffxzhEqualTo(lbHCoreAtxzh.getMffxzh());
            }
            if (null != lbHCoreAtxzh.getMffxbl()) {
                criteria.andMffxblEqualTo(lbHCoreAtxzh.getMffxbl());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getTxfxfs())) {
                criteria.andTxfxfsEqualTo(lbHCoreAtxzh.getTxfxfs());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getCxcfbh())) {
                criteria.andCxcfbhEqualTo(lbHCoreAtxzh.getCxcfbh());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getSfxthc())) {
                criteria.andSfxthcEqualTo(lbHCoreAtxzh.getSfxthc());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getJieszh())) {
                criteria.andJieszhEqualTo(lbHCoreAtxzh.getJieszh());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getXctzrq())) {
                criteria.andXctzrqEqualTo(lbHCoreAtxzh.getXctzrq());
            }
            if (null != lbHCoreAtxzh.getBcmdlx()) {
                criteria.andBcmdlxEqualTo(lbHCoreAtxzh.getBcmdlx());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getZhungt())) {
                criteria.andZhungtEqualTo(lbHCoreAtxzh.getZhungt());
            }
            if (null != lbHCoreAtxzh.getMxxhao()) {
                criteria.andMxxhaoEqualTo(lbHCoreAtxzh.getMxxhao());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getLururq())) {
                criteria.andLururqEqualTo(lbHCoreAtxzh.getLururq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getLurugy())) {
                criteria.andLurugyEqualTo(lbHCoreAtxzh.getLurugy());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getFuherq())) {
                criteria.andFuherqEqualTo(lbHCoreAtxzh.getFuherq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getFuhegy())) {
                criteria.andFuhegyEqualTo(lbHCoreAtxzh.getFuhegy());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getWeihrq())) {
                criteria.andWeihrqEqualTo(lbHCoreAtxzh.getWeihrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getWeihgy())) {
                criteria.andWeihgyEqualTo(lbHCoreAtxzh.getWeihgy());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getJioyrq())) {
                criteria.andJioyrqEqualTo(lbHCoreAtxzh.getJioyrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getWeihjg())) {
                criteria.andWeihjgEqualTo(lbHCoreAtxzh.getWeihjg());
            }
            if (null != lbHCoreAtxzh.getWeihsj()) {
                criteria.andWeihsjEqualTo(lbHCoreAtxzh.getWeihsj());
            }
            if (null != lbHCoreAtxzh.getShinch()) {
                criteria.andShinchEqualTo(lbHCoreAtxzh.getShinch());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getJiluzt())) {
                criteria.andJiluztEqualTo(lbHCoreAtxzh.getJiluzt());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getDataDate())) {
                criteria.andDataDateEqualTo(lbHCoreAtxzh.getDataDate());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getYngyjg())) {
                criteria.andYngyjgEqualTo(lbHCoreAtxzh.getYngyjg());
            }
            if (null != lbHCoreAtxzh.getKuanxq()) {
                criteria.andKuanxqEqualTo(lbHCoreAtxzh.getKuanxq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getTxdqrq())) {
                criteria.andTxdqrqEqualTo(lbHCoreAtxzh.getTxdqrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getTxqxrq())) {
                criteria.andTxqxrqEqualTo(lbHCoreAtxzh.getTxqxrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getHuobdh())) {
                criteria.andHuobdhEqualTo(lbHCoreAtxzh.getHuobdh());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getSyrzjg())) {
                criteria.andSyrzjgEqualTo(lbHCoreAtxzh.getSyrzjg());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getSyzcjg())) {
                criteria.andSyzcjgEqualTo(lbHCoreAtxzh.getSyzcjg());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getRuzhjg())) {
                criteria.andRuzhjgEqualTo(lbHCoreAtxzh.getRuzhjg());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getZhngjg())) {
                criteria.andZhngjgEqualTo(lbHCoreAtxzh.getZhngjg());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getLilvbh())) {
                criteria.andLilvbhEqualTo(lbHCoreAtxzh.getLilvbh());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getKehzwm())) {
                criteria.andKehzwmEqualTo(lbHCoreAtxzh.getKehzwm());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getKehhao())) {
                criteria.andKehhaoEqualTo(lbHCoreAtxzh.getKehhao());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getPiojzh())) {
                criteria.andPiojzhEqualTo(lbHCoreAtxzh.getPiojzh());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getTxywzl())) {
                criteria.andTxywzlEqualTo(lbHCoreAtxzh.getTxywzl());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getTxclzl())) {
                criteria.andTxclzlEqualTo(lbHCoreAtxzh.getTxclzl());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getTiexzh())) {
                criteria.andTiexzhEqualTo(lbHCoreAtxzh.getTiexzh());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getTxnjjh())) {
                criteria.andTxnjjhEqualTo(lbHCoreAtxzh.getTxnjjh());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getFaredm())) {
                criteria.andFaredmEqualTo(lbHCoreAtxzh.getFaredm());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getNyuell())) {
                criteria.andNyuellEqualTo(lbHCoreAtxzh.getNyuell());
            }
            if (null != lbHCoreAtxzh.getTiexll()) {
                criteria.andTiexllEqualTo(lbHCoreAtxzh.getTiexll());
            }
            if (null != lbHCoreAtxzh.getTxztye()) {
                criteria.andTxztyeEqualTo(lbHCoreAtxzh.getTxztye());
            }
            if (null != lbHCoreAtxzh.getShfuje()) {
                criteria.andShfujeEqualTo(lbHCoreAtxzh.getShfuje());
            }
            if (null != lbHCoreAtxzh.getSxtxlx()) {
                criteria.andSxtxlxEqualTo(lbHCoreAtxzh.getSxtxlx());
            }
            if (null != lbHCoreAtxzh.getLjlxsr()) {
                criteria.andLjlxsrEqualTo(lbHCoreAtxzh.getLjlxsr());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getTxrzzq())) {
                criteria.andTxrzzqEqualTo(lbHCoreAtxzh.getTxrzzq());
            }
            if (null != lbHCoreAtxzh.getDtsrye()) {
                criteria.andDtsryeEqualTo(lbHCoreAtxzh.getDtsrye());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getSctsrq())) {
                criteria.andSctsrqEqualTo(lbHCoreAtxzh.getSctsrq());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getXctsro())) {
                criteria.andXctsroEqualTo(lbHCoreAtxzh.getXctsro());
            }
            if (null != lbHCoreAtxzh.getShshje()) {
                criteria.andShshjeEqualTo(lbHCoreAtxzh.getShshje());
            }
            if (null != lbHCoreAtxzh.getSftxlx()) {
                criteria.andSftxlxEqualTo(lbHCoreAtxzh.getSftxlx());
            }
            if (null != lbHCoreAtxzh.getLjlxzc()) {
                criteria.andLjlxzcEqualTo(lbHCoreAtxzh.getLjlxzc());
            }
            if (null != lbHCoreAtxzh.getDtzcye()) {
                criteria.andDtzcyeEqualTo(lbHCoreAtxzh.getDtzcye());
            }
            if (StringUtil.isNotEmpty(lbHCoreAtxzh.getSctzrq())) {
                criteria.andSctzrqEqualTo(lbHCoreAtxzh.getSctzrq());
            }
        }
        buildExampleExt(lbHCoreAtxzh, criteria);
        return example;
    }

    /**
     * 构建核心系统-历史表-贴现账户主文件ExampleExt方法
     *
     * @param lbHCoreAtxzh
     * @return
     */
    public void buildExampleExt(LbHCoreAtxzhQuery lbHCoreAtxzh, LbHCoreAtxzhExample.Criteria criteria) {

        //自定义实现
    }
    // 请在现有的LbHCoreAtxzhDaoImpl实现类中添加以下方法：

    /**
     * 批量插入核心系统贴现账户主文件-历史表信息
     *
     * @param lbHCoreAtxzhList 批量数据
     * @return int
     */
    @Override
    public int insertList(List<LbHCoreAtxzhDo> lbHCoreAtxzhList) {
        if (lbHCoreAtxzhList.isEmpty()) {
            return 0;
        }
        return getMapper().insertList(lbHCoreAtxzhList);
    }

    /**
     * 清空核心系统贴现账户主文件-历史表所有数据
     *
     * @return int
     */
    @Override
    public int deleteAll() {
        return getMapper().deleteAll();
    }

    /**
     * 根据数据日期删除核心系统贴现账户主文件-历史表数据
     *
     * @param dataDate 数据日期
     * @return int
     */
    @Override
    public int deleteByDataDate(String dataDate) {
        return getMapper().deleteByDataDate(dataDate);
    }
}
