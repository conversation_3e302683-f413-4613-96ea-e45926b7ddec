package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度体系模板记录主键
 *
 * <AUTHOR>
 * @date 2023-05-23 09:58:53
 */
@Table(name = "lc_cust_limit_template_record")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcCustLimitTemplateRecordKeyDo implements Serializable {

    private static final long serialVersionUID = 1660948407298228224L;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    /** 记录编号 */
    @Id
    @Column(name = "limit_template_record_id")
    private String limitTemplateRecordId;
}