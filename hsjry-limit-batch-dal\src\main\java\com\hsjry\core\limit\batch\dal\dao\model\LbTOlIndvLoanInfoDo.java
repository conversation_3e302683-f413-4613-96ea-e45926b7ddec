package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 网贷系统-中间表-个人借据信息Do
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
@Table(name = "lb_t_ol_indv_loan_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbTOlIndvLoanInfoDo extends LbTOlIndvLoanInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1950902007284039683L;
    /** 实体业务编号 */
    @Column(name = "entity_relation_id")
    private String entityRelationId;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 低风险余额 */
    @Column(name = "left_low_risk")
    private java.math.BigDecimal leftLowRisk;
    /** 发放低风险 */
    @Column(name = "low_risk")
    private java.math.BigDecimal lowRisk;
    /** 当前余额 */
    @Column(name = "left_amount")
    private java.math.BigDecimal leftAmount;
    /** 发放金额 */
    @Column(name = "amount")
    private java.math.BigDecimal amount;
    /** 实体申请编号 */
    @Column(name = "entity_apply_id")
    private String entityApplyId;
    /** 状态;EnumEntityStatus */
    @Column(name = "status")
    private String status;
    /** 实体类型;EnumLimitCenterEntityType */
    @Column(name = "entity_type")
    private String entityType;
    /** 证件号码 */
    @Column(name = "cert_no")
    private String certNo;
    /** 证件类型 */
    @Column(name = "cert_typ")
    private String certTyp;
    /** 客户名称 */
    @Column(name = "cust_nm")
    private String custNm;
    /** 客户类型 */
    @Column(name = "cust_typ")
    private String custTyp;
}
