package com.hsjry.core.limit.batch.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblDsctBalInfoDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 电票系统-历史表-贴现余额信息mapper
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
public interface LbHElcblDsctBalInfoMapper extends CommonMapper<LbHElcblDsctBalInfoDo> {
    // 以下方法需要添加到 LbHElcblDsctBalInfoMapper 接口中

    /**
     * 批量插入电票系统-历史表-贴现余额信息
     *
     * @param lbHElcblDsctBalInfoList 批量数据
     * @return int
     */
    @Override
    int insertList(@Param("list") List<LbHElcblDsctBalInfoDo> lbHElcblDsctBalInfoList);

    /**
     * 清空电票系统-历史表-贴现余额信息所有数据
     *
     * @return int
     */
    int deleteAll();
    /**
     * 根据数据日期删除贴现账户主文件表数据
     *
     * @param dataDate 数据日期
     * @return int
     */
    int deleteByDataDate(@Param("dataDate") String dataDate);
}
