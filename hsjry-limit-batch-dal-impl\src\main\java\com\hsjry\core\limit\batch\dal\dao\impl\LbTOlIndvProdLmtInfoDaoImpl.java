package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTOlIndvProdLmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTOlIndvProdLmtInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlIndvProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlIndvProdLmtInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlIndvProdLmtInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTOlIndvProdLmtInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-中间表-个人产品层额度信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
@Repository
public class LbTOlIndvProdLmtInfoDaoImpl extends AbstractBaseDaoImpl<LbTOlIndvProdLmtInfoDo, LbTOlIndvProdLmtInfoMapper>
    implements LbTOlIndvProdLmtInfoDao {
    /**
     * 分页查询
     *
     * @param lbTOlIndvProdLmtInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTOlIndvProdLmtInfoDo> selectPage(LbTOlIndvProdLmtInfoQuery lbTOlIndvProdLmtInfo,
        PageParam pageParam) {
        LbTOlIndvProdLmtInfoExample example = buildExample(lbTOlIndvProdLmtInfo);
        return PageHelper.<LbTOlIndvProdLmtInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询网贷系统-中间表-个人产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTOlIndvProdLmtInfoDo selectByKey(String custNo, String custLimitId) {
        LbTOlIndvProdLmtInfoKeyDo lbTOlIndvProdLmtInfoKeyDo = new LbTOlIndvProdLmtInfoKeyDo();
        lbTOlIndvProdLmtInfoKeyDo.setCustNo(custNo);
        lbTOlIndvProdLmtInfoKeyDo.setCustLimitId(custLimitId);
        lbTOlIndvProdLmtInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTOlIndvProdLmtInfoKeyDo);
    }

    /**
     * 根据key删除网贷系统-中间表-个人产品层额度信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTOlIndvProdLmtInfoKeyDo lbTOlIndvProdLmtInfoKeyDo = new LbTOlIndvProdLmtInfoKeyDo();
        lbTOlIndvProdLmtInfoKeyDo.setCustNo(custNo);
        lbTOlIndvProdLmtInfoKeyDo.setCustLimitId(custLimitId);
        lbTOlIndvProdLmtInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTOlIndvProdLmtInfoKeyDo);
    }

    /**
     * 查询网贷系统-中间表-个人产品层额度信息信息
     *
     * @param lbTOlIndvProdLmtInfo 条件
     * @return List<LbTOlIndvProdLmtInfoDo>
     */
    @Override
    public List<LbTOlIndvProdLmtInfoDo> selectByExample(LbTOlIndvProdLmtInfoQuery lbTOlIndvProdLmtInfo) {
        return getMapper().selectByExample(buildExample(lbTOlIndvProdLmtInfo));
    }

    /**
     * 新增网贷系统-中间表-个人产品层额度信息信息
     *
     * @param lbTOlIndvProdLmtInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTOlIndvProdLmtInfoDo lbTOlIndvProdLmtInfo) {
        if (lbTOlIndvProdLmtInfo == null) {
            return -1;
        }

        lbTOlIndvProdLmtInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTOlIndvProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTOlIndvProdLmtInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTOlIndvProdLmtInfo);
    }

    /**
     * 修改网贷系统-中间表-个人产品层额度信息信息
     *
     * @param lbTOlIndvProdLmtInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTOlIndvProdLmtInfoDo lbTOlIndvProdLmtInfo) {
        if (lbTOlIndvProdLmtInfo == null) {
            return -1;
        }
        lbTOlIndvProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTOlIndvProdLmtInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTOlIndvProdLmtInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTOlIndvProdLmtInfoDo lbTOlIndvProdLmtInfo,
        LbTOlIndvProdLmtInfoQuery lbTOlIndvProdLmtInfoQuery) {
        lbTOlIndvProdLmtInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTOlIndvProdLmtInfo, buildExample(lbTOlIndvProdLmtInfoQuery));
    }

    /**
     * 构建网贷系统-中间表-个人产品层额度信息Example信息
     *
     * @param lbTOlIndvProdLmtInfo
     * @return
     */
    public LbTOlIndvProdLmtInfoExample buildExample(LbTOlIndvProdLmtInfoQuery lbTOlIndvProdLmtInfo) {
        LbTOlIndvProdLmtInfoExample example = new LbTOlIndvProdLmtInfoExample();
        LbTOlIndvProdLmtInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTOlIndvProdLmtInfo != null) {
            //添加查询条件
            if (null != lbTOlIndvProdLmtInfo.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTOlIndvProdLmtInfo.getLowRiskAmount());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvProdLmtInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTOlIndvProdLmtInfo.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvProdLmtInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTOlIndvProdLmtInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvProdLmtInfo.getEntityId())) {
                criteria.andEntityIdEqualTo(lbTOlIndvProdLmtInfo.getEntityId());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvProdLmtInfo.getVirtualContractFlag())) {
                criteria.andVirtualContractFlagEqualTo(lbTOlIndvProdLmtInfo.getVirtualContractFlag());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvProdLmtInfo.getRelationId())) {
                criteria.andRelationIdEqualTo(lbTOlIndvProdLmtInfo.getRelationId());
            }
            if (null != lbTOlIndvProdLmtInfo.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTOlIndvProdLmtInfo.getRealOccupyLowRiskAmt());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvProdLmtInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTOlIndvProdLmtInfo.getCustNo());
            }
            if (null != lbTOlIndvProdLmtInfo.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTOlIndvProdLmtInfo.getRealOccupyAmount());
            }
            if (null != lbTOlIndvProdLmtInfo.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTOlIndvProdLmtInfo.getTotalAmount());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvProdLmtInfo.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTOlIndvProdLmtInfo.getLimitStatus());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvProdLmtInfo.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTOlIndvProdLmtInfo.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvProdLmtInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTOlIndvProdLmtInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvProdLmtInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTOlIndvProdLmtInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvProdLmtInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTOlIndvProdLmtInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvProdLmtInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTOlIndvProdLmtInfo.getCustTyp());
            }
        }
        buildExampleExt(lbTOlIndvProdLmtInfo, criteria);
        return example;
    }

    /**
     * 构建网贷系统-中间表-个人产品层额度信息ExampleExt方法
     *
     * @param lbTOlIndvProdLmtInfo
     * @return
     */
    public void buildExampleExt(LbTOlIndvProdLmtInfoQuery lbTOlIndvProdLmtInfo,
        LbTOlIndvProdLmtInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 将网贷个人客户额度同步数据插入到产品层额度信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_ol_indv_prod_lmt_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    @Override
    public int insertOlIndvProdLmtInfo(List<String> templateNodeIdList, List<String> custLimitIdList) {
        return getMapper().insertOlIndvProdLmtInfo(templateNodeIdList, custLimitIdList);
    }

    /**
     * 更新额度实例金额信息表
     * 根据网贷系统中间表数据更新LC_CUST_LIMIT_AMT_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    @Override
    public int updateCustLimitAmtInfo(List<String> custLimitIdList) {
        return getMapper().updateCustLimitAmtInfo(custLimitIdList);
    }

}
