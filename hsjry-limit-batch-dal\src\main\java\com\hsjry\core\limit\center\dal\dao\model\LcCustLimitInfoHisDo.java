package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度实例历史Do
 *
 * <AUTHOR>
 * @date 2023-11-13 01:05:13
 */
@Table(name = "lc_cust_limit_info_his")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitInfoHisDo extends LcCustLimitInfoHisKeyDo implements Serializable {
    private static final long serialVersionUID = 1723869594676035584L;
    /** 总低风险额度 */
    @Column(name = "low_risk_amount")
    private java.math.BigDecimal lowRiskAmount;
    /** 是否向上占用;EnumBool(Y-是，N-否) */
    @Column(name = "up_flag")
    private String upFlag;
    /** 是否合同额度;EnumBool(Y-是，N-否) */
    @Column(name = "contract_limit_flag")
    private String contractLimitFlag;
    /** 是否唯一;EnumBool(Y-是，N-否) */
    @Column(name = "sole_flag")
    private String soleFlag;
    /** 超额占用方式;EnumExcessOccupationType:001-压缩占用、002-超额占用 */
    @Column(name = "excess_occupation_type")
    private String excessOccupationType;
    /** 关联编号 */
    @Column(name = "relation_id")
    private String relationId;
    /** 业务条线 */
    @Column(name = "biz_line")
    private String bizLine;
    /** 是否允许被串用;EnumBool(Y-是，N-否) */
    @Column(name = "support_shared_flag")
    private String supportSharedFlag;
    /** 是否允许串用;EnumBool(Y-是，N-否) */
    @Column(name = "support_share_flag")
    private String supportShareFlag;
    /** 虚拟合同标识;EnumBool(Y-是，N-否) */
    @Column(name = "virtual_contract_flag")
    private String virtualContractFlag;
    /** 总额度 */
    @Column(name = "total_amount")
    private java.math.BigDecimal totalAmount;
    /** 额度占用方式;EnumOccupationType(001-合同占用, 002-余额占用,003-总量占用) */
    @Column(name = "limit_occupation_type")
    private String limitOccupationType;
    /** 额度币种 */
    @Column(name = "currency")
    private String currency;
    /** 低风险币种 */
    @Column(name = "low_risk_currency")
    private String lowRiskCurrency;
    /** 客户类型 */
    @Column(name = "user_type")
    private String userType;
    /** 客户名称 */
    @Column(name = "user_name")
    private String userName;
    /** 客户证件类型 */
    @Column(name = "user_certificate_kind")
    private String userCertificateKind;
    /** 客户证件编号 */
    @Column(name = "user_certificate_no")
    private String userCertificateNo;
    /** 同业金融产品类型 */
    @Column(name = "ib_financial_prod_type")
    private String ibFinancialProdType;
    /** 同业金融产品名称 */
    @Column(name = "ib_financial_prod_name")
    private String ibFinancialProdName;
    /** 同业金融类型 */
    @Column(name = "ib_financial_type")
    private String ibFinancialType;
    /** 同业金融编号 */
    @Column(name = "ib_financial_id")
    private String ibFinancialId;
    /** 期限单位;001-年、002-月、003-日 */
    @Column(name = "limit_term_unit")
    private String limitTermUnit;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 额度状态;EnumCustLimitStatus(010-未生效，020-生效，030-冻结，040-终止-失效，050-到期-失效) */
    @Column(name = "limit_status")
    private String limitStatus;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 生效起始时间 */
    @Column(name = "effective_start_time")
    private java.util.Date effectiveStartTime;
    /** 生效结束时间 */
    @Column(name = "effective_end_time")
    private java.util.Date effectiveEndTime;
    /** 期限 */
    @Column(name = "limit_term")
    private Integer limitTerm;
    /** 三方额度编号 */
    @Column(name = "out_cust_limit_id")
    private String outCustLimitId;
    /** 模板节点编号 */
    @Column(name = "template_node_id")
    private String templateNodeId;
    /** 额度模板编号 */
    @Column(name = "limit_template_id")
    private String limitTemplateId;
    /** 所属对象编号 */
    @Column(name = "limit_object_id")
    private String limitObjectId;
    /** 所属对象类型 */
    @Column(name = "limit_object_type")
    private String limitObjectType;
    /** 业务品种【产品】 */
    @Column(name = "product_id")
    private String productId;
    /** 额度层级;EnumLimitLevel:001-根节点、002-中间节点、003-末级节点 */
    @Column(name = "limit_level")
    private String limitLevel;
    /** 额度分类;dictKey:LIMIT_CLASSIFICATION */
    @Column(name = "limit_classification")
    private String limitClassification;
    /** 额度使用方式;EnumUsageType(001-非循环授信, 002-循环授信） */
    @Column(name = "limit_usage_type")
    private String limitUsageType;
    /** 额度设立方式;EnumLimitGrantType 001-拆分、002-共享、003-汇总 */
    @Column(name = "limit_grant_type")
    private String limitGrantType;
}
