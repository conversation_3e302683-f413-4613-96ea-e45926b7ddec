package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度串用信息Do
 *
 * <AUTHOR>
 * @date 2023-07-12 02:27:14
 */
@Table(name = "lc_cust_limit_share_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitShareInfoDo extends LcCustLimitShareInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1678954139251572737L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 串用状态;EnumCustLimitShareStatus:010-生效、020-失效 */
    @Column(name = "share_status")
    private String shareStatus;
    /** 串用额度编号 */
    @Column(name = "borrow_cust_limit_id")
    private String borrowCustLimitId;
    /** 被串用额度编号 */
    @Column(name = "shared_cust_limit_id")
    private String sharedCustLimitId;
    /** 串用预占金额 */
    @Column(name = "pre_occupy_amount")
    private java.math.BigDecimal preOccupyAmount;
    /** 串用预占余额 */
    @Column(name = "left_pre_occupy_amount")
    private java.math.BigDecimal leftPreOccupyAmount;
    /** 串用实占金额 */
    @Column(name = "real_occupy_amount")
    private java.math.BigDecimal realOccupyAmount;
    /** 串用实占余额 */
    @Column(name = "left_real_occupy_amount")
    private java.math.BigDecimal leftRealOccupyAmount;
    /** 币种 */
    @Column(name = "currency")
    private String currency;
    /** 串用金额上限 "空"为不控制 */
    @Column(name = "limit_amount")
    private java.math.BigDecimal limitAmount;
}
