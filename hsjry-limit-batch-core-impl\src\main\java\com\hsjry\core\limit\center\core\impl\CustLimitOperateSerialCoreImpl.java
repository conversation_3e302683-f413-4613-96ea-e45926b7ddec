package com.hsjry.core.limit.center.core.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.hsjry.base.common.model.enums.limit.EnumCustLimitOperateStatus;
import com.hsjry.base.common.model.enums.limit.EnumCustLimitOperateType;
import com.hsjry.core.limit.center.core.ICustLimitOperateSerialCore;
import com.hsjry.core.limit.center.core.bo.CustLimitOperateSerialBo;
import com.hsjry.core.limit.center.core.convert.LcCustLimitOperateSerialDoCnvs;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitOperateSerialDao;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitOperateSerialQuery;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 额度操作流水Core实现类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2022/12/29 17:01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustLimitOperateSerialCoreImpl implements ICustLimitOperateSerialCore {
    private final LcCustLimitOperateSerialDao custLimitOperateSerialDao;

    @Override
    public List<CustLimitOperateSerialBo> enqrByExample(LcCustLimitOperateSerialQuery query) {
        return LcCustLimitOperateSerialDoCnvs.INSTANCE.cnvsDoListToBoList(
            custLimitOperateSerialDao.selectByExample(query));
    }

    @Override
    public List<CustLimitOperateSerialBo> enqrPreOccpySerialList() {
        return LcCustLimitOperateSerialDoCnvs.INSTANCE.cnvsDoListToBoList(
            custLimitOperateSerialDao.selectByExample(LcCustLimitOperateSerialQuery.builder()
                .operateType(EnumCustLimitOperateType.PRE_OCCUPY.getCode())
                .status(EnumCustLimitOperateStatus.SUCCESS.getCode())
                .build()));
    }

}
