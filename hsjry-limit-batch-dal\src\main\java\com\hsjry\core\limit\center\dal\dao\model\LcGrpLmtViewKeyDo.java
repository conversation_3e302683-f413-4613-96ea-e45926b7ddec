package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 集团客户额度视图主键
 *
 * <AUTHOR>
 * @date 2025-07-07 12:58:26
 */
@Table(name = "lc_grp_lmt_view")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcGrpLmtViewKeyDo implements Serializable {

    private static final long serialVersionUID = 1942206575120941061L;
    /** 集团客户额度视图主键 */
    @Id
    @Column(name = "grp_lmt_view_id")
    private String grpLmtViewId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}