/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.batch.biz.entity.LbSElcblDsctBalInfoData;
import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblDsctBalInfoDo;

/**
 * 电票系统-贴现余额信息数据转换器
 * 使用MapStruct进行高性能对象映射
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/7/17
 */
@Mapper(componentModel = "spring")
public interface LbSElcblDsctBalInfoCnvs {

    LbSElcblDsctBalInfoCnvs INSTANCE = Mappers.getMapper(LbSElcblDsctBalInfoCnvs.class);

    /**
     * 单个Data转DO
     * 基础转换方法，使用MapStruct自动映射
     *
     * @param data 源数据对象
     * @return 目标DO对象
     */
    LbSElcblDsctBalInfoDo data2Do(LbSElcblDsctBalInfoData data);

    /**
     * 单个DO转Data（新增方法）
     * 反向转换，用于数据回显或其他场景
     *
     * @param dataObject DO对象
     * @return Data对象
     */
    LbSElcblDsctBalInfoData do2Data(LbSElcblDsctBalInfoDo dataObject);

    /**
     * 批量Data转DO列表（新增方法）
     * 利用MapStruct的批量转换能力
     *
     * @param dataList 源数据列表
     * @return 目标DO列表
     */
    List<LbSElcblDsctBalInfoDo> dataListToDoList(List<LbSElcblDsctBalInfoData> dataList);

    /**
     * 批量DO转Data列表（新增方法）
     * 反向批量转换
     *
     * @param doList DO列表
     * @return Data列表
     */
    List<LbSElcblDsctBalInfoData> doListToDataList(List<LbSElcblDsctBalInfoDo> doList);

}