package com.hsjry.core.limit.batch.dal.dao.query;

import java.io.Serializable;

import lombok.Data;
import lombok.Builder;

/**
 * 电票系统-历史表-贴现余额信息查询条件
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Data
@Builder
public class LbHElcblDsctBalInfoQuery implements Serializable {

    /** serialVersionUID */
    private static final long serialVersionUID = 1945747816769060867L;

    /** 法人行机构编号 */
    private String orgNo;
    /** 贴现编号(核心借据号) */
    private String dicCno;
    /** 票据编号 */
    private String billNo;
    /** 子票区间起始 */
    private String billRangeStart;
    /** 子票区间截止 */
    private String billRangeEnd;
    /** 贴现客户编号(对公客户编号) */
    private String userId;
    /** 贴现客户名称(对公客户名称) */
    private String userName;
    /** 票据币种 */
    private String currency;
    /** 贴现金额(票面金额) */
    private java.math.BigDecimal discountAmt;
    /** 贴现余额 */
    private java.math.BigDecimal discountBal;
    /** 贴现起始日期 */
    private String startDate;
    /** 贴现到期日期 */
    private String endDate;
    /** 数据日期 */
    private String dataDate;
}
