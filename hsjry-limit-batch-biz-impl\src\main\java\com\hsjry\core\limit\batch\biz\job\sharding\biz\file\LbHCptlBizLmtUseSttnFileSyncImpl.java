/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.core.limit.batch.biz.convert.file.LbHCptlBizLmtUseSttnConverter;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.biz.entity.LbHCptlBizLmtUseSttnData;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.AbstractFileBaseShardingPrepareBizImpl;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.constants.CharsetConstants;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.dal.dao.intf.LbHCptlBizLmtUseSttnDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCptlBizLmtUseSttnDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.sequence.SequenceTool;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 资金系统历史表日终业务额度使用情况同步实现类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/15 10:00
 */
@Slf4j
@Service("lbHCptlBizLmtUseSttnFileSyncImpl")
@RequiredArgsConstructor
public class LbHCptlBizLmtUseSttnFileSyncImpl extends AbstractFileBaseShardingPrepareBizImpl<LbHCptlBizLmtUseSttnData> {

    /** 客户编号列数 */
    private static final int USER_ID_NUM = 1;
    /** 客户类型列数 */
    private static final int USER_TYPE_NUM = 2;
    /** 客户名称列数 */
    private static final int USER_NAME_NUM = 3;
    /** 证件类型列数 */
    private static final int USER_CERTIFICATE_KIND_NUM = 4;
    /** 证件号码列数 */
    private static final int USER_CERTIFICATE_NO_NUM = 5;
    /** 产品编号列数 */
    private static final int PRODUCT_ID_NUM = 6;
    /** 产品名称列数 */
    private static final int PRODUCT_NAME_NUM = 7;
    /** 额度类型名称列数 */
    private static final int CUST_LIMIT_TYPE_NAME_NUM = 8;
    /** 已用额度列数 */
    private static final int USED_AMOUNT_NUM = 9;
    private static final int CORE_INST_NO_NUM = 10;
    /** 最小字段数量 */
    private static final int MIN_FIELD_COUNT = 10;
    /** 分隔符 */
    private static final String FIELD_SEPARATOR = "\\|\\+\\|";
    /** 批处理大小 */
    private static final int BATCH_SIZE = 1000;
    private final String SEQUENCE_NO = "SEQUENCE_NO";

    private final LbHCptlBizLmtUseSttnDao lbHCptlBizLmtUseSttnDao;
    @Value("${project.cptl.biz.lmt.use.sttn.his.filename:cptl_biz_lmt_use_sttn_his_[DATE].csv}")
    private String fileName;
    @Value("${limit.batch.remoteFilePath:/app/bftfiles/cfs/recv/ebbs/}")
    private String remoteFilePathDefine;

    @Override
    public ShardingResult<LbHCptlBizLmtUseSttnData> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始文件数据分片查询[{}]", GsonUtil.objToStrForLog(jobShared));

        ShardingResult<LbHCptlBizLmtUseSttnData> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        try {
            // 读取文件分片数据
            FileLineData fileLineData = GsonUtil.json2Obj(jobShared.getExtParam(), FileLineData.class);
            log.info(prefixLog + "开始读取文件数据分片[{}]", GsonUtil.objToStrForLog(fileLineData));
            List<String> originData = FileShardingUtils.readFileSharedData(jobShared, skipFirst(), CharsetConstants.GBK);

            // 使用并行流处理数据，提升性能
            List<LbHCptlBizLmtUseSttnData> fileDataList = processOriginDataParallel(originData, prefixLog);

            log.info(prefixLog + "读取文件数据分片总量[{}]", fileDataList.size());
            shardingResult.setShardingResultList(fileDataList);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        jobShared.setBatchSerialNo(inParam.getString(SEQUENCE_NO));
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbHCptlBizLmtUseSttnData> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
        List<LbHCptlBizLmtUseSttnData> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "资金系统历史表业务额度使用情况数据文件处理:文件分片数据为空,执行中断。");
            return;
        }

        log.info(prefixLog + "资金系统历史表业务额度使用情况数据文件处理:开始执行分片数据操作,数据量:[{}]", dataList.size());

        // 设置业务日期
        String dataDateStr = String.valueOf(businessDate);
        dataList.forEach(data -> data.setDataDate(dataDateStr));
        // 检查是否是第一个分片，如果是则删除当前业务日期的数据
        if (sliceBatchSerialDo.getBatchNum() == 1) {
            log.info(prefixLog + "第一个分片,删除目标表 lb_h_cptl_biz_lmt_use_sttn 中业务日期[{}]的数据", dataDateStr);
            lbHCptlBizLmtUseSttnDao.deleteByDataDate(dataDateStr);
        }

        // 确保分片流水对象的状态字段不为null
        if (sliceBatchSerialDo.getSharedStatus() == null) {
            log.debug(prefixLog + "分片流水状态为null,设置为处理中状态");
            sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
        }

        // 使用并行流进行数据转换和验证
        List<LbHCptlBizLmtUseSttnDo> insertList = dataList.parallelStream().map(LbHCptlBizLmtUseSttnConverter::data2Do)//
            .filter(this::validateData).collect(Collectors.toCollection(ArrayList::new));

        if (CollectionUtil.isNotEmpty(insertList)) {
            // 批量插入数据
            processBatchInsert(insertList, prefixLog);
            log.info(prefixLog + "插入[{}]条资金系统历史表业务额度使用情况数据", insertList.size());
        }

        // 更新分片流水成功
        normalUpdateSliceSerial(dataList.size(), sliceBatchSerialDo);
        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataList.size());
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.H_CPTL_BIZ_LMT_USE_STTN;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        JSONObject param = JSON.parseObject(jobInitDto.getInPara());
        //设置流水ID,解决多次从redis获取唯一值的性能问题
        param.put(SEQUENCE_NO, SequenceTool.nextId());
        //更新jobInitDto的inpara参数
        jobInitDto.setInPara(param.toJSONString());

        // 确保fixNum被正确设置
        if (jobInitDto.getFixNum() == null) {
            log.warn(prefixLog + "fixNum为null,设置默认值1000");
            jobInitDto.setFixNum(1000);
        }

        List<JobShared> sharedList = Lists.newArrayList();
        log.info(prefixLog + "[{}]文件处理", jobTradeDesc);
        String localFilePath = FileShardingUtils.getLocalFilePath(jobInitDto.getBusinessDate(),
            remoteFilePathDefine + FileShardingUtils.ACCT_DATE_CODE_MARK + File.separator);
        Integer acctDate = jobInitDto.getBusinessDate();
        String localFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK, String.valueOf(acctDate));
        log.info(prefixLog + "转换后的本地文件路径: [{}]", localFilePath);
        try {
            String fileAttr = FIELD_SEPARATOR;
            String filePath = localFilePath + localFileName;
            log.info("实际查找的文件路径: [{}]", filePath);

            // 检查目录是否存在
            File directory = new File(localFilePath);
            if (!directory.exists()) {
                log.info(prefixLog + "目录[{}]不存在,尝试创建", localFilePath);
                directory.mkdirs();
            } else {
                log.info(prefixLog + "目录[{}]已存在", localFilePath);
                // 列出目录中的文件
                File[] files = directory.listFiles();
                if (files != null && files.length > 0) {
                    log.info(prefixLog + "目录[{}]中的文件列表:", localFilePath);
                    for (File f : files) {
                        log.info(prefixLog + " - {}", f.getName());
                    }
                } else {
                    log.info(prefixLog + "目录[{}]为空", localFilePath);
                }
            }

            File localFile = new File(filePath);
            log.info(prefixLog + "检查文件[{}]是否存在: {}", filePath, localFile.exists());

            //判断本地文件是否存在，不存在则直接报错
            if(!localFile.exists()){
                log.error(prefixLog + "本地文件[{}]不存在,请确认文件路径是否正确", filePath);
                String errorCode = EnumBatchJobError.FILE_PATH_NOT_EXIST.getCode();
                String errorMsg = EnumBatchJobError.FILE_PATH_NOT_EXIST.getDescription();
                throw new HsjryBizException(errorCode, errorMsg);
            }

            // 文件存在，直接进行分片处理
            log.info(prefixLog + "[{}]开始[{}]文件数据分片处理", jobTradeDesc, filePath);
            log.info(prefixLog + "分片参数: fixNum=[{}], fileAttr=[{}], skipFirst=[{}]",
                jobInitDto.getFixNum(), fileAttr, skipFirst());

            List<JobShared> jobShareds = FileShardingUtils.getFileSharedData(jobInitDto, localFile, fileAttr,
                skipFirst());
            log.info(prefixLog + "[{}]结束[{}]文件数据分片当前分片数[{}]", jobTradeDesc, filePath,
                jobShareds.size());
            sharedList.addAll(jobShareds);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }
        return sharedList;
    }

    /**
     * 并行处理原始数据
     */
    private List<LbHCptlBizLmtUseSttnData> processOriginDataParallel(List<String> originData, String prefixLog) {
        AtomicInteger lineNumber = new AtomicInteger(0);
        return originData.parallelStream().map(line -> {
            int currentLine = lineNumber.incrementAndGet();
            try {
                return parseLineToData(line, currentLine);
            } catch (Exception e) {
                log.error(prefixLog + "处理第[{}]行数据失败: [{}]", currentLine, line, e);
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 解析行数据为实体对象
     */
    private LbHCptlBizLmtUseSttnData parseLineToData(String line, int lineNumber) {
        if (StringUtil.isBlank(line)) {
            return null;
        }

        String[] fields = line.split(FIELD_SEPARATOR);
        if (fields.length < MIN_FIELD_COUNT) {
            log.warn("第[{}]行数据字段数量不足,需要[{}]个字段,实际[{}]个: [{}]", lineNumber, MIN_FIELD_COUNT, fields.length, line);
            return null;
        }

        try {
            return LbHCptlBizLmtUseSttnData.builder()
                .userId(getFieldValue(fields, USER_ID_NUM))
                .userType(getFieldValue(fields, USER_TYPE_NUM))
                .userName(getFieldValue(fields, USER_NAME_NUM))
                .userCertificateKind(getFieldValue(fields, USER_CERTIFICATE_KIND_NUM))
                .userCertificateNo(getFieldValue(fields, USER_CERTIFICATE_NO_NUM))
                .productId(getFieldValue(fields, PRODUCT_ID_NUM))
                .productName(getFieldValue(fields, PRODUCT_NAME_NUM))
                .custLimitTypeName(getFieldValue(fields, CUST_LIMIT_TYPE_NAME_NUM))
                .usedAmount(parseDecimal(getFieldValue(fields, USED_AMOUNT_NUM))).coreInstNo(
                    getFieldValue(fields, CORE_INST_NO_NUM))
                .build();
        } catch (Exception e) {
            log.error("解析第[{}]行数据失败: [{}]", lineNumber, line, e);
            return null;
        }
    }

    /**
     * 获取字段值
     */
    private String getFieldValue(String[] fields, int index) {
        if (index <= fields.length) {
            return StringUtil.trim(fields[index - 1]);
        }
        return null;
    }

    /**
     * 解析BigDecimal值
     */
    private BigDecimal parseDecimal(String value) {
        if (StringUtil.isBlank(value)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            log.warn("解析数值失败: [{}], 使用默认值0", value);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 验证数据有效性
     */
    private boolean validateData(LbHCptlBizLmtUseSttnDo data) {
        if (data == null) {
            return false;
        }
        if (StringUtil.isBlank(data.getUserId())) {
            log.warn("客户编号不能为空,跳过该记录");
            return false;
        }
        return true;
    }

    /**
     * 批量插入数据
     */
    private void processBatchInsert(List<LbHCptlBizLmtUseSttnDo> insertList, String prefixLog) {
        List<List<LbHCptlBizLmtUseSttnDo>> batches = Lists.partition(insertList, BATCH_SIZE);
        for (List<LbHCptlBizLmtUseSttnDo> batch : batches) {
            try {
                lbHCptlBizLmtUseSttnDao.batchInsert(batch);
                log.debug(prefixLog + "批量插入[{}]条数据成功", batch.size());
            } catch (Exception e) {
                log.error(prefixLog + "批量插入[{}]条数据失败", batch.size(), e);
                throw e;
            }
        }
    }

    private boolean skipFirst() {
        return false;  // 根据文件格式决定是否跳过第一行
    }
}
