-- =====================================================
-- 函数名称: generate_primary_key (更新版本)
-- 功能描述: 生成64位主键，支持动态长度的表名缩写
-- 参数说明: 
--   p_table_abbr: 表名首字母大写缩写（长度不固定）
-- 返回值: VARCHAR2(64) - 64位主键
-- 创建时间: 2024
-- 更新说明: 去掉4位长度限制，支持动态长度
-- =====================================================

CREATE
OR
REPLACE FUNCTION generate_primary_key(
    p_table_abbr VARCHAR2    -- 表名首字母大写缩写（长度不固定）
) RETURN VARCHAR2
IS
    v_datetime_str VARCHAR2(17); -- 日期时间部分 YYYYMMDDHHmmssSSS
v_tenant_str VARCHAR2(3);         -- 租户标识（3位）
v_random_str VARCHAR2(10);        -- 10位随机数
v_session_str VARCHAR2(30);       -- 会话/扩展信息（动态长度）
v_primary_key VARCHAR2(64);       -- 最终主键（64位）
v_abbr_len NUMBER;                -- 缩写长度
v_session_available_len NUMBER;   -- 会话部分可用长度
v_timestamp TIMESTAMP(3);         -- 时间戳变量
v_tenant_id NUMBER;               -- 租户ID
v_session_id NUMBER;              -- 会话ID
v_random_part NUMBER;             -- 随机部分
v_is_oceanbase NUMBER := 0;       -- 是否为OceanBase环境
BEGIN
    -- 验证输入参数不为空
    IF p_table_abbr IS NULL THEN
        RAISE_APPLICATION_ERROR(-20001, '表名缩写不能为空');
END IF;

    -- 获取表名缩写长度
v_abbr_len := LENGTH(p_table_abbr);
    
    -- 计算会话/扩展信息可用长度
    -- 64位总长度 - 表名缩写 - 3位租户 - 17位时间 - 10位随机数
v_session_available_len := 64 - v_abbr_len - 3 - 17 - 10;
    
    -- 验证是否有足够空间给会话部分（至少保留1位）
IF v_session_available_len < 1 THEN
        RAISE_APPLICATION_ERROR(-20002, '表名缩写长度过长，无法生成64位主键。当前缩写长度: ' || v_abbr_len || '，最大允许长度: 33');
END IF;

    -- 获取当前时间戳
SELECT CURRENT_TIMESTAMP(3)
INTO v_timestamp
FROM DUAL;

-- 格式化时间字符串（17位）
v_datetime_str := TO_CHAR(v_timestamp, 'YYYYMMDDHH24MISS') ||
                      LPAD(SUBSTR(TO_CHAR(EXTRACT(SECOND FROM v_timestamp) * 1000), 1, 3), 3, '0');

    -- 尝试获取租户ID（兼容OceanBase和Oracle）
BEGIN
-- 检查是否为OceanBase环境
BEGIN
SELECT TO_NUMBER(SYS_CONTEXT('USERENV', 'TENANT_ID'))
INTO v_tenant_id
FROM DUAL;
IF v_tenant_id IS NOT NULL AND v_tenant_id > 0 THEN
                v_is_oceanbase := 1;
END IF;
EXCEPTION
            WHEN OTHERS THEN
                v_tenant_id := NULL;
END;

        -- 如果第一种方法失败，尝试其他OceanBase方法
IF v_tenant_id IS NULL OR v_tenant_id = 0 THEN
BEGIN
EXECUTE IMMEDIATE 'SELECT EFFECTIVE_TENANT_ID() FROM DUAL' INTO v_tenant_id;
IF v_tenant_id IS NOT NULL AND v_tenant_id > 0 THEN
                    v_is_oceanbase := 1;
END IF;
EXCEPTION
                WHEN OTHERS THEN
                    v_tenant_id := NULL;
END;
END IF;
EXCEPTION
        WHEN OTHERS THEN
            v_tenant_id := NULL;
END;

    -- 处理租户ID
IF v_tenant_id IS NOT NULL AND v_tenant_id > 0 THEN
        v_tenant_str := LPAD(SUBSTR(TO_CHAR(v_tenant_id), -3), 3, '0');
ELSE
        v_tenant_str := '000';
END IF;

    -- 生成10位随机数（确保每毫秒内的唯一性）
v_random_part := TRUNC(DBMS_RANDOM.VALUE(0, 10000000000)); -- 0到9999999999
v_random_str := LPAD(TO_CHAR(v_random_part), 10, '0');

    -- 生成动态长度的会话/扩展信息
SELECT USERENV('SESSIONID')
INTO v_session_id
FROM DUAL;
v_session_id := MOD(v_session_id, 1000000000); -- 取9位
    
    -- 根据可用长度生成会话字符串
IF v_session_available_len >= 30 THEN
        -- 如果空间充足，使用完整的30位格式
        v_session_str := LPAD(TO_CHAR(v_session_id), 9, '0') ||
                         LPAD(TO_CHAR(DBMS_RANDOM.VALUE(0, 100000000000000000000)), 21, '0');
ELSIF v_session_available_len >= 9 THEN
        -- 如果空间足够放会话ID，则使用会话ID + 随机数
        v_session_str := LPAD(TO_CHAR(v_session_id), 9, '0') ||
                         LPAD(TO_CHAR(DBMS_RANDOM.VALUE(0, POWER(10, v_session_available_len - 9))), 
                              v_session_available_len - 9, '0');
ELSE
        -- 如果空间不足，只使用随机数
        v_session_str := LPAD(TO_CHAR(DBMS_RANDOM.VALUE(0, POWER(10, v_session_available_len))), 
                              v_session_available_len, '0');
END IF;

    -- 组合生成最终的64位主键
    -- 表名缩写 + 3位租户 + 17位时间 + 10位随机数 + 动态长度会话/扩展 = 64位
v_primary_key := p_table_abbr ||      -- 表名缩写（动态长度）
                     v_tenant_str ||      -- 3位：租户标识
                     v_datetime_str ||    -- 17位：时间戳
                     v_random_str ||      -- 10位：随机数
                     v_session_str;       -- 动态长度：会话/扩展信息

RETURN v_primary_key;

EXCEPTION
    WHEN OTHERS THEN
        RAISE_APPLICATION_ERROR(-20003, '生成主键时发生错误: ' || SQLERRM);
END generate_primary_key;
/

-- =====================================================
-- 测试用例
-- =====================================================

-- 测试用例1: 短缩写
-- SELECT generate_primary_key('USER') FROM DUAL;

-- 测试用例2: 4位缩写（原版本兼容）
-- SELECT generate_primary_key('TEST') FROM DUAL;

-- 测试用例3: 长缩写
-- SELECT generate_primary_key('CUSTOMER') FROM DUAL;

-- 测试用例4: 验证长度
-- SELECT LENGTH(generate_primary_key('TEST')) as key_length FROM DUAL;

-- 测试用例5: 验证唯一性（多次调用）
-- SELECT generate_primary_key('TEST') as key1,
--        generate_primary_key('TEST') as key2,
--        generate_primary_key('TEST') as key3
-- FROM DUAL;
