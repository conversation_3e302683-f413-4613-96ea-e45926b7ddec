package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcProductInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcProductInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 产品信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-11-23 02:55:08
 */
public interface LcProductInfoDao extends IBaseDao<LcProductInfoDo> {
    /**
     * 分页查询产品信息
     *
     * @param lcProductInfoQuery 条件
     * @return PageInfo<LcProductInfoDo>
     */
    PageInfo<LcProductInfoDo> selectPage(LcProductInfoQuery lcProductInfoQuery, PageParam pageParam);

    /**
     * 根据key查询产品信息
     *
     * @param productId
     * @return
     */
    LcProductInfoDo selectByKey(String productId);

    /**
     * 根据key删除产品信息
     *
     * @param productId
     * @return
     */
    int deleteByKey(String productId);

    /**
     * 查询产品信息信息
     *
     * @param lcProductInfoQuery 条件
     * @return List<LcProductInfoDo>
     */
    List<LcProductInfoDo> selectByExample(LcProductInfoQuery lcProductInfoQuery);

    /**
     * 新增产品信息信息
     *
     * @param lcProductInfo 条件
     * @return int>
     */
    int insertBySelective(LcProductInfoDo lcProductInfo);

    /**
     * 修改产品信息信息
     *
     * @param lcProductInfo
     * @return
     */
    int updateBySelective(LcProductInfoDo lcProductInfo);

    /**
     * 修改产品信息信息
     *
     * @param lcProductInfo
     * @param lcProductInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcProductInfoDo lcProductInfo, LcProductInfoQuery lcProductInfoQuery);
}
