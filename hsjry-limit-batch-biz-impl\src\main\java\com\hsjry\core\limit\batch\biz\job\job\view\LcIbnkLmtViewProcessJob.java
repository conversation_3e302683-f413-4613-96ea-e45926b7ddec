package com.hsjry.core.limit.batch.biz.job.job.view;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 额度中心-同业客户额度视图处理任务
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/16
 */
@Slf4j
@Service("lcIbnkLmtViewProcessJob")
public class LcIbnkLmtViewProcessJob extends AbstractBaseBatchJob {
    public LcIbnkLmtViewProcessJob() {
        log.info("LcIbnkLmtViewProcessJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lcIbnkLmtViewProcessBizImpl")
    private BaseOrdinaryBiz baseOrdinaryBiz;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return baseOrdinaryBiz;
    }
}
