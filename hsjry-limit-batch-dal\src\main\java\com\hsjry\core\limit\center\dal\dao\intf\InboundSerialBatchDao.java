/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.query.InboundSerialBatchQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcInboundSerialDo;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/4/12 14:43
 */
public interface InboundSerialBatchDao {
    /**
     * 查询分片数据
     *
     * @param batchQuery
     * @return
     */
    List<LcInboundSerialDo> selectShardList(InboundSerialBatchQuery batchQuery);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcInboundSerialDo selectFirstOne(InboundSerialBatchQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(InboundSerialBatchQuery query);
}
