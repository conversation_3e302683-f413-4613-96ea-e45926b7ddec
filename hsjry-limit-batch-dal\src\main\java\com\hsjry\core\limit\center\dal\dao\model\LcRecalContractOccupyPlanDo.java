package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 合同占用重算计划(准备)Do
 *
 * <AUTHOR>
 * @date 2023-11-08 02:51:47
 */
@Table(name = "lc_recal_contract_occupy_plan")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcRecalContractOccupyPlanDo extends LcRecalContractOccupyPlanKeyDo implements Serializable {
    private static final long serialVersionUID = 1722084472624709632L;
    /** 新汇率版本 */
    @Column(name = "new_exchange_rate_version")
    private Integer newExchangeRateVersion;
    /** 重算对象;EnumCalObject:001-额度、002-流水 */
    @Column(name = "cal_object")
    private String calObject;
    /** 撤销预占串用余额 */
    @Column(name = "ca_pre_amount_share")
    private java.math.BigDecimal caPreAmountShare;
    /** 预占串用余额 */
    @Column(name = "pre_amount_share")
    private java.math.BigDecimal preAmountShare;
    /** 撤销预占额度低风险金额余额 */
    @Column(name = "ca_pre_low_risk_amt_balance")
    private java.math.BigDecimal caPreLowRiskAmtBalance;
    /** 撤销预占额度 */
    @Column(name = "ca_pre_amount_balance")
    private java.math.BigDecimal caPreAmountBalance;
    /** 预占额度低风险金额余额 */
    @Column(name = "pre_low_risk_amt_balance")
    private java.math.BigDecimal preLowRiskAmtBalance;
    /** 预占额度金额余额 */
    @Column(name = "pre_amount_balance")
    private java.math.BigDecimal preAmountBalance;
    /** 撤销串用余额 */
    @Column(name = "ca_amount_share")
    private java.math.BigDecimal caAmountShare;
    /** 串用余额 */
    @Column(name = "amount_share")
    private java.math.BigDecimal amountShare;
    /** 汇率版本 */
    @Column(name = "exchange_rate_version")
    private Integer exchangeRateVersion;
    /** 撤销额度低风险金额余额 */
    @Column(name = "ca_low_risk_amt_balance")
    private java.math.BigDecimal caLowRiskAmtBalance;
    /** 撤销额度 */
    @Column(name = "ca_amount_balance")
    private java.math.BigDecimal caAmountBalance;
    /** 额度低风险金额余额 */
    @Column(name = "low_risk_amt_balance")
    private java.math.BigDecimal lowRiskAmtBalance;
    /** 额度金额余额 */
    @Column(name = "amount_balance")
    private java.math.BigDecimal amountBalance;
    /** 所属对象编号 */
    @Column(name = "limit_object_id")
    private String limitObjectId;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
}
