package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度场景Do
 *
 * <AUTHOR>
 * @date 2023-02-02 06:51:40
 */
@Table(name = "lc_cust_limit_scene")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitSceneDo extends LcCustLimitSceneKeyDo implements Serializable {
    private static final long serialVersionUID = 1621038625741537280L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 场景名称 */
    @Column(name = "scene_name")
    private String sceneName;
    /** 额度体系模板编号 */
    @Column(name = "limit_template_id")
    private String limitTemplateId;
}
