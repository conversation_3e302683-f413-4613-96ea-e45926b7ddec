package com.hsjry.core.limit.center.core.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.hsjry.core.limit.center.core.ICustLimitAmtInfoCore;
import com.hsjry.core.limit.center.core.bo.CustLimitAmtInfoBo;
import com.hsjry.core.limit.center.core.convert.LcCustLimitAmtInfoDoCnvs;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitAmtInfoDao;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitAmtInfoQuery;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustLimitAmtInfoCoreImpl implements ICustLimitAmtInfoCore {

    private final LcCustLimitAmtInfoDao custLimitAmtInfoDao;

    @Override
    public List<CustLimitAmtInfoBo> enqrByExample(LcCustLimitAmtInfoQuery query) {
        return LcCustLimitAmtInfoDoCnvs.INSTANCE.cnvsDoListToBoList(custLimitAmtInfoDao.selectByExample(query));
    }
}
