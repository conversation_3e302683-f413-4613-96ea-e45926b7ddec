package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.intf.LcSingleLimitRuleBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.LcSingleLimitRuleBatchMapper;
import com.hsjry.core.limit.center.dal.dao.query.LcSingleLimitRuleQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcSingleLimitRuleDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSingleLimitRuleExample;
import com.hsjry.core.limit.center.dal.dao.model.LcSingleLimitRuleKeyDo;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 单一限额规则数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
@Repository
public class LcSingleLimitRuleBatchDaoImpl extends AbstractBaseDaoImpl<LcSingleLimitRuleDo, LcSingleLimitRuleBatchMapper>
    implements LcSingleLimitRuleBatchDao {
    /**
     * 分页查询
     *
     * @param lcSingleLimitRule 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LcSingleLimitRuleDo> selectPage(LcSingleLimitRuleQuery lcSingleLimitRule, PageParam pageParam) {
        LcSingleLimitRuleExample example = buildExample(lcSingleLimitRule);
        return PageHelper.<LcSingleLimitRuleDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询单一限额规则
     *
     * @param ruleId
     * @return
     */
    @Override
    public LcSingleLimitRuleDo selectByKey(String ruleId) {
        LcSingleLimitRuleKeyDo lcSingleLimitRuleKeyDo = new LcSingleLimitRuleKeyDo();
        lcSingleLimitRuleKeyDo.setRuleId(ruleId);
        lcSingleLimitRuleKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lcSingleLimitRuleKeyDo);
    }

    /**
     * 根据key删除单一限额规则
     *
     * @param ruleId
     * @return
     */
    @Override
    public int deleteByKey(String ruleId) {
        LcSingleLimitRuleKeyDo lcSingleLimitRuleKeyDo = new LcSingleLimitRuleKeyDo();
        lcSingleLimitRuleKeyDo.setRuleId(ruleId);
        lcSingleLimitRuleKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lcSingleLimitRuleKeyDo);
    }

    /**
     * 查询单一限额规则信息
     *
     * @param lcSingleLimitRule 条件
     * @return List<LcSingleLimitRuleDo>
     */
    @Override
    public List<LcSingleLimitRuleDo> selectByExample(LcSingleLimitRuleQuery lcSingleLimitRule) {
        return getMapper().selectByExample(buildExample(lcSingleLimitRule));
    }

    /**
     * 新增单一限额规则信息
     *
     * @param lcSingleLimitRule 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LcSingleLimitRuleDo lcSingleLimitRule) {
        if (lcSingleLimitRule == null) {
            return -1;
        }

        lcSingleLimitRule.setCreateTime(BusinessDateUtil.getDate());
        lcSingleLimitRule.setUpdateTime(BusinessDateUtil.getDate());
        lcSingleLimitRule.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lcSingleLimitRule);
    }

    /**
     * 修改单一限额规则信息
     *
     * @param lcSingleLimitRule
     * @return
     */
    @Override
    public int updateBySelective(LcSingleLimitRuleDo lcSingleLimitRule) {
        if (lcSingleLimitRule == null) {
            return -1;
        }
        lcSingleLimitRule.setUpdateTime(BusinessDateUtil.getDate());
        lcSingleLimitRule.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lcSingleLimitRule);
    }

    @Override
    public int updateBySelectiveByExample(LcSingleLimitRuleDo lcSingleLimitRule,
        LcSingleLimitRuleQuery lcSingleLimitRuleQuery) {
        lcSingleLimitRule.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lcSingleLimitRule, buildExample(lcSingleLimitRuleQuery));
    }

    @Override
    public LcSingleLimitRuleDo selectFirstOne(LcSingleLimitRuleQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectFirstOne(query);

    }

    @Override
    public Integer selectCountByCurrentGroup(LcSingleLimitRuleQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectCountByCurrentGroup(query);
    }

    @Override
    public List<LcSingleLimitRuleDo> selectShardList(LcSingleLimitRuleQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectShardList(query);
    }

    /**
     * 构建单一限额规则Example信息
     *
     * @param lcSingleLimitRule
     * @return
     */
    public LcSingleLimitRuleExample buildExample(LcSingleLimitRuleQuery lcSingleLimitRule) {
        LcSingleLimitRuleExample example = new LcSingleLimitRuleExample();
        LcSingleLimitRuleExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lcSingleLimitRule != null) {
            //添加查询条件
            if (null != lcSingleLimitRule.getPreOccupyAmount()) {
                criteria.andPreOccupyAmountEqualTo(lcSingleLimitRule.getPreOccupyAmount());
            }
            if (null != lcSingleLimitRule.getUsedAmount()) {
                criteria.andUsedAmountEqualTo(lcSingleLimitRule.getUsedAmount());
            }
            if (StringUtil.isNotEmpty(lcSingleLimitRule.getUseStage())) {
                criteria.andUseStageEqualTo(lcSingleLimitRule.getUseStage());
            }
            if (StringUtil.isNotEmpty(lcSingleLimitRule.getUsageType())) {
                criteria.andUsageTypeEqualTo(lcSingleLimitRule.getUsageType());
            }
            if (null != lcSingleLimitRule.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lcSingleLimitRule.getTotalAmount());
            }
            if (StringUtil.isNotEmpty(lcSingleLimitRule.getRuleName())) {
                criteria.andRuleNameEqualTo(lcSingleLimitRule.getRuleName());
            }
            if (StringUtil.isNotEmpty(lcSingleLimitRule.getRuleId())) {
                criteria.andRuleIdEqualTo(lcSingleLimitRule.getRuleId());
            }
            if (StringUtil.isNotEmpty(lcSingleLimitRule.getCheckStrategy())) {
                criteria.andCheckStrategyEqualTo(lcSingleLimitRule.getCheckStrategy());
            }
            if (StringUtil.isNotEmpty(lcSingleLimitRule.getLimitTemplateId())) {
                criteria.andLimitTemplateIdEqualTo(lcSingleLimitRule.getLimitTemplateId());
            }
            if (StringUtil.isNotEmpty(lcSingleLimitRule.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lcSingleLimitRule.getLimitStatus());
            }
            if (StringUtil.isNotEmpty(lcSingleLimitRule.getLimitObjectType())) {
                criteria.andLimitObjectTypeEqualTo(lcSingleLimitRule.getLimitObjectType());
            }
            if (StringUtil.isNotEmpty(lcSingleLimitRule.getLimitObjectId())) {
                criteria.andLimitObjectIdEqualTo(lcSingleLimitRule.getLimitObjectId());
            }
            if (null != lcSingleLimitRule.getEffectiveStartTimeBegin()) {
                criteria.andEffectiveStartTimeGreaterThanOrEqualTo(lcSingleLimitRule.getEffectiveStartTimeBegin());
            }
            if (null != lcSingleLimitRule.getEffectiveStartTimeEnd()) {
                criteria.andEffectiveStartTimeLessThanOrEqualTo(lcSingleLimitRule.getEffectiveStartTimeEnd());
            }
            if (null != lcSingleLimitRule.getEffectiveEndTimeInt()) {
                criteria.andEffectiveEndTimeIntEqualTo(lcSingleLimitRule.getEffectiveEndTimeInt());
            }
            if (null != lcSingleLimitRule.getEffectiveEndTimeBegin()) {
                criteria.andEffectiveEndTimeGreaterThanOrEqualTo(lcSingleLimitRule.getEffectiveEndTimeBegin());
            }
            if (null != lcSingleLimitRule.getEffectiveEndTimeEnd()) {
                criteria.andEffectiveEndTimeLessThanOrEqualTo(lcSingleLimitRule.getEffectiveEndTimeEnd());
            }
            if (StringUtil.isNotEmpty(lcSingleLimitRule.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lcSingleLimitRule.getCustLimitId());
            }
        }
        buildExampleExt(lcSingleLimitRule, criteria);
        return example;
    }

    /**
     * 构建单一限额规则ExampleExt方法
     *
     * @param lcSingleLimitRule
     * @return
     */
    public void buildExampleExt(LcSingleLimitRuleQuery lcSingleLimitRule, LcSingleLimitRuleExample.Criteria criteria) {

        //自定义实现
    }

}
