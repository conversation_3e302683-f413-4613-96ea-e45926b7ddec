package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitOperateSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitOperateSerialQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度操作流水数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-01-04 07:07:04
 */
public interface LcCustLimitOperateSerialDao extends IBaseDao<LcCustLimitOperateSerialDo> {
    /**
     * 分页查询额度操作流水
     *
     * @param lcCustLimitOperateSerialQuery 条件
     * @return PageInfo<LcCustLimitOperateSerialDo>
     */
    PageInfo<LcCustLimitOperateSerialDo> selectPage(LcCustLimitOperateSerialQuery lcCustLimitOperateSerialQuery,
        PageParam pageParam);

    /**
     * 根据key查询额度操作流水
     *
     * @param closSerialNo
     * @return
     */
    LcCustLimitOperateSerialDo selectByKey(String closSerialNo);

    /**
     * 根据key删除额度操作流水
     *
     * @param closSerialNo
     * @return
     */
    int deleteByKey(String closSerialNo);

    /**
     * 查询额度操作流水信息
     *
     * @param lcCustLimitOperateSerialQuery 条件
     * @return List<LcCustLimitOperateSerialDo>
     */
    List<LcCustLimitOperateSerialDo> selectByExample(LcCustLimitOperateSerialQuery lcCustLimitOperateSerialQuery);

    /**
     * 查询额度操作流水信息
     */
    List<LcCustLimitOperateSerialDo> selectOtherSubCustLimitperateSerial(List<String> custLimitIdLis, String status,
        List<String> operateTypeList, String limitObjectId, String limitObjectIdExclude);

    /**
     * 查询额度操作流水信息-关联额度列表查询
     *
     * @param lcCustLimitOperateSerialQuery 条件
     * @return List<LcCustLimitOperateSerialDo>
     */
    List<LcCustLimitOperateSerialDo> selectByCustLimit(LcCustLimitOperateSerialQuery lcCustLimitOperateSerialQuery);

    /**
     * 新增额度操作流水信息
     *
     * @param lcCustLimitOperateSerial 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitOperateSerialDo lcCustLimitOperateSerial);

    /**
     * 修改额度操作流水信息
     *
     * @param lcCustLimitOperateSerial
     * @return
     */
    int updateBySelective(LcCustLimitOperateSerialDo lcCustLimitOperateSerial);

    /**
     * 修改额度操作流水信息
     *
     * @param lcCustLimitOperateSerial
     * @param lcCustLimitOperateSerialQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitOperateSerialDo lcCustLimitOperateSerial,
        LcCustLimitOperateSerialQuery lcCustLimitOperateSerialQuery);

    /**
     * 删除额度操作流水信息
     *
     * @param lcCustLimitOperateSerialQuery 条件
     * @return List<LcCustLimitOperateSerialDo>
     */
    int deleteByExample(LcCustLimitOperateSerialQuery lcCustLimitOperateSerialQuery);

    /**
     * 从对象信息表同步客户编号到操作流水表
     *
     * @return int
     */
    int updateCustNo();
}
