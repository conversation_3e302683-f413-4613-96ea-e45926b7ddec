package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度关联历史Do
 *
 * <AUTHOR>
 * @date 2023-11-13 01:05:13
 */
@Table(name = "lc_cust_limit_relation_his")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitRelationHisDo extends LcCustLimitRelationHisKeyDo implements Serializable {
    private static final long serialVersionUID = 1723869594676035585L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 当前节点额度编号 */
    @Column(name = "current_node_limit_id")
    private String currentNodeLimitId;
    /** 父节点额度编号 */
    @Column(name = "parent_node_limit_id")
    private String parentNodeLimitId;
    /** 关系类型;关联关系不校验体系，EnumLimitRelationType:001-默认、002-关联 */
    @Column(name = "limit_relation_type")
    private String limitRelationType;

    /** 客户编号 */
    @Column(name = "cust_no")
    private String custNo;
}
