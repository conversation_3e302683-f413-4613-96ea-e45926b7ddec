<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbTElcblIbnkProdLmtInfoMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbTElcblIbnkProdLmtInfoDo">
        <result property="custLimitId" column="cust_limit_id" jdbcType="VARCHAR"/> <!-- 额度编号 -->
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/> <!-- 更新时间 -->
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/> <!-- 创建时间 -->
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/> <!-- 租户号 -->
        <result property="ownOrganId" column="own_organ_id" jdbcType="VARCHAR"/> <!-- 所属组织编号 -->
        <result property="operatorId" column="operator_id" jdbcType="VARCHAR"/> <!-- 操作人编号 -->
        <result property="coreInstNo" column="core_inst_no" jdbcType="VARCHAR"/> <!-- 核心机构号 -->
        <result property="realOccupyAmount" column="real_occupy_amount" jdbcType="DECIMAL"/> <!-- 额度实例金额信息中实占额度 -->
        <result property="limitStatus" column="limit_status" jdbcType="VARCHAR"/> <!-- 额度状态 -->
        <result property="custNo" column="cust_no" jdbcType="VARCHAR"/> <!-- 客户编号 -->
        <result property="useOccupyAmount" column="use_occupy_amount" jdbcType="DECIMAL"/> <!-- 已占用额度 -->
        <result property="availableAmount" column="available_amount" jdbcType="DECIMAL"/> <!-- 可用额度 -->
        <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/> <!-- 总额度 -->
        <result property="certNo" column="cert_no" jdbcType="VARCHAR"/> <!-- 证件号码 -->
        <result property="certTyp" column="cert_typ" jdbcType="VARCHAR"/> <!-- 证件类型 -->
        <result property="custNm" column="cust_nm" jdbcType="VARCHAR"/> <!-- 客户名称 -->
        <result property="custTyp" column="cust_typ" jdbcType="VARCHAR"/> <!-- 客户类型 -->
    </resultMap>
    <sql id="Base_Column_List">
        cust_limit_id
        , update_time
                , create_time
                , tenant_id
                , own_organ_id
                , operator_id
                , core_inst_no
                , real_occupy_amount
                , limit_status
                , cust_no
                , use_occupy_amount
                , available_amount
                , total_amount
                , cert_no
                , cert_typ
                , cust_nm
                , cust_typ
    </sql>

    <!-- 将电票同业客户额度同步数据插入到产品层额度信息表 -->
    <insert id="insertElcblIbnkProdLmtInfo" parameterType="java.util.Map">
        insert into lb_t_elcbl_ibnk_prod_lmt_info(
        cust_no, cust_typ, cust_nm, cert_typ, cert_no, total_amount, available_amount,
        use_occupy_amount, cust_limit_id, limit_status, real_occupy_amount,
        core_inst_no, operator_id, own_organ_id, tenant_id, create_time, update_time
        )
        select ltici.CUST_NO,
        ltici.CUST_TYP,
        ltici.CUST_NM,
        ltici.cert_typ,
        ltici.cert_no,
        lseils.total_amount,
        lseils.available_amount,
        lseils.use_occupy_amount,
        lcli.CUST_LIMIT_ID,
        lcli.limit_status,
        lseils.available_amount,
        lseils.CORE_INST_NO,
        lcli.operator_id,
        lcli.own_organ_id,
        lcli.tenant_id,
        sysdate,
        sysdate
        from lb_s_elcbl_ibnk_lmt_synz lseils
        inner join LB_T_IBNK_CUST_INFO ltici on lseils.IBNK_USER_CERTIFICATE_KIND = ltici.CERT_TYP and
        lseils.IBNK_USER_CERTIFICATE_NO = ltici.CERT_NO
        inner join LC_CUST_LIMIT_INFO lcli on lcli.LIMIT_OBJECT_ID = ltici.CUST_NO
        where lcli.TEMPLATE_NODE_ID in
        <if test="templateNodeIdList != null and templateNodeIdList.size() > 0">
            <foreach collection="templateNodeIdList" item="templateNodeId" open="(" separator="," close=")">
                #{templateNodeId}
            </foreach>
        </if>
        <if test="templateNodeIdList == null or templateNodeIdList.size() == 0">
            (null)
        </if>
        and lcli.CUST_LIMIT_ID in
        <if test="custLimitIdList != null and custLimitIdList.size() > 0">
            <foreach collection="custLimitIdList" item="custLimitId" open="(" separator="," close=")">
                #{custLimitId}
            </foreach>
        </if>
        <if test="custLimitIdList == null or custLimitIdList.size() == 0">
            (null)
        </if>
    </insert>

    <!-- 更新额度实例金额信息中的实占额度 -->
    <update id="updateRealOccupyAmount" parameterType="java.util.Map">
        MERGE INTO LC_CUST_LIMIT_AMT_INFO lclai
        USING lb_t_elcbl_ibnk_prod_lmt_info lteipli
        ON (lclai.CUST_LIMIT_ID = lteipli.CUST_LIMIT_ID AND lclai.CUST_NO = lteipli.CUST_NO)
        WHEN MATCHED THEN
        UPDATE
        SET lclai.REAL_OCCUPY_AMOUNT = lteipli.REAL_OCCUPY_AMOUNT
        WHERE lclai.CUST_LIMIT_ID IN
        <if test="custLimitIdList != null and custLimitIdList.size() > 0">
            <foreach collection="custLimitIdList" item="custLimitId" open="(" separator="," close=")">
                #{custLimitId}
            </foreach>
        </if>
        <if test="custLimitIdList == null or custLimitIdList.size() == 0">
            (null)
        </if>
    </update>
</mapper>