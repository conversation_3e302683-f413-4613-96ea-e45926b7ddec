/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitObjectInfoQuery;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/17 9:58
 */
public interface CustLimitObjectInfoBatchMapper extends CommonMapper<LcCustLimitObjectInfoDo> {
    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitObjectInfoDo> selectShardList(@Param("query") CustLimitObjectInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitObjectInfoDo selectFirstOne(@Param("query") CustLimitObjectInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(@Param("query") CustLimitObjectInfoQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitObjectInfoDo> selectObjectShardList(@Param("query") CustLimitObjectInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitObjectInfoDo selectObjectFirstOne(@Param("query") CustLimitObjectInfoQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitObjectInfoDo> selectExpireShardList(@Param("query") CustLimitObjectInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitObjectInfoDo selectExpireFirstOne(@Param("query") CustLimitObjectInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectObjectCountByCurrentGroup(@Param("query") CustLimitObjectInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectExpireCountByCurrentGroup(@Param("query") CustLimitObjectInfoQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitObjectInfoDo> selectNotUsedShardList(@Param("query") CustLimitObjectInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitObjectInfoDo selectNotUsedFirstOne(@Param("query") CustLimitObjectInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNotUsedCountByCurrentGroup(@Param("query") CustLimitObjectInfoQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcCustLimitObjectInfoDo> selectNodeShardList(@Param("query") CustLimitObjectInfoQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcCustLimitObjectInfoDo selectNodeFirstOne(@Param("query") CustLimitObjectInfoQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectNodeCountByCurrentGroup(@Param("query") CustLimitObjectInfoQuery query);

    List<LcCustLimitObjectInfoDo> selectExpireLimitInfoByObjectId(@Param("query") CustLimitObjectInfoQuery query);
}
