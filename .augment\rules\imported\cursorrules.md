---
type: "manual"
---

# Java 批处理项目开发规范

## 项目概述
这是一个基于Spring Boot的Java批处理项目，主要用于限额管理系统的批量处理任务。项目采用分层架构，包含多个Maven模块。

## 代码风格规范

### 1. 命名规范
- **类名**: 使用PascalCase，如 `AmtLimitBizImpl`
- **方法名**: 使用camelCase，如 `amtLimitValid`
- **常量**: 使用UPPER_SNAKE_CASE，如 `AMT_LIMIT_DETAIL_SHARD_NUM`
- **包名**: 使用小写字母，如 `com.hsjry.core.limit.batch.biz`
- **变量名**: 使用camelCase，如 `amtLimitRecordBatchDao`

### 2. 注释规范
- 所有类必须有JavaDoc注释，包含作者、版本、创建时间
- 重要方法必须有详细的JavaDoc注释
- 复杂业务逻辑需要添加行内注释
- 注释使用中文，便于团队理解

### 3. 代码格式
- 缩进使用4个空格
- 每行代码不超过120个字符
- 方法之间空一行
- 类之间空两行
- 导入语句按字母顺序排列

## 架构规范

### 1. 分层架构
```
controller/     - 控制器层，处理RPC调用
facade/         - 外观层，定义接口
biz/           - 业务接口层
biz-impl/      - 业务实现层
core/          - 核心逻辑层
core-impl/     - 核心实现层
dal/           - 数据访问接口层
dal-impl/      - 数据访问实现层
common/        - 公共组件层
deploy/        - 部署启动层
test/          - 测试层
```

### 2. 依赖注入规范
- 使用 `@Autowired` 注解进行依赖注入
- 对于有多个实现的情况，使用 `@Qualifier` 指定具体实现
- 优先使用构造器注入，其次使用字段注入

### 3. 事务管理
- 使用 `@Transactional` 注解管理事务
- 对于复杂事务，使用 `TransactionTemplate`
- 事务方法名以 `handle` 或 `process` 结尾

## 批处理开发规范

### 1. 分片处理
- 大数据量处理必须使用分片机制
- 分片大小通过常量定义，如 `BATCH_SIZE`
- 使用 `PageInfo` 和 `PageParam` 进行分页查询

### 2. 异常处理
- 使用 `try-catch` 包装关键业务逻辑
- 记录详细的错误日志
- 对于可恢复的错误，继续处理其他数据
- 对于致命错误，及时抛出异常

### 3. 性能优化
- 使用批量操作减少数据库交互
- 合理使用索引优化查询性能
- 避免在循环中进行数据库操作
- 使用连接池管理数据库连接

## 数据库规范

### 1. 命名规范
- 表名使用下划线分隔，如 `lb_h_ccs_acct`
- 字段名使用下划线分隔，如 `record_id`
- 主键统一使用 `id` 或业务主键
- 时间字段使用 `create_time`、`update_time` 等

### 2. 查询规范
- 使用MyBatis进行数据访问
- 复杂查询使用XML映射文件
- 查询结果使用DO（Data Object）对象
- 分页查询必须使用 `PageInfo`

## 测试规范

### 1. 单元测试
- 每个业务类都要有对应的测试类
- 测试方法名以 `test` 开头
- 使用 `@Test` 注解标记测试方法
- 测试数据使用独立的测试文件

### 2. 集成测试
- 继承 `AbstractBaseShardingTest` 进行分片测试
- 使用 `@Autowired` 注入测试依赖
- 测试完成后清理测试数据

## 日志规范

### 1. 日志级别
- `ERROR`: 系统错误和异常
- `WARN`: 警告信息
- `INFO`: 重要业务信息
- `DEBUG`: 调试信息

### 2. 日志格式
- 使用 `@Slf4j` 注解
- 日志信息使用中文
- 包含关键业务参数
- 使用占位符而不是字符串拼接

### 3. 日志标点与占位符规范
- 日志中的逗号请使用英文逗号 `,`
- 日志中的冒号请使用英文冒号 `:`
- 日志占位符请使用 `[{}]`，而不是 `{}`
- 示例：

```java
log.info(prefixLog + "关系列表转换完成,关系列表大小:[{}]", relationBoList.size());
```

## 配置管理

### 1. 配置文件
- 使用 `bootstrap.yml` 进行基础配置
- 使用Apollo进行配置中心管理
- 敏感信息使用加密配置

### 2. 常量管理
- 业务常量定义在 `constants` 包下
- 枚举类型定义在 `enums` 包下
- 避免硬编码，使用常量替代

## 代码审查要点

### 1. 功能完整性
- 检查业务逻辑是否完整
- 验证异常处理是否充分
- 确认事务边界是否合理

### 2. 性能考虑
- 检查是否有性能瓶颈
- 验证分片策略是否合理
- 确认数据库查询是否优化

### 3. 代码质量
- 检查代码可读性
- 验证命名是否规范
- 确认注释是否充分

## 部署规范

### 1. 启动类
- 使用 `@SpringBootApplication` 注解
- 配置包扫描范围
- 启用Apollo配置中心

### 2. 依赖管理
- 使用Maven管理依赖
- 版本号统一管理
- 避免依赖冲突

## 开发流程

### 1. 需求分析
- 理解业务需求
- 设计数据模型
- 规划处理流程

### 2. 代码实现
- 按照分层架构实现
- 遵循命名规范
- 添加必要注释

### 3. 测试验证
- 编写单元测试
- 进行集成测试
- 验证性能指标

### 4. 代码审查
- 提交代码审查
- 根据反馈修改
- 最终合并代码

## 注意事项

1. **数据一致性**: 批处理涉及大量数据，必须保证数据一致性
2. **性能监控**: 关注处理时间和资源消耗
3. **错误恢复**: 设计合理的错误恢复机制
4. **数据备份**: 重要操作前进行数据备份
5. **监控告警**: 设置适当的监控和告警机制

## 最佳实践

1. **渐进式开发**: 先实现核心功能，再优化性能
2. **代码复用**: 提取公共组件，避免重复代码
3. **文档维护**: 及时更新技术文档
4. **知识分享**: 定期进行技术分享和代码评审 

## 空值与对象比较规范

- 判断对象非空请使用 `Objects.nonNull(x)`，不要直接用 `x != null`
- 判断对象为空请使用 `Objects.isNull(x)`，不要直接用 `x == null`
- 判断两个对象不相等请使用 `ObjectUtils.notEqual(a, b)`，不要直接用 `!a.equals(b)`
- 判断两个对象相等仍可用 `Objects.equals(a, b)`
- 需确保已引入 `java.util.Objects` 和 `org.apache.commons.lang3.ObjectUtils`

示例：

```java
if (Objects.nonNull(obj)) { ... }
if (Objects.isNull(obj)) { ... }
if (ObjectUtils.notEqual(a, b)) { ... }
if (Objects.equals(a, b)) { ... }
``` 

## 常用工具类最佳实践

### 1. Google Guava 常用工具类

#### 1.1 集合创建与操作
- **Lists**
  ```java
  Lists.newArrayList()
  Lists.newArrayList(1, 2, 3)
  Lists.newLinkedList()
  Lists.newArrayListWithCapacity(100)
  Lists.newArrayListWithExpectedSize(50)
  Lists.partition(list, 3) // 按大小分割
  Lists.reverse(list)      // 反转
  ```
- **Sets**
  ```java
  Sets.newHashSet()
  Sets.newHashSet(1, 2, 3)
  Sets.newLinkedHashSet()
  Sets.newTreeSet()
  Sets.union(set1, set2)         // 并集
  Sets.intersection(set1, set2)  // 交集
  Sets.difference(set1, set2)    // 差集
  Sets.cartesianProduct(set1, set2) // 笛卡尔积
  ```
- **Maps**
  ```java
  Maps.newHashMap()
  Maps.newLinkedHashMap()
  Maps.newTreeMap()
  Maps.newConcurrentMap()
  Maps.transformValues(map, function)
  Maps.filterKeys(map, predicate)
  Maps.filterValues(map, predicate)
  ```
- **不可变集合**
  ```java
  ImmutableList.of(1, 2, 3)
  ImmutableList.copyOf(collection)
  ImmutableList.builder().add(1).add(2).build()
  ImmutableSet.of("a", "b", "c")
  ImmutableSet.copyOf(collection)
  ImmutableMap.of("k1", "v1", "k2", "v2")
  ImmutableMap.builder().put("k1", "v1").build()
  ```
- **多重集合**
  - Multiset（允许重复元素的Set）
    ```java
    Multiset<String> multiset = HashMultiset.create();
    multiset.count("apple");
    multiset.setCount("apple", 5);
    ```
  - Multimap（一个key对应多个value）
    ```java
    Multimap<String, String> multimap = ArrayListMultimap.create();
    multimap.put("fruits", "apple");
    multimap.put("fruits", "banana");
    multimap.get("fruits");
    ```
  - BiMap（双向Map）
    ```java
    BiMap<String, Integer> biMap = HashBiMap.create();
    biMap.put("one", 1);
    biMap.inverse().get(1);
    ```
- **Iterables 工具类**
  ```java
  Iterables.filter(iterable, predicate)
  Iterables.transform(iterable, function)
  Iterables.find(iterable, predicate)
  Iterables.any(iterable, predicate)
  Iterables.all(iterable, predicate)
  Iterables.concat(iterable1, iterable2)
  ```
- **Range 范围操作**
  ```java
  Range<Integer> range = Range.closed(1, 10);
  Range<Integer> range2 = Range.open(1, 10);
  range.contains(5);
  range.intersection(range2);
  ```
- **缓存工具**
  ```java
  Cache<String, String> cache = CacheBuilder.newBuilder()
      .maximumSize(1000)
      .expireAfterWrite(10, TimeUnit.MINUTES)
      .build();
  ```

### 2. Spring Framework 常用工具类
- **CollectionUtils**
  ```java
  CollectionUtils.isEmpty(collection)
  CollectionUtils.isNotEmpty(collection)
  CollectionUtils.containsInstance(collection, element)
  CollectionUtils.mergeArrayIntoCollection(array, collection)
  CollectionUtils.mergePropertiesIntoMap(props, map)
  ```
- **StringUtils**
  ```java
  StringUtils.hasText(str)
  StringUtils.hasLength(str)
  StringUtils.trimWhitespace(str)
  StringUtils.delete(str, pattern)
  StringUtils.replace(str, old, new)
  // Java 8 友好
  Optional<String> result = Optional.ofNullable(str)
      .filter(StringUtils::hasText)
      .map(StringUtils::trimWhitespace);
  ```

### 3. ObjectUtils 使用顺序与规范
#### 3.1 首选：`java.util.Objects`（JDK原生）
```java
Objects.isNull(obj)
Objects.nonNull(obj)
Objects.equals(obj1, obj2)
Objects.deepEquals(obj1, obj2)
Objects.hash(obj1, obj2, obj3)
Objects.toString(obj, "default")
Objects.requireNonNull(obj)
Objects.requireNonNull(obj, "msg")
// Java 8 流式处理
Optional.ofNullable(obj)
    .filter(Objects::nonNull)
    .map(Objects::toString)
    .orElse("default");
list.stream()
    .filter(Objects::nonNull)
    .map(obj -> Objects.toString(obj, ""))
    .collect(Collectors.toList());
```
#### 3.2 其次：`org.springframework.util.ObjectUtils`
```java
ObjectUtils.isEmpty(obj)
ObjectUtils.nullSafeEquals(obj1, obj2)
ObjectUtils.nullSafeHashCode(obj)
ObjectUtils.nullSafeToString(obj)
ObjectUtils.identityToString(obj)
ObjectUtils.getDisplayString(obj)
ObjectUtils.isArray(obj)
ObjectUtils.addObjectToArray(array, obj)
ObjectUtils.containsElement(array, element)
// Java 8 友好
Optional.ofNullable(obj)
    .filter(o -> !ObjectUtils.isEmpty(o))
    .map(ObjectUtils::nullSafeToString)
    .ifPresent(System.out::println);
```
#### 3.3 最后：`org.apache.commons.lang3.ObjectUtils`
```java
ObjectUtils.defaultIfNull(obj, defaultValue)
ObjectUtils.firstNonNull(obj1, obj2, obj3)
ObjectUtils.anyNotNull(obj1, obj2, obj3)
ObjectUtils.allNotNull(obj1, obj2, obj3)
ObjectUtils.compare(obj1, obj2)
ObjectUtils.max(obj1, obj2, obj3)
ObjectUtils.min(obj1, obj2, obj3)
ObjectUtils.median(obj1, obj2, obj3)
ObjectUtils.clone(obj)
ObjectUtils.cloneIfPossible(obj)
Stream.of(obj1, obj2, obj3)
    .filter(ObjectUtils::anyNotNull)
    .map(obj -> ObjectUtils.defaultIfNull(obj, "default"))
    .collect(Collectors.toList());
```

- 优先使用JDK原生`Objects`，其次Spring的`ObjectUtils`，最后才用Apache的`ObjectUtils`，除非有特殊功能需求。
- 代码生成和团队开发时，务必参考本规范，提升代码质量和一致性。 

- **List相关变量**: 命名统一以`xxxList`结尾，如 `userList`、`orderList`
- **Map相关变量**: 命名统一以`xxxMap`结尾，如 `userMap`、`configMap` 

## MapStruct对象映射方法命名规范

- 数据库对象统一用Do结尾，方法名用do开头
- 常见命名风格：
  - Data → Do：`data2Do`
  - Do → Data：`do2Data`
  - Dto → Do：`dtoToDo`
  - Do → Dto：`do2Dto`
  - Bo → Do：`boToDo`
  - Do → Bo：`doToBo`
  - Copy相关：`do2Copy`、`copy2Do`
- 批量转换：
  - `dataListToDoList`
  - `doListToDataList`
  - `dtoListToDoList`
  - `doListToDtoList`
  - `boListToDoList`
  - `doListToBoList`
- 示例：
  ```java
  UserDo data2Do(UserData data);
  UserData do2Data(UserDo doObj);
  UserDo dtoToDo(UserDto dto);
  UserDto do2Dto(UserDo doObj);
  List<UserDo> dataListToDoList(List<UserData> dataList);
  List<UserData> doListToDataList(List<UserDo> doList);
  ```
- 说明：后续所有MapStruct映射方法命名均沿用上述风格，保持与现有项目一致。 

- 方法参数涉及数据库对象（Do）时，参数名统一为`model`，批量为`modelList`
- 示例：
  ```java
  UserData do2Data(UserDo model);
  UserDto do2Dto(UserDo model);
  List<UserData> doListToDataList(List<UserDo> modelList);
  ``` 

## 代码长度与可读性规范

- 每个方法建议控制在80行以内，便于阅读和维护。
- 避免将一个业务逻辑拆分成过多碎片化的小方法，导致代码跳转和阅读负担加重。
- 生成代码时，需附带简明的代码说明（如方法/类注释、关键逻辑注释），帮助理解整体业务。
- 如遇复杂业务，优先保证可读性和整体性，适当分段但不强制极端拆分。 

# 代码风格与判空最佳实践

1. 列表、对象判空优先使用 Optional
   - 推荐用 Optional.ofNullable(obj).ifPresent(...) 或 Optional.ofNullable(list).orElse(Collections.emptyList()) 等方式，提升代码健壮性和可读性。
   - 示例：
     ```java
     Optional.ofNullable(userList).ifPresent(list -> list.forEach(...));
     List<String> safeList = Optional.ofNullable(list).orElse(Collections.emptyList());
     ```

2. 对象属性判断优先使用 Objects 工具类
   - 判断对象属性是否为空，优先用 Objects.isNull(x)、Objects.nonNull(x)、Objects.equals(a, b) 等，避免直接 x == null。
   - 示例：
     ```java
     if (Objects.nonNull(user.getName())) { ... }
     if (Objects.isNull(order.getId())) { ... }
     if (Objects.equals(a.getId(), b.getId())) { ... }
     ```

3. 禁止冗余全限定名
   - 已 import 的类，直接用类名（如 Optional、Objects），不要写全限定名（如 java.util.Optional），保持代码简洁。 

4. 集合判空、判非空推荐使用 Apache Commons Collections4 的 CollectionUtils.isEmpty(list) 和 CollectionUtils.isNotEmpty(list)，代码更直观、可读性更好。
   - 示例：
     ```java
     import org.apache.commons.collections4.CollectionUtils;
     if (CollectionUtils.isNotEmpty(list)) { ... }
     if (CollectionUtils.isEmpty(list)) { ... }
     ```
   - 不推荐用 Spring 的 CollectionUtils，因为没有 isNotEmpty 方法。 