/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.core.limit.batch.biz.convert.file.LbHCcsAcctConverter;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.biz.entity.LbHCcsAcctData;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.AbstractFileBaseShardingPrepareBizImpl;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.constants.CharsetConstants;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.dal.dao.intf.LbHCcsAcctDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCcsAcctDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.sequence.SequenceTool;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
/**
 * 信用卡-历史表-第一币种贷记帐户文件同步实现类
 *
 * <AUTHOR>
 * @version V4.0.1
 * @since 4.0.1 2025/1/21
 */
@Slf4j
@Service("lbHCcsAcctFileSyncImpl")
@RequiredArgsConstructor
public class LbHCcsAcctFileSyncImpl extends AbstractFileBaseShardingPrepareBizImpl<LbHCcsAcctData> {

    /** 最小字段数量 */
    private static final int MIN_FIELD_COUNT = 286;
    /** 分隔符 */
    private static final String FIELD_SEPARATOR = "\\|\\+\\|";
    /** 批处理大小 */
    private static final int BATCH_SIZE = 1000;
    /** 并行处理阈值 */
    private static final int PARALLEL_THRESHOLD = 1000;
    private final String SEQUENCE_NO = "SEQUENCE_NO";

    private final LbHCcsAcctDao lbHCcsAcctDao;
    @Value("${project.ccs.acct.filename:h_ccs_acct_[DATE].csv}")
    private String fileName;
    @Value("${project.ccs.acct.remoteFilePath:/nfsdata/file/midd/CCS/ACCT/data/[DATE]/}")
    private String remoteFilePathDefine;

    /**
     * 获取任务交易码
     */
    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.H_CCS_ACCT;
    }

    /**
     * 生成分片任务
     * 实现文件下载、分片处理的完整流程
     */
    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        JSONObject param = JSON.parseObject(jobInitDto.getInPara());
        //设置流水ID,解决多次从redis获取唯一值的性能问题
        param.put(SEQUENCE_NO, SequenceTool.nextId());
        //更新jobInitDto的inpara参数
        jobInitDto.setInPara(param.toJSONString());

        // 确保fixNum被正确设置
        if (jobInitDto.getFixNum() == null) {
            log.warn(prefixLog + "fixNum为null,设置默认值1000");
            jobInitDto.setFixNum(1000);
        }

        List<JobShared> sharedList = Lists.newArrayList();
        log.info(prefixLog + "[{}]文件处理", jobTradeDesc);
        String localFilePath = FileShardingUtils.getLocalFilePath(jobInitDto.getBusinessDate(),
            remoteFilePathDefine + FileShardingUtils.ACCT_DATE_CODE_MARK + File.separator);
        Integer acctDate = jobInitDto.getBusinessDate();
        String localFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK, String.valueOf(acctDate));
        log.info(prefixLog + "转换后的本地文件路径: [{}]", localFilePath);
        try {
            String fileAttr = FIELD_SEPARATOR;
            String filePath = localFilePath + localFileName;
            log.info("实际查找的文件路径: [{}]", filePath);

            // 检查目录是否存在
            File directory = new File(localFilePath);
            if (!directory.exists()) {
                log.info(prefixLog + "目录[{}]不存在,尝试创建", localFilePath);
                directory.mkdirs();
            } else {
                log.info(prefixLog + "目录[{}]已存在", localFilePath);
                // 列出目录中的文件
                File[] files = directory.listFiles();
                if (files != null && files.length > 0) {
                    log.info(prefixLog + "目录[{}]中的文件列表:", localFilePath);
                    for (File f : files) {
                        log.info(prefixLog + " - {}", f.getName());
                    }
                } else {
                    log.info(prefixLog + "目录[{}]为空", localFilePath);
                }
            }

            File localFile = new File(filePath);
            log.info(prefixLog + "检查文件[{}]是否存在: {}", filePath, localFile.exists());

            //判断本地文件是否存在，不存在则直接报错
            if(!localFile.exists()){
                log.error(prefixLog + "本地文件[{}]不存在,请确认文件路径是否正确", filePath);
                String errorCode = EnumBatchJobError.FILE_PATH_NOT_EXIST.getCode();
                String errorMsg = EnumBatchJobError.FILE_PATH_NOT_EXIST.getDescription();
                throw new HsjryBizException(errorCode, errorMsg);
            }

            // 文件存在，直接进行分片处理
            log.info(prefixLog + "[{}]开始[{}]文件数据分片处理", jobTradeDesc, filePath);
            log.info(prefixLog + "分片参数: fixNum=[{}], fileAttr=[{}], skipFirst=[{}]",
                jobInitDto.getFixNum(), fileAttr, skipFirst());

            List<JobShared> jobShareds = FileShardingUtils.getFileSharedData(jobInitDto, localFile, fileAttr,
                skipFirst());
            log.info(prefixLog + "[{}]结束[{}]文件数据分片当前分片数[{}]", jobTradeDesc, filePath,
                jobShareds.size());
            sharedList.addAll(jobShareds);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }
        return sharedList;
    }

    @Override
    public ShardingResult<LbHCcsAcctData> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始文件数据分片查询[{}]", GsonUtil.objToStrForLog(jobShared));

        ShardingResult<LbHCcsAcctData> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        try {
            // 读取文件分片数据
            FileLineData fileLineData = GsonUtil.json2Obj(jobShared.getExtParam(), FileLineData.class);
            log.info(prefixLog + "开始读取文件数据分片[{}]", GsonUtil.objToStrForLog(fileLineData));
            List<String> originData = FileShardingUtils.readFileSharedData(jobShared, skipFirst(), CharsetConstants.GBK);

            // 使用并行流处理数据，提升性能
            List<LbHCcsAcctData> fileDataList = processOriginDataParallel(originData, prefixLog);

            log.info(prefixLog + "读取文件数据分片总量[{}]", fileDataList.size());
            shardingResult.setShardingResultList(fileDataList);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        jobShared.setBatchSerialNo(inParam.getString(SEQUENCE_NO));
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbHCcsAcctData> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
        List<LbHCcsAcctData> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "历史信用卡账户数据文件处理:文件分片数据为空,执行中断。");
            return;
        }

        log.info(prefixLog + "历史信用卡账户数据文件处理:开始执行分片数据操作,数据量:[{}]", dataList.size());

        // 设置业务日期
        String dataDateStr = String.valueOf(businessDate);
        dataList.forEach(data -> data.setDataDate(dataDateStr));
        // 检查是否是第一个分片，如果是则删除当前业务日期的数据
        if (sliceBatchSerialDo.getBatchNum() == 1) {
            log.info(prefixLog + "第一个分片,删除目标表 lb_h_ccs_acct 中业务日期[{}]的数据", dataDateStr);
            lbHCcsAcctDao.deleteByDataDate(dataDateStr);
        }

        // 确保分片流水对象的状态字段不为null
        if (sliceBatchSerialDo.getSharedStatus() == null) {
            log.debug(prefixLog + "分片流水状态为null,设置为处理中状态");
            sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
        }

        // 使用并行流进行数据转换和验证
        List<LbHCcsAcctDo> insertList = dataList.parallelStream().map(LbHCcsAcctConverter::data2Do)//
            .filter(this::validateData).collect(Collectors.toCollection(ArrayList::new));

        if (CollectionUtil.isNotEmpty(insertList)) {
            // 批量插入数据
            processBatchInsert(insertList, prefixLog);
            log.info(prefixLog + "插入[{}]条历史信用卡账户数据", insertList.size());
        }

        // 更新分片流水成功
        normalUpdateSliceSerial(dataList.size(), sliceBatchSerialDo);
        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataList.size());
    }

    /**
     * 获取远程文件路径定义
     */
    public String getRemoteFilePathDefine() {
        return this.remoteFilePathDefine;
    }

    /**
     * 获取文件名模板
     */
    public String getFileName() {
        return this.fileName;
    }

    /**
     * 是否跳过第一行（标题行）
     */
    private boolean skipFirst() {
        return true; // 跳过标题行
    }

    /**
     * 并行处理原始数据
     * 优化的并行流处理逻辑，专门针对286字段的历史信用卡数据优化性能
     *
     * @param originData 原始数据列表
     * @param prefixLog 日志前缀
     * @return 处理后的数据列表
     */
    private List<LbHCcsAcctData> processOriginDataParallel(List<String> originData, String prefixLog) {
        AtomicInteger invalidCount = new AtomicInteger(0);
        AtomicInteger parseErrorCount = new AtomicInteger(0);
        AtomicInteger successCount = new AtomicInteger(0);

        // 性能优化：根据数据量选择处理策略
        Stream<String> dataStream = originData.size() > PARALLEL_THRESHOLD
            ? originData.parallelStream()
            : originData.stream();

        // 预分配容量，减少扩容操作
        List<LbHCcsAcctData> result = dataStream
            .filter(this::isValidDataLine)
            .map(item -> parseLineDataOptimized(item, prefixLog, invalidCount, parseErrorCount))
            .filter(Objects::nonNull)
            .peek(item -> successCount.incrementAndGet())
            .collect(Collectors.toCollection(() -> new ArrayList<>(originData.size())));

        log.info(prefixLog + "数据解析完成: 总行数[{}], 成功[{}], 字段不足[{}], 解析错误[{}]",
            originData.size(), successCount.get(), invalidCount.get(), parseErrorCount.get());

        return result;
    }

    /**
     * 验证数据行有效性（快速检查）
     * 避免在空行或无效行上浪费解析时间
     *
     * @param line 数据行
     * @return 是否为有效数据行
     */
    private boolean isValidDataLine(String line) {
        return StringUtil.isNotBlank(line)
            && line.length() > 150  // 286字段的数据行长度应该很长
            && line.contains("|+|"); // 包含分隔符
    }

    /**
     * 优化的数据解析方法
     * 专门为286字段数据优化，减少重复操作和内存分配
     *
     * @param item 单行数据
     * @param prefixLog 日志前缀
     * @param invalidCount 无效数据计数器
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的数据对象
     */
    private LbHCcsAcctData parseLineDataOptimized(String item, String prefixLog,
        AtomicInteger invalidCount, AtomicInteger parseErrorCount) {

        // 性能优化：使用split的limit参数避免过度分割
        String[] split = item.split(FIELD_SEPARATOR, MIN_FIELD_COUNT + 1);

        if (split.length < MIN_FIELD_COUNT) {
            invalidCount.incrementAndGet();
            if (log.isDebugEnabled()) {
                log.debug(prefixLog + "数据字段数量不足: 期望[{}], 实际[{}]", MIN_FIELD_COUNT, split.length);
            }
            return null;
        }

        try {
            // 解析H_CCS_ACCT的286字段数据
            return parseLineData(item, prefixLog, invalidCount, parseErrorCount);
        } catch (Exception e) {
            parseErrorCount.incrementAndGet();
            if (log.isDebugEnabled()) {
                log.debug(prefixLog + "解析数据行异常: {}", e.getMessage());
            }
            return null;
        }
    }

    /**
     * 解析单行历史信用卡账户数据（286字段完整解析）
     *
     * @param item 单行数据
     * @param prefixLog 日志前缀
     * @param invalidCount 无效数据计数器
     * @param parseErrorCount 解析错误计数器
     * @return 解析后的数据对象
     */
    private LbHCcsAcctData parseLineData(String item, String prefixLog, AtomicInteger invalidCount,
        AtomicInteger parseErrorCount) {
        String[] split = item.split(FIELD_SEPARATOR);
        if (split.length < MIN_FIELD_COUNT) {
            invalidCount.incrementAndGet();
            log.debug(prefixLog + "数据字段数量不足: 期望[{}], 实际[{}]", MIN_FIELD_COUNT, split.length);
            return null;
        }

        try {
            LbHCcsAcctData fileData = new LbHCcsAcctData();

            // 按照H_CCS_ACCT表结构解析所有286个字段
            int idx = 0;

            // 主键字段
            fileData.setXaccount(parseIntegerSafely(split[idx++], parseErrorCount)); // 0
            fileData.setBank(parseIntegerSafely(split[idx++], parseErrorCount)); // 1

            // 基本信息字段
            fileData.setBusiness(split[idx++]); // 2
            fileData.setCategory(parseIntegerSafely(split[idx++], parseErrorCount)); // 3
            fileData.setCustrNbr(split[idx++]); // 4
            fileData.setCycleNbr(parseIntegerSafely(split[idx++], parseErrorCount)); // 5
            fileData.setDayOpened(parseIntegerSafely(split[idx++], parseErrorCount)); // 6
            fileData.setAccName1(split[idx++]); // 7
            fileData.setAccType(split[idx++]); // 8
            fileData.setAddChgday(parseIntegerSafely(split[idx++], parseErrorCount)); // 9
            fileData.setAddrType(split[idx++]); // 10
            fileData.setAddrType2(split[idx++]); // 11

            // 逾期金额字段
            fileData.setAge1(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 12
            fileData.setAge2(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 13
            fileData.setAge3(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 14
            fileData.setAge4(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 15
            fileData.setAge5(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 16
            fileData.setAge6(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 17

            // 授权相关字段
            fileData.setAppSource(split[idx++]); // 18
            fileData.setAuthCash(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 19
            fileData.setAuthOver(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 20
            fileData.setAuthovYn(split[idx++]); // 21
            fileData.setAuthsAmt(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 22

            // 余额相关字段
            fileData.setBalCmpint(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 23
            fileData.setBalFree(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 24
            fileData.setBalInt(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 25
            fileData.setBalIntflag(split[idx++]); // 26
            fileData.setBalNoint(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 27
            fileData.setBalOrint(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 28

            // 自扣还款账号字段
            fileData.setBankacct1(split[idx++]); // 29
            fileData.setBankcode1(split[idx++]); // 30
            fileData.setBankacct2(split[idx++]); // 31
            fileData.setBankcode2(split[idx++]); // 32
            fileData.setBankacct3(split[idx++]); // 33
            fileData.setBankcode3(split[idx++]); // 34
            fileData.setBankacct4(split[idx++]); // 35
            fileData.setBankcode4(split[idx++]); // 36

            // 分行和费用字段
            fileData.setBranch(parseIntegerSafely(split[idx++], parseErrorCount)); // 37
            fileData.setBranchDay(parseIntegerSafely(split[idx++], parseErrorCount)); // 38
            fileData.setCaPcnt(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 39
            fileData.setCardFees(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 40
            fileData.setCardsCanc(parseIntegerSafely(split[idx++], parseErrorCount)); // 41
            fileData.setCardsIssd(parseIntegerSafely(split[idx++], parseErrorCount)); // 42

            // 预借现金字段
            fileData.setCash1st(parseIntegerSafely(split[idx++], parseErrorCount)); // 43
            fileData.setCashAdfee(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 44
            fileData.setCashAdvce(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 45
            fileData.setCashTday(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 46
            fileData.setCash1stac(parseIntegerSafely(split[idx++], parseErrorCount)); // 47

            // 客户和账户状态字段
            fileData.setClassChdy(parseIntegerSafely(split[idx++], parseErrorCount)); // 48
            fileData.setClassCode(split[idx++]); // 49
            fileData.setCloseChdy(parseIntegerSafely(split[idx++], parseErrorCount)); // 50
            fileData.setCloseCode(split[idx++]); // 51
            fileData.setCollsDay(parseIntegerSafely(split[idx++], parseErrorCount)); // 52
            fileData.setCrdactDay(parseIntegerSafely(split[idx++], parseErrorCount)); // 53
            fileData.setCrdnxtDay(parseIntegerSafely(split[idx++], parseErrorCount)); // 54
            fileData.setCredActiv(split[idx++]); // 55 - String类型
            fileData.setCredAdj(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 56
            fileData.setCredChday(parseIntegerSafely(split[idx++], parseErrorCount)); // 57
            fileData.setCredLimit(parseIntegerSafely(split[idx++], parseErrorCount)); // 58
            fileData.setCredVouch(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 59
            fileData.setCredlimX(parseIntegerSafely(split[idx++], parseErrorCount)); // 60 - Integer类型
            fileData.setCurrNum(parseIntegerSafely(split[idx++], parseErrorCount)); // 61
            fileData.setCutoffDay(parseIntegerSafely(split[idx++], parseErrorCount)); // 62

            // 周期相关字段
            fileData.setCyChgcnt(parseIntegerSafely(split[idx++], parseErrorCount)); // 63
            fileData.setCyChgday(parseIntegerSafely(split[idx++], parseErrorCount)); // 64
            fileData.setCyEffday(parseIntegerSafely(split[idx++], parseErrorCount)); // 65
            fileData.setCyYrcnt(parseIntegerSafely(split[idx++], parseErrorCount)); // 66
            fileData.setCycleNew(parseIntegerSafely(split[idx++], parseErrorCount)); // 67
            fileData.setCyclePrv(parseIntegerSafely(split[idx++], parseErrorCount)); // 68

            // 调整和费用字段
            fileData.setDebitAdj(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 69
            fileData.setDishnrday(parseIntegerSafely(split[idx++], parseErrorCount)); // 70
            fileData.setDutyCredt(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 71
            fileData.setDutyDebit(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 72

            // 汇率相关字段
            fileData.setExchCode(split[idx++]); // 73
            fileData.setExchFlag(split[idx++]); // 74
            fileData.setExchPerc(parseIntegerSafely(split[idx++], parseErrorCount)); // 75 - Integer类型
            fileData.setExchRtdt(parseIntegerSafely(split[idx++], parseErrorCount)); // 76
            fileData.setFeeMonth(parseIntegerSafely(split[idx++], parseErrorCount)); // 77
            fileData.setFeesTaxes(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 78
            fileData.setFixExamt(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 79
            fileData.setGuarnFlag(parseIntegerSafely(split[idx++], parseErrorCount)); // 80 - Integer类型

            // 历史最高金额字段
            fileData.setHiCashadv(parseIntegerSafely(split[idx++], parseErrorCount)); // 81 - Integer类型
            fileData.setHiCasmmyy(parseIntegerSafely(split[idx++], parseErrorCount)); // 82
            fileData.setHiCrdmmyy(parseIntegerSafely(split[idx++], parseErrorCount)); // 83
            fileData.setHiCredit(parseIntegerSafely(split[idx++], parseErrorCount)); // 84 - Integer类型
            fileData.setHiDebit(parseIntegerSafely(split[idx++], parseErrorCount)); // 85 - Integer类型
            fileData.setHiDebmmyy(parseIntegerSafely(split[idx++], parseErrorCount)); // 86
            fileData.setHiMpPur(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 87
            fileData.setHiMpmmyy(parseIntegerSafely(split[idx++], parseErrorCount)); // 88
            fileData.setHiOlimit(parseIntegerSafely(split[idx++], parseErrorCount)); // 89 - Integer类型
            fileData.setHiOlimmyy(parseIntegerSafely(split[idx++], parseErrorCount)); // 90
            fileData.setHiPurchse(parseIntegerSafely(split[idx++], parseErrorCount)); // 91 - Integer类型
            fileData.setHiPurmmyy(parseIntegerSafely(split[idx++], parseErrorCount)); // 92

            // 利息相关字段
            fileData.setIntCash(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 93
            fileData.setIntChdcmp(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 94
            fileData.setIntChdy(parseIntegerSafely(split[idx++], parseErrorCount)); // 95
            fileData.setIntChgd(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 96
            fileData.setIntCmpond(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 97
            fileData.setIntCode(parseIntegerSafely(split[idx++], parseErrorCount)); // 98
            fileData.setIntCunot(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 99
            fileData.setIntCurcmp(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 100
            fileData.setIntEarned(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 101
            fileData.setIntNotion(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 102
            fileData.setIntProcdy(parseIntegerSafely(split[idx++], parseErrorCount)); // 103
            fileData.setIntRate(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 104
            fileData.setIntRatecr(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 105
            fileData.setIntUptody(parseIntegerSafely(split[idx++], parseErrorCount)); // 106

            // 语言和交易字段
            fileData.setLangCode(parseIntegerSafely(split[idx++], parseErrorCount)); // 107
            fileData.setLastTrday(parseIntegerSafely(split[idx++], parseErrorCount)); // 108
            fileData.setLastauthdy(parseIntegerSafely(split[idx++], parseErrorCount)); // 109
            fileData.setLastntdate(parseIntegerSafely(split[idx++], parseErrorCount)); // 110
            fileData.setLastpayamt(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 111
            fileData.setLastpayday(parseIntegerSafely(split[idx++], parseErrorCount)); // 112
            fileData.setLosses(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 113

            // 月份和监控字段
            fileData.setMonthNbr(parseIntegerSafely(split[idx++], parseErrorCount)); // 114
            fileData.setMontrChdy(parseIntegerSafely(split[idx++], parseErrorCount)); // 115
            fileData.setMontrCode(split[idx++]); // 116

            // 分期付款字段
            fileData.setMpAmTdy(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 117
            fileData.setMpAuths(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 118
            fileData.setMpBal(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 119
            fileData.setMpBilAmt(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 120
            fileData.setMpLimit(parseIntegerSafely(split[idx++], parseErrorCount)); // 121
            fileData.setMpNoTdy(parseIntegerSafely(split[idx++], parseErrorCount)); // 122
            fileData.setMpRemPpl(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 123
            fileData.setMplmChday(parseIntegerSafely(split[idx++], parseErrorCount)); // 124
            fileData.setMthsOdue(parseIntegerSafely(split[idx++], parseErrorCount)); // 125

            // 交易笔数字段
            fileData.setNbrCashad(parseIntegerSafely(split[idx++], parseErrorCount)); // 126
            fileData.setNbrFeedty(parseIntegerSafely(split[idx++], parseErrorCount)); // 127
            fileData.setNbrOlimit(parseIntegerSafely(split[idx++], parseErrorCount)); // 128
            fileData.setNbrOthers(parseIntegerSafely(split[idx++], parseErrorCount)); // 129
            fileData.setNbrPaymnt(parseIntegerSafely(split[idx++], parseErrorCount)); // 130
            fileData.setNbrPurch(parseIntegerSafely(split[idx++], parseErrorCount)); // 131
            fileData.setNbrTrans(parseIntegerSafely(split[idx++], parseErrorCount)); // 132

            // OCT和逾期字段
            fileData.setOctCount(parseIntegerSafely(split[idx++], parseErrorCount)); // 133
            fileData.setOctDayin(parseIntegerSafely(split[idx++], parseErrorCount)); // 134
            fileData.setOdueFlag(parseIntegerSafely(split[idx++], parseErrorCount)); // 135
            fileData.setOdueHeld(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 136
            fileData.setOlflag(parseIntegerSafely(split[idx++], parseErrorCount)); // 137
            fileData.setOtherFees(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 138

            // 还款相关字段
            fileData.setPayFlag(split[idx++]); // 139
            fileData.setPay1stInd(split[idx++]); // 140
            fileData.setPaymtClrd(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 141
            fileData.setPaymtTday(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 142
            fileData.setPaymtUncl(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 143
            fileData.setPenChrg(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 144
            fileData.setPenchgAcc(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 145

            // 积分相关字段
            fileData.setPointAdj(parseIntegerSafely(split[idx++], parseErrorCount)); // 146
            fileData.setPtAdjflag(split[idx++]); // 147
            fileData.setPointClm(parseIntegerSafely(split[idx++], parseErrorCount)); // 148
            fileData.setPointCum(parseIntegerSafely(split[idx++], parseErrorCount)); // 149
            fileData.setPtCumflag(split[idx++]); // 150
            fileData.setPointCum2(parseIntegerSafely(split[idx++], parseErrorCount)); // 151
            fileData.setPointEar(parseIntegerSafely(split[idx++], parseErrorCount)); // 152
            fileData.setPointFrez(split[idx++]); // 153
            fileData.setPointFzda(parseIntegerSafely(split[idx++], parseErrorCount)); // 154

            // 邮寄日期字段
            fileData.setPostDd(parseIntegerSafely(split[idx++], parseErrorCount)); // 155
            fileData.setPrevBrday(parseIntegerSafely(split[idx++], parseErrorCount)); // 156
            fileData.setPrevBrnch(parseIntegerSafely(split[idx++], parseErrorCount)); // 157
            fileData.setPurchases(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 158

            // 查询相关字段
            fileData.setQueryAmt(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 159
            fileData.setQueryCode(split[idx++]); // 160
            fileData.setQueryStmt(parseIntegerSafely(split[idx++], parseErrorCount)); // 161

            // 申请相关字段
            fileData.setReclaChdy(parseIntegerSafely(split[idx++], parseErrorCount)); // 162
            fileData.setReclaCode(split[idx++]); // 163
            fileData.setRecvryAmt(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 164

            // 还款设定字段
            fileData.setRepayAmt(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 165
            fileData.setRepayAmtx(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 166
            fileData.setRepayCode(split[idx++]); // 167
            fileData.setRepayCodx(split[idx++]); // 168
            fileData.setRepayDay(parseIntegerSafely(split[idx++], parseErrorCount)); // 169
            fileData.setRepayPct(parseIntegerSafely(split[idx++], parseErrorCount)); // 170
            fileData.setRepayPctx(parseIntegerSafely(split[idx++], parseErrorCount)); // 171
            fileData.setRepyChgdy(parseIntegerSafely(split[idx++], parseErrorCount)); // 172
            fileData.setScorePts(parseIntegerSafely(split[idx++], parseErrorCount)); // 173

            // 对帐单相关字段
            fileData.setStatements(parseIntegerSafely(split[idx++], parseErrorCount)); // 174
            fileData.setStmAmtOl(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 175
            fileData.setStmBalfre(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 176
            fileData.setStmBalint(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 177
            fileData.setStmbalintflag(split[idx++]); // 178
            fileData.setStmBalnce(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 179
            fileData.setStmBalflag(split[idx++]); // 180
            fileData.setStmBalori(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 181
            fileData.setStmClosdy(parseIntegerSafely(split[idx++], parseErrorCount)); // 182
            fileData.setStmCode(split[idx++]); // 183
            fileData.setStmInstl(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 184
            fileData.setStmMindue(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 185
            fileData.setStmNoint(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 186
            fileData.setStmOlflag(parseIntegerSafely(split[idx++], parseErrorCount)); // 187
            fileData.setStmOverdu(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 188
            fileData.setStmPayUn(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 189
            fileData.setStmQryamt(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 190
            fileData.setStmRepay(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 191
            fileData.setStmtDd(parseIntegerSafely(split[idx++], parseErrorCount)); // 192
            fileData.setStmtPull(split[idx++]); // 193

            // 今日金额字段
            fileData.setTodayAmt(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 194
            fileData.setTodayAmtflag(split[idx++]); // 195
            fileData.setTodayRel(parseIntegerSafely(split[idx++], parseErrorCount)); // 196
            fileData.setUnclPct(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 197

            // 转销相关字段
            fileData.setWroffChdy(parseIntegerSafely(split[idx++], parseErrorCount)); // 198
            fileData.setWroffCode(split[idx++]); // 199

            // 产品相关字段
            fileData.setProdLevel(parseIntegerSafely(split[idx++], parseErrorCount)); // 200
            fileData.setProdNbr(parseIntegerSafely(split[idx++], parseErrorCount)); // 201
            fileData.setAcctPrsts(split[idx++]); // 202
            fileData.setAcctSts(split[idx++]); // 203
            fileData.setAppApque(split[idx++]); // 204

            // 积分扩展字段
            fileData.setPointEnc(parseIntegerSafely(split[idx++], parseErrorCount)); // 205
            fileData.setPointImp(parseIntegerSafely(split[idx++], parseErrorCount)); // 206
            fileData.setPointExp(parseIntegerSafely(split[idx++], parseErrorCount)); // 207
            fileData.setPtExpflag(split[idx++]); // 208

            // 风控字段
            fileData.setRiskCount(parseIntegerSafely(split[idx++], parseErrorCount)); // 209
            fileData.setRiskDayin(parseIntegerSafely(split[idx++], parseErrorCount)); // 210

            // 分期付款扩展字段
            fileData.setBalMp(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 211
            fileData.setBalMpflag(split[idx++]); // 212
            fileData.setStmBalmp(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 213
            fileData.setStmBmflag(split[idx++]); // 214
            fileData.setLmtRsn(split[idx++]); // 215
            fileData.setPointCard(parseIntegerSafely(split[idx++], parseErrorCount)); // 216
            fileData.setPtCdflag(split[idx++]); // 217

            // 临时额度字段
            fileData.setTempLimit(parseIntegerSafely(split[idx++], parseErrorCount)); // 218
            fileData.setTlmtBeg(parseIntegerSafely(split[idx++], parseErrorCount)); // 219
            fileData.setTlmtEnd(parseIntegerSafely(split[idx++], parseErrorCount)); // 220
            fileData.setTlmtNo(parseIntegerSafely(split[idx++], parseErrorCount)); // 221
            fileData.setCanclResn(split[idx++]); // 222

            // 影子字段
            fileData.setShadowPen(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 223
            fileData.setShadowInt(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 224
            fileData.setShadowCmp(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 225

            // 不计息余额细分字段(1-10)
            fileData.setBalNint01(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 226
            fileData.setBalNint02(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 227
            fileData.setBalNint03(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 228
            fileData.setBalNint04(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 229
            fileData.setBalNint05(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 230
            fileData.setBalNint06(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 231
            fileData.setBalNint07(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 232
            fileData.setBalNint08(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 233
            fileData.setBalNint09(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 234
            fileData.setBalNint10(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 235

            // 上期应收费用细分字段(1-10)
            fileData.setStmNint01(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 236
            fileData.setStmNint02(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 237
            fileData.setStmNint03(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 238
            fileData.setStmNint04(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 239
            fileData.setStmNint05(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 240
            fileData.setStmNint06(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 241
            fileData.setStmNint07(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 242
            fileData.setStmNint08(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 243
            fileData.setStmNint09(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 244
            fileData.setStmNint10(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 245

            // 扩展字段
            fileData.setCurrNum2(parseIntegerSafely(split[idx++], parseErrorCount)); // 246
            fileData.setWrofFlag(parseIntegerSafely(split[idx++], parseErrorCount)); // 247
            fileData.setLayercoder1(split[idx++]); // 248
            fileData.setLayercoder2(split[idx++]); // 249
            fileData.setMcntrlYn(split[idx++]); // 250
            fileData.setNcredRsn(split[idx++]); // 251
            fileData.setBscCred(parseIntegerSafely(split[idx++], parseErrorCount)); // 252
            fileData.setCustrRef(split[idx++]); // 253
            fileData.setPbcBrnch(split[idx++]); // 254
            fileData.setStopmpYn(parseIntegerSafely(split[idx++], parseErrorCount)); // 255
            fileData.setCredLmt2(parseIntegerSafely(split[idx++], parseErrorCount)); // 256
            fileData.setBalCmpfee(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 257
            fileData.setBalLmp(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 258
            fileData.setStmLmp(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 259
            fileData.setOutMonth(parseIntegerSafely(split[idx++], parseErrorCount)); // 260
            fileData.setEtlDay(parseIntegerSafely(split[idx++], parseErrorCount)); // 261
            fileData.setStpay(parseIntegerSafely(split[idx++], parseErrorCount)); // 262
            fileData.setAdjFlag(split[idx++]); // 263
            fileData.setUseSource(split[idx++]); // 264
            fileData.setItcdEnddy(parseIntegerSafely(split[idx++], parseErrorCount)); // 265
            fileData.setIntCdnew(parseIntegerSafely(split[idx++], parseErrorCount)); // 266
            fileData.setItcnBegdy(parseIntegerSafely(split[idx++], parseErrorCount)); // 267
            fileData.setItcnEnddy(parseIntegerSafely(split[idx++], parseErrorCount)); // 268
            fileData.setIntEffect(parseIntegerSafely(split[idx++], parseErrorCount)); // 269
            fileData.setMarkDay(parseIntegerSafely(split[idx++], parseErrorCount)); // 270
            fileData.setPstmBalfr(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 271
            fileData.setPstmBalmp(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 272
            fileData.setPstmBalor(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 273
            fileData.setPbalCmpin(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 274
            fileData.setMaxCash(parseIntegerSafely(split[idx++], parseErrorCount)); // 275
            fileData.setPaytdyLmt(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 276
            fileData.setGlbDtime(split[idx++]); // 277
            fileData.setIntChgdC(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 278
            fileData.setBalOrintc(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 279
            fileData.setStmBalorc(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 280
            fileData.setSublmtL(parseIntegerSafely(split[idx++], parseErrorCount)); // 281
            fileData.setBalBmp(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 282
            fileData.setBalBmpFlg(split[idx++]); // 283
            fileData.setStmBmp(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 284
            fileData.setStmBmpFlg(split[idx++]); // 285
            fileData.setOtherFeesFlg(split[idx++]); // 286
            fileData.setOrintsArr(split[idx++]); // 287
            fileData.setBalNcmpis(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 288
            fileData.setPstmNoris(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 289
            fileData.setPbalNcmps(parseBigDecimalSafely(split[idx++], parseErrorCount)); // 290
            fileData.setOveramtYn(split[idx++]); // 291
            fileData.setInfoFlag(split[idx++]); // 292
            fileData.setMindueTyp(parseIntegerSafely(split[idx++], parseErrorCount)); // 293
            fileData.setFeesTaxesFlg(split[idx++]); // 294

            //数据日期
            fileData.setDataDate(split[idx++]);

            return fileData;
        } catch (Exception e) {
            parseErrorCount.incrementAndGet();
            log.debug(prefixLog + "解析数据行异常: ", e);
            return null;
        }
    }

    /**
     * 安全解析整数
     */
    private Integer parseIntegerSafely(String value, AtomicInteger parseErrorCount) {
        if (StringUtil.isBlank(value)) {
            return null;
        }
        try {
            return Integer.valueOf(value.trim());
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return null;
        }
    }

    /**
     * 安全解析BigDecimal
     */
    private BigDecimal parseBigDecimalSafely(String value, AtomicInteger parseErrorCount) {
        if (StringUtil.isBlank(value)) {
            return null;
        }
        try {
            return new BigDecimal(value.trim());
        } catch (NumberFormatException e) {
            parseErrorCount.incrementAndGet();
            return null;
        }
    }

    /**
     * 验证数据的有效性
     * 对关键业务字段进行验证，确保数据质量
     *
     * @param data 数据对象的DO形式
     * @return 是否通过验证
     */
    private boolean validateData(LbHCcsAcctDo data) {
        if (Objects.isNull(data)) {
            return false;
        }

        // 验证主键字段
        if (Objects.isNull(data.getXaccount()) || data.getXaccount() <= 0) {
            log.debug("账户号无效: {}", data.getXaccount());
            return false;
        }

        if (Objects.isNull(data.getBank()) || data.getBank() <= 0) {
            log.debug("银行编号无效: {}", data.getBank());
            return false;
        }

                 // 验证数据日期 - String类型日期验证
         if (StringUtil.isBlank(data.getDataDate())) {
             log.debug("数据日期为空: {}", data.getDataDate());
             return false;
         }

        // 验证客户号
        if (StringUtil.isBlank(data.getCustrNbr())) {
            log.debug("客户号为空");
            return false;
        }

        // 验证开户日期
        if (Objects.isNull(data.getDayOpened()) || data.getDayOpened() <= 0) {
            log.debug("开户日期无效: {}", data.getDayOpened());
            return false;
        }

        // 其他业务字段验证...

        return true;
    }

    /**
     * 批量插入处理
     * 优化的批量插入逻辑，提升插入性能和错误处理
     *
     * @param insertList 待插入的数据列表
     * @param prefixLog 日志前缀
     */
    private void processBatchInsert(List<LbHCcsAcctDo> insertList, String prefixLog) {
        if (CollectionUtil.isEmpty(insertList)) {
            return;
        }

        int totalSize = insertList.size();
        int batchCount = (totalSize + BATCH_SIZE - 1) / BATCH_SIZE;

        log.info(prefixLog + "开始批量插入数据,总数据量:[{}],分批数量:[{}],每批大小:[{}]", totalSize, batchCount,
            BATCH_SIZE);

        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * BATCH_SIZE;
            int toIndex = Math.min(fromIndex + BATCH_SIZE, totalSize);
            List<LbHCcsAcctDo> batchList = insertList.subList(fromIndex, toIndex);

            try {
                lbHCcsAcctDao.insertList(batchList);
                log.debug(prefixLog + "批次[{}]插入完成,数据量:[{}]", i + 1, batchList.size());
            } catch (Exception e) {
                log.error(prefixLog + "批次[{}]插入失败,数据量:[{}]", i + 1, batchList.size(), e);
                throw e;
            }
        }
    }
}
