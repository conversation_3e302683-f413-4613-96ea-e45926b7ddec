package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitLockInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitLockInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度实例锁信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcCustLimitLockInfoDao extends IBaseDao<LcCustLimitLockInfoDo> {
    /**
     * 分页查询额度实例锁信息
     *
     * @param lcCustLimitLockInfoQuery 条件
     * @return PageInfo<LcCustLimitLockInfoDo>
     */
    PageInfo<LcCustLimitLockInfoDo> selectPage(LcCustLimitLockInfoQuery lcCustLimitLockInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度实例锁信息
     *
     * @param custLimitId
     * @return
     */
    LcCustLimitLockInfoDo selectByKey(String custLimitId);

    /**
     * 根据key删除额度实例锁信息
     *
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custLimitId);

    /**
     * 查询额度实例锁信息信息
     *
     * @param lcCustLimitLockInfoQuery 条件
     * @return List<LcCustLimitLockInfoDo>
     */
    List<LcCustLimitLockInfoDo> selectByExample(LcCustLimitLockInfoQuery lcCustLimitLockInfoQuery);

    /**
     * 新增额度实例锁信息信息
     *
     * @param lcCustLimitLockInfo 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitLockInfoDo lcCustLimitLockInfo);

    /**
     * 修改额度实例锁信息信息
     *
     * @param lcCustLimitLockInfo
     * @return
     */
    int updateBySelective(LcCustLimitLockInfoDo lcCustLimitLockInfo);

    /**
     * 修改额度实例锁信息信息
     *
     * @param lcCustLimitLockInfo
     * @param lcCustLimitLockInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitLockInfoDo lcCustLimitLockInfo,
        LcCustLimitLockInfoQuery lcCustLimitLockInfoQuery);
}
