package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcRecalContractOccupyPlanDo;
import com.hsjry.core.limit.center.dal.dao.query.LcRecalContractOccupyPlanQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 合同占用重算计划(准备)数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-03-17 09:13:35
 */
public interface LcRecalContractOccupyPlanDao extends IBaseDao<LcRecalContractOccupyPlanDo> {
    /**
     * 分页查询合同占用重算计划(准备)
     *
     * @param lcRecalContractOccupyPlanQuery 条件
     * @return PageInfo<LcRecalContractOccupyPlanDo>
     */
    PageInfo<LcRecalContractOccupyPlanDo> selectPage(LcRecalContractOccupyPlanQuery lcRecalContractOccupyPlanQuery,
        PageParam pageParam);

    /**
     * 根据key查询合同占用重算计划(准备)
     *
     * @param custLimitId
     * @param relationId
     * @return
     */
    LcRecalContractOccupyPlanDo selectByKey(String custLimitId, String relationId);

    /**
     * 根据key删除合同占用重算计划(准备)
     *
     * @param custLimitId
     * @param relationId
     * @return
     */
    int deleteByKey(String custLimitId, String relationId);

    /**
     * 查询合同占用重算计划(准备)信息
     *
     * @param lcRecalContractOccupyPlanQuery 条件
     * @return List<LcRecalContractOccupyPlanDo>
     */
    List<LcRecalContractOccupyPlanDo> selectByExample(LcRecalContractOccupyPlanQuery lcRecalContractOccupyPlanQuery);

    /**
     * 新增合同占用重算计划(准备)信息
     *
     * @param lcRecalContractOccupyPlan 条件
     * @return int>
     */
    int insertBySelective(LcRecalContractOccupyPlanDo lcRecalContractOccupyPlan);

    /**
     * 修改合同占用重算计划(准备)信息
     *
     * @param lcRecalContractOccupyPlan
     * @return
     */
    int updateBySelective(LcRecalContractOccupyPlanDo lcRecalContractOccupyPlan);

    /**
     * 修改合同占用重算计划(准备)信息
     *
     * @param lcRecalContractOccupyPlan
     * @param lcRecalContractOccupyPlanQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcRecalContractOccupyPlanDo lcRecalContractOccupyPlan,
        LcRecalContractOccupyPlanQuery lcRecalContractOccupyPlanQuery);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    String selectFirstOne(LcRecalContractOccupyPlanQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(LcRecalContractOccupyPlanQuery query);

    /**
     * 查询分片数据
     *
     * @param query
     * @return
     */
    List<LcRecalContractOccupyPlanDo> selectShardList(LcRecalContractOccupyPlanQuery query);

    /**
     * 清理租户下所有
     *
     * @return
     */
    int deleteAll();
}
