package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.intf.CustLimitOperateSerialBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.CustLimitOperateSerialBatchMapper;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitOperateSerialBatchQuery;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitOperateSerialQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitOperateSerialDo;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/11/7 20:01
 */
@Repository
public class CustLimitOperateSerialBatchDaoImpl
    extends AbstractBaseDaoImpl<LcCustLimitOperateSerialDo, CustLimitOperateSerialBatchMapper>
    implements CustLimitOperateSerialBatchDao {

    @Override
    public List<LcCustLimitOperateSerialDo> selectShardList(CustLimitOperateSerialQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectShardList(query);
    }

    @Override
    public LcCustLimitOperateSerialDo selectFirstOne(CustLimitOperateSerialQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectFirstOne(query);
    }

    @Override
    public Integer selectCountByCurrentGroup(CustLimitOperateSerialQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectCountByCurrentGroup(query);
    }

    @Override
    public List<LcCustLimitOperateSerialDo> selectShardList(CustLimitOperateSerialBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectShardList4Batch(query);
    }

    @Override
    public LcCustLimitOperateSerialDo selectFirstOne(CustLimitOperateSerialBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectFirstOne4Batch(query);
    }

    @Override
    public Integer selectCountByCurrentGroup(CustLimitOperateSerialBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectCountByCurrentGroup4Batch(query);
    }
}
