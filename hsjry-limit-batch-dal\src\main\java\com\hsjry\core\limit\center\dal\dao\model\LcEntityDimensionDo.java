package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 实体维度信息Do
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
@Table(name = "lc_entity_dimension")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcEntityDimensionDo extends LcEntityDimensionKeyDo implements Serializable {
    private static final long serialVersionUID = 1602582710713516051L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 维度编码 */
    @Column(name = "dimension_code")
    private String dimensionCode;
    /** 维度名称 */
    @Column(name = "dimension_name")
    private String dimensionName;
    /** 维度值 */
    @Column(name = "dimension_value")
    private String dimensionValue;
    /** 实体编号 */
    @Column(name = "entity_id")
    private String entityId;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
}
