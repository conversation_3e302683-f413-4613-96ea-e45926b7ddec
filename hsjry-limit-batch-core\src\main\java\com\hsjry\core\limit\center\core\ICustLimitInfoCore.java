package com.hsjry.core.limit.center.core;

import java.util.List;

import com.hsjry.core.limit.center.core.bo.CustLimitInfoBo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery;

public interface ICustLimitInfoCore {
    /**
     * 查询额度实例信息信息
     *
     * @param query 条件
     * @return List<CustLimitInfoBo>
     */
    List<CustLimitInfoBo> enqrByExample(LcCustLimitInfoQuery query);
}
