package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTOlIndvCtrInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTOlIndvCtrInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlIndvCtrInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlIndvCtrInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTOlIndvCtrInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTOlIndvCtrInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-中间表-个人合同信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
@Repository
public class LbTOlIndvCtrInfoDaoImpl extends AbstractBaseDaoImpl<LbTOlIndvCtrInfoDo, LbTOlIndvCtrInfoMapper>
    implements LbTOlIndvCtrInfoDao {
    /**
     * 分页查询
     *
     * @param lbTOlIndvCtrInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTOlIndvCtrInfoDo> selectPage(LbTOlIndvCtrInfoQuery lbTOlIndvCtrInfo, PageParam pageParam) {
        LbTOlIndvCtrInfoExample example = buildExample(lbTOlIndvCtrInfo);
        return PageHelper.<LbTOlIndvCtrInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询网贷系统-中间表-个人合同信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTOlIndvCtrInfoDo selectByKey(String custNo, String custLimitId) {
        LbTOlIndvCtrInfoKeyDo lbTOlIndvCtrInfoKeyDo = new LbTOlIndvCtrInfoKeyDo();
        lbTOlIndvCtrInfoKeyDo.setCustNo(custNo);
        lbTOlIndvCtrInfoKeyDo.setCustLimitId(custLimitId);
        lbTOlIndvCtrInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTOlIndvCtrInfoKeyDo);
    }

    /**
     * 根据key删除网贷系统-中间表-个人合同信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTOlIndvCtrInfoKeyDo lbTOlIndvCtrInfoKeyDo = new LbTOlIndvCtrInfoKeyDo();
        lbTOlIndvCtrInfoKeyDo.setCustNo(custNo);
        lbTOlIndvCtrInfoKeyDo.setCustLimitId(custLimitId);
        lbTOlIndvCtrInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTOlIndvCtrInfoKeyDo);
    }

    /**
     * 查询网贷系统-中间表-个人合同信息信息
     *
     * @param lbTOlIndvCtrInfo 条件
     * @return List<LbTOlIndvCtrInfoDo>
     */
    @Override
    public List<LbTOlIndvCtrInfoDo> selectByExample(LbTOlIndvCtrInfoQuery lbTOlIndvCtrInfo) {
        return getMapper().selectByExample(buildExample(lbTOlIndvCtrInfo));
    }

    /**
     * 新增网贷系统-中间表-个人合同信息信息
     *
     * @param lbTOlIndvCtrInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTOlIndvCtrInfoDo lbTOlIndvCtrInfo) {
        if (lbTOlIndvCtrInfo == null) {
            return -1;
        }

        lbTOlIndvCtrInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTOlIndvCtrInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTOlIndvCtrInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTOlIndvCtrInfo);
    }

    /**
     * 修改网贷系统-中间表-个人合同信息信息
     *
     * @param lbTOlIndvCtrInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTOlIndvCtrInfoDo lbTOlIndvCtrInfo) {
        if (lbTOlIndvCtrInfo == null) {
            return -1;
        }
        lbTOlIndvCtrInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTOlIndvCtrInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTOlIndvCtrInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTOlIndvCtrInfoDo lbTOlIndvCtrInfo,
        LbTOlIndvCtrInfoQuery lbTOlIndvCtrInfoQuery) {
        lbTOlIndvCtrInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTOlIndvCtrInfo, buildExample(lbTOlIndvCtrInfoQuery));
    }

    /**
     * 构建网贷系统-中间表-个人合同信息Example信息
     *
     * @param lbTOlIndvCtrInfo
     * @return
     */
    public LbTOlIndvCtrInfoExample buildExample(LbTOlIndvCtrInfoQuery lbTOlIndvCtrInfo) {
        LbTOlIndvCtrInfoExample example = new LbTOlIndvCtrInfoExample();
        LbTOlIndvCtrInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTOlIndvCtrInfo != null) {
            //添加查询条件
            if (null != lbTOlIndvCtrInfo.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTOlIndvCtrInfo.getLowRiskAmount());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvCtrInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTOlIndvCtrInfo.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvCtrInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTOlIndvCtrInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvCtrInfo.getEntityId())) {
                criteria.andEntityIdEqualTo(lbTOlIndvCtrInfo.getEntityId());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvCtrInfo.getVirtualContractFlag())) {
                criteria.andVirtualContractFlagEqualTo(lbTOlIndvCtrInfo.getVirtualContractFlag());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvCtrInfo.getRelationId())) {
                criteria.andRelationIdEqualTo(lbTOlIndvCtrInfo.getRelationId());
            }
            if (null != lbTOlIndvCtrInfo.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTOlIndvCtrInfo.getRealOccupyLowRiskAmt());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvCtrInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTOlIndvCtrInfo.getCustNo());
            }
            if (null != lbTOlIndvCtrInfo.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTOlIndvCtrInfo.getRealOccupyAmount());
            }
            if (null != lbTOlIndvCtrInfo.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTOlIndvCtrInfo.getTotalAmount());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvCtrInfo.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTOlIndvCtrInfo.getLimitStatus());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvCtrInfo.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTOlIndvCtrInfo.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvCtrInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTOlIndvCtrInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvCtrInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTOlIndvCtrInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvCtrInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTOlIndvCtrInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTOlIndvCtrInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTOlIndvCtrInfo.getCustTyp());
            }
        }
        buildExampleExt(lbTOlIndvCtrInfo, criteria);
        return example;
    }

    /**
     * 构建网贷系统-中间表-个人合同信息ExampleExt方法
     *
     * @param lbTOlIndvCtrInfo
     * @return
     */
    public void buildExampleExt(LbTOlIndvCtrInfoQuery lbTOlIndvCtrInfo, LbTOlIndvCtrInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 将网贷个人客户额度同步数据插入到合同信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_ol_indv_ctr_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    @Override
    public int insertOlIndvCtrInfo(List<String> templateNodeIdList, List<String> custLimitIdList) {
        return getMapper().insertOlIndvCtrInfo(templateNodeIdList, custLimitIdList);
    }

    /**
     * 更新额度实例金额信息表
     * 根据网贷系统中间表数据更新LC_CUST_LIMIT_AMT_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    @Override
    public int updateCustLimitAmtInfo(List<String> custLimitIdList) {
        return getMapper().updateCustLimitAmtInfo(custLimitIdList);
    }

}
