package com.hsjry.core.limit.center.dal.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 余额占用重算计划(准备)Example
 *
 * <AUTHOR>
 * @date 2023-06-12 05:41:04
 */
public class LcRecalBalanceOccupyPlanExample {
    /** 排序字段 */
    protected String orderByClause;

    /** 是否只查询记录不同的数据 */
    protected boolean distinct;

    /** or条件查询集合 */
    protected List<Criteria> oredCriteria;

    /**
     * 初始化or条件查询集合
     */
    public LcRecalBalanceOccupyPlanExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * 排序字段set方法
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * 排序字段get方法
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * 不同记录set方法.
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * 不同记录get方法.
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * 排序集合get方法
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * or
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * or
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * 清除数据
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

                public Criteria andLowRiskAmtBalanceIsNull() {
            addCriterion("low_risk_amt_balance is null");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmtBalanceIsNotNull() {
            addCriterion("low_risk_amt_balance is not null");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmtBalanceEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk_amt_balance =", value, "lowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmtBalanceNotEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk_amt_balance <>", value, "lowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmtBalanceGreaterThan(java.math.BigDecimal value) {
            addCriterion("low_risk_amt_balance >", value, "lowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmtBalanceGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk_amt_balance >=", value, "lowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmtBalanceLessThan(java.math.BigDecimal value) {
            addCriterion("low_risk_amt_balance <", value, "lowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmtBalanceLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk_amt_balance <=", value, "lowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmtBalanceLike(java.math.BigDecimal value) {
            addCriterion("low_risk_amt_balance like", value, "lowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmtBalanceNotLike(java.math.BigDecimal value) {
            addCriterion("low_risk_amt_balance not like", value, "lowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmtBalanceIn(List<java.math.BigDecimal> values) {
            addCriterion("low_risk_amt_balance in", values, "lowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmtBalanceNotIn(List<java.math.BigDecimal> values) {
            addCriterion("low_risk_amt_balance not in", values, "lowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmtBalanceBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("low_risk_amt_balance between", value1, value2, "lowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmtBalanceNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("low_risk_amt_balance not between", value1, value2, "lowRiskAmtBalance");
            return (Criteria) this;
        }
		        public Criteria andCaPreAmountShareIsNull() {
            addCriterion("ca_pre_amount_share is null");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountShareIsNotNull() {
            addCriterion("ca_pre_amount_share is not null");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountShareEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_share =", value, "caPreAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountShareNotEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_share <>", value, "caPreAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountShareGreaterThan(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_share >", value, "caPreAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountShareGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_share >=", value, "caPreAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountShareLessThan(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_share <", value, "caPreAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountShareLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_share <=", value, "caPreAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountShareLike(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_share like", value, "caPreAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountShareNotLike(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_share not like", value, "caPreAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountShareIn(List<java.math.BigDecimal> values) {
            addCriterion("ca_pre_amount_share in", values, "caPreAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountShareNotIn(List<java.math.BigDecimal> values) {
            addCriterion("ca_pre_amount_share not in", values, "caPreAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountShareBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ca_pre_amount_share between", value1, value2, "caPreAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountShareNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ca_pre_amount_share not between", value1, value2, "caPreAmountShare");
            return (Criteria) this;
        }
		        public Criteria andCaAmountShareIsNull() {
            addCriterion("ca_amount_share is null");
            return (Criteria) this;
        }

        public Criteria andCaAmountShareIsNotNull() {
            addCriterion("ca_amount_share is not null");
            return (Criteria) this;
        }

        public Criteria andCaAmountShareEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_amount_share =", value, "caAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaAmountShareNotEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_amount_share <>", value, "caAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaAmountShareGreaterThan(java.math.BigDecimal value) {
            addCriterion("ca_amount_share >", value, "caAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaAmountShareGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_amount_share >=", value, "caAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaAmountShareLessThan(java.math.BigDecimal value) {
            addCriterion("ca_amount_share <", value, "caAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaAmountShareLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_amount_share <=", value, "caAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaAmountShareLike(java.math.BigDecimal value) {
            addCriterion("ca_amount_share like", value, "caAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaAmountShareNotLike(java.math.BigDecimal value) {
            addCriterion("ca_amount_share not like", value, "caAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaAmountShareIn(List<java.math.BigDecimal> values) {
            addCriterion("ca_amount_share in", values, "caAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaAmountShareNotIn(List<java.math.BigDecimal> values) {
            addCriterion("ca_amount_share not in", values, "caAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaAmountShareBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ca_amount_share between", value1, value2, "caAmountShare");
            return (Criteria) this;
        }

        public Criteria andCaAmountShareNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ca_amount_share not between", value1, value2, "caAmountShare");
            return (Criteria) this;
        }
		        public Criteria andPreAmountShareIsNull() {
            addCriterion("pre_amount_share is null");
            return (Criteria) this;
        }

        public Criteria andPreAmountShareIsNotNull() {
            addCriterion("pre_amount_share is not null");
            return (Criteria) this;
        }

        public Criteria andPreAmountShareEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_amount_share =", value, "preAmountShare");
            return (Criteria) this;
        }

        public Criteria andPreAmountShareNotEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_amount_share <>", value, "preAmountShare");
            return (Criteria) this;
        }

        public Criteria andPreAmountShareGreaterThan(java.math.BigDecimal value) {
            addCriterion("pre_amount_share >", value, "preAmountShare");
            return (Criteria) this;
        }

        public Criteria andPreAmountShareGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_amount_share >=", value, "preAmountShare");
            return (Criteria) this;
        }

        public Criteria andPreAmountShareLessThan(java.math.BigDecimal value) {
            addCriterion("pre_amount_share <", value, "preAmountShare");
            return (Criteria) this;
        }

        public Criteria andPreAmountShareLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_amount_share <=", value, "preAmountShare");
            return (Criteria) this;
        }

        public Criteria andPreAmountShareLike(java.math.BigDecimal value) {
            addCriterion("pre_amount_share like", value, "preAmountShare");
            return (Criteria) this;
        }

        public Criteria andPreAmountShareNotLike(java.math.BigDecimal value) {
            addCriterion("pre_amount_share not like", value, "preAmountShare");
            return (Criteria) this;
        }

        public Criteria andPreAmountShareIn(List<java.math.BigDecimal> values) {
            addCriterion("pre_amount_share in", values, "preAmountShare");
            return (Criteria) this;
        }

        public Criteria andPreAmountShareNotIn(List<java.math.BigDecimal> values) {
            addCriterion("pre_amount_share not in", values, "preAmountShare");
            return (Criteria) this;
        }

        public Criteria andPreAmountShareBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("pre_amount_share between", value1, value2, "preAmountShare");
            return (Criteria) this;
        }

        public Criteria andPreAmountShareNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("pre_amount_share not between", value1, value2, "preAmountShare");
            return (Criteria) this;
        }
		        public Criteria andAmountShareIsNull() {
            addCriterion("amount_share is null");
            return (Criteria) this;
        }

        public Criteria andAmountShareIsNotNull() {
            addCriterion("amount_share is not null");
            return (Criteria) this;
        }

        public Criteria andAmountShareEqualTo(java.math.BigDecimal value) {
            addCriterion("amount_share =", value, "amountShare");
            return (Criteria) this;
        }

        public Criteria andAmountShareNotEqualTo(java.math.BigDecimal value) {
            addCriterion("amount_share <>", value, "amountShare");
            return (Criteria) this;
        }

        public Criteria andAmountShareGreaterThan(java.math.BigDecimal value) {
            addCriterion("amount_share >", value, "amountShare");
            return (Criteria) this;
        }

        public Criteria andAmountShareGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("amount_share >=", value, "amountShare");
            return (Criteria) this;
        }

        public Criteria andAmountShareLessThan(java.math.BigDecimal value) {
            addCriterion("amount_share <", value, "amountShare");
            return (Criteria) this;
        }

        public Criteria andAmountShareLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("amount_share <=", value, "amountShare");
            return (Criteria) this;
        }

        public Criteria andAmountShareLike(java.math.BigDecimal value) {
            addCriterion("amount_share like", value, "amountShare");
            return (Criteria) this;
        }

        public Criteria andAmountShareNotLike(java.math.BigDecimal value) {
            addCriterion("amount_share not like", value, "amountShare");
            return (Criteria) this;
        }

        public Criteria andAmountShareIn(List<java.math.BigDecimal> values) {
            addCriterion("amount_share in", values, "amountShare");
            return (Criteria) this;
        }

        public Criteria andAmountShareNotIn(List<java.math.BigDecimal> values) {
            addCriterion("amount_share not in", values, "amountShare");
            return (Criteria) this;
        }

        public Criteria andAmountShareBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("amount_share between", value1, value2, "amountShare");
            return (Criteria) this;
        }

        public Criteria andAmountShareNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("amount_share not between", value1, value2, "amountShare");
            return (Criteria) this;
        }
		        public Criteria andExchangeRateVersionIsNull() {
            addCriterion("exchange_rate_version is null");
            return (Criteria) this;
        }

        public Criteria andExchangeRateVersionIsNotNull() {
            addCriterion("exchange_rate_version is not null");
            return (Criteria) this;
        }

        public Criteria andExchangeRateVersionEqualTo(Integer value) {
            addCriterion("exchange_rate_version =", value, "exchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andExchangeRateVersionNotEqualTo(Integer value) {
            addCriterion("exchange_rate_version <>", value, "exchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andExchangeRateVersionGreaterThan(Integer value) {
            addCriterion("exchange_rate_version >", value, "exchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andExchangeRateVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("exchange_rate_version >=", value, "exchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andExchangeRateVersionLessThan(Integer value) {
            addCriterion("exchange_rate_version <", value, "exchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andExchangeRateVersionLessThanOrEqualTo(Integer value) {
            addCriterion("exchange_rate_version <=", value, "exchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andExchangeRateVersionLike(Integer value) {
            addCriterion("exchange_rate_version like", value, "exchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andExchangeRateVersionNotLike(Integer value) {
            addCriterion("exchange_rate_version not like", value, "exchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andExchangeRateVersionIn(List<Integer> values) {
            addCriterion("exchange_rate_version in", values, "exchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andExchangeRateVersionNotIn(List<Integer> values) {
            addCriterion("exchange_rate_version not in", values, "exchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andExchangeRateVersionBetween(Integer value1, Integer value2) {
            addCriterion("exchange_rate_version between", value1, value2, "exchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andExchangeRateVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("exchange_rate_version not between", value1, value2, "exchangeRateVersion");
            return (Criteria) this;
        }
		        public Criteria andNewExchangeRateVersionIsNull() {
            addCriterion("new_exchange_rate_version is null");
            return (Criteria) this;
        }

        public Criteria andNewExchangeRateVersionIsNotNull() {
            addCriterion("new_exchange_rate_version is not null");
            return (Criteria) this;
        }

        public Criteria andNewExchangeRateVersionEqualTo(Integer value) {
            addCriterion("new_exchange_rate_version =", value, "newExchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andNewExchangeRateVersionNotEqualTo(Integer value) {
            addCriterion("new_exchange_rate_version <>", value, "newExchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andNewExchangeRateVersionGreaterThan(Integer value) {
            addCriterion("new_exchange_rate_version >", value, "newExchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andNewExchangeRateVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("new_exchange_rate_version >=", value, "newExchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andNewExchangeRateVersionLessThan(Integer value) {
            addCriterion("new_exchange_rate_version <", value, "newExchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andNewExchangeRateVersionLessThanOrEqualTo(Integer value) {
            addCriterion("new_exchange_rate_version <=", value, "newExchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andNewExchangeRateVersionLike(Integer value) {
            addCriterion("new_exchange_rate_version like", value, "newExchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andNewExchangeRateVersionNotLike(Integer value) {
            addCriterion("new_exchange_rate_version not like", value, "newExchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andNewExchangeRateVersionIn(List<Integer> values) {
            addCriterion("new_exchange_rate_version in", values, "newExchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andNewExchangeRateVersionNotIn(List<Integer> values) {
            addCriterion("new_exchange_rate_version not in", values, "newExchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andNewExchangeRateVersionBetween(Integer value1, Integer value2) {
            addCriterion("new_exchange_rate_version between", value1, value2, "newExchangeRateVersion");
            return (Criteria) this;
        }

        public Criteria andNewExchangeRateVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("new_exchange_rate_version not between", value1, value2, "newExchangeRateVersion");
            return (Criteria) this;
        }
		        public Criteria andCaPreLowRiskAmtBalanceIsNull() {
            addCriterion("ca_pre_low_risk_amt_balance is null");
            return (Criteria) this;
        }

        public Criteria andCaPreLowRiskAmtBalanceIsNotNull() {
            addCriterion("ca_pre_low_risk_amt_balance is not null");
            return (Criteria) this;
        }

        public Criteria andCaPreLowRiskAmtBalanceEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_pre_low_risk_amt_balance =", value, "caPreLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreLowRiskAmtBalanceNotEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_pre_low_risk_amt_balance <>", value, "caPreLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreLowRiskAmtBalanceGreaterThan(java.math.BigDecimal value) {
            addCriterion("ca_pre_low_risk_amt_balance >", value, "caPreLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreLowRiskAmtBalanceGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_pre_low_risk_amt_balance >=", value, "caPreLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreLowRiskAmtBalanceLessThan(java.math.BigDecimal value) {
            addCriterion("ca_pre_low_risk_amt_balance <", value, "caPreLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreLowRiskAmtBalanceLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_pre_low_risk_amt_balance <=", value, "caPreLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreLowRiskAmtBalanceLike(java.math.BigDecimal value) {
            addCriterion("ca_pre_low_risk_amt_balance like", value, "caPreLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreLowRiskAmtBalanceNotLike(java.math.BigDecimal value) {
            addCriterion("ca_pre_low_risk_amt_balance not like", value, "caPreLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreLowRiskAmtBalanceIn(List<java.math.BigDecimal> values) {
            addCriterion("ca_pre_low_risk_amt_balance in", values, "caPreLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreLowRiskAmtBalanceNotIn(List<java.math.BigDecimal> values) {
            addCriterion("ca_pre_low_risk_amt_balance not in", values, "caPreLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreLowRiskAmtBalanceBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ca_pre_low_risk_amt_balance between", value1, value2, "caPreLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreLowRiskAmtBalanceNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ca_pre_low_risk_amt_balance not between", value1, value2, "caPreLowRiskAmtBalance");
            return (Criteria) this;
        }
		        public Criteria andCaLowRiskAmtBalanceIsNull() {
            addCriterion("ca_low_risk_amt_balance is null");
            return (Criteria) this;
        }

        public Criteria andCaLowRiskAmtBalanceIsNotNull() {
            addCriterion("ca_low_risk_amt_balance is not null");
            return (Criteria) this;
        }

        public Criteria andCaLowRiskAmtBalanceEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_low_risk_amt_balance =", value, "caLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaLowRiskAmtBalanceNotEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_low_risk_amt_balance <>", value, "caLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaLowRiskAmtBalanceGreaterThan(java.math.BigDecimal value) {
            addCriterion("ca_low_risk_amt_balance >", value, "caLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaLowRiskAmtBalanceGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_low_risk_amt_balance >=", value, "caLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaLowRiskAmtBalanceLessThan(java.math.BigDecimal value) {
            addCriterion("ca_low_risk_amt_balance <", value, "caLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaLowRiskAmtBalanceLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_low_risk_amt_balance <=", value, "caLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaLowRiskAmtBalanceLike(java.math.BigDecimal value) {
            addCriterion("ca_low_risk_amt_balance like", value, "caLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaLowRiskAmtBalanceNotLike(java.math.BigDecimal value) {
            addCriterion("ca_low_risk_amt_balance not like", value, "caLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaLowRiskAmtBalanceIn(List<java.math.BigDecimal> values) {
            addCriterion("ca_low_risk_amt_balance in", values, "caLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaLowRiskAmtBalanceNotIn(List<java.math.BigDecimal> values) {
            addCriterion("ca_low_risk_amt_balance not in", values, "caLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaLowRiskAmtBalanceBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ca_low_risk_amt_balance between", value1, value2, "caLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andCaLowRiskAmtBalanceNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ca_low_risk_amt_balance not between", value1, value2, "caLowRiskAmtBalance");
            return (Criteria) this;
        }
		        public Criteria andPreLowRiskAmtBalanceIsNull() {
            addCriterion("pre_low_risk_amt_balance is null");
            return (Criteria) this;
        }

        public Criteria andPreLowRiskAmtBalanceIsNotNull() {
            addCriterion("pre_low_risk_amt_balance is not null");
            return (Criteria) this;
        }

        public Criteria andPreLowRiskAmtBalanceEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_low_risk_amt_balance =", value, "preLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andPreLowRiskAmtBalanceNotEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_low_risk_amt_balance <>", value, "preLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andPreLowRiskAmtBalanceGreaterThan(java.math.BigDecimal value) {
            addCriterion("pre_low_risk_amt_balance >", value, "preLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andPreLowRiskAmtBalanceGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_low_risk_amt_balance >=", value, "preLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andPreLowRiskAmtBalanceLessThan(java.math.BigDecimal value) {
            addCriterion("pre_low_risk_amt_balance <", value, "preLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andPreLowRiskAmtBalanceLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_low_risk_amt_balance <=", value, "preLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andPreLowRiskAmtBalanceLike(java.math.BigDecimal value) {
            addCriterion("pre_low_risk_amt_balance like", value, "preLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andPreLowRiskAmtBalanceNotLike(java.math.BigDecimal value) {
            addCriterion("pre_low_risk_amt_balance not like", value, "preLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andPreLowRiskAmtBalanceIn(List<java.math.BigDecimal> values) {
            addCriterion("pre_low_risk_amt_balance in", values, "preLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andPreLowRiskAmtBalanceNotIn(List<java.math.BigDecimal> values) {
            addCriterion("pre_low_risk_amt_balance not in", values, "preLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andPreLowRiskAmtBalanceBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("pre_low_risk_amt_balance between", value1, value2, "preLowRiskAmtBalance");
            return (Criteria) this;
        }

        public Criteria andPreLowRiskAmtBalanceNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("pre_low_risk_amt_balance not between", value1, value2, "preLowRiskAmtBalance");
            return (Criteria) this;
        }
		        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }
		        public Criteria andCaAmountBalanceIsNull() {
            addCriterion("ca_amount_balance is null");
            return (Criteria) this;
        }

        public Criteria andCaAmountBalanceIsNotNull() {
            addCriterion("ca_amount_balance is not null");
            return (Criteria) this;
        }

        public Criteria andCaAmountBalanceEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_amount_balance =", value, "caAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaAmountBalanceNotEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_amount_balance <>", value, "caAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaAmountBalanceGreaterThan(java.math.BigDecimal value) {
            addCriterion("ca_amount_balance >", value, "caAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaAmountBalanceGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_amount_balance >=", value, "caAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaAmountBalanceLessThan(java.math.BigDecimal value) {
            addCriterion("ca_amount_balance <", value, "caAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaAmountBalanceLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_amount_balance <=", value, "caAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaAmountBalanceLike(java.math.BigDecimal value) {
            addCriterion("ca_amount_balance like", value, "caAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaAmountBalanceNotLike(java.math.BigDecimal value) {
            addCriterion("ca_amount_balance not like", value, "caAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaAmountBalanceIn(List<java.math.BigDecimal> values) {
            addCriterion("ca_amount_balance in", values, "caAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaAmountBalanceNotIn(List<java.math.BigDecimal> values) {
            addCriterion("ca_amount_balance not in", values, "caAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaAmountBalanceBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ca_amount_balance between", value1, value2, "caAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaAmountBalanceNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ca_amount_balance not between", value1, value2, "caAmountBalance");
            return (Criteria) this;
        }
		        public Criteria andCaPreAmountBalanceIsNull() {
            addCriterion("ca_pre_amount_balance is null");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountBalanceIsNotNull() {
            addCriterion("ca_pre_amount_balance is not null");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountBalanceEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_balance =", value, "caPreAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountBalanceNotEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_balance <>", value, "caPreAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountBalanceGreaterThan(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_balance >", value, "caPreAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountBalanceGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_balance >=", value, "caPreAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountBalanceLessThan(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_balance <", value, "caPreAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountBalanceLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_balance <=", value, "caPreAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountBalanceLike(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_balance like", value, "caPreAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountBalanceNotLike(java.math.BigDecimal value) {
            addCriterion("ca_pre_amount_balance not like", value, "caPreAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountBalanceIn(List<java.math.BigDecimal> values) {
            addCriterion("ca_pre_amount_balance in", values, "caPreAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountBalanceNotIn(List<java.math.BigDecimal> values) {
            addCriterion("ca_pre_amount_balance not in", values, "caPreAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountBalanceBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ca_pre_amount_balance between", value1, value2, "caPreAmountBalance");
            return (Criteria) this;
        }

        public Criteria andCaPreAmountBalanceNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("ca_pre_amount_balance not between", value1, value2, "caPreAmountBalance");
            return (Criteria) this;
        }
		        public Criteria andPreAmountBalanceIsNull() {
            addCriterion("pre_amount_balance is null");
            return (Criteria) this;
        }

        public Criteria andPreAmountBalanceIsNotNull() {
            addCriterion("pre_amount_balance is not null");
            return (Criteria) this;
        }

        public Criteria andPreAmountBalanceEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_amount_balance =", value, "preAmountBalance");
            return (Criteria) this;
        }

        public Criteria andPreAmountBalanceNotEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_amount_balance <>", value, "preAmountBalance");
            return (Criteria) this;
        }

        public Criteria andPreAmountBalanceGreaterThan(java.math.BigDecimal value) {
            addCriterion("pre_amount_balance >", value, "preAmountBalance");
            return (Criteria) this;
        }

        public Criteria andPreAmountBalanceGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_amount_balance >=", value, "preAmountBalance");
            return (Criteria) this;
        }

        public Criteria andPreAmountBalanceLessThan(java.math.BigDecimal value) {
            addCriterion("pre_amount_balance <", value, "preAmountBalance");
            return (Criteria) this;
        }

        public Criteria andPreAmountBalanceLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_amount_balance <=", value, "preAmountBalance");
            return (Criteria) this;
        }

        public Criteria andPreAmountBalanceLike(java.math.BigDecimal value) {
            addCriterion("pre_amount_balance like", value, "preAmountBalance");
            return (Criteria) this;
        }

        public Criteria andPreAmountBalanceNotLike(java.math.BigDecimal value) {
            addCriterion("pre_amount_balance not like", value, "preAmountBalance");
            return (Criteria) this;
        }

        public Criteria andPreAmountBalanceIn(List<java.math.BigDecimal> values) {
            addCriterion("pre_amount_balance in", values, "preAmountBalance");
            return (Criteria) this;
        }

        public Criteria andPreAmountBalanceNotIn(List<java.math.BigDecimal> values) {
            addCriterion("pre_amount_balance not in", values, "preAmountBalance");
            return (Criteria) this;
        }

        public Criteria andPreAmountBalanceBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("pre_amount_balance between", value1, value2, "preAmountBalance");
            return (Criteria) this;
        }

        public Criteria andPreAmountBalanceNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("pre_amount_balance not between", value1, value2, "preAmountBalance");
            return (Criteria) this;
        }
		        public Criteria andAmountBalanceIsNull() {
            addCriterion("amount_balance is null");
            return (Criteria) this;
        }

        public Criteria andAmountBalanceIsNotNull() {
            addCriterion("amount_balance is not null");
            return (Criteria) this;
        }

        public Criteria andAmountBalanceEqualTo(java.math.BigDecimal value) {
            addCriterion("amount_balance =", value, "amountBalance");
            return (Criteria) this;
        }

        public Criteria andAmountBalanceNotEqualTo(java.math.BigDecimal value) {
            addCriterion("amount_balance <>", value, "amountBalance");
            return (Criteria) this;
        }

        public Criteria andAmountBalanceGreaterThan(java.math.BigDecimal value) {
            addCriterion("amount_balance >", value, "amountBalance");
            return (Criteria) this;
        }

        public Criteria andAmountBalanceGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("amount_balance >=", value, "amountBalance");
            return (Criteria) this;
        }

        public Criteria andAmountBalanceLessThan(java.math.BigDecimal value) {
            addCriterion("amount_balance <", value, "amountBalance");
            return (Criteria) this;
        }

        public Criteria andAmountBalanceLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("amount_balance <=", value, "amountBalance");
            return (Criteria) this;
        }

        public Criteria andAmountBalanceLike(java.math.BigDecimal value) {
            addCriterion("amount_balance like", value, "amountBalance");
            return (Criteria) this;
        }

        public Criteria andAmountBalanceNotLike(java.math.BigDecimal value) {
            addCriterion("amount_balance not like", value, "amountBalance");
            return (Criteria) this;
        }

        public Criteria andAmountBalanceIn(List<java.math.BigDecimal> values) {
            addCriterion("amount_balance in", values, "amountBalance");
            return (Criteria) this;
        }

        public Criteria andAmountBalanceNotIn(List<java.math.BigDecimal> values) {
            addCriterion("amount_balance not in", values, "amountBalance");
            return (Criteria) this;
        }

        public Criteria andAmountBalanceBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("amount_balance between", value1, value2, "amountBalance");
            return (Criteria) this;
        }

        public Criteria andAmountBalanceNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("amount_balance not between", value1, value2, "amountBalance");
            return (Criteria) this;
        }
		        public Criteria andLimitObjectIdIsNull() {
            addCriterion("limit_object_id is null");
            return (Criteria) this;
        }

        public Criteria andLimitObjectIdIsNotNull() {
            addCriterion("limit_object_id is not null");
            return (Criteria) this;
        }

        public Criteria andLimitObjectIdEqualTo(String value) {
            addCriterion("limit_object_id =", value, "limitObjectId");
            return (Criteria) this;
        }

        public Criteria andLimitObjectIdNotEqualTo(String value) {
            addCriterion("limit_object_id <>", value, "limitObjectId");
            return (Criteria) this;
        }

        public Criteria andLimitObjectIdGreaterThan(String value) {
            addCriterion("limit_object_id >", value, "limitObjectId");
            return (Criteria) this;
        }

        public Criteria andLimitObjectIdGreaterThanOrEqualTo(String value) {
            addCriterion("limit_object_id >=", value, "limitObjectId");
            return (Criteria) this;
        }

        public Criteria andLimitObjectIdLessThan(String value) {
            addCriterion("limit_object_id <", value, "limitObjectId");
            return (Criteria) this;
        }

        public Criteria andLimitObjectIdLessThanOrEqualTo(String value) {
            addCriterion("limit_object_id <=", value, "limitObjectId");
            return (Criteria) this;
        }

        public Criteria andLimitObjectIdLike(String value) {
            addCriterion("limit_object_id like", value, "limitObjectId");
            return (Criteria) this;
        }

        public Criteria andLimitObjectIdNotLike(String value) {
            addCriterion("limit_object_id not like", value, "limitObjectId");
            return (Criteria) this;
        }

        public Criteria andLimitObjectIdIn(List<String> values) {
            addCriterion("limit_object_id in", values, "limitObjectId");
            return (Criteria) this;
        }

        public Criteria andLimitObjectIdNotIn(List<String> values) {
            addCriterion("limit_object_id not in", values, "limitObjectId");
            return (Criteria) this;
        }

        public Criteria andLimitObjectIdBetween(String value1, String value2) {
            addCriterion("limit_object_id between", value1, value2, "limitObjectId");
            return (Criteria) this;
        }

        public Criteria andLimitObjectIdNotBetween(String value1, String value2) {
            addCriterion("limit_object_id not between", value1, value2, "limitObjectId");
            return (Criteria) this;
        }
		        public Criteria andEntityIdIsNull() {
            addCriterion("entity_id is null");
            return (Criteria) this;
        }

        public Criteria andEntityIdIsNotNull() {
            addCriterion("entity_id is not null");
            return (Criteria) this;
        }

        public Criteria andEntityIdEqualTo(String value) {
            addCriterion("entity_id =", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdNotEqualTo(String value) {
            addCriterion("entity_id <>", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdGreaterThan(String value) {
            addCriterion("entity_id >", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdGreaterThanOrEqualTo(String value) {
            addCriterion("entity_id >=", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdLessThan(String value) {
            addCriterion("entity_id <", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdLessThanOrEqualTo(String value) {
            addCriterion("entity_id <=", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdLike(String value) {
            addCriterion("entity_id like", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdNotLike(String value) {
            addCriterion("entity_id not like", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdIn(List<String> values) {
            addCriterion("entity_id in", values, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdNotIn(List<String> values) {
            addCriterion("entity_id not in", values, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdBetween(String value1, String value2) {
            addCriterion("entity_id between", value1, value2, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdNotBetween(String value1, String value2) {
            addCriterion("entity_id not between", value1, value2, "entityId");
            return (Criteria) this;
        }
		        public Criteria andCustLimitIdIsNull() {
            addCriterion("cust_limit_id is null");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdIsNotNull() {
            addCriterion("cust_limit_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdEqualTo(String value) {
            addCriterion("cust_limit_id =", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotEqualTo(String value) {
            addCriterion("cust_limit_id <>", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdGreaterThan(String value) {
            addCriterion("cust_limit_id >", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdGreaterThanOrEqualTo(String value) {
            addCriterion("cust_limit_id >=", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdLessThan(String value) {
            addCriterion("cust_limit_id <", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdLessThanOrEqualTo(String value) {
            addCriterion("cust_limit_id <=", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdLike(String value) {
            addCriterion("cust_limit_id like", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotLike(String value) {
            addCriterion("cust_limit_id not like", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdIn(List<String> values) {
            addCriterion("cust_limit_id in", values, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotIn(List<String> values) {
            addCriterion("cust_limit_id not in", values, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdBetween(String value1, String value2) {
            addCriterion("cust_limit_id between", value1, value2, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotBetween(String value1, String value2) {
            addCriterion("cust_limit_id not between", value1, value2, "custLimitId");
            return (Criteria) this;
        }
		        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLike(Date value) {
            addCriterion("update_time like", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotLike(Date value) {
            addCriterion("update_time not like", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
		        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLike(Date value) {
            addCriterion("create_time like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotLike(Date value) {
            addCriterion("create_time not like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
		    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}