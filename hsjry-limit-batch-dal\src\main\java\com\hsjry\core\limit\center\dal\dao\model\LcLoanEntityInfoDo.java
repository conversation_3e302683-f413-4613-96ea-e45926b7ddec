package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import lombok.Data;

/**
 * 借据实体对象
 *
 * <AUTHOR>
 * @since 2024-01-31 08:52:41
 */
@Data
public class LcLoanEntityInfoDo implements Serializable {
    private static final long serialVersionUID = -4154866884955895148L;
    /** 实体编号 */
    private String entityId;
    /** 所属对象编号 */
    private String limitObjectId;
    /** 产品编号 */
    private String productId;
    /** 产品名称 */
    private String productName;
    /** 实体类型;EnumLimitEntityType:001-借据、002-信用证、003-保函、004-垫款、005-贴现、006-贷款承诺 */
    private String entityType;
    /** 发放金额 */
    private java.math.BigDecimal amount;
    /** 当前余额 */
    private java.math.BigDecimal leftAmount;
    /** 预发放金额 */
    private java.math.BigDecimal preGrantAmount;
    /** 金额币种 */
    private String amountCurrency;
    /** 额度编号 */
    private String custLimitId;
    /** 状态;EnumLimitEntityStatus(010-未结清,020-结清,030-已冲账,040-已撤销) */
    private String status;
    /** 实体所属系统 */
    private String systemSign;
    /** 发放时间 */
    private java.util.Date grantDateTime;
    /** 更新时间 */
    private java.util.Date updateTime;

    /** 同业金融产品编号 */
    private String ibFinancialProdId;
    /** 同业金融产品名称 */
    private String ibFinancialProdName;

    /** 客户编号 */
    private String userId;
    /** 客户名称 */
    private String userName;
    /** 实体业务编号 */
    private String entityRelationId;
    /** 关联合同编号 */
    private String entityContractId;
    /** 操作人编号 */
    private String operatorId;
    /** 所属组织编号 */
    private String ownOrganId;
    /** 所属系统 */
    private String channelNo;
}
