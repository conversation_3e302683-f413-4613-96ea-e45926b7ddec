<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.center.dal.dao.mapper.EntityOperateSerialBatchMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.center.dal.dao.model.LcEntityOperateSerialDo">
        <result property="inboundSerialNo" column="inbound_serial_no" jdbcType="VARCHAR"/> <!-- 前置业务流水 -->
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
        <result property="tenantId" column="tenant_id" jdbcType="CHAR"/> <!-- 租户号 -->
        <result property="systemSign" column="system_sign" jdbcType="CHAR"/> <!-- 实体所属系统 -->
        <result property="serialNo" column="serial_no" jdbcType="VARCHAR"/> <!-- 业务流水号 -->
        <result property="preGrantExpiryDateStr" column="pre_grant_expiry_date_str"
                jdbcType="VARCHAR"/> <!-- 预发放截止时间;YYYYMMdd -->
        <result property="operateType" column="operate_type"
                jdbcType="CHAR"/> <!-- 操作类型;EnumEntityOperateType(001-强制发放,002-预发放,003-发放,004-归还,005-强制发放取消,006-预发放取消,007-发放取消,008-归还取消) -->
        <result property="operateStatus" column="operate_status"
                jdbcType="CHAR"/> <!-- 状态;EnumEntityOperatorStatus：010-处理中，020-成功，030-失败，040-取消 -->
        <result property="lowRiskCurrency" column="low_risk_currency" jdbcType="CHAR"/> <!-- 低风险币种 -->
        <result property="lowRiskAmountId" column="low_risk_amount_id" jdbcType="VARCHAR"/> <!-- 操作低风险金额编号 -->
        <result property="lowRiskAmount" column="low_risk_amount" jdbcType="DECIMAL"/> <!-- 操作低风险 -->
        <result property="leosSerialNo" column="leos_serial_no" jdbcType="VARCHAR"/> <!-- 实体操作流水 -->
        <result property="lastInboundSerialNo" column="last_inbound_serial_no" jdbcType="VARCHAR"/> <!-- 前置业务关联流水 -->
        <result property="amount" column="amount" jdbcType="DECIMAL"/> <!-- 操作金额 -->
        <result property="inboundSerialDatetime" column="inbound_serial_datetime" jdbcType="TIMESTAMP"/> <!-- 前置业务时间 -->
        <result property="globalSerialNo" column="global_serial_no" jdbcType="VARCHAR"/> <!-- 全局流水号 -->
        <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/> <!-- 失败原因 -->
        <result property="entityRelationId" column="entity_relation_id" jdbcType="VARCHAR"/> <!-- 实体业务编号 -->
        <result property="entityId" column="entity_id" jdbcType="VARCHAR"/> <!-- 实体编号 -->
        <result property="entityApplyId" column="entity_apply_id" jdbcType="VARCHAR"/> <!-- 实体申请编号 -->
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
        <result property="channelNo" column="channel_no" jdbcType="CHAR"/> <!-- 渠道号 -->
        <result property="bizDatetime" column="biz_datetime" jdbcType="TIMESTAMP"/> <!-- 业务时间 -->
        <result property="amountId" column="amount_id" jdbcType="VARCHAR"/> <!-- 操作金额编号 -->
        <result property="amountCurrency" column="amount_currency" jdbcType="CHAR"/> <!-- 金额币种 -->
    </resultMap>
    <sql id="Base_Column_List">
        inbound_serial_no
        , update_time
                , tenant_id
                , system_sign
                , serial_no
                , pre_grant_expiry_date_str
                , operate_type
                , operate_status
                , low_risk_currency
                , low_risk_amount_id
                , low_risk_amount
                , leos_serial_no
                , last_inbound_serial_no
                , amount
                , inbound_serial_datetime
                , global_serial_no
                , fail_reason
                , entity_relation_id
                , entity_id
                , entity_apply_id
                , create_time
                , channel_no
                , biz_datetime
                , amount_id
                , amount_currency
    </sql>
    <select id="selectCountByCurrentGroup" resultType="java.lang.Integer">
        SELECT count(*)
        FROM lc_entity_operate_serial WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectFirstOne" parameterType="com.hsjry.core.limit.center.dal.dao.query.EntityOperateSerialBatchQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_entity_operate_serial
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectShardList"
            parameterType="com.hsjry.core.limit.center.dal.dao.query.EntityOperateSerialBatchQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_entity_operate_serial
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>

    <sql id="fixQuerySql">
        <if test="query.preGrantExpiryDateStr !=null and query.preGrantExpiryDateStr!=''">
            AND pre_grant_expiry_date_str &lt; #{query.preGrantExpiryDateStr}
        </if>
        <if test="query.operateType != null and query.operateType!=''">
            AND operate_type = #{query.operateType}
        </if>
        <if test="query.operateStatus != null and query.operateStatus!=''">
            AND operate_status = #{query.operateStatus}
        </if>
        <if test="query.entityId != null and query.entityId!=''">
            AND entity_id > #{query.entityId}
        </if>
        <if test="query.inboundSerialNoList !=null">
            AND inbound_serial_no in
            <foreach collection="query.inboundSerialNoList" index="index" item="item"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY entity_id
    </sql>
</mapper>