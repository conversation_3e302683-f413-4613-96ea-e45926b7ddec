truncate table lb_t_ibnk_cust_info;
select *
from lb_t_ibnk_cust_info;
insert into lb_t_ibnk_cust_info(cust_no, cust_typ, cust_nm, cert_typ, cert_no, operator_id, own_organ_id, tenant_id,
                                create_time, update_time)
select distinct lcloi.user_id,
                lcloi.user_type,
                lcloi.user_name,
                lcloi.user_certificate_kind,
                lcloi.user_certificate_no,
                'admin'  as operator_id,
                '100000' as own_organ_id,
                lcloi.tenant_id,
                sysdate,
                sysdate
from lc_cust_limit_object_info lcloi
where lcloi.user_type = '110'
  and lcloi.user_certificate_kind = '610063'
  and lcloi.user_certificate_no is not null
  and length(lcloi.user_certificate_no) = 18;