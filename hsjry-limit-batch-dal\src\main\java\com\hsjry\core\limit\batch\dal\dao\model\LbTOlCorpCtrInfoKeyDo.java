package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 网贷系统-中间表-对公合同信息主键
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
@Table(name = "lb_t_ol_corp_ctr_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbTOlCorpCtrInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1950902007284039682L;
    /** 客户编号 */
    @Id
    @Column(name = "cust_no")
    private String custNo;
    /** 额度编号 */
    @Id
    @Column(name = "cust_limit_id")
    private String custLimitId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}