package com.hsjry.core.limit.batch.dal.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 电票系统-中间表-贴现对公借据信息Example
 *
 * <AUTHOR>
 * @date 2025-07-29 02:46:35
 */
public class LbTElcblCorpLoanInfoExample {
    /** 排序字段 */
    protected String orderByClause;

    /** 是否只查询记录不同的数据 */
    protected boolean distinct;

    /** or条件查询集合 */
    protected List<Criteria> oredCriteria;

    /**
     * 初始化or条件查询集合
     */
    public LbTElcblCorpLoanInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * 排序字段set方法
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * 排序字段get方法
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * 不同记录set方法.
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * 不同记录get方法.
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * 排序集合get方法
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * or
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * or
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * 清除数据
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andEntityRelationIdIsNull() {
            addCriterion("entity_relation_id is null");
            return (Criteria) this;
        }

        public Criteria andEntityRelationIdIsNotNull() {
            addCriterion("entity_relation_id is not null");
            return (Criteria) this;
        }

        public Criteria andEntityRelationIdEqualTo(String value) {
            addCriterion("entity_relation_id =", value, "entityRelationId");
            return (Criteria) this;
        }

        public Criteria andEntityRelationIdNotEqualTo(String value) {
            addCriterion("entity_relation_id <>", value, "entityRelationId");
            return (Criteria) this;
        }

        public Criteria andEntityRelationIdGreaterThan(String value) {
            addCriterion("entity_relation_id >", value, "entityRelationId");
            return (Criteria) this;
        }

        public Criteria andEntityRelationIdGreaterThanOrEqualTo(String value) {
            addCriterion("entity_relation_id >=", value, "entityRelationId");
            return (Criteria) this;
        }

        public Criteria andEntityRelationIdLessThan(String value) {
            addCriterion("entity_relation_id <", value, "entityRelationId");
            return (Criteria) this;
        }

        public Criteria andEntityRelationIdLessThanOrEqualTo(String value) {
            addCriterion("entity_relation_id <=", value, "entityRelationId");
            return (Criteria) this;
        }

        public Criteria andEntityRelationIdLike(String value) {
            addCriterion("entity_relation_id like", value, "entityRelationId");
            return (Criteria) this;
        }

        public Criteria andEntityRelationIdNotLike(String value) {
            addCriterion("entity_relation_id not like", value, "entityRelationId");
            return (Criteria) this;
        }

        public Criteria andEntityRelationIdIn(List<String> values) {
            addCriterion("entity_relation_id in", values, "entityRelationId");
            return (Criteria) this;
        }

        public Criteria andEntityRelationIdNotIn(List<String> values) {
            addCriterion("entity_relation_id not in", values, "entityRelationId");
            return (Criteria) this;
        }

        public Criteria andEntityRelationIdBetween(String value1, String value2) {
            addCriterion("entity_relation_id between", value1, value2, "entityRelationId");
            return (Criteria) this;
        }

        public Criteria andEntityRelationIdNotBetween(String value1, String value2) {
            addCriterion("entity_relation_id not between", value1, value2, "entityRelationId");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLike(Date value) {
            addCriterion("update_time like", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotLike(Date value) {
            addCriterion("update_time not like", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLike(Date value) {
            addCriterion("create_time like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotLike(Date value) {
            addCriterion("create_time not like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdIsNull() {
            addCriterion("own_organ_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdIsNotNull() {
            addCriterion("own_organ_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdEqualTo(String value) {
            addCriterion("own_organ_id =", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdNotEqualTo(String value) {
            addCriterion("own_organ_id <>", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdGreaterThan(String value) {
            addCriterion("own_organ_id >", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdGreaterThanOrEqualTo(String value) {
            addCriterion("own_organ_id >=", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdLessThan(String value) {
            addCriterion("own_organ_id <", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdLessThanOrEqualTo(String value) {
            addCriterion("own_organ_id <=", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdLike(String value) {
            addCriterion("own_organ_id like", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdNotLike(String value) {
            addCriterion("own_organ_id not like", value, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdIn(List<String> values) {
            addCriterion("own_organ_id in", values, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdNotIn(List<String> values) {
            addCriterion("own_organ_id not in", values, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdBetween(String value1, String value2) {
            addCriterion("own_organ_id between", value1, value2, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOwnOrganIdNotBetween(String value1, String value2) {
            addCriterion("own_organ_id not between", value1, value2, "ownOrganId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIsNull() {
            addCriterion("operator_id is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIsNotNull() {
            addCriterion("operator_id is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorIdEqualTo(String value) {
            addCriterion("operator_id =", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotEqualTo(String value) {
            addCriterion("operator_id <>", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdGreaterThan(String value) {
            addCriterion("operator_id >", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdGreaterThanOrEqualTo(String value) {
            addCriterion("operator_id >=", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLessThan(String value) {
            addCriterion("operator_id <", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLessThanOrEqualTo(String value) {
            addCriterion("operator_id <=", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdLike(String value) {
            addCriterion("operator_id like", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotLike(String value) {
            addCriterion("operator_id not like", value, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdIn(List<String> values) {
            addCriterion("operator_id in", values, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotIn(List<String> values) {
            addCriterion("operator_id not in", values, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdBetween(String value1, String value2) {
            addCriterion("operator_id between", value1, value2, "operatorId");
            return (Criteria) this;
        }

        public Criteria andOperatorIdNotBetween(String value1, String value2) {
            addCriterion("operator_id not between", value1, value2, "operatorId");
            return (Criteria) this;
        }

        public Criteria andLeftLowRiskIsNull() {
            addCriterion("left_low_risk is null");
            return (Criteria) this;
        }

        public Criteria andLeftLowRiskIsNotNull() {
            addCriterion("left_low_risk is not null");
            return (Criteria) this;
        }

        public Criteria andLeftLowRiskEqualTo(java.math.BigDecimal value) {
            addCriterion("left_low_risk =", value, "leftLowRisk");
            return (Criteria) this;
        }

        public Criteria andLeftLowRiskNotEqualTo(java.math.BigDecimal value) {
            addCriterion("left_low_risk <>", value, "leftLowRisk");
            return (Criteria) this;
        }

        public Criteria andLeftLowRiskGreaterThan(java.math.BigDecimal value) {
            addCriterion("left_low_risk >", value, "leftLowRisk");
            return (Criteria) this;
        }

        public Criteria andLeftLowRiskGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("left_low_risk >=", value, "leftLowRisk");
            return (Criteria) this;
        }

        public Criteria andLeftLowRiskLessThan(java.math.BigDecimal value) {
            addCriterion("left_low_risk <", value, "leftLowRisk");
            return (Criteria) this;
        }

        public Criteria andLeftLowRiskLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("left_low_risk <=", value, "leftLowRisk");
            return (Criteria) this;
        }

        public Criteria andLeftLowRiskLike(java.math.BigDecimal value) {
            addCriterion("left_low_risk like", value, "leftLowRisk");
            return (Criteria) this;
        }

        public Criteria andLeftLowRiskNotLike(java.math.BigDecimal value) {
            addCriterion("left_low_risk not like", value, "leftLowRisk");
            return (Criteria) this;
        }

        public Criteria andLeftLowRiskIn(List<java.math.BigDecimal> values) {
            addCriterion("left_low_risk in", values, "leftLowRisk");
            return (Criteria) this;
        }

        public Criteria andLeftLowRiskNotIn(List<java.math.BigDecimal> values) {
            addCriterion("left_low_risk not in", values, "leftLowRisk");
            return (Criteria) this;
        }

        public Criteria andLeftLowRiskBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("left_low_risk between", value1, value2, "leftLowRisk");
            return (Criteria) this;
        }

        public Criteria andLeftLowRiskNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("left_low_risk not between", value1, value2, "leftLowRisk");
            return (Criteria) this;
        }

        public Criteria andLowRiskIsNull() {
            addCriterion("low_risk is null");
            return (Criteria) this;
        }

        public Criteria andLowRiskIsNotNull() {
            addCriterion("low_risk is not null");
            return (Criteria) this;
        }

        public Criteria andLowRiskEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk =", value, "lowRisk");
            return (Criteria) this;
        }

        public Criteria andLowRiskNotEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk <>", value, "lowRisk");
            return (Criteria) this;
        }

        public Criteria andLowRiskGreaterThan(java.math.BigDecimal value) {
            addCriterion("low_risk >", value, "lowRisk");
            return (Criteria) this;
        }

        public Criteria andLowRiskGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk >=", value, "lowRisk");
            return (Criteria) this;
        }

        public Criteria andLowRiskLessThan(java.math.BigDecimal value) {
            addCriterion("low_risk <", value, "lowRisk");
            return (Criteria) this;
        }

        public Criteria andLowRiskLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk <=", value, "lowRisk");
            return (Criteria) this;
        }

        public Criteria andLowRiskLike(java.math.BigDecimal value) {
            addCriterion("low_risk like", value, "lowRisk");
            return (Criteria) this;
        }

        public Criteria andLowRiskNotLike(java.math.BigDecimal value) {
            addCriterion("low_risk not like", value, "lowRisk");
            return (Criteria) this;
        }

        public Criteria andLowRiskIn(List<java.math.BigDecimal> values) {
            addCriterion("low_risk in", values, "lowRisk");
            return (Criteria) this;
        }

        public Criteria andLowRiskNotIn(List<java.math.BigDecimal> values) {
            addCriterion("low_risk not in", values, "lowRisk");
            return (Criteria) this;
        }

        public Criteria andLowRiskBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("low_risk between", value1, value2, "lowRisk");
            return (Criteria) this;
        }

        public Criteria andLowRiskNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("low_risk not between", value1, value2, "lowRisk");
            return (Criteria) this;
        }

        public Criteria andLeftAmountIsNull() {
            addCriterion("left_amount is null");
            return (Criteria) this;
        }

        public Criteria andLeftAmountIsNotNull() {
            addCriterion("left_amount is not null");
            return (Criteria) this;
        }

        public Criteria andLeftAmountEqualTo(java.math.BigDecimal value) {
            addCriterion("left_amount =", value, "leftAmount");
            return (Criteria) this;
        }

        public Criteria andLeftAmountNotEqualTo(java.math.BigDecimal value) {
            addCriterion("left_amount <>", value, "leftAmount");
            return (Criteria) this;
        }

        public Criteria andLeftAmountGreaterThan(java.math.BigDecimal value) {
            addCriterion("left_amount >", value, "leftAmount");
            return (Criteria) this;
        }

        public Criteria andLeftAmountGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("left_amount >=", value, "leftAmount");
            return (Criteria) this;
        }

        public Criteria andLeftAmountLessThan(java.math.BigDecimal value) {
            addCriterion("left_amount <", value, "leftAmount");
            return (Criteria) this;
        }

        public Criteria andLeftAmountLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("left_amount <=", value, "leftAmount");
            return (Criteria) this;
        }

        public Criteria andLeftAmountLike(java.math.BigDecimal value) {
            addCriterion("left_amount like", value, "leftAmount");
            return (Criteria) this;
        }

        public Criteria andLeftAmountNotLike(java.math.BigDecimal value) {
            addCriterion("left_amount not like", value, "leftAmount");
            return (Criteria) this;
        }

        public Criteria andLeftAmountIn(List<java.math.BigDecimal> values) {
            addCriterion("left_amount in", values, "leftAmount");
            return (Criteria) this;
        }

        public Criteria andLeftAmountNotIn(List<java.math.BigDecimal> values) {
            addCriterion("left_amount not in", values, "leftAmount");
            return (Criteria) this;
        }

        public Criteria andLeftAmountBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("left_amount between", value1, value2, "leftAmount");
            return (Criteria) this;
        }

        public Criteria andLeftAmountNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("left_amount not between", value1, value2, "leftAmount");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(java.math.BigDecimal value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(java.math.BigDecimal value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(java.math.BigDecimal value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(java.math.BigDecimal value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLike(java.math.BigDecimal value) {
            addCriterion("amount like", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotLike(java.math.BigDecimal value) {
            addCriterion("amount not like", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<java.math.BigDecimal> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<java.math.BigDecimal> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andCustNoIsNull() {
            addCriterion("cust_no is null");
            return (Criteria) this;
        }

        public Criteria andCustNoIsNotNull() {
            addCriterion("cust_no is not null");
            return (Criteria) this;
        }

        public Criteria andCustNoEqualTo(String value) {
            addCriterion("cust_no =", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoNotEqualTo(String value) {
            addCriterion("cust_no <>", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoGreaterThan(String value) {
            addCriterion("cust_no >", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoGreaterThanOrEqualTo(String value) {
            addCriterion("cust_no >=", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoLessThan(String value) {
            addCriterion("cust_no <", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoLessThanOrEqualTo(String value) {
            addCriterion("cust_no <=", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoLike(String value) {
            addCriterion("cust_no like", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoNotLike(String value) {
            addCriterion("cust_no not like", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoIn(List<String> values) {
            addCriterion("cust_no in", values, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoNotIn(List<String> values) {
            addCriterion("cust_no not in", values, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoBetween(String value1, String value2) {
            addCriterion("cust_no between", value1, value2, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoNotBetween(String value1, String value2) {
            addCriterion("cust_no not between", value1, value2, "custNo");
            return (Criteria) this;
        }

        public Criteria andEntityApplyIdIsNull() {
            addCriterion("entity_apply_id is null");
            return (Criteria) this;
        }

        public Criteria andEntityApplyIdIsNotNull() {
            addCriterion("entity_apply_id is not null");
            return (Criteria) this;
        }

        public Criteria andEntityApplyIdEqualTo(String value) {
            addCriterion("entity_apply_id =", value, "entityApplyId");
            return (Criteria) this;
        }

        public Criteria andEntityApplyIdNotEqualTo(String value) {
            addCriterion("entity_apply_id <>", value, "entityApplyId");
            return (Criteria) this;
        }

        public Criteria andEntityApplyIdGreaterThan(String value) {
            addCriterion("entity_apply_id >", value, "entityApplyId");
            return (Criteria) this;
        }

        public Criteria andEntityApplyIdGreaterThanOrEqualTo(String value) {
            addCriterion("entity_apply_id >=", value, "entityApplyId");
            return (Criteria) this;
        }

        public Criteria andEntityApplyIdLessThan(String value) {
            addCriterion("entity_apply_id <", value, "entityApplyId");
            return (Criteria) this;
        }

        public Criteria andEntityApplyIdLessThanOrEqualTo(String value) {
            addCriterion("entity_apply_id <=", value, "entityApplyId");
            return (Criteria) this;
        }

        public Criteria andEntityApplyIdLike(String value) {
            addCriterion("entity_apply_id like", value, "entityApplyId");
            return (Criteria) this;
        }

        public Criteria andEntityApplyIdNotLike(String value) {
            addCriterion("entity_apply_id not like", value, "entityApplyId");
            return (Criteria) this;
        }

        public Criteria andEntityApplyIdIn(List<String> values) {
            addCriterion("entity_apply_id in", values, "entityApplyId");
            return (Criteria) this;
        }

        public Criteria andEntityApplyIdNotIn(List<String> values) {
            addCriterion("entity_apply_id not in", values, "entityApplyId");
            return (Criteria) this;
        }

        public Criteria andEntityApplyIdBetween(String value1, String value2) {
            addCriterion("entity_apply_id between", value1, value2, "entityApplyId");
            return (Criteria) this;
        }

        public Criteria andEntityApplyIdNotBetween(String value1, String value2) {
            addCriterion("entity_apply_id not between", value1, value2, "entityApplyId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andEntityTypeIsNull() {
            addCriterion("entity_type is null");
            return (Criteria) this;
        }

        public Criteria andEntityTypeIsNotNull() {
            addCriterion("entity_type is not null");
            return (Criteria) this;
        }

        public Criteria andEntityTypeEqualTo(String value) {
            addCriterion("entity_type =", value, "entityType");
            return (Criteria) this;
        }

        public Criteria andEntityTypeNotEqualTo(String value) {
            addCriterion("entity_type <>", value, "entityType");
            return (Criteria) this;
        }

        public Criteria andEntityTypeGreaterThan(String value) {
            addCriterion("entity_type >", value, "entityType");
            return (Criteria) this;
        }

        public Criteria andEntityTypeGreaterThanOrEqualTo(String value) {
            addCriterion("entity_type >=", value, "entityType");
            return (Criteria) this;
        }

        public Criteria andEntityTypeLessThan(String value) {
            addCriterion("entity_type <", value, "entityType");
            return (Criteria) this;
        }

        public Criteria andEntityTypeLessThanOrEqualTo(String value) {
            addCriterion("entity_type <=", value, "entityType");
            return (Criteria) this;
        }

        public Criteria andEntityTypeLike(String value) {
            addCriterion("entity_type like", value, "entityType");
            return (Criteria) this;
        }

        public Criteria andEntityTypeNotLike(String value) {
            addCriterion("entity_type not like", value, "entityType");
            return (Criteria) this;
        }

        public Criteria andEntityTypeIn(List<String> values) {
            addCriterion("entity_type in", values, "entityType");
            return (Criteria) this;
        }

        public Criteria andEntityTypeNotIn(List<String> values) {
            addCriterion("entity_type not in", values, "entityType");
            return (Criteria) this;
        }

        public Criteria andEntityTypeBetween(String value1, String value2) {
            addCriterion("entity_type between", value1, value2, "entityType");
            return (Criteria) this;
        }

        public Criteria andEntityTypeNotBetween(String value1, String value2) {
            addCriterion("entity_type not between", value1, value2, "entityType");
            return (Criteria) this;
        }

        public Criteria andEntityIdIsNull() {
            addCriterion("entity_id is null");
            return (Criteria) this;
        }

        public Criteria andEntityIdIsNotNull() {
            addCriterion("entity_id is not null");
            return (Criteria) this;
        }

        public Criteria andEntityIdEqualTo(String value) {
            addCriterion("entity_id =", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdNotEqualTo(String value) {
            addCriterion("entity_id <>", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdGreaterThan(String value) {
            addCriterion("entity_id >", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdGreaterThanOrEqualTo(String value) {
            addCriterion("entity_id >=", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdLessThan(String value) {
            addCriterion("entity_id <", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdLessThanOrEqualTo(String value) {
            addCriterion("entity_id <=", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdLike(String value) {
            addCriterion("entity_id like", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdNotLike(String value) {
            addCriterion("entity_id not like", value, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdIn(List<String> values) {
            addCriterion("entity_id in", values, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdNotIn(List<String> values) {
            addCriterion("entity_id not in", values, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdBetween(String value1, String value2) {
            addCriterion("entity_id between", value1, value2, "entityId");
            return (Criteria) this;
        }

        public Criteria andEntityIdNotBetween(String value1, String value2) {
            addCriterion("entity_id not between", value1, value2, "entityId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdIsNull() {
            addCriterion("cust_limit_id is null");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdIsNotNull() {
            addCriterion("cust_limit_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdEqualTo(String value) {
            addCriterion("cust_limit_id =", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotEqualTo(String value) {
            addCriterion("cust_limit_id <>", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdGreaterThan(String value) {
            addCriterion("cust_limit_id >", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdGreaterThanOrEqualTo(String value) {
            addCriterion("cust_limit_id >=", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdLessThan(String value) {
            addCriterion("cust_limit_id <", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdLessThanOrEqualTo(String value) {
            addCriterion("cust_limit_id <=", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdLike(String value) {
            addCriterion("cust_limit_id like", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotLike(String value) {
            addCriterion("cust_limit_id not like", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdIn(List<String> values) {
            addCriterion("cust_limit_id in", values, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotIn(List<String> values) {
            addCriterion("cust_limit_id not in", values, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdBetween(String value1, String value2) {
            addCriterion("cust_limit_id between", value1, value2, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotBetween(String value1, String value2) {
            addCriterion("cust_limit_id not between", value1, value2, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCertNoIsNull() {
            addCriterion("cert_no is null");
            return (Criteria) this;
        }

        public Criteria andCertNoIsNotNull() {
            addCriterion("cert_no is not null");
            return (Criteria) this;
        }

        public Criteria andCertNoEqualTo(String value) {
            addCriterion("cert_no =", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoNotEqualTo(String value) {
            addCriterion("cert_no <>", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoGreaterThan(String value) {
            addCriterion("cert_no >", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoGreaterThanOrEqualTo(String value) {
            addCriterion("cert_no >=", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoLessThan(String value) {
            addCriterion("cert_no <", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoLessThanOrEqualTo(String value) {
            addCriterion("cert_no <=", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoLike(String value) {
            addCriterion("cert_no like", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoNotLike(String value) {
            addCriterion("cert_no not like", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoIn(List<String> values) {
            addCriterion("cert_no in", values, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoNotIn(List<String> values) {
            addCriterion("cert_no not in", values, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoBetween(String value1, String value2) {
            addCriterion("cert_no between", value1, value2, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoNotBetween(String value1, String value2) {
            addCriterion("cert_no not between", value1, value2, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertTypIsNull() {
            addCriterion("cert_typ is null");
            return (Criteria) this;
        }

        public Criteria andCertTypIsNotNull() {
            addCriterion("cert_typ is not null");
            return (Criteria) this;
        }

        public Criteria andCertTypEqualTo(String value) {
            addCriterion("cert_typ =", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypNotEqualTo(String value) {
            addCriterion("cert_typ <>", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypGreaterThan(String value) {
            addCriterion("cert_typ >", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypGreaterThanOrEqualTo(String value) {
            addCriterion("cert_typ >=", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypLessThan(String value) {
            addCriterion("cert_typ <", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypLessThanOrEqualTo(String value) {
            addCriterion("cert_typ <=", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypLike(String value) {
            addCriterion("cert_typ like", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypNotLike(String value) {
            addCriterion("cert_typ not like", value, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypIn(List<String> values) {
            addCriterion("cert_typ in", values, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypNotIn(List<String> values) {
            addCriterion("cert_typ not in", values, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypBetween(String value1, String value2) {
            addCriterion("cert_typ between", value1, value2, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCertTypNotBetween(String value1, String value2) {
            addCriterion("cert_typ not between", value1, value2, "certTyp");
            return (Criteria) this;
        }

        public Criteria andCustNmIsNull() {
            addCriterion("cust_nm is null");
            return (Criteria) this;
        }

        public Criteria andCustNmIsNotNull() {
            addCriterion("cust_nm is not null");
            return (Criteria) this;
        }

        public Criteria andCustNmEqualTo(String value) {
            addCriterion("cust_nm =", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmNotEqualTo(String value) {
            addCriterion("cust_nm <>", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmGreaterThan(String value) {
            addCriterion("cust_nm >", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmGreaterThanOrEqualTo(String value) {
            addCriterion("cust_nm >=", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmLessThan(String value) {
            addCriterion("cust_nm <", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmLessThanOrEqualTo(String value) {
            addCriterion("cust_nm <=", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmLike(String value) {
            addCriterion("cust_nm like", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmNotLike(String value) {
            addCriterion("cust_nm not like", value, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmIn(List<String> values) {
            addCriterion("cust_nm in", values, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmNotIn(List<String> values) {
            addCriterion("cust_nm not in", values, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmBetween(String value1, String value2) {
            addCriterion("cust_nm between", value1, value2, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustNmNotBetween(String value1, String value2) {
            addCriterion("cust_nm not between", value1, value2, "custNm");
            return (Criteria) this;
        }

        public Criteria andCustTypIsNull() {
            addCriterion("cust_typ is null");
            return (Criteria) this;
        }

        public Criteria andCustTypIsNotNull() {
            addCriterion("cust_typ is not null");
            return (Criteria) this;
        }

        public Criteria andCustTypEqualTo(String value) {
            addCriterion("cust_typ =", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypNotEqualTo(String value) {
            addCriterion("cust_typ <>", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypGreaterThan(String value) {
            addCriterion("cust_typ >", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypGreaterThanOrEqualTo(String value) {
            addCriterion("cust_typ >=", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypLessThan(String value) {
            addCriterion("cust_typ <", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypLessThanOrEqualTo(String value) {
            addCriterion("cust_typ <=", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypLike(String value) {
            addCriterion("cust_typ like", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypNotLike(String value) {
            addCriterion("cust_typ not like", value, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypIn(List<String> values) {
            addCriterion("cust_typ in", values, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypNotIn(List<String> values) {
            addCriterion("cust_typ not in", values, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypBetween(String value1, String value2) {
            addCriterion("cust_typ between", value1, value2, "custTyp");
            return (Criteria) this;
        }

        public Criteria andCustTypNotBetween(String value1, String value2) {
            addCriterion("cust_typ not between", value1, value2, "custTyp");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}