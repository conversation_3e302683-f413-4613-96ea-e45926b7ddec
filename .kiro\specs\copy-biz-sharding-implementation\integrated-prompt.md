# hsjry-limit-batch Copy业务分片实现完整指南

## 项目概述

本文档为hsjry-limit-batch项目中copy目录下的bizImpl类完成分片执行方法的执行逻辑实现提供完整的指导。该功能需要使用对应的sharding impl类的方法进行分片处理和分片落库逻辑执行，确保数据备份同步任务能够高效、可靠地处理大量数据。

## 业务需求

### 核心用户故事

#### 需求1: 分片执行逻辑实现
**用户故事:** 作为一个批处理系统开发者，我希望copy目录下的所有bizImpl类都能正确实现分片执行逻辑，以便能够高效处理大量数据备份同步任务。

**验收标准:**
- 执行copy目录下的任何一个bizImpl类时，系统应该能够正确调用对应的sharding impl类进行分片处理
- 分片处理开始时，系统应该能够生成合适的分片任务
- 分片执行完成时，系统应该能够正确更新分片流水状态并记录处理结果
- 分片执行过程中出现异常时，系统应该能够正确处理异常并记录错误信息

#### 需求2: 监控和跟踪
**用户故事:** 作为一个系统管理员，我希望能够监控和跟踪每个分片任务的执行状态，以便及时发现和处理问题。

**验收标准:**
- 分片任务开始执行时，系统应该记录详细的日志信息包括任务参数和执行时间
- 分片任务执行过程中，系统应该定期输出进度信息和处理统计
- 分片任务完成时，系统应该记录执行结果和性能指标
- 分片任务执行失败时，系统应该记录详细的错误信息和堆栈跟踪

#### 需求3: 数据处理完整性
**用户故事:** 作为一个数据处理专员，我希望分片执行逻辑能够正确处理数据转换和落库操作，以确保数据的完整性和一致性。

**验收标准:**
- 执行数据查询时，系统应该使用正确的查询条件和分页参数
- 进行数据转换时，系统应该使用对应的Converter类进行源数据到目标数据的转换
- 执行数据插入时，系统应该使用批量插入方式提高性能
- 第一个分片时，系统应该先清空目标表再进行数据插入
- 数据处理完成时，系统应该更新分片流水记录处理状态

#### 需求4: 架构一致性
**用户故事:** 作为一个系统架构师，我希望分片执行逻辑能够遵循现有的架构模式和代码规范，以保持系统的一致性和可维护性。

**验收标准:**
- 实现分片执行逻辑时，代码应该遵循现有的AbstractShardingPrepareBiz抽象类模式
- 处理业务逻辑时，应该正确实现JobCoreBusiness接口的所有方法
- 进行依赖注入时，应该使用@RequiredArgsConstructor和final字段的模式
- 记录日志时，应该使用统一的日志格式和前缀
- 处理异常时，应该抛出HsjryBizException并使用标准的错误码

#### 需求5: 性能优化
**用户故事:** 作为一个性能优化专员，我希望分片执行逻辑能够高效处理大量数据，以满足批处理系统的性能要求。

**验收标准:**
- 处理大量数据时，系统应该使用合适的批处理大小避免内存溢出
- 执行数据库操作时，系统应该使用批量操作提高数据库访问效率
- 进行数据转换时，系统应该避免不必要的对象创建和内存分配
- 数据量为空时，系统应该快速返回避免不必要的处理开销
- 分片任务并发执行时，系统应该确保线程安全和数据一致性

## 系统设计

### 整体架构

系统采用分层架构设计，将业务调度层(BizImpl)与分片执行层(ShardingImpl)分离，通过工厂模式实现松耦合，确保系统的可扩展性和可维护性。

```mermaid
graph TB
    A[JobInitDto] --> B[BizImpl Classes]
    B --> C[JobCoreBusinessFactory]
    C --> D[ShardingImpl Classes]
    D --> E[AbstractShardingPrepareBiz]
    E --> F[Database Operations]
    
    subgraph "Business Layer"
        B1[LbCEntityInfoBakSyncBizImpl]
        B2[LbCLimitInfoBakSyncBizImpl]
        B3[LbCEntityOperateSerialBakSyncBizImpl]
        B4[LbCLimitAmtInfoBakSyncBizImpl]
        B5[LbCLimitObjectInfoBakSyncBizImpl]
        B6[LbCLimitOperateSerialBakSyncBizImpl]
        B7[LbCLimitRelationBakSyncBizImpl]
    end
    
    subgraph "Sharding Layer"
        D1[LbCEntityInfoBakSyncImpl]
        D2[LbCLimitInfoBakSyncImpl]
        D3[LbCEntityOperateSerialBakSyncImpl]
        D4[LbCLimitAmtInfoBakSyncImpl]
        D5[LbCLimitObjectInfoBakSyncImpl]
        D6[LbCLimitOperateSerialFileSyncImpl]
        D7[LbCLimitRelationFileSyncImpl]
    end
    
    B1 --> D1
    B2 --> D2
    B3 --> D3
    B4 --> D4
    B5 --> D5
    B6 --> D6
    B7 --> D7
```

### 分片处理流程

```mermaid
sequenceDiagram
    participant Client as 调度系统
    participant BizImpl as BizImpl类
    participant Factory as JobCoreBusinessFactory
    participant ShardingImpl as ShardingImpl类
    participant DB as 数据库
    
    Client->>BizImpl: execBaseJob(jobInitDto)
    BizImpl->>Factory: getJobCoreBusiness(jobTradeCode)
    Factory->>ShardingImpl: 返回对应的实现类
    BizImpl->>ShardingImpl: preHandle(jobInitDto)
    ShardingImpl->>ShardingImpl: generateJobSharding(jobInitDto)
    
    loop 分片处理
        ShardingImpl->>ShardingImpl: queryShardingResult(...)
        ShardingImpl->>DB: 查询分片数据
        DB->>ShardingImpl: 返回数据
        ShardingImpl->>ShardingImpl: execJobCoreBusiness(...)
        ShardingImpl->>DB: 清空目标表(仅第一个分片)
        ShardingImpl->>ShardingImpl: 数据转换
        ShardingImpl->>DB: 批量插入数据
        ShardingImpl->>ShardingImpl: 更新分片流水
    end
    
    BizImpl->>ShardingImpl: afterHandle(jobInitDto)
    BizImpl->>Client: 返回执行结果
```

### 核心组件

#### 1. BizImpl业务实现类
- **职责**: 作为任务入口点，负责调度和管理整个数据备份同步流程
- **接口**: 实现`BaseOrdinaryBiz`接口
- **关键方法**:
  - `getJobTrade()`: 返回任务交易码
  - `execBaseJob(JobInitDto)`: 执行基础任务逻辑

#### 2. ShardingImpl分片实现类
- **职责**: 实现具体的分片处理逻辑和数据操作
- **接口**: 继承`AbstractShardingPrepareBiz`，实现`JobCoreBusiness`接口
- **关键方法**:
  - `generateJobSharding()`: 生成分片任务
  - `queryShardingResult()`: 查询分片数据
  - `execJobCoreBusiness()`: 执行分片业务逻辑

#### 3. JobCoreBusinessFactory工厂类
- **职责**: 管理和提供JobCoreBusiness实例
- **模式**: 工厂模式 + Spring ApplicationContextAware
- **功能**: 根据jobTradeCode获取对应的实现类

### 数据模型

#### JobInitDto - 任务初始化参数
```java
public class JobInitDto {
    private Integer businessDate;    // 业务日期
    private String batchSerialNo;   // 批次流水号
    private Integer fixNum;         // 分片大小
    // 其他字段...
}
```

#### JobShared - 分片任务参数
```java
public class JobShared {
    private Integer businessDate;    // 业务日期
    private String batchSerialNo;   // 批次流水号
    private Integer batchNum;       // 分片号
    private Integer offset;         // 偏移量
    private Integer limit;          // 限制数量
    private String extParam;        // 扩展参数
    // 其他字段...
}
```

#### ShardingResult - 分片结果
```java
public class ShardingResult<T> {
    private LcSliceBatchSerialDo lcSliceBatchSerialDo;  // 分片流水
    private JobShared jobShared;                        // 分片参数
    private List<T> shardingResultList;                 // 分片数据
    // 其他字段...
}
```

## 实现规范

### 代码规范

#### 1. 类命名规范
- BizImpl类: `Lb{业务名}BakSyncBizImpl`
- ShardingImpl类: `Lb{业务名}BakSyncImpl`
- 服务注册名: 使用小驼峰命名

#### 2. 日志规范
```java
String prefixLog = String.format("营运日期:[%s],批量流水号:[%s],任务交易码:[%s-%s]", 
    businessDate, batchSerialNo, jobTradeCode, jobTradeDesc);
```

#### 3. 异常处理规范
```java
try {
    // 业务逻辑
} catch (Exception e) {
    log.error(prefixLog + "执行失败", e);
    throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
        EnumBatchJobError.SYSTEM_ERR.getDescription());
}
```

### 异常处理策略

#### 1. 业务异常处理
- **异常类型**: `HsjryBizException`
- **错误码**: `EnumBatchJobError.SYSTEM_ERR`
- **处理方式**: 记录详细错误日志，抛出标准业务异常

#### 2. 系统异常处理
- **异常类型**: `RuntimeException`
- **处理方式**: 包装原始异常信息，提供有意义的错误描述

#### 3. 数据异常处理
- **空数据**: 快速返回，避免不必要的处理
- **数据转换异常**: 记录错误并继续处理其他数据
- **数据库异常**: 回滚事务，记录详细错误信息

### 性能优化指导

#### 1. 批量处理优化
- 使用合适的批处理大小(建议1000-5000条)
- 避免在循环中进行数据库操作
- 使用批量插入而非单条插入

#### 2. 内存管理优化
- 及时释放大对象引用
- 使用流式处理避免一次性加载大量数据
- 合理设置JVM堆内存参数

#### 3. 数据库优化
- 使用索引优化查询性能
- 合理设置数据库连接池参数
- 避免长时间持有数据库连接

## 实施计划

### 已完成任务
- [x] 1. 分析现有代码结构和依赖关系
- [x] 2. 完善LbCEntityInfoBakSyncBizImpl分片执行逻辑

### 待完成任务

#### 第一阶段: 核心业务实现
- [ ] 3. 完善LbCLimitInfoBakSyncBizImpl分片执行逻辑
- [ ] 4. 完善LbCEntityOperateSerialBakSyncBizImpl分片执行逻辑
- [ ] 5. 完善LbCLimitAmtInfoBakSyncBizImpl分片执行逻辑
- [ ] 6. 完善LbCLimitObjectInfoBakSyncBizImpl分片执行逻辑
- [ ] 7. 完善LbCLimitOperateSerialBakSyncBizImpl分片执行逻辑
- [ ] 8. 完善LbCLimitRelationBakSyncBizImpl分片执行逻辑

#### 第二阶段: 质量保障
- [ ] 9. 统一异常处理和日志记录机制
- [ ] 10. 实现性能优化和监控机制
- [ ] 11. 创建单元测试用例
- [ ] 12. 创建集成测试用例

#### 第三阶段: 性能验证
- [ ] 13. 性能测试和优化验证
- [ ] 14. 文档和代码审查
- [ ] 15. 部署验证和监控配置

### 任务详细说明

#### 任务3: 完善LbCLimitInfoBakSyncBizImpl分片执行逻辑
- 修改execBaseJob方法，实现完整的分片处理流程
- 添加统一的日志格式和错误处理机制
- 确保与LbCLimitInfoBakSyncImpl的正确集成
- 验证数据转换和批量插入逻辑
- _关联需求: 1.1, 1.2, 2.1, 2.2, 3.2, 3.3_

#### 任务4: 完善LbCEntityOperateSerialBakSyncBizImpl分片执行逻辑
- 实现完整的分片处理流程
- 添加性能监控和进度跟踪日志
- 确保与LbCEntityOperateSerialBakSyncImpl的正确集成
- 实现异常恢复和重试机制
- _关联需求: 1.1, 1.3, 2.2, 2.3, 5.1_

#### 任务5: 完善LbCLimitAmtInfoBakSyncBizImpl分片执行逻辑
- 实现完整的分片处理流程
- 优化大数据量处理的内存使用
- 确保与LbCLimitAmtInfoBakSyncImpl的正确集成
- 添加数据质量检查和验证
- _关联需求: 1.1, 1.4, 3.2, 3.3, 5.2, 5.3_

#### 任务6: 完善LbCLimitObjectInfoBakSyncBizImpl分片执行逻辑
- 实现完整的分片处理流程
- 添加详细的执行状态跟踪
- 确保与LbCLimitObjectInfoBakSyncImpl的正确集成
- 实现数据一致性检查机制
- _关联需求: 1.1, 2.1, 2.2, 3.4, 5.5_

#### 任务7: 完善LbCLimitOperateSerialBakSyncBizImpl分片执行逻辑
- 实现完整的分片处理流程
- 优化并发处理和线程安全
- 确保与LbCLimitOperateSerialFileSyncImpl的正确集成
- 添加性能基准测试和监控
- _关联需求: 1.1, 1.4, 5.1, 5.4, 5.5_

#### 任务8: 完善LbCLimitRelationBakSyncBizImpl分片执行逻辑
- 实现完整的分片处理流程
- 添加关联数据处理逻辑
- 确保与LbCLimitRelationFileSyncImpl的正确集成
- 实现数据完整性验证
- _关联需求: 1.1, 3.1, 3.4, 3.5_

#### 任务9: 统一异常处理和日志记录机制
- 为所有BizImpl类实现统一的异常处理模式
- 标准化日志格式和前缀规范
- 实现HsjryBizException的正确使用
- 添加异常分类和错误码管理
- _关联需求: 1.4, 2.2, 2.3, 4.5_

#### 任务10: 实现性能优化和监控机制
- 优化批量处理大小和内存使用
- 添加执行时间和处理量统计
- 实现数据库连接池优化
- 添加性能指标收集和报告
- _关联需求: 5.1, 5.2, 5.3, 5.4_

## 测试策略

### 测试层次

#### 1. 单元测试
- **测试范围**: 各个方法的独立功能
- **测试工具**: JUnit + Mockito
- **覆盖率要求**: 核心业务逻辑 > 80%

#### 2. 集成测试
- **测试范围**: 组件间的交互和数据流
- **测试环境**: Spring Boot Test环境
- **数据准备**: 使用测试数据库和模拟数据

#### 3. 性能测试
- **测试指标**: 处理速度、内存使用、数据库连接
- **测试场景**: 大数据量分片处理
- **性能基准**: 每分钟处理数据量 > 10万条

### 测试用例模板

#### BizImpl类测试用例
```java
@Test
public void testExecBaseJob_Success() {
    // 测试正常执行流程
}

@Test
public void testExecBaseJob_JobCoreBusinessNotFound() {
    // 测试工厂返回null的情况
}

@Test
public void testExecBaseJob_PreHandleException() {
    // 测试前置处理异常
}

@Test
public void testExecBaseJob_AfterHandleException() {
    // 测试后置处理异常
}
```

## 监控和运维

### 日志监控
- 记录关键节点的执行时间
- 记录数据处理量和处理速度
- 记录异常和错误信息

### 性能监控
- 监控内存使用情况
- 监控数据库连接数
- 监控任务执行时间

### 告警机制
- 任务执行失败告警
- 性能指标异常告警
- 数据质量问题告警

## 总结

本文档提供了hsjry-limit-batch项目中copy业务分片实现的完整指南，包括需求分析、系统设计、实现规范、实施计划和测试策略。通过遵循本指南，可以确保所有copy目录下的bizImpl类都能正确实现分片执行逻辑，满足高效、可靠的数据备份同步需求。

关键成功因素：
1. 严格遵循现有架构模式和代码规范
2. 实现完整的异常处理和日志记录机制
3. 确保数据处理的完整性和一致性
4. 优化性能以满足大数据量处理需求
5. 建立完善的测试和监控体系