/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.Date;
import java.util.List;

import com.hsjry.core.limit.center.dal.dao.query.AmtLimitRecordInfoBatchQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordInfoDo;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/6 15:29
 */
public interface AmtLimitRecordInfoBatchDao {

    /**
     * 查询
     * @param date
     * @param pageParam
     * @return
     */
    PageInfo<LcAmtLimitRecordInfoDo> queryNeedValidByPage(Date date, PageParam pageParam);
    /**
     * 查询分片数据
     *
     * @param batchQuery
     * @return
     */
    List<LcAmtLimitRecordInfoDo> selectShardList(AmtLimitRecordInfoBatchQuery batchQuery);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query
     * @return
     */
    LcAmtLimitRecordInfoDo selectFirstOne(AmtLimitRecordInfoBatchQuery query);

    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(AmtLimitRecordInfoBatchQuery query);
}
