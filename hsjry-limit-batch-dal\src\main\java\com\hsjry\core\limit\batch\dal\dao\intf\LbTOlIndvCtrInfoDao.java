package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTOlIndvCtrInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTOlIndvCtrInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-中间表-个人合同信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
public interface LbTOlIndvCtrInfoDao extends IBaseDao<LbTOlIndvCtrInfoDo> {
    /**
     * 分页查询网贷系统-中间表-个人合同信息
     *
     * @param lbTOlIndvCtrInfoQuery 条件
     * @return PageInfo<LbTOlIndvCtrInfoDo>
     */
    PageInfo<LbTOlIndvCtrInfoDo> selectPage(LbTOlIndvCtrInfoQuery lbTOlIndvCtrInfoQuery, PageParam pageParam);

    /**
     * 根据key查询网贷系统-中间表-个人合同信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTOlIndvCtrInfoDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除网贷系统-中间表-个人合同信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询网贷系统-中间表-个人合同信息信息
     *
     * @param lbTOlIndvCtrInfoQuery 条件
     * @return List<LbTOlIndvCtrInfoDo>
     */
    List<LbTOlIndvCtrInfoDo> selectByExample(LbTOlIndvCtrInfoQuery lbTOlIndvCtrInfoQuery);

    /**
     * 新增网贷系统-中间表-个人合同信息信息
     *
     * @param lbTOlIndvCtrInfo 条件
     * @return int>
     */
    int insertBySelective(LbTOlIndvCtrInfoDo lbTOlIndvCtrInfo);

    /**
     * 修改网贷系统-中间表-个人合同信息信息
     *
     * @param lbTOlIndvCtrInfo
     * @return
     */
    int updateBySelective(LbTOlIndvCtrInfoDo lbTOlIndvCtrInfo);

    /**
     * 修改网贷系统-中间表-个人合同信息信息
     *
     * @param lbTOlIndvCtrInfo
     * @param lbTOlIndvCtrInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTOlIndvCtrInfoDo lbTOlIndvCtrInfo, LbTOlIndvCtrInfoQuery lbTOlIndvCtrInfoQuery);

    /**
     * 将网贷个人客户额度同步数据插入到合同信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_ol_indv_ctr_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    int insertOlIndvCtrInfo(List<String> templateNodeIdList, List<String> custLimitIdList);

    /**
     * 更新额度实例金额信息表
     * 根据网贷系统中间表数据更新LC_CUST_LIMIT_AMT_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    int updateCustLimitAmtInfo(List<String> custLimitIdList);
}
