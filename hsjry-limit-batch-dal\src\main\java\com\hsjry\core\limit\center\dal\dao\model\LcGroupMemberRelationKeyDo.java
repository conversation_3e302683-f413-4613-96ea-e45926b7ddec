package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 集团成员关联关系主键
 *
 * <AUTHOR>
 * @date 2024-05-29 02:31:51
 */
@Table(name = "lc_group_member_relation")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcGroupMemberRelationKeyDo implements Serializable {

    private static final long serialVersionUID = 1795644192496746496L;
    /** 关联编号 */
    @Id
    @Column(name = "relation_id")
    private String relationId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}