package com.hsjry.core.limit.batch.dal.dao.query;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * 额度中心-中间表-客户经营查询条件
 *
 * <AUTHOR>
 * @date 2025-07-15 03:50:12
 */
@Data
@Builder
public class LbTUserManageInfoQuery implements Serializable {

    /** serialVersionUID */
    private static final long serialVersionUID = 1944967709649469441L;

    /** 资源项id */
    private String resourceId;
    /** 客户id */
    private String userId;
    /** 创建人 */
    private String creator;
    /** 修改人 */
    private String modifier;
    /** 证件类型 */
    private String certificateKind;
    /** 证件号码 */
    private String certificateNo;
    /** 国民经济行业分类-小类 */
    private String industryCategory;
    /** 国民经济行业分类-大类 */
    private String vocationLevelTwo;
    /** 国民经济行业分类-中类 */
    private String vocationLevelThree;
    /** 国民经济行业分类-门类 */
    private String industryType;
}
