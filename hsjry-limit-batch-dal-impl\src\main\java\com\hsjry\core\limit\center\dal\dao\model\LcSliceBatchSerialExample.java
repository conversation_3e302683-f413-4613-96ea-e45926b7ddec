package com.hsjry.core.limit.center.dal.dao.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 额度分片流水Example
 *
 * <AUTHOR>
 * @date 2023-03-13 12:30:49
 */
public class LcSliceBatchSerialExample {
    /** 排序字段 */
    protected String orderByClause;

    /** 是否只查询记录不同的数据 */
    protected boolean distinct;

    /** or条件查询集合 */
    protected List<Criteria> oredCriteria;

    /**
     * 初始化or条件查询集合
     */
    public LcSliceBatchSerialExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * 排序字段set方法
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * 排序字段get方法
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * 不同记录set方法.
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * 不同记录get方法.
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * 排序集合get方法
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * or
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * or
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * 清除数据
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

                public Criteria andTradeCodeIsNull() {
            addCriterion("trade_code is null");
            return (Criteria) this;
        }

        public Criteria andTradeCodeIsNotNull() {
            addCriterion("trade_code is not null");
            return (Criteria) this;
        }

        public Criteria andTradeCodeEqualTo(String value) {
            addCriterion("trade_code =", value, "tradeCode");
            return (Criteria) this;
        }

        public Criteria andTradeCodeNotEqualTo(String value) {
            addCriterion("trade_code <>", value, "tradeCode");
            return (Criteria) this;
        }

        public Criteria andTradeCodeGreaterThan(String value) {
            addCriterion("trade_code >", value, "tradeCode");
            return (Criteria) this;
        }

        public Criteria andTradeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("trade_code >=", value, "tradeCode");
            return (Criteria) this;
        }

        public Criteria andTradeCodeLessThan(String value) {
            addCriterion("trade_code <", value, "tradeCode");
            return (Criteria) this;
        }

        public Criteria andTradeCodeLessThanOrEqualTo(String value) {
            addCriterion("trade_code <=", value, "tradeCode");
            return (Criteria) this;
        }

        public Criteria andTradeCodeLike(String value) {
            addCriterion("trade_code like", value, "tradeCode");
            return (Criteria) this;
        }

        public Criteria andTradeCodeNotLike(String value) {
            addCriterion("trade_code not like", value, "tradeCode");
            return (Criteria) this;
        }

        public Criteria andTradeCodeIn(List<String> values) {
            addCriterion("trade_code in", values, "tradeCode");
            return (Criteria) this;
        }

        public Criteria andTradeCodeNotIn(List<String> values) {
            addCriterion("trade_code not in", values, "tradeCode");
            return (Criteria) this;
        }

        public Criteria andTradeCodeBetween(String value1, String value2) {
            addCriterion("trade_code between", value1, value2, "tradeCode");
            return (Criteria) this;
        }

        public Criteria andTradeCodeNotBetween(String value1, String value2) {
            addCriterion("trade_code not between", value1, value2, "tradeCode");
            return (Criteria) this;
        }
		        public Criteria andFailureTimesIsNull() {
            addCriterion("failure_times is null");
            return (Criteria) this;
        }

        public Criteria andFailureTimesIsNotNull() {
            addCriterion("failure_times is not null");
            return (Criteria) this;
        }

        public Criteria andFailureTimesEqualTo(Integer value) {
            addCriterion("failure_times =", value, "failureTimes");
            return (Criteria) this;
        }

        public Criteria andFailureTimesNotEqualTo(Integer value) {
            addCriterion("failure_times <>", value, "failureTimes");
            return (Criteria) this;
        }

        public Criteria andFailureTimesGreaterThan(Integer value) {
            addCriterion("failure_times >", value, "failureTimes");
            return (Criteria) this;
        }

        public Criteria andFailureTimesGreaterThanOrEqualTo(Integer value) {
            addCriterion("failure_times >=", value, "failureTimes");
            return (Criteria) this;
        }

        public Criteria andFailureTimesLessThan(Integer value) {
            addCriterion("failure_times <", value, "failureTimes");
            return (Criteria) this;
        }

        public Criteria andFailureTimesLessThanOrEqualTo(Integer value) {
            addCriterion("failure_times <=", value, "failureTimes");
            return (Criteria) this;
        }

        public Criteria andFailureTimesLike(Integer value) {
            addCriterion("failure_times like", value, "failureTimes");
            return (Criteria) this;
        }

        public Criteria andFailureTimesNotLike(Integer value) {
            addCriterion("failure_times not like", value, "failureTimes");
            return (Criteria) this;
        }

        public Criteria andFailureTimesIn(List<Integer> values) {
            addCriterion("failure_times in", values, "failureTimes");
            return (Criteria) this;
        }

        public Criteria andFailureTimesNotIn(List<Integer> values) {
            addCriterion("failure_times not in", values, "failureTimes");
            return (Criteria) this;
        }

        public Criteria andFailureTimesBetween(Integer value1, Integer value2) {
            addCriterion("failure_times between", value1, value2, "failureTimes");
            return (Criteria) this;
        }

        public Criteria andFailureTimesNotBetween(Integer value1, Integer value2) {
            addCriterion("failure_times not between", value1, value2, "failureTimes");
            return (Criteria) this;
        }
		        public Criteria andFinishFlagIsNull() {
            addCriterion("finish_flag is null");
            return (Criteria) this;
        }

        public Criteria andFinishFlagIsNotNull() {
            addCriterion("finish_flag is not null");
            return (Criteria) this;
        }

        public Criteria andFinishFlagEqualTo(String value) {
            addCriterion("finish_flag =", value, "finishFlag");
            return (Criteria) this;
        }

        public Criteria andFinishFlagNotEqualTo(String value) {
            addCriterion("finish_flag <>", value, "finishFlag");
            return (Criteria) this;
        }

        public Criteria andFinishFlagGreaterThan(String value) {
            addCriterion("finish_flag >", value, "finishFlag");
            return (Criteria) this;
        }

        public Criteria andFinishFlagGreaterThanOrEqualTo(String value) {
            addCriterion("finish_flag >=", value, "finishFlag");
            return (Criteria) this;
        }

        public Criteria andFinishFlagLessThan(String value) {
            addCriterion("finish_flag <", value, "finishFlag");
            return (Criteria) this;
        }

        public Criteria andFinishFlagLessThanOrEqualTo(String value) {
            addCriterion("finish_flag <=", value, "finishFlag");
            return (Criteria) this;
        }

        public Criteria andFinishFlagLike(String value) {
            addCriterion("finish_flag like", value, "finishFlag");
            return (Criteria) this;
        }

        public Criteria andFinishFlagNotLike(String value) {
            addCriterion("finish_flag not like", value, "finishFlag");
            return (Criteria) this;
        }

        public Criteria andFinishFlagIn(List<String> values) {
            addCriterion("finish_flag in", values, "finishFlag");
            return (Criteria) this;
        }

        public Criteria andFinishFlagNotIn(List<String> values) {
            addCriterion("finish_flag not in", values, "finishFlag");
            return (Criteria) this;
        }

        public Criteria andFinishFlagBetween(String value1, String value2) {
            addCriterion("finish_flag between", value1, value2, "finishFlag");
            return (Criteria) this;
        }

        public Criteria andFinishFlagNotBetween(String value1, String value2) {
            addCriterion("finish_flag not between", value1, value2, "finishFlag");
            return (Criteria) this;
        }
		        public Criteria andSharedNoteIsNull() {
            addCriterion("shared_note is null");
            return (Criteria) this;
        }

        public Criteria andSharedNoteIsNotNull() {
            addCriterion("shared_note is not null");
            return (Criteria) this;
        }

        public Criteria andSharedNoteEqualTo(String value) {
            addCriterion("shared_note =", value, "sharedNote");
            return (Criteria) this;
        }

        public Criteria andSharedNoteNotEqualTo(String value) {
            addCriterion("shared_note <>", value, "sharedNote");
            return (Criteria) this;
        }

        public Criteria andSharedNoteGreaterThan(String value) {
            addCriterion("shared_note >", value, "sharedNote");
            return (Criteria) this;
        }

        public Criteria andSharedNoteGreaterThanOrEqualTo(String value) {
            addCriterion("shared_note >=", value, "sharedNote");
            return (Criteria) this;
        }

        public Criteria andSharedNoteLessThan(String value) {
            addCriterion("shared_note <", value, "sharedNote");
            return (Criteria) this;
        }

        public Criteria andSharedNoteLessThanOrEqualTo(String value) {
            addCriterion("shared_note <=", value, "sharedNote");
            return (Criteria) this;
        }

        public Criteria andSharedNoteLike(String value) {
            addCriterion("shared_note like", value, "sharedNote");
            return (Criteria) this;
        }

        public Criteria andSharedNoteNotLike(String value) {
            addCriterion("shared_note not like", value, "sharedNote");
            return (Criteria) this;
        }

        public Criteria andSharedNoteIn(List<String> values) {
            addCriterion("shared_note in", values, "sharedNote");
            return (Criteria) this;
        }

        public Criteria andSharedNoteNotIn(List<String> values) {
            addCriterion("shared_note not in", values, "sharedNote");
            return (Criteria) this;
        }

        public Criteria andSharedNoteBetween(String value1, String value2) {
            addCriterion("shared_note between", value1, value2, "sharedNote");
            return (Criteria) this;
        }

        public Criteria andSharedNoteNotBetween(String value1, String value2) {
            addCriterion("shared_note not between", value1, value2, "sharedNote");
            return (Criteria) this;
        }
		        public Criteria andSharedStatisticsIsNull() {
            addCriterion("shared_statistics is null");
            return (Criteria) this;
        }

        public Criteria andSharedStatisticsIsNotNull() {
            addCriterion("shared_statistics is not null");
            return (Criteria) this;
        }

        public Criteria andSharedStatisticsEqualTo(String value) {
            addCriterion("shared_statistics =", value, "sharedStatistics");
            return (Criteria) this;
        }

        public Criteria andSharedStatisticsNotEqualTo(String value) {
            addCriterion("shared_statistics <>", value, "sharedStatistics");
            return (Criteria) this;
        }

        public Criteria andSharedStatisticsGreaterThan(String value) {
            addCriterion("shared_statistics >", value, "sharedStatistics");
            return (Criteria) this;
        }

        public Criteria andSharedStatisticsGreaterThanOrEqualTo(String value) {
            addCriterion("shared_statistics >=", value, "sharedStatistics");
            return (Criteria) this;
        }

        public Criteria andSharedStatisticsLessThan(String value) {
            addCriterion("shared_statistics <", value, "sharedStatistics");
            return (Criteria) this;
        }

        public Criteria andSharedStatisticsLessThanOrEqualTo(String value) {
            addCriterion("shared_statistics <=", value, "sharedStatistics");
            return (Criteria) this;
        }

        public Criteria andSharedStatisticsLike(String value) {
            addCriterion("shared_statistics like", value, "sharedStatistics");
            return (Criteria) this;
        }

        public Criteria andSharedStatisticsNotLike(String value) {
            addCriterion("shared_statistics not like", value, "sharedStatistics");
            return (Criteria) this;
        }

        public Criteria andSharedStatisticsIn(List<String> values) {
            addCriterion("shared_statistics in", values, "sharedStatistics");
            return (Criteria) this;
        }

        public Criteria andSharedStatisticsNotIn(List<String> values) {
            addCriterion("shared_statistics not in", values, "sharedStatistics");
            return (Criteria) this;
        }

        public Criteria andSharedStatisticsBetween(String value1, String value2) {
            addCriterion("shared_statistics between", value1, value2, "sharedStatistics");
            return (Criteria) this;
        }

        public Criteria andSharedStatisticsNotBetween(String value1, String value2) {
            addCriterion("shared_statistics not between", value1, value2, "sharedStatistics");
            return (Criteria) this;
        }
		        public Criteria andSharedPassCountIsNull() {
            addCriterion("shared_pass_count is null");
            return (Criteria) this;
        }

        public Criteria andSharedPassCountIsNotNull() {
            addCriterion("shared_pass_count is not null");
            return (Criteria) this;
        }

        public Criteria andSharedPassCountEqualTo(Integer value) {
            addCriterion("shared_pass_count =", value, "sharedPassCount");
            return (Criteria) this;
        }

        public Criteria andSharedPassCountNotEqualTo(Integer value) {
            addCriterion("shared_pass_count <>", value, "sharedPassCount");
            return (Criteria) this;
        }

        public Criteria andSharedPassCountGreaterThan(Integer value) {
            addCriterion("shared_pass_count >", value, "sharedPassCount");
            return (Criteria) this;
        }

        public Criteria andSharedPassCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("shared_pass_count >=", value, "sharedPassCount");
            return (Criteria) this;
        }

        public Criteria andSharedPassCountLessThan(Integer value) {
            addCriterion("shared_pass_count <", value, "sharedPassCount");
            return (Criteria) this;
        }

        public Criteria andSharedPassCountLessThanOrEqualTo(Integer value) {
            addCriterion("shared_pass_count <=", value, "sharedPassCount");
            return (Criteria) this;
        }

        public Criteria andSharedPassCountLike(Integer value) {
            addCriterion("shared_pass_count like", value, "sharedPassCount");
            return (Criteria) this;
        }

        public Criteria andSharedPassCountNotLike(Integer value) {
            addCriterion("shared_pass_count not like", value, "sharedPassCount");
            return (Criteria) this;
        }

        public Criteria andSharedPassCountIn(List<Integer> values) {
            addCriterion("shared_pass_count in", values, "sharedPassCount");
            return (Criteria) this;
        }

        public Criteria andSharedPassCountNotIn(List<Integer> values) {
            addCriterion("shared_pass_count not in", values, "sharedPassCount");
            return (Criteria) this;
        }

        public Criteria andSharedPassCountBetween(Integer value1, Integer value2) {
            addCriterion("shared_pass_count between", value1, value2, "sharedPassCount");
            return (Criteria) this;
        }

        public Criteria andSharedPassCountNotBetween(Integer value1, Integer value2) {
            addCriterion("shared_pass_count not between", value1, value2, "sharedPassCount");
            return (Criteria) this;
        }
		        public Criteria andSharedDetailIsNull() {
            addCriterion("shared_detail is null");
            return (Criteria) this;
        }

        public Criteria andSharedDetailIsNotNull() {
            addCriterion("shared_detail is not null");
            return (Criteria) this;
        }

        public Criteria andSharedDetailEqualTo(String value) {
            addCriterion("shared_detail =", value, "sharedDetail");
            return (Criteria) this;
        }

        public Criteria andSharedDetailNotEqualTo(String value) {
            addCriterion("shared_detail <>", value, "sharedDetail");
            return (Criteria) this;
        }

        public Criteria andSharedDetailGreaterThan(String value) {
            addCriterion("shared_detail >", value, "sharedDetail");
            return (Criteria) this;
        }

        public Criteria andSharedDetailGreaterThanOrEqualTo(String value) {
            addCriterion("shared_detail >=", value, "sharedDetail");
            return (Criteria) this;
        }

        public Criteria andSharedDetailLessThan(String value) {
            addCriterion("shared_detail <", value, "sharedDetail");
            return (Criteria) this;
        }

        public Criteria andSharedDetailLessThanOrEqualTo(String value) {
            addCriterion("shared_detail <=", value, "sharedDetail");
            return (Criteria) this;
        }

        public Criteria andSharedDetailLike(String value) {
            addCriterion("shared_detail like", value, "sharedDetail");
            return (Criteria) this;
        }

        public Criteria andSharedDetailNotLike(String value) {
            addCriterion("shared_detail not like", value, "sharedDetail");
            return (Criteria) this;
        }

        public Criteria andSharedDetailIn(List<String> values) {
            addCriterion("shared_detail in", values, "sharedDetail");
            return (Criteria) this;
        }

        public Criteria andSharedDetailNotIn(List<String> values) {
            addCriterion("shared_detail not in", values, "sharedDetail");
            return (Criteria) this;
        }

        public Criteria andSharedDetailBetween(String value1, String value2) {
            addCriterion("shared_detail between", value1, value2, "sharedDetail");
            return (Criteria) this;
        }

        public Criteria andSharedDetailNotBetween(String value1, String value2) {
            addCriterion("shared_detail not between", value1, value2, "sharedDetail");
            return (Criteria) this;
        }
		        public Criteria andExecIpIsNull() {
            addCriterion("exec_ip is null");
            return (Criteria) this;
        }

        public Criteria andExecIpIsNotNull() {
            addCriterion("exec_ip is not null");
            return (Criteria) this;
        }

        public Criteria andExecIpEqualTo(String value) {
            addCriterion("exec_ip =", value, "execIp");
            return (Criteria) this;
        }

        public Criteria andExecIpNotEqualTo(String value) {
            addCriterion("exec_ip <>", value, "execIp");
            return (Criteria) this;
        }

        public Criteria andExecIpGreaterThan(String value) {
            addCriterion("exec_ip >", value, "execIp");
            return (Criteria) this;
        }

        public Criteria andExecIpGreaterThanOrEqualTo(String value) {
            addCriterion("exec_ip >=", value, "execIp");
            return (Criteria) this;
        }

        public Criteria andExecIpLessThan(String value) {
            addCriterion("exec_ip <", value, "execIp");
            return (Criteria) this;
        }

        public Criteria andExecIpLessThanOrEqualTo(String value) {
            addCriterion("exec_ip <=", value, "execIp");
            return (Criteria) this;
        }

        public Criteria andExecIpLike(String value) {
            addCriterion("exec_ip like", value, "execIp");
            return (Criteria) this;
        }

        public Criteria andExecIpNotLike(String value) {
            addCriterion("exec_ip not like", value, "execIp");
            return (Criteria) this;
        }

        public Criteria andExecIpIn(List<String> values) {
            addCriterion("exec_ip in", values, "execIp");
            return (Criteria) this;
        }

        public Criteria andExecIpNotIn(List<String> values) {
            addCriterion("exec_ip not in", values, "execIp");
            return (Criteria) this;
        }

        public Criteria andExecIpBetween(String value1, String value2) {
            addCriterion("exec_ip between", value1, value2, "execIp");
            return (Criteria) this;
        }

        public Criteria andExecIpNotBetween(String value1, String value2) {
            addCriterion("exec_ip not between", value1, value2, "execIp");
            return (Criteria) this;
        }
		        public Criteria andExecDateIsNull() {
            addCriterion("exec_date is null");
            return (Criteria) this;
        }

        public Criteria andExecDateIsNotNull() {
            addCriterion("exec_date is not null");
            return (Criteria) this;
        }

        public Criteria andExecDateEqualTo(Integer value) {
            addCriterion("exec_date =", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateNotEqualTo(Integer value) {
            addCriterion("exec_date <>", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateGreaterThan(Integer value) {
            addCriterion("exec_date >", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateGreaterThanOrEqualTo(Integer value) {
            addCriterion("exec_date >=", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateLessThan(Integer value) {
            addCriterion("exec_date <", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateLessThanOrEqualTo(Integer value) {
            addCriterion("exec_date <=", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateLike(Integer value) {
            addCriterion("exec_date like", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateNotLike(Integer value) {
            addCriterion("exec_date not like", value, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateIn(List<Integer> values) {
            addCriterion("exec_date in", values, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateNotIn(List<Integer> values) {
            addCriterion("exec_date not in", values, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateBetween(Integer value1, Integer value2) {
            addCriterion("exec_date between", value1, value2, "execDate");
            return (Criteria) this;
        }

        public Criteria andExecDateNotBetween(Integer value1, Integer value2) {
            addCriterion("exec_date not between", value1, value2, "execDate");
            return (Criteria) this;
        }
		        public Criteria andExtSerialIdIsNull() {
            addCriterion("ext_serial_id is null");
            return (Criteria) this;
        }

        public Criteria andExtSerialIdIsNotNull() {
            addCriterion("ext_serial_id is not null");
            return (Criteria) this;
        }

        public Criteria andExtSerialIdEqualTo(String value) {
            addCriterion("ext_serial_id =", value, "extSerialId");
            return (Criteria) this;
        }

        public Criteria andExtSerialIdNotEqualTo(String value) {
            addCriterion("ext_serial_id <>", value, "extSerialId");
            return (Criteria) this;
        }

        public Criteria andExtSerialIdGreaterThan(String value) {
            addCriterion("ext_serial_id >", value, "extSerialId");
            return (Criteria) this;
        }

        public Criteria andExtSerialIdGreaterThanOrEqualTo(String value) {
            addCriterion("ext_serial_id >=", value, "extSerialId");
            return (Criteria) this;
        }

        public Criteria andExtSerialIdLessThan(String value) {
            addCriterion("ext_serial_id <", value, "extSerialId");
            return (Criteria) this;
        }

        public Criteria andExtSerialIdLessThanOrEqualTo(String value) {
            addCriterion("ext_serial_id <=", value, "extSerialId");
            return (Criteria) this;
        }

        public Criteria andExtSerialIdLike(String value) {
            addCriterion("ext_serial_id like", value, "extSerialId");
            return (Criteria) this;
        }

        public Criteria andExtSerialIdNotLike(String value) {
            addCriterion("ext_serial_id not like", value, "extSerialId");
            return (Criteria) this;
        }

        public Criteria andExtSerialIdIn(List<String> values) {
            addCriterion("ext_serial_id in", values, "extSerialId");
            return (Criteria) this;
        }

        public Criteria andExtSerialIdNotIn(List<String> values) {
            addCriterion("ext_serial_id not in", values, "extSerialId");
            return (Criteria) this;
        }

        public Criteria andExtSerialIdBetween(String value1, String value2) {
            addCriterion("ext_serial_id between", value1, value2, "extSerialId");
            return (Criteria) this;
        }

        public Criteria andExtSerialIdNotBetween(String value1, String value2) {
            addCriterion("ext_serial_id not between", value1, value2, "extSerialId");
            return (Criteria) this;
        }
		        public Criteria andFileStatusIsNull() {
            addCriterion("file_status is null");
            return (Criteria) this;
        }

        public Criteria andFileStatusIsNotNull() {
            addCriterion("file_status is not null");
            return (Criteria) this;
        }

        public Criteria andFileStatusEqualTo(String value) {
            addCriterion("file_status =", value, "fileStatus");
            return (Criteria) this;
        }

        public Criteria andFileStatusNotEqualTo(String value) {
            addCriterion("file_status <>", value, "fileStatus");
            return (Criteria) this;
        }

        public Criteria andFileStatusGreaterThan(String value) {
            addCriterion("file_status >", value, "fileStatus");
            return (Criteria) this;
        }

        public Criteria andFileStatusGreaterThanOrEqualTo(String value) {
            addCriterion("file_status >=", value, "fileStatus");
            return (Criteria) this;
        }

        public Criteria andFileStatusLessThan(String value) {
            addCriterion("file_status <", value, "fileStatus");
            return (Criteria) this;
        }

        public Criteria andFileStatusLessThanOrEqualTo(String value) {
            addCriterion("file_status <=", value, "fileStatus");
            return (Criteria) this;
        }

        public Criteria andFileStatusLike(String value) {
            addCriterion("file_status like", value, "fileStatus");
            return (Criteria) this;
        }

        public Criteria andFileStatusNotLike(String value) {
            addCriterion("file_status not like", value, "fileStatus");
            return (Criteria) this;
        }

        public Criteria andFileStatusIn(List<String> values) {
            addCriterion("file_status in", values, "fileStatus");
            return (Criteria) this;
        }

        public Criteria andFileStatusNotIn(List<String> values) {
            addCriterion("file_status not in", values, "fileStatus");
            return (Criteria) this;
        }

        public Criteria andFileStatusBetween(String value1, String value2) {
            addCriterion("file_status between", value1, value2, "fileStatus");
            return (Criteria) this;
        }

        public Criteria andFileStatusNotBetween(String value1, String value2) {
            addCriterion("file_status not between", value1, value2, "fileStatus");
            return (Criteria) this;
        }
		        public Criteria andSharedStatusIsNull() {
            addCriterion("shared_status is null");
            return (Criteria) this;
        }

        public Criteria andSharedStatusIsNotNull() {
            addCriterion("shared_status is not null");
            return (Criteria) this;
        }

        public Criteria andSharedStatusEqualTo(String value) {
            addCriterion("shared_status =", value, "sharedStatus");
            return (Criteria) this;
        }

        public Criteria andSharedStatusNotEqualTo(String value) {
            addCriterion("shared_status <>", value, "sharedStatus");
            return (Criteria) this;
        }

        public Criteria andSharedStatusGreaterThan(String value) {
            addCriterion("shared_status >", value, "sharedStatus");
            return (Criteria) this;
        }

        public Criteria andSharedStatusGreaterThanOrEqualTo(String value) {
            addCriterion("shared_status >=", value, "sharedStatus");
            return (Criteria) this;
        }

        public Criteria andSharedStatusLessThan(String value) {
            addCriterion("shared_status <", value, "sharedStatus");
            return (Criteria) this;
        }

        public Criteria andSharedStatusLessThanOrEqualTo(String value) {
            addCriterion("shared_status <=", value, "sharedStatus");
            return (Criteria) this;
        }

        public Criteria andSharedStatusLike(String value) {
            addCriterion("shared_status like", value, "sharedStatus");
            return (Criteria) this;
        }

        public Criteria andSharedStatusNotLike(String value) {
            addCriterion("shared_status not like", value, "sharedStatus");
            return (Criteria) this;
        }

        public Criteria andSharedStatusIn(List<String> values) {
            addCriterion("shared_status in", values, "sharedStatus");
            return (Criteria) this;
        }

        public Criteria andSharedStatusNotIn(List<String> values) {
            addCriterion("shared_status not in", values, "sharedStatus");
            return (Criteria) this;
        }

        public Criteria andSharedStatusBetween(String value1, String value2) {
            addCriterion("shared_status between", value1, value2, "sharedStatus");
            return (Criteria) this;
        }

        public Criteria andSharedStatusNotBetween(String value1, String value2) {
            addCriterion("shared_status not between", value1, value2, "sharedStatus");
            return (Criteria) this;
        }
		        public Criteria andGlobalSerialNoIsNull() {
            addCriterion("global_serial_no is null");
            return (Criteria) this;
        }

        public Criteria andGlobalSerialNoIsNotNull() {
            addCriterion("global_serial_no is not null");
            return (Criteria) this;
        }

        public Criteria andGlobalSerialNoEqualTo(String value) {
            addCriterion("global_serial_no =", value, "globalSerialNo");
            return (Criteria) this;
        }

        public Criteria andGlobalSerialNoNotEqualTo(String value) {
            addCriterion("global_serial_no <>", value, "globalSerialNo");
            return (Criteria) this;
        }

        public Criteria andGlobalSerialNoGreaterThan(String value) {
            addCriterion("global_serial_no >", value, "globalSerialNo");
            return (Criteria) this;
        }

        public Criteria andGlobalSerialNoGreaterThanOrEqualTo(String value) {
            addCriterion("global_serial_no >=", value, "globalSerialNo");
            return (Criteria) this;
        }

        public Criteria andGlobalSerialNoLessThan(String value) {
            addCriterion("global_serial_no <", value, "globalSerialNo");
            return (Criteria) this;
        }

        public Criteria andGlobalSerialNoLessThanOrEqualTo(String value) {
            addCriterion("global_serial_no <=", value, "globalSerialNo");
            return (Criteria) this;
        }

        public Criteria andGlobalSerialNoLike(String value) {
            addCriterion("global_serial_no like", value, "globalSerialNo");
            return (Criteria) this;
        }

        public Criteria andGlobalSerialNoNotLike(String value) {
            addCriterion("global_serial_no not like", value, "globalSerialNo");
            return (Criteria) this;
        }

        public Criteria andGlobalSerialNoIn(List<String> values) {
            addCriterion("global_serial_no in", values, "globalSerialNo");
            return (Criteria) this;
        }

        public Criteria andGlobalSerialNoNotIn(List<String> values) {
            addCriterion("global_serial_no not in", values, "globalSerialNo");
            return (Criteria) this;
        }

        public Criteria andGlobalSerialNoBetween(String value1, String value2) {
            addCriterion("global_serial_no between", value1, value2, "globalSerialNo");
            return (Criteria) this;
        }

        public Criteria andGlobalSerialNoNotBetween(String value1, String value2) {
            addCriterion("global_serial_no not between", value1, value2, "globalSerialNo");
            return (Criteria) this;
        }
		        public Criteria andBatchNumIsNull() {
            addCriterion("batch_num is null");
            return (Criteria) this;
        }

        public Criteria andBatchNumIsNotNull() {
            addCriterion("batch_num is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNumEqualTo(Integer value) {
            addCriterion("batch_num =", value, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumNotEqualTo(Integer value) {
            addCriterion("batch_num <>", value, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumGreaterThan(Integer value) {
            addCriterion("batch_num >", value, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("batch_num >=", value, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumLessThan(Integer value) {
            addCriterion("batch_num <", value, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumLessThanOrEqualTo(Integer value) {
            addCriterion("batch_num <=", value, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumLike(Integer value) {
            addCriterion("batch_num like", value, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumNotLike(Integer value) {
            addCriterion("batch_num not like", value, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumIn(List<Integer> values) {
            addCriterion("batch_num in", values, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumNotIn(List<Integer> values) {
            addCriterion("batch_num not in", values, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumBetween(Integer value1, Integer value2) {
            addCriterion("batch_num between", value1, value2, "batchNum");
            return (Criteria) this;
        }

        public Criteria andBatchNumNotBetween(Integer value1, Integer value2) {
            addCriterion("batch_num not between", value1, value2, "batchNum");
            return (Criteria) this;
        }
		        public Criteria andBatchSerialNoIsNull() {
            addCriterion("batch_serial_no is null");
            return (Criteria) this;
        }

        public Criteria andBatchSerialNoIsNotNull() {
            addCriterion("batch_serial_no is not null");
            return (Criteria) this;
        }

        public Criteria andBatchSerialNoEqualTo(String value) {
            addCriterion("batch_serial_no =", value, "batchSerialNo");
            return (Criteria) this;
        }

        public Criteria andBatchSerialNoNotEqualTo(String value) {
            addCriterion("batch_serial_no <>", value, "batchSerialNo");
            return (Criteria) this;
        }

        public Criteria andBatchSerialNoGreaterThan(String value) {
            addCriterion("batch_serial_no >", value, "batchSerialNo");
            return (Criteria) this;
        }

        public Criteria andBatchSerialNoGreaterThanOrEqualTo(String value) {
            addCriterion("batch_serial_no >=", value, "batchSerialNo");
            return (Criteria) this;
        }

        public Criteria andBatchSerialNoLessThan(String value) {
            addCriterion("batch_serial_no <", value, "batchSerialNo");
            return (Criteria) this;
        }

        public Criteria andBatchSerialNoLessThanOrEqualTo(String value) {
            addCriterion("batch_serial_no <=", value, "batchSerialNo");
            return (Criteria) this;
        }

        public Criteria andBatchSerialNoLike(String value) {
            addCriterion("batch_serial_no like", value, "batchSerialNo");
            return (Criteria) this;
        }

        public Criteria andBatchSerialNoNotLike(String value) {
            addCriterion("batch_serial_no not like", value, "batchSerialNo");
            return (Criteria) this;
        }

        public Criteria andBatchSerialNoIn(List<String> values) {
            addCriterion("batch_serial_no in", values, "batchSerialNo");
            return (Criteria) this;
        }

        public Criteria andBatchSerialNoNotIn(List<String> values) {
            addCriterion("batch_serial_no not in", values, "batchSerialNo");
            return (Criteria) this;
        }

        public Criteria andBatchSerialNoBetween(String value1, String value2) {
            addCriterion("batch_serial_no between", value1, value2, "batchSerialNo");
            return (Criteria) this;
        }

        public Criteria andBatchSerialNoNotBetween(String value1, String value2) {
            addCriterion("batch_serial_no not between", value1, value2, "batchSerialNo");
            return (Criteria) this;
        }
		        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLike(Date value) {
            addCriterion("update_time like", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotLike(Date value) {
            addCriterion("update_time not like", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
		        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLike(Date value) {
            addCriterion("create_time like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotLike(Date value) {
            addCriterion("create_time not like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
		        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }
		        public Criteria andInboundSerialDatetimeIsNull() {
            addCriterion("inbound_serial_datetime is null");
            return (Criteria) this;
        }

        public Criteria andInboundSerialDatetimeIsNotNull() {
            addCriterion("inbound_serial_datetime is not null");
            return (Criteria) this;
        }

        public Criteria andInboundSerialDatetimeEqualTo(Date value) {
            addCriterion("inbound_serial_datetime =", value, "inboundSerialDatetime");
            return (Criteria) this;
        }

        public Criteria andInboundSerialDatetimeNotEqualTo(Date value) {
            addCriterion("inbound_serial_datetime <>", value, "inboundSerialDatetime");
            return (Criteria) this;
        }

        public Criteria andInboundSerialDatetimeGreaterThan(Date value) {
            addCriterion("inbound_serial_datetime >", value, "inboundSerialDatetime");
            return (Criteria) this;
        }

        public Criteria andInboundSerialDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("inbound_serial_datetime >=", value, "inboundSerialDatetime");
            return (Criteria) this;
        }

        public Criteria andInboundSerialDatetimeLessThan(Date value) {
            addCriterion("inbound_serial_datetime <", value, "inboundSerialDatetime");
            return (Criteria) this;
        }

        public Criteria andInboundSerialDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("inbound_serial_datetime <=", value, "inboundSerialDatetime");
            return (Criteria) this;
        }

        public Criteria andInboundSerialDatetimeLike(Date value) {
            addCriterion("inbound_serial_datetime like", value, "inboundSerialDatetime");
            return (Criteria) this;
        }

        public Criteria andInboundSerialDatetimeNotLike(Date value) {
            addCriterion("inbound_serial_datetime not like", value, "inboundSerialDatetime");
            return (Criteria) this;
        }

        public Criteria andInboundSerialDatetimeIn(List<Date> values) {
            addCriterion("inbound_serial_datetime in", values, "inboundSerialDatetime");
            return (Criteria) this;
        }

        public Criteria andInboundSerialDatetimeNotIn(List<Date> values) {
            addCriterion("inbound_serial_datetime not in", values, "inboundSerialDatetime");
            return (Criteria) this;
        }

        public Criteria andInboundSerialDatetimeBetween(Date value1, Date value2) {
            addCriterion("inbound_serial_datetime between", value1, value2, "inboundSerialDatetime");
            return (Criteria) this;
        }

        public Criteria andInboundSerialDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("inbound_serial_datetime not between", value1, value2, "inboundSerialDatetime");
            return (Criteria) this;
        }
		        public Criteria andInboundSerialNoIsNull() {
            addCriterion("inbound_serial_no is null");
            return (Criteria) this;
        }

        public Criteria andInboundSerialNoIsNotNull() {
            addCriterion("inbound_serial_no is not null");
            return (Criteria) this;
        }

        public Criteria andInboundSerialNoEqualTo(String value) {
            addCriterion("inbound_serial_no =", value, "inboundSerialNo");
            return (Criteria) this;
        }

        public Criteria andInboundSerialNoNotEqualTo(String value) {
            addCriterion("inbound_serial_no <>", value, "inboundSerialNo");
            return (Criteria) this;
        }

        public Criteria andInboundSerialNoGreaterThan(String value) {
            addCriterion("inbound_serial_no >", value, "inboundSerialNo");
            return (Criteria) this;
        }

        public Criteria andInboundSerialNoGreaterThanOrEqualTo(String value) {
            addCriterion("inbound_serial_no >=", value, "inboundSerialNo");
            return (Criteria) this;
        }

        public Criteria andInboundSerialNoLessThan(String value) {
            addCriterion("inbound_serial_no <", value, "inboundSerialNo");
            return (Criteria) this;
        }

        public Criteria andInboundSerialNoLessThanOrEqualTo(String value) {
            addCriterion("inbound_serial_no <=", value, "inboundSerialNo");
            return (Criteria) this;
        }

        public Criteria andInboundSerialNoLike(String value) {
            addCriterion("inbound_serial_no like", value, "inboundSerialNo");
            return (Criteria) this;
        }

        public Criteria andInboundSerialNoNotLike(String value) {
            addCriterion("inbound_serial_no not like", value, "inboundSerialNo");
            return (Criteria) this;
        }

        public Criteria andInboundSerialNoIn(List<String> values) {
            addCriterion("inbound_serial_no in", values, "inboundSerialNo");
            return (Criteria) this;
        }

        public Criteria andInboundSerialNoNotIn(List<String> values) {
            addCriterion("inbound_serial_no not in", values, "inboundSerialNo");
            return (Criteria) this;
        }

        public Criteria andInboundSerialNoBetween(String value1, String value2) {
            addCriterion("inbound_serial_no between", value1, value2, "inboundSerialNo");
            return (Criteria) this;
        }

        public Criteria andInboundSerialNoNotBetween(String value1, String value2) {
            addCriterion("inbound_serial_no not between", value1, value2, "inboundSerialNo");
            return (Criteria) this;
        }
		        public Criteria andBizDatetimeIsNull() {
            addCriterion("biz_datetime is null");
            return (Criteria) this;
        }

        public Criteria andBizDatetimeIsNotNull() {
            addCriterion("biz_datetime is not null");
            return (Criteria) this;
        }

        public Criteria andBizDatetimeEqualTo(Date value) {
            addCriterion("biz_datetime =", value, "bizDatetime");
            return (Criteria) this;
        }

        public Criteria andBizDatetimeNotEqualTo(Date value) {
            addCriterion("biz_datetime <>", value, "bizDatetime");
            return (Criteria) this;
        }

        public Criteria andBizDatetimeGreaterThan(Date value) {
            addCriterion("biz_datetime >", value, "bizDatetime");
            return (Criteria) this;
        }

        public Criteria andBizDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("biz_datetime >=", value, "bizDatetime");
            return (Criteria) this;
        }

        public Criteria andBizDatetimeLessThan(Date value) {
            addCriterion("biz_datetime <", value, "bizDatetime");
            return (Criteria) this;
        }

        public Criteria andBizDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("biz_datetime <=", value, "bizDatetime");
            return (Criteria) this;
        }

        public Criteria andBizDatetimeLike(Date value) {
            addCriterion("biz_datetime like", value, "bizDatetime");
            return (Criteria) this;
        }

        public Criteria andBizDatetimeNotLike(Date value) {
            addCriterion("biz_datetime not like", value, "bizDatetime");
            return (Criteria) this;
        }

        public Criteria andBizDatetimeIn(List<Date> values) {
            addCriterion("biz_datetime in", values, "bizDatetime");
            return (Criteria) this;
        }

        public Criteria andBizDatetimeNotIn(List<Date> values) {
            addCriterion("biz_datetime not in", values, "bizDatetime");
            return (Criteria) this;
        }

        public Criteria andBizDatetimeBetween(Date value1, Date value2) {
            addCriterion("biz_datetime between", value1, value2, "bizDatetime");
            return (Criteria) this;
        }

        public Criteria andBizDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("biz_datetime not between", value1, value2, "bizDatetime");
            return (Criteria) this;
        }
		        public Criteria andChannelNoIsNull() {
            addCriterion("channel_no is null");
            return (Criteria) this;
        }

        public Criteria andChannelNoIsNotNull() {
            addCriterion("channel_no is not null");
            return (Criteria) this;
        }

        public Criteria andChannelNoEqualTo(String value) {
            addCriterion("channel_no =", value, "channelNo");
            return (Criteria) this;
        }

        public Criteria andChannelNoNotEqualTo(String value) {
            addCriterion("channel_no <>", value, "channelNo");
            return (Criteria) this;
        }

        public Criteria andChannelNoGreaterThan(String value) {
            addCriterion("channel_no >", value, "channelNo");
            return (Criteria) this;
        }

        public Criteria andChannelNoGreaterThanOrEqualTo(String value) {
            addCriterion("channel_no >=", value, "channelNo");
            return (Criteria) this;
        }

        public Criteria andChannelNoLessThan(String value) {
            addCriterion("channel_no <", value, "channelNo");
            return (Criteria) this;
        }

        public Criteria andChannelNoLessThanOrEqualTo(String value) {
            addCriterion("channel_no <=", value, "channelNo");
            return (Criteria) this;
        }

        public Criteria andChannelNoLike(String value) {
            addCriterion("channel_no like", value, "channelNo");
            return (Criteria) this;
        }

        public Criteria andChannelNoNotLike(String value) {
            addCriterion("channel_no not like", value, "channelNo");
            return (Criteria) this;
        }

        public Criteria andChannelNoIn(List<String> values) {
            addCriterion("channel_no in", values, "channelNo");
            return (Criteria) this;
        }

        public Criteria andChannelNoNotIn(List<String> values) {
            addCriterion("channel_no not in", values, "channelNo");
            return (Criteria) this;
        }

        public Criteria andChannelNoBetween(String value1, String value2) {
            addCriterion("channel_no between", value1, value2, "channelNo");
            return (Criteria) this;
        }

        public Criteria andChannelNoNotBetween(String value1, String value2) {
            addCriterion("channel_no not between", value1, value2, "channelNo");
            return (Criteria) this;
        }
		        public Criteria andSerialNoIsNull() {
            addCriterion("serial_no is null");
            return (Criteria) this;
        }

        public Criteria andSerialNoIsNotNull() {
            addCriterion("serial_no is not null");
            return (Criteria) this;
        }

        public Criteria andSerialNoEqualTo(String value) {
            addCriterion("serial_no =", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoNotEqualTo(String value) {
            addCriterion("serial_no <>", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoGreaterThan(String value) {
            addCriterion("serial_no >", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoGreaterThanOrEqualTo(String value) {
            addCriterion("serial_no >=", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoLessThan(String value) {
            addCriterion("serial_no <", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoLessThanOrEqualTo(String value) {
            addCriterion("serial_no <=", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoLike(String value) {
            addCriterion("serial_no like", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoNotLike(String value) {
            addCriterion("serial_no not like", value, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoIn(List<String> values) {
            addCriterion("serial_no in", values, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoNotIn(List<String> values) {
            addCriterion("serial_no not in", values, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoBetween(String value1, String value2) {
            addCriterion("serial_no between", value1, value2, "serialNo");
            return (Criteria) this;
        }

        public Criteria andSerialNoNotBetween(String value1, String value2) {
            addCriterion("serial_no not between", value1, value2, "serialNo");
            return (Criteria) this;
        }
		    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}