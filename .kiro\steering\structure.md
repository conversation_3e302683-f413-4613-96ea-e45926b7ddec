# Project Structure

## Multi-Module Maven Architecture

This project follows a layered, multi-module Maven architecture with clear separation of concerns:

```
hsjry-limit-batch/
├── hsjry-limit-batch-facade/          # API interfaces and contracts
├── hsjry-limit-batch-controller/      # Web controllers and REST endpoints
├── hsjry-limit-batch-biz/            # Business logic interfaces
├── hsjry-limit-batch-biz-impl/       # Business logic implementations
├── hsjry-limit-batch-core/           # Core domain logic interfaces
├── hsjry-limit-batch-core-impl/      # Core domain logic implementations
├── hsjry-limit-batch-dal/            # Data access layer interfaces
├── hsjry-limit-batch-dal-impl/       # Data access layer implementations
├── hsjry-limit-batch-common/         # Shared utilities and constants
├── hsjry-limit-batch-deploy/         # Deployment configurations
├── hsjry-limit-batch-starter/        # Spring Boot starter configuration
└── hsjry-limit-batch-test/           # Integration and unit tests
```

## Layer Dependencies

**Top-down dependency flow:**
- Controller → Biz → Core → DAL
- Each layer depends only on the interface of the layer below
- Implementation modules depend on their corresponding interface modules

## Package Structure

All modules follow the standard package structure:
```
com.hsjry.core.limit.batch.{module}/
├── src/main/java/
└── src/test/java/ (where applicable)
```

## Key Conventions

### Naming Patterns
- **Interfaces**: Located in base modules (e.g., `hsjry-limit-batch-biz`)
- **Implementations**: Located in `-impl` modules with `Impl` suffix
- **Services**: Use `@Service` annotation with bean name matching class name
- **Primary Implementations**: Use `@Primary` when multiple implementations exist

### File Processing Classes
- Extend `AbstractFileBaseShardingPrepareBizImpl<T>` for file sync operations
- Use dedicated data classes for CSV parsing (e.g., `FileCorePprodData`)
- Implement converter classes for data transformation (e.g., `FileCorePprodConverter`)

### Testing Structure
- **Integration Tests**: Spring-based tests in `-test` module
- **Unit Tests**: Standalone tests that don't require Spring context
- **Test Resources**: Located in `src/test/resources/` with descriptive names

## Module Responsibilities

- **facade**: Public API contracts and DTOs
- **controller**: HTTP endpoints and request/response handling  
- **biz**: Business logic orchestration and validation
- **core**: Domain-specific business rules and calculations
- **dal**: Database operations and entity mapping
- **common**: Shared utilities, constants, and base classes
- **deploy**: Application configuration and deployment scripts
- **starter**: Spring Boot auto-configuration
- **test**: Comprehensive test suites and test utilities