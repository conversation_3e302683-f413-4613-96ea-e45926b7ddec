package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitTemplateDimRelDo;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitTemplateDimRelQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 限额模板维度关系数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcAmtLimitTemplateDimRelDao extends IBaseDao<LcAmtLimitTemplateDimRelDo> {
    /**
     * 分页查询限额模板维度关系
     *
     * @param lcAmtLimitTemplateDimRelQuery 条件
     * @return PageInfo<LcAmtLimitTemplateDimRelDo>
     */
    PageInfo<LcAmtLimitTemplateDimRelDo> selectPage(LcAmtLimitTemplateDimRelQuery lcAmtLimitTemplateDimRelQuery,
        PageParam pageParam);

    /**
     * 根据key查询限额模板维度关系
     *
     * @param templatDimRelId
     * @return
     */
    LcAmtLimitTemplateDimRelDo selectByKey(String templatDimRelId);

    /**
     * 根据key删除限额模板维度关系
     *
     * @param templatDimRelId
     * @return
     */
    int deleteByKey(String templatDimRelId);

    /**
     * 查询限额模板维度关系信息
     *
     * @param lcAmtLimitTemplateDimRelQuery 条件
     * @return List<LcAmtLimitTemplateDimRelDo>
     */
    List<LcAmtLimitTemplateDimRelDo> selectByExample(LcAmtLimitTemplateDimRelQuery lcAmtLimitTemplateDimRelQuery);

    /**
     * 新增限额模板维度关系信息
     *
     * @param lcAmtLimitTemplateDimRel 条件
     * @return int>
     */
    int insertBySelective(LcAmtLimitTemplateDimRelDo lcAmtLimitTemplateDimRel);

    /**
     * 修改限额模板维度关系信息
     *
     * @param lcAmtLimitTemplateDimRel
     * @return
     */
    int updateBySelective(LcAmtLimitTemplateDimRelDo lcAmtLimitTemplateDimRel);

    /**
     * 修改限额模板维度关系信息
     *
     * @param lcAmtLimitTemplateDimRel
     * @param lcAmtLimitTemplateDimRelQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcAmtLimitTemplateDimRelDo lcAmtLimitTemplateDimRel,
        LcAmtLimitTemplateDimRelQuery lcAmtLimitTemplateDimRelQuery);

    /**
     * 删除限额模板维度关系信息
     *
     * @param lcAmtLimitTemplateDimRelQuery 条件
     * @return List<LcAmtLimitTemplateDimRelDo>
     */
    int deleteByExample(LcAmtLimitTemplateDimRelQuery lcAmtLimitTemplateDimRelQuery);
}
