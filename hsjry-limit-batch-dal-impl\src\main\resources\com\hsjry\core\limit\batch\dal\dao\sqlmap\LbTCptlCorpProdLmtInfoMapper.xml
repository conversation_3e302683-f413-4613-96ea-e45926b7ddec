<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbTCptlCorpProdLmtInfoMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbTCptlCorpProdLmtInfoDo">
        <result property="productId" column="product_id" jdbcType="VARCHAR"/> <!-- 产品编号 -->
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/> <!-- 租户号 -->
        <result property="ownOrganId" column="own_organ_id" jdbcType="VARCHAR"/> <!-- 所属组织编号 -->
        <result property="operatorId" column="operator_id" jdbcType="VARCHAR"/> <!-- 操作人编号 -->
        <result property="coreInstNo" column="core_inst_no" jdbcType="VARCHAR"/> <!-- 核心机构号 -->
        <result property="realOccupyAmount" column="real_occupy_amount" jdbcType="DECIMAL"/> <!-- 额度实例金额信息中实占额度 -->
        <result property="productName" column="product_name" jdbcType="VARCHAR"/> <!-- 产品名称 -->
        <result property="custNo" column="cust_no" jdbcType="VARCHAR"/> <!-- 客户编号 -->
        <result property="limitStatus" column="limit_status" jdbcType="VARCHAR"/> <!-- 额度状态 -->
        <result property="custLimitId" column="cust_limit_id" jdbcType="VARCHAR"/> <!-- 额度编号 -->
        <result property="usedAmount" column="used_amount" jdbcType="DECIMAL"/> <!-- 已用额度 -->
        <result property="certNo" column="cert_no" jdbcType="VARCHAR"/> <!-- 证件号码 -->
        <result property="certTyp" column="cert_typ" jdbcType="VARCHAR"/> <!-- 证件类型 -->
        <result property="custNm" column="cust_nm" jdbcType="VARCHAR"/> <!-- 客户名称 -->
        <result property="custTyp" column="cust_typ" jdbcType="VARCHAR"/> <!-- 客户类型 -->
    </resultMap>
    <sql id="Base_Column_List">
        product_id
        , update_time
                , create_time
                , tenant_id
                , own_organ_id
                , operator_id
                , core_inst_no
                , real_occupy_amount
                , product_name
                , cust_no
                , limit_status
                , cust_limit_id
                , used_amount
                , cert_no
                        , cert_typ
        , cust_nm
        , cust_typ
    </sql>

    <!-- 往[资金系统-中间表-对公客户产品层额度信息]插入数据 -->
    <insert id="insertCptlCorpProdLmtInfo" parameterType="java.util.Map">
        insert into LB_T_CPTL_CORP_PROD_LMT_INFO(cust_no, cust_typ, cust_nm, cert_typ, cert_no, used_amount,
        cust_limit_id,
        limit_status, product_id, product_name, real_occupy_amount, core_inst_no,
        operator_id, own_organ_id, tenant_id, create_time, update_time)
        select ltcci.CUST_NO,
        ltcci.CUST_TYP,
        ltcci.CUST_NM,
        ltcci.cert_typ,
        ltcci.cert_no,
        lscblus.USED_AMOUNT,
        lcli.CUST_LIMIT_ID,
        lcli.limit_status,
        lscblus.PRODUCT_ID,
        lscblus.PRODUCT_NAME,
        lscblus.USED_AMOUNT as real_occupy_amount,
        lscblus.CORE_INST_NO,
        lcli.operator_id,
        lcli.own_organ_id,
        lcli.TENANT_ID,
        sysdate as create_time,
        sysdate as update_time
        from lb_s_cptl_biz_lmt_use_sttn lscblus
        inner join LB_T_CORP_CUST_INFO ltcci on lscblus.USER_ID = ltcci.CUST_NO
        inner join LC_CUST_LIMIT_INFO lcli
        on lcli.LIMIT_OBJECT_ID = ltcci.CUST_NO and lcli.PRODUCT_ID = lscblus.PRODUCT_ID
        where lcli.TEMPLATE_NODE_ID in
        <if test="templateNodeIdList != null and templateNodeIdList.size() > 0">
            <foreach collection="templateNodeIdList" item="templateNodeId" open="(" separator="," close=")">
                #{templateNodeId}
            </foreach>
        </if>
        <if test="templateNodeIdList == null or templateNodeIdList.size() == 0">
            (null)
        </if>
        and lcli.CUST_LIMIT_ID in
        <if test="limitIdList != null and limitIdList.size() > 0">
            <foreach collection="limitIdList" item="limitId" open="(" separator="," close=")">
                #{limitId}
            </foreach>
        </if>
        <if test="limitIdList == null or limitIdList.size() == 0">
            (null)
        </if>
    </insert>

    <!-- 更新[额度实例金额信息]中[实占额度] -->
    <update id="updateRealOccupyAmount" parameterType="java.util.Map">
        MERGE INTO LC_CUST_LIMIT_AMT_INFO lclai
        USING LB_T_CPTL_CORP_PROD_LMT_INFO ltccpli
        ON (lclai.CUST_LIMIT_ID = ltccpli.CUST_LIMIT_ID AND lclai.CUST_NO = ltccpli.CUST_NO)
        WHEN MATCHED THEN
        UPDATE
        SET lclai.REAL_OCCUPY_AMOUNT = ltccpli.REAL_OCCUPY_AMOUNT
        WHERE lclai.CUST_LIMIT_ID IN
        <if test="limitIdList != null and limitIdList.size() > 0">
            <foreach collection="limitIdList" item="limitId" open="(" separator="," close=")">
                #{limitId}
            </foreach>
        </if>
        <if test="limitIdList == null or limitIdList.size() == 0">
            (null)
        </if>
    </update>

</mapper>