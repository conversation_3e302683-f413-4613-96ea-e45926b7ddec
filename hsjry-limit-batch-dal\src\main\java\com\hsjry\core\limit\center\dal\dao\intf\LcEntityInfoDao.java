package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.Date;
import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcEntityInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcLoanEntityInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcEntityInfoQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcLoanEntityInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 实体信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcEntityInfoDao extends IBaseDao<LcEntityInfoDo> {
    /**
     * 分页查询实体信息
     *
     * @param lcEntityInfoQuery 条件
     * @return PageInfo<LcEntityInfoDo>
     */
    PageInfo<LcEntityInfoDo> selectPage(LcEntityInfoQuery lcEntityInfoQuery, PageParam pageParam);

    /**
     * 分页查询借据实体信息
     *
     * @param query 条件
     * @param pageParam
     * @return PageInfo<LcLoanEntityInfoDo>
     */
    PageInfo<LcLoanEntityInfoDo> selectLoanEntityPage(LcLoanEntityInfoQuery query, PageParam pageParam);

    /**
     * 查询借据实体信息
     *
     * @param query 条件
     * @return PageInfo<LcLoanEntityInfoDo>
     */
    List<LcLoanEntityInfoDo> selectLoanEntityList(LcLoanEntityInfoQuery query);

    /**
     * 根据key查询实体信息
     *
     * @param entityId
     * @return
     */
    LcEntityInfoDo selectByKey(String entityId);

    /**
     * 根据key删除实体信息
     *
     * @param entityId
     * @return
     */
    int deleteByKey(String entityId);

    /**
     * 查询实体信息信息
     *
     * @param lcEntityInfoQuery 条件
     * @return List<LcEntityInfoDo>
     */
    List<LcEntityInfoDo> selectByExample(LcEntityInfoQuery lcEntityInfoQuery);

    /**
     * 新增实体信息信息
     *
     * @param lcEntityInfo 条件
     * @return int>
     */
    int insertBySelective(LcEntityInfoDo lcEntityInfo);

    /**
     * 修改实体信息信息
     *
     * @param lcEntityInfo
     * @return
     */
    int updateBySelective(LcEntityInfoDo lcEntityInfo);

    /**
     * 修改实体信息信息
     *
     * @param lcEntityInfo
     * @param lcEntityInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcEntityInfoDo lcEntityInfo, LcEntityInfoQuery lcEntityInfoQuery);



    /**
     * 更新重算标记
     *
     * @param entityId
     * @param custLimitExRateRecalFlag
     */
    void updateCalFlagLimit(String entityId, String custLimitExRateRecalFlag);

    /**
     * 更新重算标记(批量)
     *
     * @param entityIdList
     * @param custLimitExRateRecalFlag
     */
    void updateCalFlagLimitBatch(List<String> entityIdList, String custLimitExRateRecalFlag);

    /**
     * 更新重算标记
     *
     * @param entityId
     * @param amtLimitExRateRecalFlag
     */
    void updateCalFlagAmtLimit(String entityId, String amtLimitExRateRecalFlag);
    /**
     * 更新重算标记(批量)
     *
     * @param entityIdList
     * @param amtLimitExRateRecalFlag
     */
    void updateCalFlagAmtLimitBatch(List<String> entityIdList, String amtLimitExRateRecalFlag);

    /**
     * 更新汇率变更
     *
     * @param entityId
     * @param exchangeRateVersion
     */
    void updateExchangeRateVersion(String entityId, Integer exchangeRateVersion, boolean forceFlag);

    /**
     * 更新汇率变更（批量）
     *
     * @param entityIdList
     * @param exchangeRateVersion
     */
    void updateExchangeRateVersionBatch(List<String> entityIdList, Integer exchangeRateVersion, boolean forceFlag);

    /**
     * 删除实体信息信息
     *
     * @param lcEntityInfoQuery 条件
     * @return List<LcEntityInfoDo>
     */
    int deleteByExample(LcEntityInfoQuery lcEntityInfoQuery);
}
