package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 单一限额规则主键
 *
 * <AUTHOR>
 * @date 2023-06-28 08:56:52
 */
@Table(name = "lc_single_limit_rule")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcSingleLimitRuleKeyDo implements Serializable {

    private static final long serialVersionUID = 1673978759944011776L;
    /** 单一限额规则编号 */
    @Id
    @Column(name = "rule_id")
    private String ruleId;
        /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    }