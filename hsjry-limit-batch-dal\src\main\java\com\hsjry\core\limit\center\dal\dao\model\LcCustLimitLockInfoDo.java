package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度实例锁信息Do
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
@Table(name = "lc_cust_limit_lock_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcCustLimitLockInfoDo extends LcCustLimitLockInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1602582710713516042L;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 锁定流水 */
    @Column(name = "lock_serial_no")
    private String lockSerialNo;
    /** 锁状态;EnumBool(Y-是，N-否) */
    @Column(name = "lock_status")
    private String lockStatus;
    /** 锁定时间 */
    @Column(name = "lock_time")
    private java.util.Date lockTime;
    /** 锁定交易码 */
    @Column(name = "lock_trade_code")
    private String lockTradeCode;
    /** 锁定交易码 */
    @Column(name = "original_lock_trade_code")
    private String originalLockTradeCode;
    /** 锁定交易描述 */
    @Column(name = "lock_trade_description")
    private String lockTradeDescription;
    /** 乐观锁版本 */
    @Column(name = "lock_version")
    private Integer lockVersion;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
}
