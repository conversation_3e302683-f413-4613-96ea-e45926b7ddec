package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTElcblCorpCtrInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTElcblCorpCtrInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpCtrInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpCtrInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTElcblCorpCtrInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTElcblCorpCtrInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-中间表-贴现对公合同信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-29 02:46:35
 */
@Repository
public class LbTElcblCorpCtrInfoDaoImpl extends AbstractBaseDaoImpl<LbTElcblCorpCtrInfoDo, LbTElcblCorpCtrInfoMapper>
    implements LbTElcblCorpCtrInfoDao {
    private static final Logger log = LoggerFactory.getLogger(LbTElcblCorpCtrInfoDaoImpl.class);

    /**
     * 分页查询
     *
     * @param lbTElcblCorpCtrInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTElcblCorpCtrInfoDo> selectPage(LbTElcblCorpCtrInfoQuery lbTElcblCorpCtrInfo,
        PageParam pageParam) {
        LbTElcblCorpCtrInfoExample example = buildExample(lbTElcblCorpCtrInfo);
        return PageHelper.<LbTElcblCorpCtrInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询电票系统-中间表-贴现对公合同信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTElcblCorpCtrInfoDo selectByKey(String custNo, String custLimitId) {
        LbTElcblCorpCtrInfoKeyDo lbTElcblCorpCtrInfoKeyDo = new LbTElcblCorpCtrInfoKeyDo();
        lbTElcblCorpCtrInfoKeyDo.setCustNo(custNo);
        lbTElcblCorpCtrInfoKeyDo.setCustLimitId(custLimitId);
        lbTElcblCorpCtrInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectByPrimaryKey(lbTElcblCorpCtrInfoKeyDo);
    }

    /**
     * 根据key删除电票系统-中间表-贴现对公合同信息
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTElcblCorpCtrInfoKeyDo lbTElcblCorpCtrInfoKeyDo = new LbTElcblCorpCtrInfoKeyDo();
        lbTElcblCorpCtrInfoKeyDo.setCustNo(custNo);
        lbTElcblCorpCtrInfoKeyDo.setCustLimitId(custLimitId);
        lbTElcblCorpCtrInfoKeyDo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().deleteByPrimaryKey(lbTElcblCorpCtrInfoKeyDo);
    }

    /**
     * 查询电票系统-中间表-贴现对公合同信息信息
     *
     * @param lbTElcblCorpCtrInfo 条件
     * @return List<LbTElcblCorpCtrInfoDo>
     */
    @Override
    public List<LbTElcblCorpCtrInfoDo> selectByExample(LbTElcblCorpCtrInfoQuery lbTElcblCorpCtrInfo) {
        return getMapper().selectByExample(buildExample(lbTElcblCorpCtrInfo));
    }

    /**
     * 新增电票系统-中间表-贴现对公合同信息信息
     *
     * @param lbTElcblCorpCtrInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTElcblCorpCtrInfoDo lbTElcblCorpCtrInfo) {
        if (lbTElcblCorpCtrInfo == null) {
            return -1;
        }

        lbTElcblCorpCtrInfo.setCreateTime(BusinessDateUtil.getDate());
        lbTElcblCorpCtrInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTElcblCorpCtrInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().insertSelective(lbTElcblCorpCtrInfo);
    }

    /**
     * 修改电票系统-中间表-贴现对公合同信息信息
     *
     * @param lbTElcblCorpCtrInfo
     * @return
     */
    @Override
    public int updateBySelective(LbTElcblCorpCtrInfoDo lbTElcblCorpCtrInfo) {
        if (lbTElcblCorpCtrInfo == null) {
            return -1;
        }
        lbTElcblCorpCtrInfo.setUpdateTime(BusinessDateUtil.getDate());
        lbTElcblCorpCtrInfo.setTenantId(AppParamUtil.getTenantId());
        return getMapper().updateByPrimaryKeySelective(lbTElcblCorpCtrInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbTElcblCorpCtrInfoDo lbTElcblCorpCtrInfo,
        LbTElcblCorpCtrInfoQuery lbTElcblCorpCtrInfoQuery) {
        lbTElcblCorpCtrInfo.setUpdateTime(BusinessDateUtil.getDate());
        return getMapper().updateByExampleSelective(lbTElcblCorpCtrInfo, buildExample(lbTElcblCorpCtrInfoQuery));
    }

    /**
     * 构建电票系统-中间表-贴现对公合同信息Example信息
     *
     * @param lbTElcblCorpCtrInfo
     * @return
     */
    public LbTElcblCorpCtrInfoExample buildExample(LbTElcblCorpCtrInfoQuery lbTElcblCorpCtrInfo) {
        LbTElcblCorpCtrInfoExample example = new LbTElcblCorpCtrInfoExample();
        LbTElcblCorpCtrInfoExample.Criteria criteria = example.createCriteria();
        criteria.andTenantIdEqualTo(AppParamUtil.getTenantId());
        if (lbTElcblCorpCtrInfo != null) {
            //添加查询条件
            if (null != lbTElcblCorpCtrInfo.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTElcblCorpCtrInfo.getLowRiskAmount());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpCtrInfo.getOwnOrganId())) {
                criteria.andOwnOrganIdEqualTo(lbTElcblCorpCtrInfo.getOwnOrganId());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpCtrInfo.getOperatorId())) {
                criteria.andOperatorIdEqualTo(lbTElcblCorpCtrInfo.getOperatorId());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpCtrInfo.getEntityId())) {
                criteria.andEntityIdEqualTo(lbTElcblCorpCtrInfo.getEntityId());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpCtrInfo.getVirtualContractFlag())) {
                criteria.andVirtualContractFlagEqualTo(lbTElcblCorpCtrInfo.getVirtualContractFlag());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpCtrInfo.getRelationId())) {
                criteria.andRelationIdEqualTo(lbTElcblCorpCtrInfo.getRelationId());
            }
            if (null != lbTElcblCorpCtrInfo.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTElcblCorpCtrInfo.getRealOccupyLowRiskAmt());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpCtrInfo.getCustNo())) {
                criteria.andCustNoEqualTo(lbTElcblCorpCtrInfo.getCustNo());
            }
            if (null != lbTElcblCorpCtrInfo.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTElcblCorpCtrInfo.getRealOccupyAmount());
            }
            if (null != lbTElcblCorpCtrInfo.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTElcblCorpCtrInfo.getTotalAmount());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpCtrInfo.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTElcblCorpCtrInfo.getLimitStatus());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpCtrInfo.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTElcblCorpCtrInfo.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpCtrInfo.getCertNo())) {
                criteria.andCertNoEqualTo(lbTElcblCorpCtrInfo.getCertNo());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpCtrInfo.getCertTyp())) {
                criteria.andCertTypEqualTo(lbTElcblCorpCtrInfo.getCertTyp());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpCtrInfo.getCustNm())) {
                criteria.andCustNmEqualTo(lbTElcblCorpCtrInfo.getCustNm());
            }
            if (StringUtil.isNotEmpty(lbTElcblCorpCtrInfo.getCustTyp())) {
                criteria.andCustTypEqualTo(lbTElcblCorpCtrInfo.getCustTyp());
            }
        }
        buildExampleExt(lbTElcblCorpCtrInfo, criteria);
        return example;
    }

    /**
     * 构建电票系统-中间表-贴现对公合同信息ExampleExt方法
     *
     * @param lbTElcblCorpCtrInfo
     * @return
     */
    public void buildExampleExt(LbTElcblCorpCtrInfoQuery lbTElcblCorpCtrInfo,
        LbTElcblCorpCtrInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 将电票对公客户额度同步数据插入到合同信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_elcbl_corp_ctr_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    @Override
    public int insertElcblCorpCtrInfo(List<String> templateNodeIdList, List<String> custLimitIdList) {
        if (CollectionUtils.isEmpty(templateNodeIdList) || CollectionUtils.isEmpty(custLimitIdList)) {
            log.warn("模板节点ID列表或客户额度ID列表为空，跳过插入操作");
            return 0;
        }
        return getMapper().insertElcblCorpCtrInfo(templateNodeIdList, custLimitIdList);
    }

    /**
     * 更新额度实例金额信息表
     * 根据电票系统中间表数据更新LC_CUST_LIMIT_AMT_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    @Override
    public int updateCustLimitAmtInfo(List<String> custLimitIdList) {
        if (CollectionUtils.isEmpty(custLimitIdList)) {
            log.warn("客户额度ID列表为空，跳过更新额度实例金额信息操作");
            return 0;
        }
        return getMapper().updateCustLimitAmtInfo(custLimitIdList);
    }

}
