package com.hsjry.core.limit.center.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.center.dal.dao.intf.EntityOperateSerialBatchDao;
import com.hsjry.core.limit.center.dal.dao.mapper.EntityOperateSerialBatchMapper;
import com.hsjry.core.limit.center.dal.dao.query.EntityOperateSerialBatchQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityOperateSerialDo;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/3/27 16:11
 */
@Repository
public class EntityOperateSerialBatchDaoImpl
    extends AbstractBaseDaoImpl<LcEntityOperateSerialDo, EntityOperateSerialBatchMapper>
    implements EntityOperateSerialBatchDao {

    @Override
    public List<LcEntityOperateSerialDo> selectShardList(EntityOperateSerialBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectShardList(query);
    }

    @Override
    public LcEntityOperateSerialDo selectFirstOne(EntityOperateSerialBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        PageHelper.offsetPage(query.getOffset(), query.getLimit(), false);
        return getMapper().selectFirstOne(query);
    }

    @Override
    public Integer selectCountByCurrentGroup(EntityOperateSerialBatchQuery query) {
        query.setTenantId(AppParamUtil.getTenantId());
        return getMapper().selectCountByCurrentGroup(query);
    }
}
