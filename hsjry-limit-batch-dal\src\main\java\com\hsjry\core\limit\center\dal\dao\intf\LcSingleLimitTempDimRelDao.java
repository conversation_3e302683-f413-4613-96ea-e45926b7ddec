package com.hsjry.core.limit.center.dal.dao.intf;

import com.hsjry.core.limit.center.dal.dao.model.LcSingleLimitTempDimRelDo;
import com.hsjry.core.limit.center.dal.dao.query.LcSingleLimitTempDimRelQuery;


import java.util.List;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 单一限额模板维度关联数据库操作接口
 * 
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
public interface LcSingleLimitTempDimRelDao extends IBaseDao<LcSingleLimitTempDimRelDo> {
    /**
     * 分页查询单一限额模板维度关联
     *
     * @param lcSingleLimitTempDimRelQuery 条件
     * @return PageInfo<LcSingleLimitTempDimRelDo>
     */
    PageInfo<LcSingleLimitTempDimRelDo> selectPage(LcSingleLimitTempDimRelQuery lcSingleLimitTempDimRelQuery,PageParam pageParam);
	  /**
     * 根据key查询单一限额模板维度关联
     *
     	 	 * @param templateDimRelId
	 	 	 	      * @return
     */
	LcSingleLimitTempDimRelDo selectByKey(String templateDimRelId);
    /**
     * 根据key删除单一限额模板维度关联
     *
               * @param templateDimRelId
                      * @return
     */
    int deleteByKey(String templateDimRelId);

    /**
     * 查询单一限额模板维度关联信息
     *
     * @param lcSingleLimitTempDimRelQuery 条件
     * @return List<LcSingleLimitTempDimRelDo>
     */
    List<LcSingleLimitTempDimRelDo> selectByExample(LcSingleLimitTempDimRelQuery lcSingleLimitTempDimRelQuery);

    /**
     * 新增单一限额模板维度关联信息
     *
     * @param lcSingleLimitTempDimRel 条件
     * @return int>
     */
    int insertBySelective(LcSingleLimitTempDimRelDo lcSingleLimitTempDimRel);

    /**
     * 修改单一限额模板维度关联信息
     *
     * @param lcSingleLimitTempDimRel
     * @return
     */
    int updateBySelective(LcSingleLimitTempDimRelDo lcSingleLimitTempDimRel);
    /**
     * 修改单一限额模板维度关联信息
     *
     * @param lcSingleLimitTempDimRel
     * @param lcSingleLimitTempDimRelQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcSingleLimitTempDimRelDo lcSingleLimitTempDimRel,
    LcSingleLimitTempDimRelQuery lcSingleLimitTempDimRelQuery);
}
