package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitTypeInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitTypeInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度类型信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-02 03:03:37
 */
public interface LcCustLimitTypeInfoDao extends IBaseDao<LcCustLimitTypeInfoDo> {
    /**
     * 分页查询额度类型信息
     *
     * @param lcCustLimitTypeInfoQuery 条件
     * @return PageInfo<LcCustLimitTypeInfoDo>
     */
    PageInfo<LcCustLimitTypeInfoDo> selectPage(LcCustLimitTypeInfoQuery lcCustLimitTypeInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度类型信息
     *
     * @param custLimitTypeId
     * @return
     */
    LcCustLimitTypeInfoDo selectByKey(String custLimitTypeId);

    /**
     * 根据key删除额度类型信息
     *
     * @param custLimitTypeId
     * @return
     */
    int deleteByKey(String custLimitTypeId);

    /**
     * 查询额度类型信息信息
     *
     * @param lcCustLimitTypeInfoQuery 条件
     * @return List<LcCustLimitTypeInfoDo>
     */
    List<LcCustLimitTypeInfoDo> selectByExample(LcCustLimitTypeInfoQuery lcCustLimitTypeInfoQuery);

    /**
     * 新增额度类型信息信息
     *
     * @param lcCustLimitTypeInfo 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitTypeInfoDo lcCustLimitTypeInfo);

    /**
     * 修改额度类型信息信息
     *
     * @param lcCustLimitTypeInfo
     * @return
     */
    int updateBySelective(LcCustLimitTypeInfoDo lcCustLimitTypeInfo);

    /**
     * 修改额度类型信息信息
     *
     * @param lcCustLimitTypeInfo
     * @param lcCustLimitTypeInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitTypeInfoDo lcCustLimitTypeInfo,
        LcCustLimitTypeInfoQuery lcCustLimitTypeInfoQuery);

    /**
     * 删除额度类型信息信息
     *
     * @param lcCustLimitTypeInfoQuery 条件
     * @return List<LcCustLimitTypeInfoDo>
     */
    int deleteByExample(LcCustLimitTypeInfoQuery lcCustLimitTypeInfoQuery);
}
