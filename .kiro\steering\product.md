# Product Overview

## hsjry-limit-batch

A Java-based batch processing system for limit management operations. The system handles file synchronization, data processing, and batch operations for financial limit calculations and management.

## Key Features

- **File Processing**: CSV file parsing and synchronization with custom delimiters (`|+|`)
- **Batch Operations**: Sharded batch processing for large datasets
- **Data Synchronization**: File-to-database synchronization with transaction support
- **Limit Management**: Core business logic for financial limit calculations

## Business Context

The system processes financial data files, particularly focusing on product information (CorePprod) and limit calculations. It follows a sharded processing approach to handle large volumes of data efficiently while maintaining data consistency through transactional operations.