/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.hsjry.core.limit.center.dal.dao.query.InboundSerialBatchQuery;
import com.hsjry.core.limit.center.dal.dao.model.LcInboundSerialDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/4/12 14:46
 */
public interface InboundSerialBatchMapper extends CommonMapper<LcInboundSerialDo> {
    /**
     * 获取 当前组的 数据量
     *
     * @param query
     * @return
     */
    Integer selectCountByCurrentGroup(@Param("query") InboundSerialBatchQuery query);

    /**
     * 获取 第一个 对象，limit m，1
     *
     * @param query 查询条件
     * @return
     */
    LcInboundSerialDo selectFirstOne(@Param("query") InboundSerialBatchQuery query);

    /**
     * 查询列表
     *
     * @param query
     * @return
     */
    List<LcInboundSerialDo> selectShardList(@Param("query") InboundSerialBatchQuery query);

}
