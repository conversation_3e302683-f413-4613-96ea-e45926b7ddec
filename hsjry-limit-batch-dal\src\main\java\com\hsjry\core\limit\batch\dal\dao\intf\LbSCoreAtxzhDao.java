package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreAtxzhDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSCoreAtxzhQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 核心系统-落地表-贴现账户主文件数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
public interface LbSCoreAtxzhDao extends IBaseDao<LbSCoreAtxzhDo> {
    /**
     * 分页查询核心系统-落地表-贴现账户主文件
     *
     * @param lbSCoreAtxzhQuery 条件
     * @return PageInfo<LbSCoreAtxzhDo>
     */
    PageInfo<LbSCoreAtxzhDo> selectPage(LbSCoreAtxzhQuery lbSCoreAtxzhQuery, PageParam pageParam);

    /**
     * 根据key查询核心系统-落地表-贴现账户主文件
     *
     * @param faredm
     * @param txnjjh
     * @return
     */
    LbSCoreAtxzhDo selectByKey(String faredm, String txnjjh);

    /**
     * 根据key删除核心系统-落地表-贴现账户主文件
     *
     * @param faredm
     * @param txnjjh
     * @return
     */
    int deleteByKey(String faredm, String txnjjh);

    /**
     * 查询核心系统-落地表-贴现账户主文件信息
     *
     * @param lbSCoreAtxzhQuery 条件
     * @return List<LbSCoreAtxzhDo>
     */
    List<LbSCoreAtxzhDo> selectByExample(LbSCoreAtxzhQuery lbSCoreAtxzhQuery);

    /**
     * 新增核心系统-落地表-贴现账户主文件信息
     *
     * @param lbSCoreAtxzh 条件
     * @return int>
     */
    int insertBySelective(LbSCoreAtxzhDo lbSCoreAtxzh);

    /**
     * 修改核心系统-落地表-贴现账户主文件信息
     *
     * @param lbSCoreAtxzh
     * @return
     */
    int updateBySelective(LbSCoreAtxzhDo lbSCoreAtxzh);

    /**
     * 修改核心系统-落地表-贴现账户主文件信息
     *
     * @param lbSCoreAtxzh
     * @param lbSCoreAtxzhQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSCoreAtxzhDo lbSCoreAtxzh, LbSCoreAtxzhQuery lbSCoreAtxzhQuery);
    // 在 LbSCoreAtxzhDao 接口中添加以下方法：

    /**
     * 批量插入核心系统贴现账户主文件-落地信息
     *
     * @param lbSCoreAtxzhList 批量数据
     * @return int
     */
    int insertList(List<LbSCoreAtxzhDo> lbSCoreAtxzhList);

    /**
     * 清空核心系统贴现账户主文件-落地所有数据
     *
     * @return int
     */
    int deleteAll();
}
