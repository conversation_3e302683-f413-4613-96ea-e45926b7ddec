package com.hsjry.core.limit.center.core;

import java.util.List;

import com.hsjry.core.limit.center.core.bo.CustLimitAmtInfoBo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitAmtInfoQuery;

public interface ICustLimitAmtInfoCore {
    /**
     * 查询额度实例金额信息信息
     *
     * @param query 条件
     * @return List<CustLimitAmtInfoBo>
     */
    List<CustLimitAmtInfoBo> enqrByExample(LcCustLimitAmtInfoQuery query);
}
