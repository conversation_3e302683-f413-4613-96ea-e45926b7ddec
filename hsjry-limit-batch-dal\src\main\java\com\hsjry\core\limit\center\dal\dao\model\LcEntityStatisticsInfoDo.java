package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 实体统计信息Do
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
@Table(name = "lc_entity_statistics_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcEntityStatisticsInfoDo extends LcEntityStatisticsInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1602582710713516045L;
    /** 余额 */
    @Column(name = "amount")
    private java.math.BigDecimal amount;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 币种 */
    @Column(name = "currency")
    private String currency;
    /** 最后刷新日期 */
    @Column(name = "flush_db_date")
    private java.util.Date flushDbDate;
    /** 统计金额类型;EnumEntityStatisticsAmountType: 001-总金额、002-低风险金额 */
    @Column(name = "statistics_amount_type")
    private String statisticsAmountType;
    /** 统计描述 */
    @Column(name = "statistics_desc")
    private String statisticsDesc;
    /** 统计名称 */
    @Column(name = "statistics_name")
    private String statisticsName;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 待发余额 */
    @Column(name = "wait_amount")
    private java.math.BigDecimal waitAmount;
}
