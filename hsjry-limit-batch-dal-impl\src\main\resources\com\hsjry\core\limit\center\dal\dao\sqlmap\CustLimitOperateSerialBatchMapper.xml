<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.center.dal.dao.mapper.CustLimitOperateSerialBatchMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.center.dal.dao.model.LcCustLimitOperateSerialDo">
        <result property="operateAmountId" column="operate_amount_id" jdbcType="VARCHAR"/> <!-- 操作金额编号 -->
        <result property="operatePath" column="operate_path" jdbcType="VARCHAR"/> <!-- 操作路径 -->
        <result property="contractRecalFlag" column="contract_recal_flag" jdbcType="CHAR"/> <!-- 合同额度汇率重算标记 -->
        <result property="exchangeRateVersion" column="exchange_rate_version" jdbcType="INTEGER"/> <!-- 汇率版本 -->
        <result property="entityId" column="entity_id" jdbcType="VARCHAR"/> <!-- 实体编号 -->
        <result property="remark" column="remark" jdbcType="VARCHAR"/> <!-- 备注 -->
        <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/> <!-- 失败原因 -->
        <result property="operateDirection" column="operate_direction" jdbcType="VARCHAR"/> <!-- 操作方向 -->
        <result property="custLimitId" column="cust_limit_id" jdbcType="VARCHAR"/> <!-- 额度编号 -->
        <result property="lastInboundSerialNo" column="last_inbound_serial_no" jdbcType="VARCHAR"/> <!-- 前置业务关联流水 -->
        <result property="relationId" column="relation_id" jdbcType="VARCHAR"/> <!-- 关联编号 -->
        <result property="operateLowRiskCurrency" column="operate_low_risk_currency" jdbcType="CHAR"/> <!-- 操作低风险币种 -->
        <result property="operateLowRiskAmtId" column="operate_low_risk_amt_id" jdbcType="VARCHAR"/> <!-- 操作低风险金额编号 -->
        <result property="operateLowRiskAmount" column="operate_low_risk_amount" jdbcType="DECIMAL"/> <!-- 操作低风险金额 -->
        <result property="operateAmountCurrency" column="operate_amount_currency" jdbcType="CHAR"/> <!-- 操作金额币种 -->
        <result property="globalSerialNo" column="global_serial_no" jdbcType="VARCHAR"/> <!-- 全局流水号 -->
        <result property="operateAmount" column="operate_amount" jdbcType="DECIMAL"/> <!-- 操作金额 -->
        <result property="operateType" column="operate_type"
                jdbcType="CHAR"/> <!-- 操作类型;EnumCustLimitOperatorType(001-发放额度,002-预占额度,003-预占取消,004-实占额度,005-实占取消,006-调整额度,007-恢复额度,008-恢复 -->
        <result property="ownOrganId" column="own_organ_id" jdbcType="VARCHAR"/> <!-- 所属组织编号 -->
        <result property="operatorId" column="operator_id" jdbcType="VARCHAR"/> <!-- 操作人编号 -->
        <result property="status" column="status" jdbcType="CHAR"/> <!-- 状态 -->
        <result property="closSerialNo" column="clos_serial_no" jdbcType="VARCHAR"/> <!-- 操作流水编号 -->
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/> <!-- 更新时间 -->
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/> <!-- 创建时间 -->
        <result property="tenantId" column="tenant_id" jdbcType="CHAR"/> <!-- 租户号 -->
        <result property="inboundSerialDatetime" column="inbound_serial_datetime" jdbcType="TIMESTAMP"/> <!-- 前置业务时间 -->
        <result property="inboundSerialNo" column="inbound_serial_no" jdbcType="VARCHAR"/> <!-- 前置业务流水 -->
        <result property="bizDatetime" column="biz_datetime" jdbcType="TIMESTAMP"/> <!-- 业务时间 -->
        <result property="channelNo" column="channel_no" jdbcType="CHAR"/> <!-- 渠道号 -->
        <result property="serialNo" column="serial_no" jdbcType="VARCHAR"/> <!-- 业务流水号 -->
    </resultMap>
    <sql id="Base_Column_List">
        operate_amount_id
        , operate_path
                , contract_recal_flag
                , exchange_rate_version
                , entity_id
                , remark
                , fail_reason
                , operate_direction
                , cust_limit_id
                , last_inbound_serial_no
                , relation_id
                , operate_low_risk_currency
                , operate_low_risk_amt_id
                , operate_low_risk_amount
                , operate_amount_currency
                , global_serial_no
                , operate_amount
                , operate_type
                , own_organ_id
                , operator_id
                , status
                , clos_serial_no
                , update_time
                , create_time
                , tenant_id
                , inbound_serial_datetime
                , inbound_serial_no
                , biz_datetime
                , channel_no
                , serial_no
    </sql>
    <select id="selectCountByCurrentGroup" resultType="java.lang.Integer">
        SELECT count(*)
        FROM lc_cust_limit_operate_serial WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectFirstOne"
            parameterType="com.hsjry.core.limit.center.dal.dao.query.CustLimitOperateSerialQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_cust_limit_operate_serial
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectShardList"
            parameterType="com.hsjry.core.limit.center.dal.dao.query.CustLimitOperateSerialQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_cust_limit_operate_serial
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>

    <sql id="fixQuerySql">
        <if test="query.exchangeRateVersion !=null and query.exchangeRateVersion!=''">
            AND exchange_rate_version <![CDATA[ < ]]> #{query.exchangeRateVersion}
        </if>
        <if test="query.status !=null and query.status!=''">
            AND status = #{query.status}
        </if>
        <if test="query.closSerialNo != null and query.closSerialNo!=''">
            AND clos_serial_no > #{query.closSerialNo}
        </if>
        <if test="query.contractRecalFlag !=null and query.contractRecalFlag!=''">
            AND contract_recal_flag = #{query.contractRecalFlag}
        </if>
        <if test="query.operateType !=null and query.operateType!=''">
            AND operate_type = #{query.operateType}
        </if>
        ORDER BY clos_serial_no
    </sql>
    <select id="selectFirstOne4Batch"
            parameterType="com.hsjry.core.limit.center.dal.dao.query.CustLimitOperateSerialBatchQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_cust_limit_operate_serial
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectShardList4Batch"
            parameterType="com.hsjry.core.limit.center.dal.dao.query.CustLimitOperateSerialBatchQuery"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lc_cust_limit_operate_serial
        WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>
    <select id="selectCountByCurrentGroup4Batch" resultType="java.lang.Integer">
        SELECT count(*)
        FROM lc_cust_limit_operate_serial WHERE TENANT_ID = #{query.tenantId,jdbcType=VARCHAR}
        <include refid="fixQuerySql"/>
    </select>


</mapper>