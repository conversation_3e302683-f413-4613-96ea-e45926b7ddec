package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度分片流水主键
 *
 * <AUTHOR>
 * @date 2023-03-13 12:30:49
 */
@Table(name = "lc_slice_batch_serial")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcSliceBatchSerialKeyDo implements Serializable {

    private static final long serialVersionUID = 1635257101087604736L;
        /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
        /** 批次总流水 */
    @Id
    @Column(name = "batch_serial_no")
    private String batchSerialNo;
        /** 批次号 */
    @Id
    @Column(name = "batch_num")
    private Integer batchNum;
    }