package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcEntityHistoryInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcEntityHistoryInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 实体历史信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-04-17 11:39:12
 */
public interface LcEntityHistoryInfoDao extends IBaseDao<LcEntityHistoryInfoDo> {
    /**
     * 分页查询实体历史信息
     *
     * @param lcEntityHistoryInfoQuery 条件
     * @return PageInfo<LcEntityHistoryInfoDo>
     */
    PageInfo<LcEntityHistoryInfoDo> selectPage(LcEntityHistoryInfoQuery lcEntityHistoryInfoQuery, PageParam pageParam);

    /**
     * 根据key查询实体历史信息
     *
     * @param entityId
     * @return
     */
    LcEntityHistoryInfoDo selectByKey(String entityId);

    /**
     * 根据key删除实体历史信息
     *
     * @param entityId
     * @return
     */
    int deleteByKey(String entityId);

    /**
     * 查询实体历史信息信息
     *
     * @param lcEntityHistoryInfoQuery 条件
     * @return List<LcEntityHistoryInfoDo>
     */
    List<LcEntityHistoryInfoDo> selectByExample(LcEntityHistoryInfoQuery lcEntityHistoryInfoQuery);

    /**
     * 新增实体历史信息信息
     *
     * @param lcEntityHistoryInfo 条件
     * @return int>
     */
    int insertBySelective(LcEntityHistoryInfoDo lcEntityHistoryInfo);

    /**
     * 修改实体历史信息信息
     *
     * @param lcEntityHistoryInfo
     * @return
     */
    int updateBySelective(LcEntityHistoryInfoDo lcEntityHistoryInfo);

    /**
     * 修改实体历史信息信息
     *
     * @param lcEntityHistoryInfo
     * @param lcEntityHistoryInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcEntityHistoryInfoDo lcEntityHistoryInfo,
        LcEntityHistoryInfoQuery lcEntityHistoryInfoQuery);
}
