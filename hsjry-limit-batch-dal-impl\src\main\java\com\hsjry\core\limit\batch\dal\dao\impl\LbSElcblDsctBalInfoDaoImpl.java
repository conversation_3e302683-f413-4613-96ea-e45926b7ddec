package com.hsjry.core.limit.batch.dal.dao.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbSElcblDsctBalInfoDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbSElcblDsctBalInfoMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblDsctBalInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblDsctBalInfoExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblDsctBalInfoKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSElcblDsctBalInfoQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-落地表-贴现余额信息数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Repository
public class LbSElcblDsctBalInfoDaoImpl extends AbstractBaseDaoImpl<LbSElcblDsctBalInfoDo, LbSElcblDsctBalInfoMapper>
    implements LbSElcblDsctBalInfoDao {
    /**
     * 分页查询
     *
     * @param lbSElcblDsctBalInfo 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbSElcblDsctBalInfoDo> selectPage(LbSElcblDsctBalInfoQuery lbSElcblDsctBalInfo,
        PageParam pageParam) {
        LbSElcblDsctBalInfoExample example = buildExample(lbSElcblDsctBalInfo);
        return PageHelper.<LbSElcblDsctBalInfoDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询电票系统-落地表-贴现余额信息
     *
     * @param dicCno
     * @param billNo
     * @param userId
     * @return
     */
    @Override
    public LbSElcblDsctBalInfoDo selectByKey(String dicCno, String billNo, String userId) {
        LbSElcblDsctBalInfoKeyDo lbSElcblDsctBalInfoKeyDo = new LbSElcblDsctBalInfoKeyDo();
        lbSElcblDsctBalInfoKeyDo.setDicCno(dicCno);
        lbSElcblDsctBalInfoKeyDo.setBillNo(billNo);
        lbSElcblDsctBalInfoKeyDo.setUserId(userId);
        return getMapper().selectByPrimaryKey(lbSElcblDsctBalInfoKeyDo);
    }

    /**
     * 根据key删除电票系统-落地表-贴现余额信息
     *
     * @param dicCno
     * @param billNo
     * @param userId
     * @return
     */
    @Override
    public int deleteByKey(String dicCno, String billNo, String userId) {
        LbSElcblDsctBalInfoKeyDo lbSElcblDsctBalInfoKeyDo = new LbSElcblDsctBalInfoKeyDo();
        lbSElcblDsctBalInfoKeyDo.setDicCno(dicCno);
        lbSElcblDsctBalInfoKeyDo.setBillNo(billNo);
        lbSElcblDsctBalInfoKeyDo.setUserId(userId);
        return getMapper().deleteByPrimaryKey(lbSElcblDsctBalInfoKeyDo);
    }

    /**
     * 查询电票系统-落地表-贴现余额信息信息
     *
     * @param lbSElcblDsctBalInfo 条件
     * @return List<LbSElcblDsctBalInfoDo>
     */
    @Override
    public List<LbSElcblDsctBalInfoDo> selectByExample(LbSElcblDsctBalInfoQuery lbSElcblDsctBalInfo) {
        return getMapper().selectByExample(buildExample(lbSElcblDsctBalInfo));
    }

    /**
     * 新增电票系统-落地表-贴现余额信息信息
     *
     * @param lbSElcblDsctBalInfo 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbSElcblDsctBalInfoDo lbSElcblDsctBalInfo) {
        if (lbSElcblDsctBalInfo == null) {
            return -1;
        }
        // 获取日期时间对象
        LocalDateTime localDateTime = LocalDateTime.now();
        // 转换为字符串
        String dateTimeString = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        lbSElcblDsctBalInfo.setCreateTime(dateTimeString);
        lbSElcblDsctBalInfo.setUpdateTime(dateTimeString);
        return getMapper().insertSelective(lbSElcblDsctBalInfo);
    }

    /**
     * 修改电票系统-落地表-贴现余额信息信息
     *
     * @param lbSElcblDsctBalInfo
     * @return
     */
    @Override
    public int updateBySelective(LbSElcblDsctBalInfoDo lbSElcblDsctBalInfo) {
        if (lbSElcblDsctBalInfo == null) {
            return -1;
        }
        // 获取日期时间对象
        LocalDateTime localDateTime = LocalDateTime.now();
        // 转换为字符串
        String dateTimeString = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        lbSElcblDsctBalInfo.setUpdateTime(dateTimeString);
        return getMapper().updateByPrimaryKeySelective(lbSElcblDsctBalInfo);
    }

    @Override
    public int updateBySelectiveByExample(LbSElcblDsctBalInfoDo lbSElcblDsctBalInfo,
        LbSElcblDsctBalInfoQuery lbSElcblDsctBalInfoQuery) {

        // 获取日期时间对象
        LocalDateTime localDateTime = LocalDateTime.now();
        // 转换为字符串
        String dateTimeString = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        lbSElcblDsctBalInfo.setUpdateTime(dateTimeString);
        return getMapper().updateByExampleSelective(lbSElcblDsctBalInfo, buildExample(lbSElcblDsctBalInfoQuery));
    }

    /**
     * 构建电票系统-落地表-贴现余额信息Example信息
     *
     * @param lbSElcblDsctBalInfo
     * @return
     */
    public LbSElcblDsctBalInfoExample buildExample(LbSElcblDsctBalInfoQuery lbSElcblDsctBalInfo) {
        LbSElcblDsctBalInfoExample example = new LbSElcblDsctBalInfoExample();
        LbSElcblDsctBalInfoExample.Criteria criteria = example.createCriteria();
        if (lbSElcblDsctBalInfo != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbSElcblDsctBalInfo.getOrgNo())) {
                criteria.andOrgNoEqualTo(lbSElcblDsctBalInfo.getOrgNo());
            }
            if (StringUtil.isNotEmpty(lbSElcblDsctBalInfo.getDicCno())) {
                criteria.andDicCnoEqualTo(lbSElcblDsctBalInfo.getDicCno());
            }
            if (StringUtil.isNotEmpty(lbSElcblDsctBalInfo.getBillNo())) {
                criteria.andBillNoEqualTo(lbSElcblDsctBalInfo.getBillNo());
            }
            if (StringUtil.isNotEmpty(lbSElcblDsctBalInfo.getBillRangeStart())) {
                criteria.andBillRangeStartEqualTo(lbSElcblDsctBalInfo.getBillRangeStart());
            }
            if (StringUtil.isNotEmpty(lbSElcblDsctBalInfo.getBillRangeEnd())) {
                criteria.andBillRangeEndEqualTo(lbSElcblDsctBalInfo.getBillRangeEnd());
            }
            if (StringUtil.isNotEmpty(lbSElcblDsctBalInfo.getUserId())) {
                criteria.andUserIdEqualTo(lbSElcblDsctBalInfo.getUserId());
            }
            if (StringUtil.isNotEmpty(lbSElcblDsctBalInfo.getUserName())) {
                criteria.andUserNameEqualTo(lbSElcblDsctBalInfo.getUserName());
            }
            if (StringUtil.isNotEmpty(lbSElcblDsctBalInfo.getCurrency())) {
                criteria.andCurrencyEqualTo(lbSElcblDsctBalInfo.getCurrency());
            }
            if (null != lbSElcblDsctBalInfo.getDiscountAmt()) {
                criteria.andDiscountAmtEqualTo(lbSElcblDsctBalInfo.getDiscountAmt());
            }
            if (null != lbSElcblDsctBalInfo.getDiscountBal()) {
                criteria.andDiscountBalEqualTo(lbSElcblDsctBalInfo.getDiscountBal());
            }
            if (StringUtil.isNotEmpty(lbSElcblDsctBalInfo.getStartDate())) {
                criteria.andStartDateEqualTo(lbSElcblDsctBalInfo.getStartDate());
            }
            if (StringUtil.isNotEmpty(lbSElcblDsctBalInfo.getEndDate())) {
                criteria.andEndDateEqualTo(lbSElcblDsctBalInfo.getEndDate());
            }
        }
        buildExampleExt(lbSElcblDsctBalInfo, criteria);
        return example;
    }

    /**
     * 构建电票系统-落地表-贴现余额信息ExampleExt方法
     *
     * @param lbSElcblDsctBalInfo
     * @return
     */
    public void buildExampleExt(LbSElcblDsctBalInfoQuery lbSElcblDsctBalInfo,
        LbSElcblDsctBalInfoExample.Criteria criteria) {

        //自定义实现
    }

    /**
     * 批量插入电票系统-贴现余额信息-落地表
     *
     * @param lbSElcblDsctBalInfoList 批量数据
     * @return int
     */
    @Override
    public int insertList(List<LbSElcblDsctBalInfoDo> lbSElcblDsctBalInfoList) {
        if (lbSElcblDsctBalInfoList.isEmpty()) {
            return 0;
        }
        return getMapper().insertList(lbSElcblDsctBalInfoList);
    }

    /**
     * 清空电票系统-贴现余额信息-落地表所有数据
     *
     * @return int
     */
    @Override
    public int deleteAll() {
        return getMapper().deleteAll();
    }

    /**
     * 获取第一个对象，用于分片查询
     * 根据复合主键排序，获取指定偏移量的第一条记录
     *
     * @param query 查询条件
     * @return 第一条记录，如果没有则返回null
     */
    @Override
    public LbSElcblDsctBalInfoDo selectFirstOne(LbSElcblDsctBalInfoQuery query) {
        LbSElcblDsctBalInfoExample example = buildExample(query);
        example.setOrderByClause("dic_cno ASC, bill_no ASC, user_id ASC");
        List<LbSElcblDsctBalInfoDo> list = getMapper().selectByExample(example);
        return list != null && !list.isEmpty() ? list.get(0) : null;
    }

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据复合主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含主键范围
     * @return 当前分片的数据量
     */
    @Override
    public Integer selectCountByCurrentGroup(LbSElcblDsctBalInfoQuery query) {
        LbSElcblDsctBalInfoExample example = buildExample(query);
        return Math.toIntExact(getMapper().countByExample(example));
    }

}
