package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAtxzhDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHCoreAtxzhQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 核心系统-历史表-贴现账户主文件数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
public interface LbHCoreAtxzhDao extends IBaseDao<LbHCoreAtxzhDo> {
    /**
     * 分页查询核心系统-历史表-贴现账户主文件
     *
     * @param lbHCoreAtxzhQuery 条件
     * @return PageInfo<LbHCoreAtxzhDo>
     */
    PageInfo<LbHCoreAtxzhDo> selectPage(LbHCoreAtxzhQuery lbHCoreAtxzhQuery, PageParam pageParam);

    /**
     * 根据key查询核心系统-历史表-贴现账户主文件
     *
     * @param faredm
     * @param txnjjh
     * @param dataDate
     * @return
     */
    LbHCoreAtxzhDo selectByKey(String faredm, String txnjjh, String dataDate);

    /**
     * 根据key删除核心系统-历史表-贴现账户主文件
     *
     * @param faredm
     * @param txnjjh
     * @param dataDate
     * @return
     */
    int deleteByKey(String faredm, String txnjjh, String dataDate);

    /**
     * 查询核心系统-历史表-贴现账户主文件信息
     *
     * @param lbHCoreAtxzhQuery 条件
     * @return List<LbHCoreAtxzhDo>
     */
    List<LbHCoreAtxzhDo> selectByExample(LbHCoreAtxzhQuery lbHCoreAtxzhQuery);

    /**
     * 新增核心系统-历史表-贴现账户主文件信息
     *
     * @param lbHCoreAtxzh 条件
     * @return int>
     */
    int insertBySelective(LbHCoreAtxzhDo lbHCoreAtxzh);

    /**
     * 修改核心系统-历史表-贴现账户主文件信息
     *
     * @param lbHCoreAtxzh
     * @return
     */
    int updateBySelective(LbHCoreAtxzhDo lbHCoreAtxzh);

    /**
     * 修改核心系统-历史表-贴现账户主文件信息
     *
     * @param lbHCoreAtxzh
     * @param lbHCoreAtxzhQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHCoreAtxzhDo lbHCoreAtxzh, LbHCoreAtxzhQuery lbHCoreAtxzhQuery);
    // 请在现有的LbHCoreAtxzhDao接口中添加以下方法：

    /**
     * 批量插入核心系统贴现账户主文件-历史表信息
     *
     * @param lbHCoreAtxzhList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbHCoreAtxzhDo> lbHCoreAtxzhList);

    /**
     * 清空核心系统贴现账户主文件-历史表所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 根据数据日期删除核心系统贴现账户主文件-历史表数据
     *
     * @param dataDate 数据日期
     * @return int
     */
    int deleteByDataDate(String dataDate);
}
