/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.Date;

import com.hsjry.core.limit.center.dal.dao.model.LcAmtRuleAdjustPlanDo;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/8 10:31
 */
public interface AmtLimitAdjustPlanBatchDao {

    /**
     * 查询需要调整的限额计划
     *
     * @param date
     * @param pageParam
     * @return
     */
    PageInfo<LcAmtRuleAdjustPlanDo> queryNeedAdjustPlan(Date date, PageParam pageParam);
}
