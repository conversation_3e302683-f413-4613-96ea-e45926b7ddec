package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 单一限额模板维度关联主键
 *
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
@Table(name = "lc_single_limit_temp_dim_rel")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcSingleLimitTempDimRelKeyDo implements Serializable {

    private static final long serialVersionUID = 1673314101088157696L;
        /** 模板关联编号 */
    @Id
    @Column(name = "template_dim_rel_id")
    private String templateDimRelId;
        /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    }