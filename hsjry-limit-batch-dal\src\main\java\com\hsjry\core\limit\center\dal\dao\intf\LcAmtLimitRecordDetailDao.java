package com.hsjry.core.limit.center.dal.dao.intf;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordDetailDo;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitRecordDetailQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 限额记录明细数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcAmtLimitRecordDetailDao extends IBaseDao<LcAmtLimitRecordDetailDo> {
    /**
     * 分页查询限额记录明细
     *
     * @param lcAmtLimitRecordDetailQuery 条件
     * @return PageInfo<LcAmtLimitRecordDetailDo>
     */
    PageInfo<LcAmtLimitRecordDetailDo> selectPage(LcAmtLimitRecordDetailQuery lcAmtLimitRecordDetailQuery,
        PageParam pageParam);

    /**
     * 根据key查询限额记录明细
     *
     * @param recordId
     * @param shardSeq
     * @return
     */
    LcAmtLimitRecordDetailDo selectByKey(String recordId, Integer shardSeq);

    /**
     * 根据key删除限额记录明细
     *
     * @param recordId
     * @param shardSeq
     * @return
     */
    int deleteByKey(String recordId, Integer shardSeq);

    /**
     * 查询限额记录明细信息
     *
     * @param lcAmtLimitRecordDetailQuery 条件
     * @return List<LcAmtLimitRecordDetailDo>
     */
    List<LcAmtLimitRecordDetailDo> selectByExample(LcAmtLimitRecordDetailQuery lcAmtLimitRecordDetailQuery);

    /**
     * 新增限额记录明细信息
     *
     * @param lcAmtLimitRecordDetail 条件
     * @return int>
     */
    int insertBySelective(LcAmtLimitRecordDetailDo lcAmtLimitRecordDetail);

    /**
     * 修改限额记录明细信息
     *
     * @param lcAmtLimitRecordDetail
     * @return
     */
    int updateBySelective(LcAmtLimitRecordDetailDo lcAmtLimitRecordDetail);

    /**
     * 修改限额记录明细信息
     *
     * @param lcAmtLimitRecordDetail
     * @param lcAmtLimitRecordDetailQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcAmtLimitRecordDetailDo lcAmtLimitRecordDetail,
        LcAmtLimitRecordDetailQuery lcAmtLimitRecordDetailQuery);
}
