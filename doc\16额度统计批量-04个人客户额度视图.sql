--1.查询[额度中心-中间表-个人客户信息]
select ltici.cust_no, ltici.*
from lb_t_indv_cust_info ltici
where 1 = 1;
select *
from lb_t_indv_cust_info;
select *
from lc_indv_lmt_view;
truncate table lc_indv_lmt_view;

--1.处理客户信息,使用MERGE语句实现存在则更新,不存在则插入
merge into lc_indv_lmt_view tgt
using (select ltici.tenant_id,
              ltici.cust_no,
              ltici.cust_nm,
              ltici.cert_typ,
              ltici.cert_no
       from lb_t_indv_cust_info ltici
       where 1 = 1) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.cust_nm = src.cust_nm
    when not matched then
insert (indv_lmt_view_id, cust_nm, cust_no, cert_typ, cert_no,
    tenant_id)
    values
(generate_primary_key('LILV'),
    src.cust_nm,
    src.cust_no,
    src.cert_typ,
    src.cert_no,
    src.tenant_id);

--2.更新经营贷额度/经营贷可用额度
select lcli.tenant_id           as tenant_id,
       lcli.limit_object_id     as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.total_amount       as op_loan_lmt,
       lclai.total_amount - lclai.pre_occupy_amount -
       lclai.real_occupy_amount as op_loan_avl_lmt,
       lcli.operator_id         as operator_id,
       lcli.own_organ_id        as own_organ_id,
       lcli.create_time         as create_time
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai
                    on lcli.tenant_id = lclai.tenant_id
                        and lcli.cust_limit_id = lclai.cust_limit_id
                        and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSDGKHEDTX')
  and lcli.template_node_id in ('GRJYDED');

merge into lc_indv_lmt_view tgt
using (select lcli.tenant_id           as tenant_id,
              lcli.limit_object_id     as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.total_amount       as op_loan_lmt,
              lclai.total_amount - lclai.pre_occupy_amount -
              lclai.real_occupy_amount as op_loan_avl_lmt,
              lcli.operator_id         as operator_id,
              lcli.own_organ_id        as own_organ_id,
              lcli.create_time         as create_time
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai
                           on lcli.tenant_id = lclai.tenant_id
                               and lcli.cust_limit_id = lclai.cust_limit_id
                               and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSDGKHEDTX')
         and lcli.template_node_id in ('GRJYDED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.op_loan_lmt = src.op_loan_lmt,
    tgt.op_loan_avl_lmt = src.op_loan_avl_lmt,
    tgt.operator_id = src.operator_id,
    tgt.own_organ_id = src.own_organ_id,
    tgt.create_time = src.create_time,
    tgt.update_time = sysdate;

--3.更新经营贷产品总额度/经营贷产品可用额度
select lcli.tenant_id                as tenant_id,
       lcli.limit_object_id          as cust_no,
       lcli.template_node_id,
       sum(lclai.total_amount)       as op_loan_prod_tot_lmt,
       sum(lclai.total_amount) - sum(lclai.pre_occupy_amount) -
       sum(lclai.real_occupy_amount) as op_loan_prod_avl_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai
                    on lcli.tenant_id = lclai.tenant_id
                        and lcli.cust_limit_id = lclai.cust_limit_id
                        and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSDGKHEDTX')
  and lcli.template_node_id in ('GRJYDCPED')
group by lcli.LIMIT_OBJECT_ID, lcli.TEMPLATE_NODE_ID, lcli.tenant_id;

merge into lc_indv_lmt_view tgt
using (select lcli.tenant_id                as tenant_id,
              lcli.limit_object_id          as cust_no,
              lcli.template_node_id,
              sum(lclai.total_amount)       as op_loan_prod_tot_lmt,
              sum(lclai.total_amount) - sum(lclai.pre_occupy_amount) -
              sum(lclai.real_occupy_amount) as op_loan_prod_avl_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai
                           on lcli.tenant_id = lclai.tenant_id
                               and lcli.cust_limit_id = lclai.cust_limit_id
                               and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSDGKHEDTX')
         and lcli.template_node_id in ('GRJYDCPED')
       group by lcli.LIMIT_OBJECT_ID, lcli.TEMPLATE_NODE_ID, lcli.tenant_id) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.op_loan_prod_tot_lmt = src.op_loan_prod_tot_lmt,
    tgt.op_loan_prod_avl_lmt = src.op_loan_prod_avl_lmt,
    tgt.update_time = sysdate;

--4.更新经营按揭产品总额度/经营按揭产品可用额度
select lcli.tenant_id                as tenant_id,
       lcli.limit_object_id          as cust_no,
       lcli.template_node_id,
       sum(lclai.total_amount)       as op_mrtg_prod_tot_lmt,
       sum(lclai.total_amount) - sum(lclai.pre_occupy_amount) -
       sum(lclai.real_occupy_amount) as op_mrtg_prod_tot_avl_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai
                    on lcli.tenant_id = lclai.tenant_id
                        and lcli.cust_limit_id = lclai.cust_limit_id
                        and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSDGKHEDTX')
  and lcli.template_node_id in ('GRJYAJCPED')
group by lcli.LIMIT_OBJECT_ID, lcli.TEMPLATE_NODE_ID, lcli.tenant_id;

merge into lc_indv_lmt_view tgt
using (select lcli.tenant_id                as tenant_id,
              lcli.limit_object_id          as cust_no,
              lcli.template_node_id,
              sum(lclai.total_amount)       as op_mrtg_prod_tot_lmt,
              sum(lclai.total_amount) - sum(lclai.pre_occupy_amount) -
              sum(lclai.real_occupy_amount) as op_mrtg_prod_tot_avl_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai
                           on lcli.tenant_id = lclai.tenant_id
                               and lcli.cust_limit_id = lclai.cust_limit_id
                               and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSDGKHEDTX')
         and lcli.template_node_id in ('GRJYAJCPED')
       group by lcli.LIMIT_OBJECT_ID, lcli.TEMPLATE_NODE_ID, lcli.tenant_id) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.op_mrtg_prod_tot_lmt = src.op_mrtg_prod_tot_lmt,
    tgt.op_mrtg_prod_tot_avl_lmt = src.op_mrtg_prod_tot_avl_lmt,
    tgt.update_time = sysdate;

--5.更新消费贷额度/消费贷可用额度
select lcli.tenant_id           as tenant_id,
       lcli.limit_object_id     as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.total_amount       as consm_loan_lmt,
       lclai.total_amount - lclai.pre_occupy_amount -
       lclai.real_occupy_amount as consm_loan_avl_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai
                    on lcli.tenant_id = lclai.tenant_id
                        and lcli.cust_limit_id = lclai.cust_limit_id
                        and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSDGKHEDTX')
  and lcli.template_node_id in ('GRXFDED');

merge into lc_indv_lmt_view tgt
using (select lcli.tenant_id           as tenant_id,
              lcli.limit_object_id     as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.total_amount       as consm_loan_lmt,
              lclai.total_amount - lclai.pre_occupy_amount -
              lclai.real_occupy_amount as consm_loan_avl_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai
                           on lcli.tenant_id = lclai.tenant_id
                               and lcli.cust_limit_id = lclai.cust_limit_id
                               and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSDGKHEDTX')
         and lcli.template_node_id in ('GRXFDED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.consm_loan_lmt = src.consm_loan_lmt,
    tgt.consm_loan_avl_lmt = src.consm_loan_avl_lmt,
    tgt.update_time = sysdate;

--6.更新消费贷产品总额度/消费贷产品可用额度
select lcli.tenant_id                as tenant_id,
       lcli.limit_object_id          as cust_no,
       lcli.template_node_id,
       sum(lclai.total_amount)       as consm_loan_prod_tot_lmt,
       sum(lclai.total_amount) - sum(lclai.pre_occupy_amount) -
       sum(lclai.real_occupy_amount) as consm_loan_prod_avl_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai
                    on lcli.tenant_id = lclai.tenant_id
                        and lcli.cust_limit_id = lclai.cust_limit_id
                        and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSDGKHEDTX')
  and lcli.template_node_id in ('GRXFDCPED')
group by lcli.LIMIT_OBJECT_ID, lcli.TEMPLATE_NODE_ID, lcli.tenant_id;

merge into lc_indv_lmt_view tgt
using (select lcli.tenant_id                as tenant_id,
              lcli.limit_object_id          as cust_no,
              lcli.template_node_id,
              sum(lclai.total_amount)       as consm_loan_prod_tot_lmt,
              sum(lclai.total_amount) - sum(lclai.pre_occupy_amount) -
              sum(lclai.real_occupy_amount) as consm_loan_prod_avl_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai
                           on lcli.tenant_id = lclai.tenant_id
                               and lcli.cust_limit_id = lclai.cust_limit_id
                               and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSDGKHEDTX')
         and lcli.template_node_id in ('GRXFDCPED')
       group by lcli.LIMIT_OBJECT_ID, lcli.TEMPLATE_NODE_ID, lcli.tenant_id) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.consm_loan_prod_tot_lmt = src.consm_loan_prod_tot_lmt,
    tgt.consm_loan_prod_avl_lmt = src.consm_loan_prod_avl_lmt,
    tgt.update_time = sysdate;

--7.更新消费按揭产品总额度/消费按揭产品可用额度
select lcli.tenant_id                as tenant_id,
       lcli.limit_object_id          as cust_no,
       lcli.template_node_id,
       sum(lclai.total_amount)       as consm_mrtg_prod_tot_avl_lmt,
       sum(lclai.total_amount) - sum(lclai.pre_occupy_amount) -
       sum(lclai.real_occupy_amount) as consm_mrtg_prod_avl_avl_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai
                    on lcli.tenant_id = lclai.tenant_id
                        and lcli.cust_limit_id = lclai.cust_limit_id
                        and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSDGKHEDTX')
  and lcli.template_node_id in ('GRXFAJCPED')
group by lcli.LIMIT_OBJECT_ID, lcli.TEMPLATE_NODE_ID, lcli.tenant_id;

merge into lc_indv_lmt_view tgt
using (select lcli.tenant_id                as tenant_id,
              lcli.limit_object_id          as cust_no,
              lcli.template_node_id,
              sum(lclai.total_amount)       as consm_mrtg_prod_tot_avl_lmt,
              sum(lclai.total_amount) - sum(lclai.pre_occupy_amount) -
              sum(lclai.real_occupy_amount) as consm_mrtg_prod_avl_avl_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai
                           on lcli.tenant_id = lclai.tenant_id
                               and lcli.cust_limit_id = lclai.cust_limit_id
                               and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSDGKHEDTX')
         and lcli.template_node_id in ('GRXFAJCPED')
       group by lcli.LIMIT_OBJECT_ID, lcli.TEMPLATE_NODE_ID, lcli.tenant_id) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.consm_mrtg_prod_tot_avl_lmt = src.consm_mrtg_prod_tot_avl_lmt,
    tgt.consm_mrtg_prod_avl_avl_lmt = src.consm_mrtg_prod_avl_avl_lmt,
    tgt.update_time = sysdate;

--8.更新信用卡额度/信用卡可用额度
select lcli.tenant_id           as tenant_id,
       lcli.limit_object_id     as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.total_amount       as crd_cd_lmt,
       lclai.total_amount - lclai.pre_occupy_amount -
       lclai.real_occupy_amount as crd_cd_avl_lmt
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai
                    on lcli.tenant_id = lclai.tenant_id
                        and lcli.cust_limit_id = lclai.cust_limit_id
                        and lcli.limit_object_id = lclai.cust_no
where lcli.limit_template_id in ('HNNSDGKHEDTX')
  and lcli.template_node_id in ('GRXYKED');

merge into lc_indv_lmt_view tgt
using (select lcli.tenant_id           as tenant_id,
              lcli.limit_object_id     as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.total_amount       as crd_cd_lmt,
              lclai.total_amount - lclai.pre_occupy_amount -
              lclai.real_occupy_amount as crd_cd_avl_lmt
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai
                           on lcli.tenant_id = lclai.tenant_id
                               and lcli.cust_limit_id = lclai.cust_limit_id
                               and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSDGKHEDTX')
         and lcli.template_node_id in ('GRXYKED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.crd_cd_lmt = src.crd_cd_lmt,
    tgt.crd_cd_avl_lmt = src.crd_cd_avl_lmt,
    tgt.update_time = sysdate;
