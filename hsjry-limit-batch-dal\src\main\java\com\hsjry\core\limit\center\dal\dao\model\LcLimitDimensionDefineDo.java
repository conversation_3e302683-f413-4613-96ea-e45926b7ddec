package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 限额维度定义Do
 *
 * <AUTHOR>
 * @date 2023-06-26 12:55:45
 */
@Table(name = "lc_limit_dimension_define")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LcLimitDimensionDefineDo extends LcLimitDimensionDefineKeyDo implements Serializable{
private static final long serialVersionUID=1673314101088157701L;
        /** 创建时间 */
@Column(name = "create_time")
     private java.util.Date createTime;
                        /** 维度比对类型;EnumDimensionCompareType: 001-全匹配，002-范围【前后都包含】 */
@Column(name = "dimension_compare_type")
     private String dimensionCompareType;
                /** 维度名称 */
@Column(name = "dimension_name")
     private String dimensionName;
                /** 模块内部维度标记;内部维度不需要在模板中配置，通过规则自动初始化，Enumbool(Y-是、N-否)默认N */
@Column(name = "inter_dimension_flag")
     private String interDimensionFlag;
                /** 维度序号;不同维度序号不一致，用于排序 */
@Column(name = "seq_no")
     private Integer seqNo;
                        /** 更新时间 */
@Column(name = "update_time")
     private java.util.Date updateTime;
                }
