package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRuleDo;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitRuleQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 限额规则数据库操作接口
 *
 * <AUTHOR>
 * @date 2023-01-10 09:40:56
 */
public interface LcAmtLimitRuleDao extends IBaseDao<LcAmtLimitRuleDo> {
    /**
     * 分页查询限额规则
     *
     * @param lcAmtLimitRuleQuery 条件
     * @return PageInfo<LcAmtLimitRuleDo>
     */
    PageInfo<LcAmtLimitRuleDo> selectPage(LcAmtLimitRuleQuery lcAmtLimitRuleQuery, PageParam pageParam);

    /**
     * 根据key查询限额规则
     *
     * @param ruleId
     * @return
     */
    LcAmtLimitRuleDo selectByKey(String ruleId);

    /**
     * 根据key删除限额规则
     *
     * @param ruleId
     * @return
     */
    int deleteByKey(String ruleId);

    /**
     * 查询限额规则信息
     *
     * @param lcAmtLimitRuleQuery 条件
     * @return List<LcAmtLimitRuleDo>
     */
    List<LcAmtLimitRuleDo> selectByExample(LcAmtLimitRuleQuery lcAmtLimitRuleQuery);

    /**
     * 新增限额规则信息
     *
     * @param lcAmtLimitRule 条件
     * @return int>
     */
    int insertBySelective(LcAmtLimitRuleDo lcAmtLimitRule);

    /**
     * 修改限额规则信息
     *
     * @param lcAmtLimitRule
     * @return
     */
    int updateBySelective(LcAmtLimitRuleDo lcAmtLimitRule);

    /**
     * 修改限额规则信息
     *
     * @param lcAmtLimitRule
     * @param lcAmtLimitRuleQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcAmtLimitRuleDo lcAmtLimitRule, LcAmtLimitRuleQuery lcAmtLimitRuleQuery);
}
