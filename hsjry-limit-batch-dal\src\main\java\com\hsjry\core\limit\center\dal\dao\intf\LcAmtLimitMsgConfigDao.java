package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.Date;
import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitMsgConfigDo;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitMsgConfigQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 限额消息配置数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcAmtLimitMsgConfigDao extends IBaseDao<LcAmtLimitMsgConfigDo> {
    /**
     * 分页查询限额消息配置
     *
     * @param lcAmtLimitMsgConfigQuery 条件
     * @return PageInfo<LcAmtLimitMsgConfigDo>
     */
    PageInfo<LcAmtLimitMsgConfigDo> selectPage(LcAmtLimitMsgConfigQuery lcAmtLimitMsgConfigQuery, PageParam pageParam);

    /**
     * 根据key查询限额消息配置
     *
     * @param msgConfigId
     * @return
     */
    LcAmtLimitMsgConfigDo selectByKey(String msgConfigId);

    /**
     * 根据key删除限额消息配置
     *
     * @param msgConfigId
     * @return
     */
    int deleteByKey(String msgConfigId);

    /**
     * 查询限额消息配置信息
     *
     * @param lcAmtLimitMsgConfigQuery 条件
     * @return List<LcAmtLimitMsgConfigDo>
     */
    List<LcAmtLimitMsgConfigDo> selectByExample(LcAmtLimitMsgConfigQuery lcAmtLimitMsgConfigQuery);

    /**
     * 新增限额消息配置信息
     *
     * @param lcAmtLimitMsgConfig 条件
     * @return int>
     */
    int insertBySelective(LcAmtLimitMsgConfigDo lcAmtLimitMsgConfig);

    /**
     * 修改限额消息配置信息
     *
     * @param lcAmtLimitMsgConfig
     * @return
     */
    int updateBySelective(LcAmtLimitMsgConfigDo lcAmtLimitMsgConfig);

    /**
     * 修改限额消息配置信息
     *
     * @param lcAmtLimitMsgConfig
     * @param lcAmtLimitMsgConfigQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcAmtLimitMsgConfigDo lcAmtLimitMsgConfig,
        LcAmtLimitMsgConfigQuery lcAmtLimitMsgConfigQuery);

    /**
     * 删除限额消息配置信息
     *
     * @param lcAmtLimitMsgConfigQuery 条件
     * @return List<LcAmtLimitMsgConfigDo>
     */
    int deleteByExample(LcAmtLimitMsgConfigQuery lcAmtLimitMsgConfigQuery);

}
