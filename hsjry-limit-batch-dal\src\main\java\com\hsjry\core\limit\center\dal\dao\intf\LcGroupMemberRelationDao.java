package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcGroupMemberRelationDo;
import com.hsjry.core.limit.center.dal.dao.query.LcGroupMemberRelationQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 集团成员关联关系数据库操作接口
 *
 * <AUTHOR>
 * @date 2024-05-29 02:31:51
 */
public interface LcGroupMemberRelationDao extends IBaseDao<LcGroupMemberRelationDo> {
    /**
     * 分页查询集团成员关联关系
     *
     * @param groupMemberRelationQuery 条件
     * @return PageInfo<LcGroupMemberRelationDo>
     */
    PageInfo<LcGroupMemberRelationDo> selectPage(LcGroupMemberRelationQuery groupMemberRelationQuery,
        PageParam pageParam);

    /**
     * 根据key查询集团成员关联关系
     *
     * @param relationId
     * @return
     */
    LcGroupMemberRelationDo selectByKey(String relationId);

    /**
     * 根据key删除集团成员关联关系
     *
     * @param relationId
     * @return
     */
    int deleteByKey(String relationId);

    /**
     * 查询集团成员关联关系信息
     *
     * @param groupMemberRelationQuery 条件
     * @return List<LcGroupMemberRelationDo>
     */
    List<LcGroupMemberRelationDo> selectByExample(LcGroupMemberRelationQuery groupMemberRelationQuery);

    /**
     * 新增集团成员关联关系信息
     *
     * @param groupMemberRelation 条件
     * @return int>
     */
    int insertBySelective(LcGroupMemberRelationDo groupMemberRelation);

    /**
     * 修改集团成员关联关系信息
     *
     * @param groupMemberRelation
     * @return
     */
    int updateBySelective(LcGroupMemberRelationDo groupMemberRelation);

    /**
     * 修改集团成员关联关系信息
     *
     * @param groupMemberRelation
     * @param groupMemberRelationQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcGroupMemberRelationDo groupMemberRelation,
        LcGroupMemberRelationQuery groupMemberRelationQuery);
}
