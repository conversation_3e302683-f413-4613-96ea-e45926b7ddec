package com.hsjry.core.limit.batch.biz.job.job.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 电票系统-历史表-同业客户额度同步文件的同步处理任务
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/11 11:41
 */
@Slf4j
@Service("lbHElcblIbnkLmtSynzFileSyncJob")
public class LbHElcblIbnkLmtSynzFileSyncJob extends AbstractBaseBatchJob {

    public LbHElcblIbnkLmtSynzFileSyncJob() {
        log.info("LbHElcblIbnkLmtSynzFileSyncJob Bean 正在创建...");
    }

    @Autowired
    @Qualifier("lbHElcblIbnkLmtSynzFileSyncBizImpl")
    private BaseOrdinaryBiz baseOrdinaryBiz;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return baseOrdinaryBiz;
    }

    /**
     * 设置基础业务逻辑对象
     *
     * @param baseOrdinaryBiz 基础业务逻辑对象
     */
    public void setBaseOrdinaryBiz(BaseOrdinaryBiz baseOrdinaryBiz) {
        this.baseOrdinaryBiz = baseOrdinaryBiz;
    }
}