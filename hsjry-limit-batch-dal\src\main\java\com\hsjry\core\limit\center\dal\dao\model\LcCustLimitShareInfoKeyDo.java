package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度串用信息主键
 *
 * <AUTHOR>
 * @date 2023-07-12 02:27:14
 */
@Table(name = "lc_cust_limit_share_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcCustLimitShareInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1678954139251572737L;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    /** 额度串用编号 */
    @Id
    @Column(name = "cust_limit_share_id")
    private String custLimitShareId;
}