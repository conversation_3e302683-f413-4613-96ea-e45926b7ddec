--1.查询[额度中心-中间表-同业客户信息]
select ltici.cust_no, ltici.*
from lb_t_ibnk_cust_info ltici
where 1 = 1;
select *
from lb_t_ibnk_cust_info;
select *
from lc_ibnk_lmt_view;
truncate table lc_ibnk_lmt_view;

--1.处理客户信息,使用MERGE语句实现存在则更新,不存在则插入
merge into lc_ibnk_lmt_view tgt
using (select ltici.tenant_id,
              ltici.cust_no,
              ltici.cust_nm,
              ltici.cert_typ,
              ltici.cert_no
       from lb_t_ibnk_cust_info ltici
       where 1 = 1) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.cust_nm = src.cust_nm,
    tgt.cert_typ = src.cert_typ,
    tgt.cert_no = src.cert_no
    when not matched then
insert (ibnk_lmt_view_id, cust_nm, cust_no, cert_typ, cert_no,
    tenant_id)
    values
(generate_primary_key('LILV'),
    src.cust_nm,
    src.cust_no,
    src.cert_typ,
    src.cert_no,
    src.tenant_id);
--2.更新客户总额度/可用金额/业务币种等额度信息
select lcli.tenant_id                                                          as tenant_id,
       lcli.limit_object_id                                                    as cust_no,
       lcli.cust_limit_id,
       lcli.template_node_id,
       lclai.total_amount                                                      as totl_crdt_lmt,
       lclai.total_amount - lclai.pre_occupy_amount - lclai.real_occupy_amount as avl_amt,
       lclai.currency                                                          as cur,
       lcli.limit_term                                                         as crdt_term,
       lcli.limit_term_unit                                                    as crdt_term_unit,
       lcli.effective_start_time                                               as crdt_bgn_dt,
       lcli.effective_end_time                                                 as crdt_ddt,
       lcli.limit_status                                                       as lmt_stat,
       lcli.operator_id                                                        as operator_id,
       lcli.own_organ_id                                                       as own_organ_id,
       lcli.create_time                                                        as create_time
from lc_cust_limit_info lcli
         inner join lc_cust_limit_amt_info lclai on lcli.cust_limit_id = lclai.cust_limit_id
    and lcli.limit_object_id = lclai.cust_no
where lcli.LIMIT_TEMPLATE_ID in ('HNNSTYKHEDTX')
  and lcli.TEMPLATE_NODE_ID in ('TYZED');

merge into lc_ibnk_lmt_view tgt
using (select lcli.tenant_id                                                          as tenant_id,
              lcli.limit_object_id                                                    as cust_no,
              lcli.cust_limit_id,
              lcli.template_node_id,
              lclai.total_amount                                                      as totl_crdt_lmt,
              lclai.total_amount - lclai.pre_occupy_amount - lclai.real_occupy_amount as avl_amt,
              lclai.currency                                                          as cur,
              lcli.limit_term                                                         as crdt_term,
              lcli.limit_term_unit                                                    as crdt_term_unit,
              lcli.effective_start_time                                               as crdt_bgn_dt,
              lcli.effective_end_time                                                 as crdt_ddt,
              lcli.limit_status                                                       as lmt_stat,
              lcli.operator_id                                                        as operator_id,
              lcli.own_organ_id                                                       as own_organ_id,
              lcli.create_time                                                        as create_time
       from lc_cust_limit_info lcli
                inner join lc_cust_limit_amt_info lclai
                           on lcli.tenant_id = lclai.tenant_id
                               and lcli.cust_limit_id = lclai.cust_limit_id
                               and lcli.limit_object_id = lclai.cust_no
       where lcli.limit_template_id in ('HNNSTYKHEDTX')
         and lcli.template_node_id in ('TYZED')) src
on (
    tgt.tenant_id = src.tenant_id
        and tgt.cust_no = src.cust_no
    )
when matched then
update
    set tgt.totl_crdt_lmt = src.totl_crdt_lmt,
    tgt.avl_amt = src.avl_amt,
    tgt.cur = src.cur,
    tgt.crdt_term = src.crdt_term,
    tgt.crdt_term_unit = src.crdt_term_unit,
    tgt.crdt_bgn_dt = src.crdt_bgn_dt,
    tgt.crdt_ddt = src.crdt_ddt,
    tgt.lmt_stat = src.lmt_stat,
    tgt.operator_id = src.operator_id,
    tgt.own_organ_id = src.own_organ_id,
    tgt.create_time = src.create_time,
    tgt.update_time = sysdate;

