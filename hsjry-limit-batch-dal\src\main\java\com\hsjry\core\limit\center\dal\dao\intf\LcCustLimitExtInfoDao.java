package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitExtInfoDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitExtInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度拓展信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcCustLimitExtInfoDao extends IBaseDao<LcCustLimitExtInfoDo> {
    /**
     * 分页查询额度拓展信息
     *
     * @param lcCustLimitExtInfoQuery 条件
     * @return PageInfo<LcCustLimitExtInfoDo>
     */
    PageInfo<LcCustLimitExtInfoDo> selectPage(LcCustLimitExtInfoQuery lcCustLimitExtInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度拓展信息
     *
     * @param custLimitExtId
     * @return
     */
    LcCustLimitExtInfoDo selectByKey(String custLimitExtId);

    /**
     * 根据key删除额度拓展信息
     *
     * @param custLimitExtId
     * @return
     */
    int deleteByKey(String custLimitExtId);

    /**
     * 查询额度拓展信息信息
     *
     * @param lcCustLimitExtInfoQuery 条件
     * @return List<LcCustLimitExtInfoDo>
     */
    List<LcCustLimitExtInfoDo> selectByExample(LcCustLimitExtInfoQuery lcCustLimitExtInfoQuery);

    /**
     * 新增额度拓展信息信息
     *
     * @param lcCustLimitExtInfo 条件
     * @return int>
     */
    int insertBySelective(LcCustLimitExtInfoDo lcCustLimitExtInfo);

    /**
     * 修改额度拓展信息信息
     *
     * @param lcCustLimitExtInfo
     * @return
     */
    int updateBySelective(LcCustLimitExtInfoDo lcCustLimitExtInfo);

    /**
     * 修改额度拓展信息信息
     *
     * @param lcCustLimitExtInfo
     * @param lcCustLimitExtInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcCustLimitExtInfoDo lcCustLimitExtInfo,
        LcCustLimitExtInfoQuery lcCustLimitExtInfoQuery);


    /**
     * 根据map查询
     * @param query
     * @return
     */
    List<LcCustLimitExtInfoDo> selectByCustRelationBizInfoMap(List<LcCustLimitExtInfoQuery> query);
}
