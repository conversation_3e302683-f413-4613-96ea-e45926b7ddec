package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 电票系统-落地表-同业客户额度同步主键
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
@Table(name = "lb_s_elcbl_ibnk_lmt_synz")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbSElcblIbnkLmtSynzKeyDo implements Serializable {

    private static final long serialVersionUID = 1945747816769060868L;
    /** 主键ID */
    @Id
    @Column(name = "id")
    private String id;
    /** 同业客户编号 */
    @Column(name = "ibnk_user_id")
    private String ibnkUserId;
}