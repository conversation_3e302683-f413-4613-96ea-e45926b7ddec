# 业务层开发规范

## 接口定义规范

### 业务接口层 (biz)
位于 [hsjry-limit-batch-biz](mdc:hsjry-limit-batch-biz) 模块：

#### 接口命名
```java
// 业务接口以 Biz 结尾
public interface AmtLimitBiz {
    /**
     * 处理金额限额验证业务
     */
    ShardingResult amtLimitValid(SharedBo sharedBo);
}
```

#### 接口设计原则
- **单一职责**: 每个接口负责一个具体的业务领域
- **参数统一**: 使用 `SharedBo` 作为主要参数传递对象
- **返回统一**: 返回 `ShardingResult` 表示处理结果
- **异常处理**: 明确定义业务异常类型

### 主要业务接口
- [AmtLimitBiz.java](mdc:hsjry-limit-batch-biz/src/main/java/com/hsjry/core/limit/batch/biz/AmtLimitBiz.java) - 金额限额业务接口
- [AmtLimitRuleBiz.java](mdc:hsjry-limit-batch-biz/src/main/java/com/hsjry/core/limit/batch/biz/AmtLimitRuleBiz.java) - 金额限额规则业务接口
- [ICustLimitBiz.java](mdc:hsjry-limit-batch-biz/src/main/java/com/hsjry/core/limit/batch/biz/ICustLimitBiz.java) - 客户限额业务接口

## 实现层规范

### 业务实现层 (biz-impl)
位于 [hsjry-limit-batch-biz-impl](mdc:hsjry-limit-batch-biz-impl) 模块：

#### 实现类结构
```java
@Service
@Slf4j
public class AmtLimitBizImpl implements AmtLimitBiz {
    
    @Autowired
    private CustLimitInfoBatchDao custLimitInfoBatchDao;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShardingResult amtLimitValid(SharedBo sharedBo) {
        try {
            // 1. 参数验证
            validateParameters(sharedBo);
            
            // 2. 业务逻辑处理
            processAmtLimitValidation(sharedBo);
            
            // 3. 返回结果
            return buildSuccessResult();
            
        } catch (Exception e) {
            log.error("金额限额验证失败", e);
            return buildFailureResult(e);
        }
    }
}
```

### 主要实现类
- [AmtLimitBizImpl.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/impl/AmtLimitBizImpl.java) - 金额限额业务实现
- [AmtLimitRuleBizImpl.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/impl/AmtLimitRuleBizImpl.java) - 金额限额规则业务实现

## 分片处理规范

### 分片业务设计
分片相关类位于 `biz/job/sharding/` 目录：

#### 分片工厂
[JobCoreBusinessFactory.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/sharding/JobCoreBusinessFactory.java) - 作业核心业务工厂

#### 分片处理模式
```java
public class ShardingBizImpl {
    
    /**
     * 分片处理数据
     */
    public ShardingResult processSharding(SharedBo sharedBo) {
        int shardNum = LimitBatchConstants.AMT_LIMIT_DETAIL_SHARD_NUM;
        int totalCount = 0;
        int successCount = 0;
        
        for (int i = 0; i < shardNum; i++) {
            try {
                // 构建分片查询条件
                Query query = buildShardingQuery(sharedBo, i, shardNum);
                
                // 分页处理
                List<DataObject> dataList = processPage(query);
                
                // 处理每个分片的数据
                processShardData(dataList);
                
                successCount += dataList.size();
            } catch (Exception e) {
                log.error("分片{}处理失败", i, e);
            }
            totalCount++;
        }
        
        return buildShardingResult(totalCount, successCount);
    }
}
```

### 分片常量使用
使用 [LimitBatchConstants.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/constants/LimitBatchConstants.java) 中定义的分片常量：
- `AMT_LIMIT_DETAIL_SHARD_NUM = 10` - 限额明细分片数量

## 事务管理规范

### 事务注解使用
```java
@Transactional(rollbackFor = Exception.class)
public ShardingResult handleBusiness(SharedBo sharedBo) {
    // 业务逻辑
}
```

### 事务传播机制
- **REQUIRED**: 默认传播机制，适用于大部分业务方法
- **REQUIRES_NEW**: 用于独立事务处理，如日志记录
- **NOT_SUPPORTED**: 用于查询操作，提高性能

### 分布式事务考虑
- 对于跨数据源操作，考虑使用分布式事务管理
- 合理设计补偿机制，处理事务失败场景

## 异常处理规范

### 异常类型
使用 [EnumLimitBatchErrorCode.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/enums/EnumLimitBatchErrorCode.java) 中定义的错误码

### 异常处理模式
```java
public ShardingResult processBusiness(SharedBo sharedBo) {
    try {
        // 业务逻辑
        return buildSuccessResult();
    } catch (BusinessException e) {
        log.warn("业务异常：{}", e.getMessage());
        return buildBusinessFailureResult(e);
    } catch (SystemException e) {
        log.error("系统异常", e);
        return buildSystemFailureResult(e);
    } catch (Exception e) {
        log.error("未知异常", e);
        return buildUnknownFailureResult(e);
    }
}
```

## 数据转换规范

### 转换器位置
转换器位于 `biz-impl/convert/` 目录：
- `copy/` - 复制相关转换器
- `file/` - 文件相关转换器
- `mid/` - 中间对象转换器

### 转换器命名
- 转换器类以 `Converter` 结尾
- 转换规则类以 `Cnvs` 结尾

### 转换器示例
```java
@Component
public class LbCEntityInfoConverter {
    
    public TargetBo convertToTargetBo(SourceDo sourceDo) {
        if (sourceDo == null) {
            return null;
        }
        // 转换逻辑
        return targetBo;
    }
}
```

## 性能优化指南

### 批量处理
- 使用批量查询减少数据库交互
- 合理设置批处理大小
- 避免在循环中执行数据库操作

### 内存管理
- 及时释放大对象引用
- 使用流式处理大数据集
- 合理设置JVM内存参数

### 缓存策略
- 适当使用本地缓存
- 缓存静态配置数据
- 注意缓存一致性问题
