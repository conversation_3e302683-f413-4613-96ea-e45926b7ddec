package com.hsjry.core.limit.center.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度体系模板节点主键
 *
 * <AUTHOR>
 * @date 2023-08-11 07:45:57
 */
@Table(name = "lc_cust_limit_template_node")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LcCustLimitTemplateNodeKeyDo implements Serializable {

    private static final long serialVersionUID = 1689905980290301952L;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
    /** 体系模板节点编号 */
    @Id
    @Column(name = "template_node_id")
    private String templateNodeId;
}