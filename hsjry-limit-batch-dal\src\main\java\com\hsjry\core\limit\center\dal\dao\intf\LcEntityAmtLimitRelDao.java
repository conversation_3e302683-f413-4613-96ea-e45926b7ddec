package com.hsjry.core.limit.center.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.center.dal.dao.model.LcEntityAmtLimitRelDo;
import com.hsjry.core.limit.center.dal.dao.query.LcEntityAmtLimitRelQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 实体限额关系数据库操作接口
 *
 * <AUTHOR>
 * @date 2022-12-13 08:34:27
 */
public interface LcEntityAmtLimitRelDao extends IBaseDao<LcEntityAmtLimitRelDo> {
    /**
     * 分页查询实体限额关系
     *
     * @param lcEntityAmtLimitRelQuery 条件
     * @return PageInfo<LcEntityAmtLimitRelDo>
     */
    PageInfo<LcEntityAmtLimitRelDo> selectPage(LcEntityAmtLimitRelQuery lcEntityAmtLimitRelQuery, PageParam pageParam);

    /**
     * 根据key查询实体限额关系
     *
     * @param entityAmtLimitId
     * @return
     */
    LcEntityAmtLimitRelDo selectByKey(String entityAmtLimitId);

    /**
     * 根据key删除实体限额关系
     *
     * @param entityAmtLimitId
     * @return
     */
    int deleteByKey(String entityAmtLimitId);

    /**
     * 查询实体限额关系信息
     *
     * @param lcEntityAmtLimitRelQuery 条件
     * @return List<LcEntityAmtLimitRelDo>
     */
    List<LcEntityAmtLimitRelDo> selectByExample(LcEntityAmtLimitRelQuery lcEntityAmtLimitRelQuery);

    /**
     * 新增实体限额关系信息
     *
     * @param lcEntityAmtLimitRel 条件
     * @return int>
     */
    int insertBySelective(LcEntityAmtLimitRelDo lcEntityAmtLimitRel);

    /**
     * 修改实体限额关系信息
     *
     * @param lcEntityAmtLimitRel
     * @return
     */
    int updateBySelective(LcEntityAmtLimitRelDo lcEntityAmtLimitRel);

    /**
     * 修改实体限额关系信息
     *
     * @param lcEntityAmtLimitRel
     * @param lcEntityAmtLimitRelQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LcEntityAmtLimitRelDo lcEntityAmtLimitRel,
        LcEntityAmtLimitRelQuery lcEntityAmtLimitRelQuery);

    /**
     * 删除实体限额关系信息
     *
     * @param lcEntityAmtLimitRelQuery 条件
     * @return List<LcEntityAmtLimitRelDo>
     */
    int deleteByExample(LcEntityAmtLimitRelQuery lcEntityAmtLimitRelQuery);
}
